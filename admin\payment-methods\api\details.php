<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/PaymentMethod.php';

header('Content-Type: application/json');

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get payment method ID
    $id = (int)($_GET['id'] ?? 0);
    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
        exit;
    }
    
    // Load payment method
    $paymentMethod = PaymentMethod::find($id);
    if (!$paymentMethod) {
        echo json_encode(['success' => false, 'message' => 'Payment method not found']);
        exit;
    }
    
    // Get usage statistics
    $depositsCount = $paymentMethod->getDepositsCount();
    $totalDeposits = $paymentMethod->getTotalDepositsAmount();
    
    // Get pending deposits count
    $db = getDBConnection();
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM deposits WHERE payment_method_id = ? AND status = 'pending'");
    $stmt->execute([$id]);
    $pendingDeposits = $stmt->fetch()['count'];
    
    echo json_encode([
        'success' => true,
        'data' => [
            'id' => $paymentMethod->getId(),
            'name' => $paymentMethod->name,
            'type' => $paymentMethod->type,
            'status' => $paymentMethod->status,
            'sort_order' => $paymentMethod->sort_order,
            'details_array' => $paymentMethod->getDetailsArray(),
            'formatted_details' => $paymentMethod->getFormattedDetails(),
            'created_at' => $paymentMethod->created_at,
            'updated_at' => $paymentMethod->updated_at,
            'stats' => [
                'deposits_count' => $depositsCount,
                'total_deposits' => $totalDeposits,
                'pending_deposits' => $pendingDeposits
            ]
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Payment method details error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading payment method details'
    ]);
}
?>