<?php
require_once __DIR__ . '/../../includes/db_connect.php';

/**
 * Base Model class providing common CRUD operations and database functionality
 * All model classes should extend this base class
 * 
 * @abstract
 * @package Models
 */
abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    protected $data = [];
    protected $original = [];
    protected $exists = false;
    
    // Validation errors
    protected $errors = [];
    
    public function __construct($data = []) {
        $this->db = getDB();
        if (!empty($data)) {
            $this->fill($data);
            $this->exists = isset($data[$this->primaryKey]);
            $this->original = $this->data;
        }
    }
    
    /**
     * Fill the model with data
     */
    public function fill(array $data) {
        foreach ($data as $key => $value) {
            if (in_array($key, $this->fillable) || empty($this->fillable)) {
                $this->data[$key] = $value;
            }
        }
        return $this;
    }
    
    /**
     * Get attribute value
     */
    public function __get($key) {
        return $this->data[$key] ?? null;
    }
    
    /**
     * Set attribute value
     */
    public function __set($key, $value) {
        if (in_array($key, $this->fillable) || empty($this->fillable)) {
            $this->data[$key] = $value;
        }
    }
    
    /**
     * Check if attribute exists
     */
    public function __isset($key) {
        return isset($this->data[$key]);
    }
    
    /**
     * Get all attributes as array
     */
    public function toArray() {
        $data = $this->data;
        foreach ($this->hidden as $hidden) {
            unset($data[$hidden]);
        }
        return $data;
    }
    
    /**
     * Get all attributes including hidden ones
     */
    public function getAllAttributes() {
        return $this->data;
    }
    
    /**
     * Save the model to database
     */
    public function save() {
        if ($this->exists) {
            return $this->update();
        } else {
            return $this->insert();
        }
    }
    
    /**
     * Insert new record
     */
    protected function insert() {
        $data = $this->data;
        
        if ($this->timestamps) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        
        try {
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->data[$this->primaryKey] = $this->db->lastInsertId();
                $this->exists = true;
                $this->original = $this->data;
                return true;
            }
        } catch (PDOException $e) {
            error_log("Insert error: " . $e->getMessage());
            return false;
        }
        
        return false;
    }
    
    /**
     * Update existing record
     */
    protected function update() {
        $data = $this->data;
        $id = $data[$this->primaryKey];
        unset($data[$this->primaryKey]);
        
        if ($this->timestamps) {
            $data['updated_at'] = date('Y-m-d H:i:s');
        }
        
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = :id";
        $data['id'] = $id;
        
        try {
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute($data);
            
            if ($result) {
                $this->original = $this->data;
                return true;
            }
        } catch (PDOException $e) {
            error_log("Update error: " . $e->getMessage());
            return false;
        }
        
        return false;
    }
    
    /**
     * Delete the model from database
     */
    public function delete() {
        if (!$this->exists) {
            return false;
        }
        
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        
        try {
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$this->data[$this->primaryKey]]);
            
            if ($result) {
                $this->exists = false;
                return true;
            }
        } catch (PDOException $e) {
            error_log("Delete error: " . $e->getMessage());
            return false;
        }
        
        return false;
    }
    
    /**
     * Find record by ID with caching
     */
    public static function find($id) {
        // Simple in-memory cache for single request
        static $cache = [];
        $cacheKey = static::class . ':' . $id;
        
        if (isset($cache[$cacheKey])) {
            return $cache[$cacheKey];
        }
        
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE {$instance->primaryKey} = :id LIMIT 1";
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute(['id' => $id]);
            $data = $stmt->fetch();
            
            if ($data) {
                $model = new static($data);
                $cache[$cacheKey] = $model;
                return $model;
            }
        } catch (PDOException $e) {
            error_log("Find error: " . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * Find all records
     */
    public static function all($limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table}";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute();
            $results = $stmt->fetchAll();
            
            $models = [];
            foreach ($results as $data) {
                $models[] = new static($data);
            }
            
            return $models;
        } catch (PDOException $e) {
            error_log("All error: " . $e->getMessage());
        }
        
        return [];
    }
    
    /**
     * Find all records with conditions and limit
     */
    public static function findAll($conditions = [], $limit = null, $orderBy = null) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table}";
        $params = [];
        
        if (!empty($conditions)) {
            // Filter out non-WHERE clause parameters that might be passed incorrectly
            $reservedKeys = ['order', 'limit', 'offset', 'joins', 'select', 'group', 'having'];
            $filteredConditions = array_diff_key($conditions, array_flip($reservedKeys));

            if (!empty($filteredConditions)) {
                $whereClauses = [];
                foreach ($filteredConditions as $column => $value) {
                    $whereClauses[] = "{$column} = :{$column}";
                    $params[$column] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClauses);
            }
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute($params);
            $results = $stmt->fetchAll();
            
            $models = [];
            foreach ($results as $data) {
                $models[] = new static($data);
            }
            
            return $models;
        } catch (PDOException $e) {
            error_log("FindAll error: " . $e->getMessage());
        }
        
        return [];
    }
    
    /**
     * Find records by condition
     */
    public static function where($column, $operator, $value = null) {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE {$column} {$operator} :value";
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute(['value' => $value]);
            $results = $stmt->fetchAll();
            
            $models = [];
            foreach ($results as $data) {
                $models[] = new static($data);
            }
            
            return $models;
        } catch (PDOException $e) {
            error_log("Where error: " . $e->getMessage());
        }
        
        return [];
    }
    
    /**
     * Count records
     */
    public static function count($conditions = [], $params = []) {
        $instance = new static();
        $sql = "SELECT COUNT(*) as count FROM {$instance->table}";
        $queryParams = [];

        if (!empty($conditions)) {
            if (is_string($conditions)) {
                // Handle string conditions like "status = 'approved'"
                $sql .= " WHERE " . $conditions;
                $queryParams = $params;
            } elseif (is_array($conditions)) {
                // Filter out non-WHERE clause parameters that might be passed incorrectly
                $reservedKeys = ['order', 'limit', 'offset', 'joins', 'select', 'group', 'having'];
                $filteredConditions = array_diff_key($conditions, array_flip($reservedKeys));

                if (!empty($filteredConditions)) {
                    // Handle array conditions
                    $whereClauses = [];
                    foreach ($filteredConditions as $column => $value) {
                        $whereClauses[] = "{$column} = :{$column}";
                        $queryParams[$column] = $value;
                    }
                    $sql .= " WHERE " . implode(' AND ', $whereClauses);
                }
            }
        }
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute($queryParams);
            $result = $stmt->fetch();
            
            return (int) $result['count'];
        } catch (PDOException $e) {
            error_log("Count error: " . $e->getMessage());
        }
        
        return 0;
    }
    
    /**
     * Sum records
     */
    public static function sum($column, $conditions = []) {
        $instance = new static();
        $sql = "SELECT SUM({$column}) as total FROM {$instance->table}";
        $params = [];
        
        if (!empty($conditions)) {
            // Filter out non-WHERE clause parameters that might be passed incorrectly
            $reservedKeys = ['order', 'limit', 'offset', 'joins', 'select', 'group', 'having'];
            $filteredConditions = array_diff_key($conditions, array_flip($reservedKeys));

            if (!empty($filteredConditions)) {
                $whereClauses = [];
                foreach ($filteredConditions as $col => $value) {
                    $whereClauses[] = "{$col} = :{$col}";
                    $params[$col] = $value;
                }
                $sql .= " WHERE " . implode(' AND ', $whereClauses);
            }
        }
        
        try {
            $stmt = $instance->db->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            return (float) ($result['total'] ?? 0);
        } catch (PDOException $e) {
            error_log("Sum error: " . $e->getMessage());
        }
        
        return 0;
    }
    
    /**
     * Execute raw SQL query
     */
    protected function query($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Query error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Begin database transaction
     */
    protected function beginTransaction() {
        return $this->db->beginTransaction();
    }
    
    /**
     * Commit database transaction
     */
    protected function commit() {
        return $this->db->commit();
    }
    
    /**
     * Rollback database transaction
     */
    protected function rollback() {
        return $this->db->rollback();
    }
    
    /**
     * Check if model has been modified
     */
    public function isDirty() {
        return $this->data !== $this->original;
    }
    
    /**
     * Get the primary key value
     */
    public function getId() {
        return $this->data[$this->primaryKey] ?? null;
    }
    
    /**
     * Check if model exists in database
     */
    public function exists() {
        return $this->exists;
    }
    
    /**
     * Validation method - to be overridden in child classes
     */
    public function validate() {
        return [];
    }
    
    /**
     * Check if model is valid
     */
    public function isValid() {
        return empty($this->validate());
    }
}
?>