<?php
require_once __DIR__ . '/InputSanitizer.php';
require_once __DIR__ . '/../validators/SecurityValidator.php';

/**
 * Security Middleware
 * Handles request security validation and sanitization
 */
class SecurityMiddleware {
    private static $instance = null;
    private $securityValidator;
    private $config;
    
    private function __construct() {
        $this->securityValidator = new SecurityValidator();
        $this->config = [
            'max_request_size' => 10485760, // 10MB
            'allowed_methods' => ['GET', 'POST', 'PUT', 'DELETE'],
            'rate_limit_enabled' => true,
            'csrf_protection' => true,
            'xss_protection' => true,
            'sql_injection_protection' => true
        ];
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Process incoming request
     */
    public function processRequest() {
        $this->validateRequestMethod();
        $this->validateRequestSize();
        $this->validateHeaders();
        $this->sanitizeGlobalInputs();
        $this->validateSession();
        
        if ($this->config['rate_limit_enabled']) {
            $this->checkRateLimit();
        }
        
        return true;
    }
    
    /**
     * Validate HTTP method
     */
    private function validateRequestMethod() {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        if (!in_array($method, $this->config['allowed_methods'])) {
            $this->blockRequest('Invalid HTTP method', 405);
        }
    }
    
    /**
     * Validate request size
     */
    private function validateRequestSize() {
        $contentLength = $_SERVER['CONTENT_LENGTH'] ?? 0;
        
        if ($contentLength > $this->config['max_request_size']) {
            $this->blockRequest('Request too large', 413);
        }
    }
    
    /**
     * Validate security headers
     */
    private function validateHeaders() {
        // Check for suspicious headers
        $suspiciousHeaders = [
            'HTTP_X_FORWARDED_FOR' => '/[<>"\']/',
            'HTTP_USER_AGENT' => '/<script|javascript:|vbscript:/i',
            'HTTP_REFERER' => '/<script|javascript:|vbscript:/i'
        ];
        
        foreach ($suspiciousHeaders as $header => $pattern) {
            if (isset($_SERVER[$header]) && preg_match($pattern, $_SERVER[$header])) {
                $this->blockRequest('Suspicious header detected', 400);
            }
        }
        
        // Validate Content-Type for POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            $allowedTypes = [
                'application/x-www-form-urlencoded',
                'multipart/form-data',
                'application/json'
            ];
            
            $isValidType = false;
            foreach ($allowedTypes as $type) {
                if (strpos($contentType, $type) === 0) {
                    $isValidType = true;
                    break;
                }
            }
            
            if (!$isValidType && !empty($contentType)) {
                $this->blockRequest('Invalid content type', 400);
            }
        }
    }
    
    /**
     * Sanitize global input arrays
     */
    private function sanitizeGlobalInputs() {
        if ($this->config['xss_protection']) {
            $_GET = InputSanitizer::sanitizeUrlParams($_GET);
            $_POST = InputSanitizer::sanitizeForm($_POST);
            $_COOKIE = $this->sanitizeCookies($_COOKIE);
        }
        
        if ($this->config['sql_injection_protection']) {
            $_GET = $this->applySQLProtection($_GET);
            $_POST = $this->applySQLProtection($_POST);
        }
    }
    
    /**
     * Sanitize cookies
     */
    private function sanitizeCookies($cookies) {
        $sanitized = [];
        foreach ($cookies as $name => $value) {
            $sanitizedName = preg_replace('/[^a-zA-Z0-9_\-]/', '', $name);
            $sanitized[$sanitizedName] = ValidationHelper::sanitize($value);
        }
        return $sanitized;
    }
    
    /**
     * Apply SQL injection protection
     */
    private function applySQLProtection($data) {
        if (is_array($data)) {
            return array_map([$this, 'applySQLProtection'], $data);
        }
        return ValidationHelper::preventSQLInjection($data);
    }
    
    /**
     * Validate session security
     */
    private function validateSession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            if (!$this->securityValidator->validateSession()) {
                session_destroy();
                $this->blockRequest('Session security violation', 401);
            }
        }
    }
    
    /**
     * Check rate limiting
     */
    private function checkRateLimit() {
        $ip = $this->getClientIP();
        $endpoint = $_SERVER['REQUEST_URI'] ?? '/';
        
        // Different limits for different endpoints
        $limits = [
            '/login.php' => ['attempts' => 5, 'window' => 300],
            '/register.php' => ['attempts' => 3, 'window' => 600],
            '/forgot-password.php' => ['attempts' => 3, 'window' => 600],
            'default' => ['attempts' => 100, 'window' => 60]
        ];
        
        $limit = $limits[$endpoint] ?? $limits['default'];
        
        if (!$this->securityValidator->validateRateLimit(
            'request_' . $endpoint, 
            $ip, 
            $limit['attempts'], 
            $limit['window']
        )) {
            $this->blockRequest('Rate limit exceeded', 429);
        }
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Block request and log security event
     */
    private function blockRequest($reason, $httpCode = 403) {
        $this->logSecurityEvent($reason, $httpCode);
        
        http_response_code($httpCode);
        
        // Send appropriate response based on content type
        if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Request blocked for security reasons']);
        } else {
            header('Content-Type: text/html');
            echo '<h1>Access Denied</h1><p>Your request was blocked for security reasons.</p>';
        }
        
        exit();
    }
    
    /**
     * Log security events
     */
    private function logSecurityEvent($reason, $httpCode) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
            'uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'reason' => $reason,
            'http_code' => $httpCode,
            'user_id' => $_SESSION['user_id'] ?? null
        ];
        
        $logEntry = json_encode($logData) . "\n";
        
        // Log to security log file
        $logFile = __DIR__ . '/../../logs/security.log';
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Also log to PHP error log
        error_log("Security Event: $reason - IP: " . $this->getClientIP());
    }
    
    /**
     * Set security headers
     */
    public function setSecurityHeaders() {
        // Prevent XSS
        header('X-XSS-Protection: 1; mode=block');
        
        // Prevent MIME type sniffing
        header('X-Content-Type-Options: nosniff');
        
        // Prevent clickjacking
        header('X-Frame-Options: DENY');
        
        // HSTS (if using HTTPS)
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
        }
        
        // Content Security Policy
        $csp = "default-src 'self'; " .
               "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "img-src 'self' data: https:; " .
               "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "connect-src 'self'; " .
               "frame-ancestors 'none';";
        
        header("Content-Security-Policy: $csp");
        
        // Referrer Policy
        header('Referrer-Policy: strict-origin-when-cross-origin');
        
        // Remove server information
        header_remove('X-Powered-By');
        header_remove('Server');
    }
    
    /**
     * Validate CSRF token for forms
     */
    public function validateCSRF($token) {
        if (!$this->config['csrf_protection']) {
            return true;
        }
        
        // Use ValidationHelper for CSRF validation
        require_once __DIR__ . '/../validators/ValidationHelper.php';
        $error = ValidationHelper::validateCSRFToken($token);
        return $error === null;
    }
    
    /**
     * Check if IP is allowed
     */
    public function validateIP($ip = null) {
        $ip = $ip ?: $this->getClientIP();
        return $this->securityValidator->validateIPAddress($ip);
    }
    
    /**
     * Get security configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Update security configuration
     */
    public function updateConfig($key, $value) {
        if (array_key_exists($key, $this->config)) {
            $this->config[$key] = $value;
            return true;
        }
        return false;
    }
}
?>