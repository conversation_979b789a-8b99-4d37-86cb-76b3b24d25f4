<?php
// Simple SMTP test
require_once __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

echo "=== SMTP Connection and Email Test ===\n";

// SMTP Configuration
$smtp_host = 'smtp.hostinger.com';
$smtp_port = 465;
$smtp_username = '<EMAIL>';
$smtp_password = 'Money2025@Demo#';
$from_name = 'Coinage Trading';
$from_email = '<EMAIL>';

echo "SMTP Host: $smtp_host\n";
echo "SMTP Port: $smtp_port\n";
echo "Username: $smtp_username\n";
echo "From: $from_name <$from_email>\n\n";

try {
    $mail = new PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host = $smtp_host;
    $mail->SMTPAuth = true;
    $mail->Username = $smtp_username;
    $mail->Password = $smtp_password;
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port = $smtp_port;
    
    echo "=== Testing SMTP Connection ===\n";
    
    // Test connection
    if ($mail->smtpConnect()) {
        echo "✓ SMTP connection successful!\n";
        $mail->smtpClose();
        
        // Send test email
        echo "\n=== Sending Test Email ===\n";
        
        $mail->setFrom($from_email, $from_name);
        $mail->addAddress('<EMAIL>', 'Test Recipient');
        
        $mail->isHTML(true);
        $mail->Subject = 'Test Email from Coinage Trading Platform';
        
        $mail->Body = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #007bff, #6c757d); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
                .button { display: inline-block; background: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 Coinage Trading</h1>
                    <p>SMTP Test Email - Success!</p>
                </div>
                <div class="content">
                    <h2>Hello from Coinage Trading Platform!</h2>
                    <p>This is a test email to verify that our SMTP configuration is working correctly.</p>
                    
                    <p><strong>Test Details:</strong></p>
                    <ul>
                        <li>📅 Sent at: ' . date('Y-m-d H:i:s') . '</li>
                        <li>📧 From: ' . $from_email . '</li>
                        <li>🌐 SMTP Host: ' . $smtp_host . '</li>
                        <li>🔌 Port: ' . $smtp_port . '</li>
                        <li>🔐 Encryption: SSL/TLS</li>
                    </ul>
                    
                    <p>✅ If you received this email, the SMTP configuration is working perfectly!</p>
                    
                    <div style="text-align: center;">
                        <a href="http://localhost/coinage/" class="button">Visit Platform</a>
                    </div>
                </div>
                <div class="footer">
                    <p>&copy; 2025 Coinage Trading. All rights reserved.</p>
                    <p>This is an automated test email from the development environment.</p>
                </div>
            </div>
        </body>
        </html>';
        
        $mail->AltBody = 'This is a test email from Coinage Trading Platform. SMTP configuration test successful!';
        
        if ($mail->send()) {
            echo "✓ Test email sent successfully to: <EMAIL>\n";
            echo "📧 Please check the inbox (and spam folder) for the test email.\n";
            echo "📋 Subject: Test Email from Coinage Trading Platform\n";
        } else {
            echo "✗ Failed to send test email!\n";
        }
        
    } else {
        echo "✗ SMTP connection failed!\n";
        echo "Please check your SMTP settings.\n";
    }
    
} catch (Exception $e) {
    echo "✗ SMTP Error: " . $e->getMessage() . "\n";
    echo "Debug info: " . $mail->ErrorInfo . "\n";
}

echo "\n=== Test Completed ===\n";
?>