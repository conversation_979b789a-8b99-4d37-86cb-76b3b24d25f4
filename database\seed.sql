-- Crypto Trading Platform Database Seeding
-- Insert default data for system initialization

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, category, is_public) VALUES
('site_name', 'Coinage Trading', 'string', 'Website name displayed across the platform', 'general', true),
('site_currency', 'USD', 'string', 'Default currency for the platform', 'general', true),
('currency_symbol', '$', 'string', 'Currency symbol to display', 'general', true),
('registration_bonus', '0', 'number', 'Bonus amount given to new users', 'financial', false),
('deposit_bonus_percent', '10', 'number', 'Percentage bonus on deposits', 'financial', false),
('min_withdrawal_amount', '50', 'number', 'Minimum withdrawal amount', 'financial', false),
('max_withdrawal_amount', '10000', 'number', 'Maximum withdrawal amount per transaction', 'financial', false),
('withdrawal_fee_percent', '2', 'number', 'Withdrawal fee percentage', 'financial', false),
('email_verification_required', 'true', 'boolean', 'Require email verification for new accounts', 'security', false),
('two_fa_enforcement', 'false', 'boolean', 'Force all users to enable 2FA', 'security', false),
('kyc_verification_required', 'false', 'boolean', 'Require KYC verification for withdrawals', 'security', false),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'system', false),
('email_notifications', 'true', 'boolean', 'Enable email notifications', 'notifications', false),
('sms_notifications', 'false', 'boolean', 'Enable SMS notifications', 'notifications', false),
('primary_color', '#007bff', 'string', 'Primary theme color', 'appearance', true),
('secondary_color', '#6c757d', 'string', 'Secondary theme color', 'appearance', true),
('logo_url', '', 'string', 'Platform logo URL', 'appearance', true),
('favicon_url', '', 'string', 'Platform favicon URL', 'appearance', true),
('contact_email', '<EMAIL>', 'string', 'Contact email address', 'contact', true),
('contact_phone', '******-0123', 'string', 'Contact phone number', 'contact', true),
('company_address', '123 Trading Street, Finance City, FC 12345', 'string', 'Company address', 'contact', true);

-- Insert default trading plans
INSERT INTO trading_plans (name, min_deposit, max_deposit, daily_return, duration_days, description, features, sort_order, status) VALUES
('STARTER PLAN', 100.00, 999.99, 1.5, 30, 'Perfect for beginners looking to start their trading journey', '["24/7 Support", "Basic Analytics", "Mobile App Access"]', 1, 'active'),
('BASIC PLAN', 1000.00, 4999.99, 2.5, 30, 'Ideal for regular traders with moderate investment capacity', '["24/7 Support", "Advanced Analytics", "Mobile App Access", "Priority Support"]', 2, 'active'),
('GOLD PLAN', 5000.00, 19999.99, 3.5, 30, 'Premium plan for serious traders with higher returns', '["24/7 Support", "Advanced Analytics", "Mobile App Access", "Priority Support", "Personal Account Manager"]', 3, 'active'),
('PLATINUM PLAN', 20000.00, 49999.99, 4.5, 30, 'Elite plan for professional traders', '["24/7 Support", "Advanced Analytics", "Mobile App Access", "Priority Support", "Personal Account Manager", "Exclusive Market Insights"]', 4, 'active'),
('VIP PLAN', 50000.00, 999999.99, 6.0, 30, 'Ultimate plan for high-net-worth individuals', '["24/7 Support", "Advanced Analytics", "Mobile App Access", "Priority Support", "Personal Account Manager", "Exclusive Market Insights", "VIP Events Access"]', 5, 'active');

-- Insert default payment methods
INSERT INTO payment_methods (name, type, details, status, sort_order) VALUES
('Bitcoin (BTC)', 'crypto', '{"address": "******************************************", "network": "Bitcoin", "confirmations": 3, "fee": "0.0005 BTC"}', 'active', 1),
('Ethereum (ETH)', 'crypto', '{"address": "******************************************", "network": "Ethereum", "confirmations": 12, "fee": "0.01 ETH"}', 'active', 2),
('Tether (USDT)', 'crypto', '{"address": "TQn9Y2khEsLJW1ChVWFMSMeRDow5oRP8V", "network": "Tron (TRC20)", "confirmations": 20, "fee": "1 USDT"}', 'active', 3),
('USD Coin (USDC)', 'crypto', '{"address": "******************************************", "network": "Ethereum (ERC20)", "confirmations": 12, "fee": "2 USDC"}', 'active', 4),
('PayPal', 'paypal', '{"email": "<EMAIL>", "fee_percent": "3.5", "processing_time": "1-2 business days"}', 'active', 5),
('Bank Transfer', 'bank', '{"account_name": "Coinage Trading Ltd", "account_number": "**********", "routing_number": "*********", "bank_name": "Chase Bank", "swift_code": "CHASUS33", "fee": "$25"}', 'active', 6),
('Skrill', 'other', '{"email": "<EMAIL>", "fee_percent": "2.9", "processing_time": "Instant"}', 'active', 7),
('Neteller', 'other', '{"email": "<EMAIL>", "fee_percent": "2.5", "processing_time": "Instant"}', 'active', 8);

-- Insert default email templates
INSERT INTO email_templates (template_key, subject, body, variables, status) VALUES
('welcome', 'Welcome to {{site_name}}!', 
'<h2>Welcome to {{site_name}}, {{first_name}}!</h2><p>Thank you for joining our trading platform. Your account has been successfully created.</p><p><strong>Account Details:</strong></p><ul><li>Username: {{username}}</li><li>Email: {{email}}</li><li>Registration Date: {{registration_date}}</li></ul><p>To get started:</p><ol><li>Complete your profile information</li><li>Choose a trading plan that suits your needs</li><li>Make your first deposit to start trading</li></ol><p>If you have any questions, feel free to contact our support team.</p><p>Happy Trading!<br>The {{site_name}} Team</p>', 
'["site_name", "first_name", "username", "email", "registration_date"]', 'active'),

('deposit_confirmation', 'Deposit Approved - {{site_name}}', 
'<h2>Deposit Confirmation</h2><p>Dear {{first_name}},</p><p>Your deposit has been successfully approved and credited to your account.</p><p><strong>Deposit Details:</strong></p><ul><li>Amount: {{currency_symbol}}{{deposit_amount}}</li><li>Bonus: {{currency_symbol}}{{bonus_amount}}</li><li>Total Credited: {{currency_symbol}}{{total_credited}}</li><li>Transaction ID: {{transaction_id}}</li><li>Date: {{deposit_date}}</li></ul><p>Your new account balance is: {{currency_symbol}}{{new_balance}}</p><p>Thank you for choosing {{site_name}}!</p>', 
'["site_name", "first_name", "currency_symbol", "deposit_amount", "bonus_amount", "total_credited", "transaction_id", "deposit_date", "new_balance"]', 'active'),

('withdrawal_processed', 'Withdrawal Processed - {{site_name}}', 
'<h2>Withdrawal Processed</h2><p>Dear {{first_name}},</p><p>Your withdrawal request has been processed successfully.</p><p><strong>Withdrawal Details:</strong></p><ul><li>Amount: {{currency_symbol}}{{withdrawal_amount}}</li><li>Method: {{withdrawal_method}}</li><li>Transaction ID: {{transaction_id}}</li><li>Processing Date: {{processing_date}}</li></ul><p>The funds should arrive in your account within the specified timeframe for your chosen withdrawal method.</p><p>Your new account balance is: {{currency_symbol}}{{new_balance}}</p><p>Thank you for using {{site_name}}!</p>', 
'["site_name", "first_name", "currency_symbol", "withdrawal_amount", "withdrawal_method", "transaction_id", "processing_date", "new_balance"]', 'active'),

('password_reset', 'Password Reset Request - {{site_name}}', 
'<h2>Password Reset Request</h2><p>Dear {{first_name}},</p><p>You have requested to reset your password for your {{site_name}} account.</p><p>Click the link below to reset your password:</p><p><a href="{{reset_link}}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Reset Password</a></p><p>This link will expire in 1 hour for security reasons.</p><p>If you did not request this password reset, please ignore this email.</p><p>Best regards,<br>The {{site_name}} Team</p>', 
'["site_name", "first_name", "reset_link"]', 'active'),

('support_ticket_created', 'Support Ticket Created - {{site_name}}', 
'<h2>Support Ticket Created</h2><p>Dear {{first_name}},</p><p>Your support ticket has been created successfully.</p><p><strong>Ticket Details:</strong></p><ul><li>Ticket ID: #{{ticket_id}}</li><li>Subject: {{ticket_subject}}</li><li>Priority: {{ticket_priority}}</li><li>Created: {{created_date}}</li></ul><p>Our support team will review your request and respond as soon as possible.</p><p>You can track the status of your ticket in your account dashboard.</p><p>Thank you for contacting {{site_name}} support!</p>', 
'["site_name", "first_name", "ticket_id", "ticket_subject", "ticket_priority", "created_date"]', 'active');

-- Create default super admin user (password: admin123)
-- Note: This should be changed immediately after installation
INSERT INTO users (
    username, 
    email, 
    password, 
    first_name, 
    last_name, 
    role, 
    status, 
    email_verified,
    balance,
    bonus
) VALUES (
    'superadmin', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'Super', 
    'Admin', 
    'superadmin', 
    'active', 
    1,
    0.00,
    0.00
);

-- Create default admin user (password: admin123)
INSERT INTO users (
    username, 
    email, 
    password, 
    first_name, 
    last_name, 
    role, 
    status, 
    email_verified,
    balance,
    bonus
) VALUES (
    'admin', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
    'Admin', 
    'User', 
    'admin', 
    'active', 
    1,
    0.00,
    0.00
);

-- Create demo user for testing (password: demo123)
INSERT INTO users (
    username, 
    email, 
    password, 
    first_name, 
    last_name, 
    role, 
    status, 
    email_verified,
    balance,
    bonus
) VALUES (
    'demo', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: demo123
    'Demo', 
    'User', 
    'user', 
    'active', 
    1,
    1000.00,
    100.00
);

-- Insert sample transaction for demo user
INSERT INTO transactions (
    user_id, 
    type, 
    amount, 
    balance_before, 
    balance_after, 
    description, 
    reference_id, 
    reference_type, 
    status
) VALUES (
    3, -- demo user
    'admin_credit', 
    1100.00, 
    0.00, 
    1100.00, 
    'Initial demo balance and bonus', 
    'DEMO_INIT_001', 
    'manual', 
    'completed'
);-- Ins
ert default email templates
INSERT INTO email_templates (template_type, template_name, subject, body_html, body_text, is_active) VALUES
('welcome', 'Welcome Email', 'Welcome to {{site_name}}!', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #007bff;">Welcome to {{site_name}}, {{first_name}}!</h2>
    <p>Thank you for joining our trading platform. Your account has been successfully created.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Account Details:</h3>
        <p><strong>Email:</strong> {{email}}</p>
        <p><strong>Registration Bonus:</strong> {{currency_symbol}}{{registration_bonus}}</p>
    </div>
    <p>You can now log in to your account and start trading:</p>
    <a href="{{login_url}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Welcome to {{site_name}}, {{first_name}}!

Thank you for joining our trading platform. Your account has been successfully created.

Account Details:
Email: {{email}}
Registration Bonus: {{currency_symbol}}{{registration_bonus}}

You can now log in to your account and start trading: {{login_url}}

Best regards,
The {{site_name}} Team', TRUE),

('deposit_confirmation', 'Deposit Confirmation', 'Deposit Confirmation - {{currency_symbol}}{{amount}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #28a745;">Deposit Confirmation</h2>
    <p>Dear {{user_name}},</p>
    <p>Your deposit has been successfully processed.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Transaction Details:</h3>
        <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
        <p><strong>Payment Method:</strong> {{payment_method}}</p>
        <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
        <p><strong>Date:</strong> {{date}}</p>
    </div>
    <p>The funds have been added to your account balance and are available for trading.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Deposit Confirmation

Dear {{user_name}},

Your deposit has been successfully processed.

Transaction Details:
Amount: {{currency_symbol}}{{amount}}
Payment Method: {{payment_method}}
Transaction ID: {{transaction_id}}
Date: {{date}}

The funds have been added to your account balance and are available for trading.

Best regards,
The {{site_name}} Team', TRUE),

('withdrawal_notification', 'Withdrawal Notification', 'Withdrawal {{status}} - {{currency_symbol}}{{amount}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #ffc107;">Withdrawal {{status}}</h2>
    <p>Dear {{user_name}},</p>
    <p>Your withdrawal request has been {{status}}.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Withdrawal Details:</h3>
        <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
        <p><strong>Status:</strong> {{status}}</p>
        <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
        <p><strong>Date:</strong> {{date}}</p>
    </div>
    <p>If you have any questions, please contact our support team.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Withdrawal {{status}}

Dear {{user_name}},

Your withdrawal request has been {{status}}.

Withdrawal Details:
Amount: {{currency_symbol}}{{amount}}
Status: {{status}}
Transaction ID: {{transaction_id}}
Date: {{date}}

If you have any questions, please contact our support team.

Best regards,
The {{site_name}} Team', TRUE),

('password_reset', 'Password Reset', 'Reset Your Password - {{site_name}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #dc3545;">Password Reset Request</h2>
    <p>Dear {{user_name}},</p>
    <p>You have requested to reset your password. Click the link below to create a new password:</p>
    <div style="text-align: center; margin: 30px 0;">
        <a href="{{reset_link}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">Reset Password</a>
    </div>
    <p><strong>Important:</strong> This link will expire in {{expiry_time}}.</p>
    <p>If you did not request this password reset, please ignore this email.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Password Reset Request

Dear {{user_name}},

You have requested to reset your password. Click the link below to create a new password:

{{reset_link}}

Important: This link will expire in {{expiry_time}}.

If you did not request this password reset, please ignore this email.

Best regards,
The {{site_name}} Team', TRUE);