/**
 * Admin User Management JavaScript
 * Handles all user management functionality in the admin panel
 */

class AdminUserManagement {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeModals();
        this.setupFormValidation();
    }

    bindEvents() {
        // Create user form
        const createUserForm = document.getElementById('createUserForm');
        if (createUserForm) {
            createUserForm.addEventListener('submit', (e) => this.handleCreateUser(e));
        }

        // Edit user form
        const editUserForm = document.getElementById('editUserForm');
        if (editUserForm) {
            editUserForm.addEventListener('submit', (e) => this.handleEditUser(e));
        }

        // Balance management form
        const balanceForm = document.getElementById('balanceForm');
        if (balanceForm) {
            balanceForm.addEventListener('submit', (e) => this.handleBalanceUpdate(e));
        }

        // Plan assignment form
        const planForm = document.getElementById('planForm');
        if (planForm) {
            planForm.addEventListener('submit', (e) => this.handlePlanAssignment(e));
        }

        // Balance action radio buttons
        const balanceActions = document.querySelectorAll('input[name="action"]');
        balanceActions.forEach(radio => {
            radio.addEventListener('change', (e) => this.updateBalanceActionUI(e.target.value));
        });
    }

    initializeModals() {
        // Initialize Bootstrap modals
        this.createUserModal = new bootstrap.Modal(document.getElementById('createUserModal'));
        this.userDetailsModal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
        this.editUserModal = new bootstrap.Modal(document.getElementById('editUserModal'));
        this.balanceModal = new bootstrap.Modal(document.getElementById('balanceModal'));
        this.planModal = new bootstrap.Modal(document.getElementById('planModal'));
    }

    setupFormValidation() {
        // Add real-time validation to forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required], select[required]');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
        }

        // Username validation
        if (field.name === 'username' && value) {
            if (value.length < 3) {
                isValid = false;
                errorMessage = 'Username must be at least 3 characters';
            } else if (!/^[a-zA-Z0-9_]+$/.test(value)) {
                isValid = false;
                errorMessage = 'Username can only contain letters, numbers, and underscores';
            }
        }

        // Password validation
        if (field.type === 'password' && value) {
            if (value.length < 6) {
                isValid = false;
                errorMessage = 'Password must be at least 6 characters';
            }
        }

        // Amount validation
        if (field.name === 'amount' && value) {
            const amount = parseFloat(value);
            if (isNaN(amount) || amount <= 0) {
                isValid = false;
                errorMessage = 'Amount must be greater than 0';
            }
        }

        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    showFieldValidation(field, isValid, errorMessage) {
        const formGroup = field.closest('.mb-3') || field.closest('.col-md-6') || field.parentElement;
        let feedback = formGroup.querySelector('.invalid-feedback');

        // Remove existing validation classes
        field.classList.remove('is-valid', 'is-invalid');

        if (!isValid) {
            field.classList.add('is-invalid');
            if (!feedback) {
                feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                formGroup.appendChild(feedback);
            }
            feedback.textContent = errorMessage;
        } else if (field.value.trim()) {
            field.classList.add('is-valid');
            if (feedback) {
                feedback.remove();
            }
        }
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const formGroup = field.closest('.mb-3') || field.closest('.col-md-6') || field.parentElement;
        const feedback = formGroup.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
    }

    async handleCreateUser(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Add CSRF token
        formData.append('csrf_token', this.getCSRFToken());
        
        // Validate form
        if (!this.validateForm(form)) {
            return;
        }

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        try {
            this.setButtonLoading(submitBtn, 'Creating...');
            
            const response = await fetch('/admin/users/api/create.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', result.message);
                this.createUserModal.hide();
                form.reset();
                this.clearFormValidation(form);
                // Reload page to show new user
                setTimeout(() => window.location.reload(), 1000);
            } else {
                if (result.errors) {
                    this.showFormErrors(form, result.errors);
                } else {
                    this.showAlert('danger', result.message);
                }
            }
        } catch (error) {
            console.error('Error creating user:', error);
            this.showAlert('danger', 'An error occurred while creating the user');
        } finally {
            this.setButtonLoading(submitBtn, originalText, false);
        }
    }

    async handleEditUser(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Add CSRF token
        formData.append('csrf_token', this.getCSRFToken());
        
        // Validate form
        if (!this.validateForm(form)) {
            return;
        }

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        try {
            this.setButtonLoading(submitBtn, 'Updating...');
            
            const response = await fetch('/admin/users/api/update.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', result.message);
                this.editUserModal.hide();
                // Reload page to show updated user
                setTimeout(() => window.location.reload(), 1000);
            } else {
                if (result.errors) {
                    this.showFormErrors(form, result.errors);
                } else {
                    this.showAlert('danger', result.message);
                }
            }
        } catch (error) {
            console.error('Error updating user:', error);
            this.showAlert('danger', 'An error occurred while updating the user');
        } finally {
            this.setButtonLoading(submitBtn, originalText, false);
        }
    }

    async handleBalanceUpdate(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Add CSRF token
        formData.append('csrf_token', this.getCSRFToken());
        
        // Validate form
        if (!this.validateForm(form)) {
            return;
        }

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        const action = formData.get('action');
        
        try {
            this.setButtonLoading(submitBtn, `${action === 'credit' ? 'Crediting' : 'Debiting'}...`);
            
            const response = await fetch('/admin/users/api/balance.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', result.message);
                this.balanceModal.hide();
                form.reset();
                this.clearFormValidation(form);
                // Reload page to show updated balance
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showAlert('danger', result.message);
            }
        } catch (error) {
            console.error('Error updating balance:', error);
            this.showAlert('danger', 'An error occurred while updating the balance');
        } finally {
            this.setButtonLoading(submitBtn, originalText, false);
        }
    }

    async handlePlanAssignment(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Add CSRF token
        formData.append('csrf_token', this.getCSRFToken());
        
        // Validate form
        if (!this.validateForm(form)) {
            return;
        }

        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        try {
            this.setButtonLoading(submitBtn, 'Assigning...');
            
            const response = await fetch('/admin/users/api/assign-plan.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', result.message);
                this.planModal.hide();
                form.reset();
                this.clearFormValidation(form);
                // Reload page to show updated plan
                setTimeout(() => window.location.reload(), 1000);
            } else {
                this.showAlert('danger', result.message);
            }
        } catch (error) {
            console.error('Error assigning plan:', error);
            this.showAlert('danger', 'An error occurred while assigning the plan');
        } finally {
            this.setButtonLoading(submitBtn, originalText, false);
        }
    }

    validateForm(form) {
        const requiredFields = form.querySelectorAll('input[required], select[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    }

    showFormErrors(form, errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.showFieldValidation(field, false, errors[fieldName]);
            }
        });
    }

    clearFormValidation(form) {
        const fields = form.querySelectorAll('.is-valid, .is-invalid');
        fields.forEach(field => {
            field.classList.remove('is-valid', 'is-invalid');
        });
        
        const feedbacks = form.querySelectorAll('.invalid-feedback');
        feedbacks.forEach(feedback => feedback.remove());
    }

    updateBalanceActionUI(action) {
        const amountField = document.getElementById('amount');
        const reasonField = document.getElementById('reason');
        
        if (action === 'credit') {
            amountField.placeholder = 'Amount to credit';
            reasonField.placeholder = 'Reason for crediting balance...';
        } else {
            amountField.placeholder = 'Amount to debit';
            reasonField.placeholder = 'Reason for debiting balance...';
        }
    }

    setButtonLoading(button, text, loading = true) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
        } else {
            button.disabled = false;
            button.innerHTML = text;
        }
    }

    showAlert(type, message) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert-dismissible');
        existingAlerts.forEach(alert => alert.remove());
        
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // Auto-dismiss success alerts
        if (type === 'success') {
            setTimeout(() => {
                const alert = document.querySelector('.alert-success');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    }

    getCSRFToken() {
        const tokenMeta = document.querySelector('meta[name="csrf-token"]');
        return tokenMeta ? tokenMeta.getAttribute('content') : '';
    }
}

// Global functions called from the view
window.viewUser = async function(userId) {
    try {
        const response = await fetch(`/admin/users/api/details.php?id=${userId}`);
        const result = await response.json();
        
        if (result.success) {
            const user = result.user;
            const content = `
                <div class="user-details-header">
                    <div class="user-details-avatar">
                        ${user.profile_picture ? 
                            `<img src="${user.profile_picture}" class="avatar-img rounded-circle" alt="Profile">` :
                            `<div class="avatar-title bg-primary text-white rounded-circle">${user.first_name.charAt(0).toUpperCase()}</div>`
                        }
                    </div>
                    <div class="user-details-info">
                        <h5>${user.first_name} ${user.last_name}</h5>
                        <p>@${user.username} • ${user.email}</p>
                    </div>
                </div>
                
                <div class="financial-summary">
                    <h6><i class="fas fa-wallet me-2"></i>Financial Summary</h6>
                    <div class="financial-grid">
                        <div class="financial-item">
                            <div class="financial-amount">$${parseFloat(user.balance).toFixed(2)}</div>
                            <div class="financial-label">Current Balance</div>
                        </div>
                        <div class="financial-item">
                            <div class="financial-amount">$${parseFloat(user.bonus || 0).toFixed(2)}</div>
                            <div class="financial-label">Bonus</div>
                        </div>
                        <div class="financial-item">
                            <div class="financial-amount">$${parseFloat(user.total_deposits || 0).toFixed(2)}</div>
                            <div class="financial-label">Total Deposits</div>
                        </div>
                        <div class="financial-item">
                            <div class="financial-amount">$${parseFloat(user.total_withdrawals || 0).toFixed(2)}</div>
                            <div class="financial-label">Total Withdrawals</div>
                        </div>
                    </div>
                </div>
                
                <div class="user-details-section">
                    <h6><i class="fas fa-user me-2"></i>Account Information</h6>
                    <div class="detail-row">
                        <span class="detail-label">Status</span>
                        <span class="detail-value">
                            <span class="badge bg-${getStatusColor(user.status)}">${user.status.charAt(0).toUpperCase() + user.status.slice(1)}</span>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Email Verified</span>
                        <span class="detail-value">
                            ${user.email_verified ? 
                                '<i class="fas fa-check-circle text-success"></i> Verified' : 
                                '<i class="fas fa-times-circle text-danger"></i> Not Verified'
                            }
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Registration Date</span>
                        <span class="detail-value">${new Date(user.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Last Login</span>
                        <span class="detail-value">${user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}</span>
                    </div>
                </div>
                
                <div class="user-details-section">
                    <h6><i class="fas fa-clock me-2"></i>Pending Transactions</h6>
                    <div class="detail-row">
                        <span class="detail-label">Pending Deposits</span>
                        <span class="detail-value">${user.pending_deposits || 0}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Pending Withdrawals</span>
                        <span class="detail-value">${user.pending_withdrawals || 0}</span>
                    </div>
                </div>
                
                ${user.recent_transactions && user.recent_transactions.length > 0 ? `
                    <div class="user-details-section">
                        <h6><i class="fas fa-history me-2"></i>Recent Transactions</h6>
                        <div class="transaction-list">
                            ${user.recent_transactions.map(transaction => `
                                <div class="transaction-item">
                                    <div class="transaction-info">
                                        <div class="transaction-type">${transaction.type.replace('_', ' ').toUpperCase()}</div>
                                        <div class="transaction-date">${new Date(transaction.created_at).toLocaleDateString()}</div>
                                    </div>
                                    <div class="transaction-amount ${parseFloat(transaction.amount) >= 0 ? 'positive' : 'negative'}">
                                        ${parseFloat(transaction.amount) >= 0 ? '+' : ''}$${Math.abs(parseFloat(transaction.amount)).toFixed(2)}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;
            
            document.getElementById('userDetailsContent').innerHTML = content;
            userManagement.userDetailsModal.show();
        } else {
            userManagement.showAlert('danger', result.message);
        }
    } catch (error) {
        console.error('Error loading user details:', error);
        userManagement.showAlert('danger', 'Failed to load user details');
    }
};

window.editUser = async function(userId) {
    try {
        const response = await fetch(`/admin/users/api/details.php?id=${userId}`);
        const result = await response.json();
        
        if (result.success) {
            const user = result.user;
            const content = `
                <input type="hidden" name="user_id" value="${user.id}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="editFirstName" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="editFirstName" name="first_name" value="${user.first_name}" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="editLastName" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="editLastName" name="last_name" value="${user.last_name}" required>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="editUsername" class="form-label">Username</label>
                    <input type="text" class="form-control" id="editUsername" name="username" value="${user.username}" required>
                </div>
                <div class="mb-3">
                    <label for="editEmail" class="form-label">Email</label>
                    <input type="email" class="form-control" id="editEmail" name="email" value="${user.email}" required>
                </div>
                <div class="mb-3">
                    <label for="editPassword" class="form-label">New Password</label>
                    <input type="password" class="form-control" id="editPassword" name="password" placeholder="Leave blank to keep current password">
                    <div class="form-text">Leave blank to keep the current password</div>
                </div>
            `;
            
            document.getElementById('editUserContent').innerHTML = content;
            userManagement.editUserModal.show();
        } else {
            userManagement.showAlert('danger', result.message);
        }
    } catch (error) {
        console.error('Error loading user for edit:', error);
        userManagement.showAlert('danger', 'Failed to load user information');
    }
};

window.suspendUser = async function(userId) {
    if (!confirm('Are you sure you want to suspend this user?')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('user_id', userId);
        formData.append('csrf_token', userManagement.getCSRFToken());
        
        const response = await fetch('/admin/users/api/suspend.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            userManagement.showAlert('success', result.message);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            userManagement.showAlert('danger', result.message);
        }
    } catch (error) {
        console.error('Error suspending user:', error);
        userManagement.showAlert('danger', 'Failed to suspend user');
    }
};

window.activateUser = async function(userId) {
    if (!confirm('Are you sure you want to activate this user?')) {
        return;
    }
    
    try {
        const formData = new FormData();
        formData.append('user_id', userId);
        formData.append('csrf_token', userManagement.getCSRFToken());
        
        const response = await fetch('/admin/users/api/activate.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            userManagement.showAlert('success', result.message);
            setTimeout(() => window.location.reload(), 1000);
        } else {
            userManagement.showAlert('danger', result.message);
        }
    } catch (error) {
        console.error('Error activating user:', error);
        userManagement.showAlert('danger', 'Failed to activate user');
    }
};

window.manageBalance = function(userId) {
    document.getElementById('balanceUserId').value = userId;
    userManagement.balanceModal.show();
};

window.assignPlan = function(userId) {
    document.getElementById('planUserId').value = userId;
    userManagement.planModal.show();
};

window.exportUsers = function() {
    // Get current filters
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    
    // Create download link
    const link = document.createElement('a');
    link.href = `/admin/users/export.php?${params.toString()}`;
    link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// Helper function for status colors
function getStatusColor(status) {
    switch (status) {
        case 'active': return 'success';
        case 'suspended': return 'danger';
        case 'pending': return 'warning';
        default: return 'secondary';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.userManagement = new AdminUserManagement();
});