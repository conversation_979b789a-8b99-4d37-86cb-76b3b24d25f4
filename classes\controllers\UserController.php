<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../services/UserService.php';
require_once __DIR__ . '/../services/SessionManager.php';
require_once __DIR__ . '/../services/CSRFProtection.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * UserController - Handles user dashboard and profile operations
 */
class UserController {
    
    /**
     * Display user dashboard
     */
    public static function dashboard() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            header('Location: /login.php');
            exit();
        }
        
        $userId = SessionManager::getUserId();
        $user = User::find($userId);
        
        if (!$user || !$user->isActive()) {
            SessionManager::logout();
            header('Location: /login.php?error=account_inactive');
            exit();
        }
        
        // Get user statistics
        $stats = UserService::getUserStats($userId);
        
        // Get recent activity
        $activity = UserService::getUserActivity($userId, 7); // Last 7 days
        
        // Get recent transactions (last 5)
        $recentTransactions = $user->getTransactions(5);
        
        return [
            'user' => $user,
            'stats' => $stats,
            'activity' => $activity,
            'recent_transactions' => $recentTransactions
        ];
    }
    
    /**
     * Display user profile
     */
    public static function profile() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            header('Location: /login.php');
            exit();
        }
        
        $userId = SessionManager::getUserId();
        $user = User::find($userId);
        
        if (!$user) {
            SessionManager::logout();
            header('Location: /login.php');
            exit();
        }
        
        return ['user' => $user];
    }
    
    /**
     * Update user profile
     */
    public static function updateProfile() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            return ['success' => false, 'error' => 'Not authenticated'];
        }
        
        // Verify CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid security token'];
        }
        
        $userId = SessionManager::getUserId();
        $profileData = [
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'phone' => trim($_POST['phone'] ?? '')
        ];
        
        $result = UserService::updateProfile($userId, $profileData);
        
        if ($result['success']) {
            $_SESSION['success_message'] = 'Profile updated successfully';
        }
        
        return $result;
    }
    
    /**
     * Change user password
     */
    public static function changePassword() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            return ['success' => false, 'error' => 'Not authenticated'];
        }
        
        // Verify CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid security token'];
        }
        
        $userId = SessionManager::getUserId();
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validate passwords match
        if ($newPassword !== $confirmPassword) {
            return ['success' => false, 'errors' => ['confirm_password' => 'Passwords do not match']];
        }
        
        $result = UserService::changePassword($userId, $currentPassword, $newPassword);
        
        if ($result['success']) {
            $_SESSION['success_message'] = 'Password changed successfully';
        }
        
        return $result;
    }
    
    /**
     * Upload profile picture
     */
    public static function uploadProfilePicture() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            return ['success' => false, 'error' => 'Not authenticated'];
        }
        
        // Verify CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid security token'];
        }
        
        $userId = SessionManager::getUserId();
        $user = User::find($userId);
        
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        // Check if file was uploaded
        if (!isset($_FILES['profile_picture']) || $_FILES['profile_picture']['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'error' => 'No file uploaded or upload error'];
        }
        
        $file = $_FILES['profile_picture'];
        
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($file['type'], $allowedTypes)) {
            return ['success' => false, 'error' => 'Invalid file type. Only JPEG, PNG, and GIF are allowed'];
        }
        
        // Validate file size (max 2MB)
        $maxSize = 2 * 1024 * 1024; // 2MB
        if ($file['size'] > $maxSize) {
            return ['success' => false, 'error' => 'File too large. Maximum size is 2MB'];
        }
        
        // Create uploads directory if it doesn't exist
        $uploadDir = 'assets/uploads/profiles/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'profile_' . $userId . '_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            // Delete old profile picture if exists
            if ($user->profile_picture && file_exists($user->profile_picture)) {
                unlink($user->profile_picture);
            }
            
            // Update user profile picture
            $user->profile_picture = $filepath;
            
            if ($user->save()) {
                return ['success' => true, 'profile_picture' => $filepath];
            } else {
                // Clean up uploaded file if database update fails
                unlink($filepath);
                return ['success' => false, 'error' => 'Failed to update profile picture'];
            }
        }
        
        return ['success' => false, 'error' => 'Failed to upload file'];
    }
    
    /**
     * Get user settings
     */
    public static function settings() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            header('Location: /login.php');
            exit();
        }
        
        $userId = SessionManager::getUserId();
        $user = User::find($userId);
        
        if (!$user) {
            SessionManager::logout();
            header('Location: /login.php');
            exit();
        }
        
        return ['user' => $user];
    }
    
    /**
     * Update user settings
     */
    public static function updateSettings() {
        // Check authentication
        if (!SessionManager::isLoggedIn()) {
            return ['success' => false, 'error' => 'Not authenticated'];
        }
        
        // Verify CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid security token'];
        }
        
        $userId = SessionManager::getUserId();
        $user = User::find($userId);
        
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        // Handle 2FA toggle (if implemented)
        if (isset($_POST['two_fa_enabled'])) {
            $user->two_fa_enabled = $_POST['two_fa_enabled'] === '1';
        }
        
        if ($user->save()) {
            $_SESSION['success_message'] = 'Settings updated successfully';
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Failed to update settings'];
    }
}
?>