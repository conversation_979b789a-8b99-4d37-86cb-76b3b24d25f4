<?php
require_once __DIR__ . '/../includes/db_connect.php';

/**
 * Insert Email Templates Directly
 * Creates email templates using PHP to avoid SQL parsing issues
 */

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h1>Inserting Email Templates</h1>\n";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
    
    // Check if email_templates table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if (!$stmt->fetch()) {
        echo "❌ email_templates table does not exist. Please run the email templates migration first.\n";
        exit();
    }
    
    // Define email templates
    $templates = [
        [
            'template_type' => 'email_verification',
            'template_name' => 'Email Verification',
            'subject' => 'Verify Your Email Address - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #007bff;">Email Verification Required</h2>
                <p>Dear {{first_name}},</p>
                <p>Thank you for registering with {{site_name}}. Please verify your email address:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{verification_link}}" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Email</a>
                </div>
                <p>This link expires in {{expiry_hours}} hours.</p>
                <p>Best regards,<br>The {{site_name}} Team</p>
            </div>',
            'body_text' => 'Dear {{first_name}}, Please verify your email: {{verification_link}}. Best regards, {{site_name}} Team',
            'is_active' => 1
        ],
        [
            'template_type' => '2fa_enabled',
            'template_name' => '2FA Enabled Notification',
            'subject' => 'Two-Factor Authentication Enabled - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #28a745;">Two-Factor Authentication Enabled</h2>
                <p>Dear {{first_name}},</p>
                <p>2FA has been enabled for your account on {{date}} from IP {{ip_address}}.</p>
                <p>Your account is now more secure!</p>
                <p>Best regards,<br>The {{site_name}} Team</p>
            </div>',
            'body_text' => 'Dear {{first_name}}, 2FA enabled on {{date}} from {{ip_address}}. Best regards, {{site_name}} Team',
            'is_active' => 1
        ],
        [
            'template_type' => '2fa_disabled',
            'template_name' => '2FA Disabled Notification',
            'subject' => 'Two-Factor Authentication Disabled - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ffc107;">Two-Factor Authentication Disabled</h2>
                <p>Dear {{first_name}},</p>
                <p>2FA has been disabled for your account on {{date}} from IP {{ip_address}}.</p>
                <p>If this was not you, please contact support immediately.</p>
                <p>Best regards,<br>The {{site_name}} Team</p>
            </div>',
            'body_text' => 'Dear {{first_name}}, 2FA disabled on {{date}} from {{ip_address}}. Contact support if not you. Best regards, {{site_name}} Team',
            'is_active' => 1
        ],
        [
            'template_type' => 'suspicious_login',
            'template_name' => 'Suspicious Login Alert',
            'subject' => 'Security Alert: Unusual Login Activity - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #dc3545;">Security Alert</h2>
                <p>Dear {{first_name}},</p>
                <p>Unusual login detected:</p>
                <ul>
                    <li>Time: {{login_time}}</li>
                    <li>IP: {{ip_address}}</li>
                    <li>Location: {{location}}</li>
                </ul>
                <p>If this was not you, secure your account immediately.</p>
                <p>Best regards,<br>The {{site_name}} Security Team</p>
            </div>',
            'body_text' => 'Security Alert: Unusual login at {{login_time}} from {{ip_address}}. Secure your account if not you. {{site_name}} Security Team',
            'is_active' => 1
        ],
        [
            'template_type' => 'support_ticket_created',
            'template_name' => 'Support Ticket Created',
            'subject' => 'Support Ticket Created - #{{ticket_id}} - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #007bff;">Support Ticket Created</h2>
                <p>Dear {{first_name}},</p>
                <p>Your support ticket #{{ticket_id}} has been created.</p>
                <p><strong>Subject:</strong> {{subject}}</p>
                <p><strong>Priority:</strong> {{priority}}</p>
                <p><strong>Status:</strong> {{status}}</p>
                <p>We will respond as soon as possible.</p>
                <p>Best regards,<br>The {{site_name}} Support Team</p>
            </div>',
            'body_text' => 'Support ticket #{{ticket_id}} created. Subject: {{subject}}. We will respond soon. {{site_name}} Support Team',
            'is_active' => 1
        ],
        [
            'template_type' => 'support_ticket_reply',
            'template_name' => 'Support Ticket Reply',
            'subject' => 'Support Ticket Reply - #{{ticket_id}} - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #28a745;">Support Ticket Reply</h2>
                <p>Dear {{first_name}},</p>
                <p>We have replied to your ticket #{{ticket_id}}.</p>
                <p><strong>Reply:</strong></p>
                <div style="background: #f8f9fa; padding: 15px; border-left: 4px solid #28a745;">
                    {{admin_reply}}
                </div>
                <p>Best regards,<br>The {{site_name}} Support Team</p>
            </div>',
            'body_text' => 'Reply to ticket #{{ticket_id}}: {{admin_reply}}. {{site_name}} Support Team',
            'is_active' => 1
        ],
        [
            'template_type' => 'admin_support_notification',
            'template_name' => 'New Support Ticket (Admin)',
            'subject' => 'New Support Ticket - #{{ticket_id}} - {{site_name}}',
            'body_html' => '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #ffc107;">New Support Ticket</h2>
                <p>A new support ticket requires attention:</p>
                <p><strong>Ticket ID:</strong> #{{ticket_id}}</p>
                <p><strong>User:</strong> {{user_name}} ({{user_email}})</p>
                <p><strong>Subject:</strong> {{subject}}</p>
                <p><strong>Priority:</strong> {{priority}}</p>
                <p><strong>Message:</strong></p>
                <div style="background: #f8f9fa; padding: 15px;">
                    {{message}}
                </div>
                <p>{{site_name}} Admin System</p>
            </div>',
            'body_text' => 'New ticket #{{ticket_id}} from {{user_name}}: {{subject}}. Priority: {{priority}}. {{site_name}} Admin',
            'is_active' => 1
        ]
    ];
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($templates as $template) {
        try {
            // Check if template already exists
            $stmt = $db->prepare("SELECT id FROM email_templates WHERE template_type = ?");
            $stmt->execute([$template['template_type']]);
            
            if ($stmt->fetch()) {
                echo "⚠️ Template '{$template['template_type']}' already exists, skipping\n";
                continue;
            }
            
            // Insert template
            $stmt = $db->prepare("
                INSERT INTO email_templates (template_type, template_name, subject, body_html, body_text, is_active, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $template['template_type'],
                $template['template_name'],
                $template['subject'],
                $template['body_html'],
                $template['body_text'],
                $template['is_active']
            ]);
            
            $successCount++;
            echo "✅ Created template: {$template['template_type']}\n";
            
        } catch (Exception $e) {
            $errorCount++;
            echo "❌ Error creating template '{$template['template_type']}': " . $e->getMessage() . "\n";
        }
    }
    
    echo "<h2>Verifying email templates...</h2>\n";
    
    // Verify templates
    $stmt = $db->query("SELECT template_type, template_name, is_active FROM email_templates ORDER BY template_type");
    $allTemplates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Available Email Templates:</h3>\n";
    foreach ($allTemplates as $template) {
        $status = $template['is_active'] ? '✅ Active' : '❌ Inactive';
        echo "- {$template['template_type']}: {$template['template_name']} ($status)\n";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>✅ Email Templates Setup Complete!</h3>\n";
    echo "<p style='color: #155724;'>Successfully created $successCount templates with $errorCount errors.</p>\n";
    echo "<p style='color: #155724;'>Total templates available: " . count($allTemplates) . "</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Creating Email Templates</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>