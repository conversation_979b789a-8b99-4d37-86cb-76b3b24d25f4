<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';

// Check if user is logged in and is a regular user
if (!isLoggedIn() || !hasRole('user')) {
    header('Location: /user/login.php');
    exit();
}

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .dashboard-header {
            background: linear-gradient(135deg, #007bff, #28a745);
            color: white;
            padding: 2rem 0;
        }
        .card { border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card { transition: transform 0.3s; }
        .stat-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-tachometer-alt me-2"></i>Welcome, <?php echo htmlspecialchars($user['first_name']); ?>!</h1>
                    <p class="mb-0">Manage your trading account and investments</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="/logout.php" class="btn btn-light">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container mt-4">
        <!-- Account Overview -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-wallet fa-2x text-primary mb-2"></i>
                        <h5>Account Balance</h5>
                        <h3 class="text-primary">$<?php echo number_format($user['balance'] ?? 0, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-gift fa-2x text-success mb-2"></i>
                        <h5>Bonus Balance</h5>
                        <h3 class="text-success">$<?php echo number_format($user['bonus'] ?? 0, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-arrow-up fa-2x text-info mb-2"></i>
                        <h5>Total Deposits</h5>
                        <h3 class="text-info">$<?php echo number_format($user['total_deposit'] ?? 0, 2); ?></h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="fas fa-arrow-down fa-2x text-warning mb-2"></i>
                        <h5>Total Withdrawals</h5>
                        <h3 class="text-warning">$<?php echo number_format($user['total_withdrawal'] ?? 0, 2); ?></h3>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 mb-2">
                                <a href="/user/deposit/" class="btn btn-success w-100">
                                    <i class="fas fa-plus-circle me-2"></i>Deposit
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/user/withdraw/" class="btn btn-warning w-100">
                                    <i class="fas fa-minus-circle me-2"></i>Withdraw
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/user/trading-plans/" class="btn btn-primary w-100">
                                    <i class="fas fa-chart-line me-2"></i>Trading Plans
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/user/transactions/" class="btn btn-info w-100">
                                    <i class="fas fa-history me-2"></i>Transactions
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/user/profile/" class="btn btn-secondary w-100">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a>
                            </div>
                            <div class="col-md-2 mb-2">
                                <a href="/user/support/" class="btn btn-dark w-100">
                                    <i class="fas fa-headset me-2"></i>Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Account Status -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-check me-2"></i>Account Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Account Status:</strong>
                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'success' : 'warning'; ?>">
                                <?php echo ucfirst($user['status']); ?>
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong>Email Verified:</strong>
                            <span class="badge bg-<?php echo $user['email_verified'] ? 'success' : 'danger'; ?>">
                                <?php echo $user['email_verified'] ? 'Verified' : 'Not Verified'; ?>
                            </span>
                        </div>
                        <div class="mb-3">
                            <strong>KYC Status:</strong>
                            <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'success' : 'warning'; ?>">
                                <?php echo ucfirst($user['kyc_status']); ?>
                            </span>
                        </div>
                        <div class="mb-0">
                            <strong>Member Since:</strong>
                            <?php echo date('F j, Y', strtotime($user['created_at'])); ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bell me-2"></i>Recent Activity</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>No recent activity</p>
                            <small>Your recent transactions and activities will appear here</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>