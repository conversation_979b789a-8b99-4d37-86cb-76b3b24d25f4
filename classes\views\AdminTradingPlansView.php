<?php
require_once __DIR__ . '/BaseView.php';

/**
 * Admin Trading Plans View - Handles trading plan management interface
 */
class AdminTradingPlansView extends BaseView {
    
    protected function getTitle() {
        return 'Trading Plans Management - Admin Panel';
    }
    
    protected function getAdditionalHead() {
        return '
        <link href="../../assets/css/admin-trading-plans.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css" rel="stylesheet">
        ';
    }
    
    protected function getBodyContent($data) {
        $plans = $data['plans'] ?? [];
        $statistics = $data['statistics'] ?? [];
        $error = $data['error'] ?? null;
        
        ob_start();
        ?>
        
        <div class="admin-content">
            <!-- Header -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Trading Plans Management</h1>
                        <p class="text-muted">Manage investment plans and trading options</p>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlanModal">
                        <i class="fas fa-plus"></i> Create New Plan
                    </button>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <?php if (!empty($statistics)): ?>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['total_plans'] ?? 0) ?></h3>
                            <p>Total Plans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['active_plans'] ?? 0) ?></h3>
                            <p>Active Plans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['inactive_plans'] ?? 0) ?></h3>
                            <p>Inactive Plans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['avg_daily_return'] ?? 0, 2) ?>%</h3>
                            <p>Avg Daily Return</p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Plans Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Trading Plans</h5>
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-secondary btn-sm" id="sortPlansBtn">
                                <i class="fas fa-sort"></i> Reorder Plans
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="all">All Plans</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="active">Active Only</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="inactive">Inactive Only</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($plans)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>No Trading Plans Found</h5>
                        <p class="text-muted">Create your first trading plan to get started.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createPlanModal">
                            <i class="fas fa-plus"></i> Create Plan
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="plansTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="30"><i class="fas fa-grip-vertical text-muted"></i></th>
                                    <th>Plan Name</th>
                                    <th>Deposit Range</th>
                                    <th>Daily Return</th>
                                    <th>Duration</th>
                                    <th>Total Return</th>
                                    <th>Deposits</th>
                                    <th>Status</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="sortablePlans">
                                <?php foreach ($plans as $plan): ?>
                                <tr data-plan-id="<?= $plan['id'] ?>" data-status="<?= $plan['status'] ?>">
                                    <td class="sort-handle">
                                        <i class="fas fa-grip-vertical text-muted"></i>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($plan['name']) ?></h6>
                                                <?php if (!empty($plan['description'])): ?>
                                                <small class="text-muted"><?= htmlspecialchars(substr($plan['description'], 0, 50)) ?>...</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="fw-medium">$<?= number_format($plan['min_deposit'], 2) ?></span>
                                        <?php if ($plan['max_deposit']): ?>
                                        - $<?= number_format($plan['max_deposit'], 2) ?>
                                        <?php else: ?>
                                        +
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= number_format($plan['daily_return'], 2) ?>%</span>
                                    </td>
                                    <td>
                                        <?= $plan['duration_days'] ?> days
                                    </td>
                                    <td>
                                        <span class="fw-medium text-success">
                                            <?= number_format($plan['daily_return'] * $plan['duration_days'], 2) ?>%
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium"><?= number_format($plan['deposits_count']) ?> deposits</span>
                                            <small class="text-muted">$<?= number_format($plan['total_deposits'], 2) ?></small>
                                            <?php if ($plan['pending_deposits'] > 0): ?>
                                            <small class="text-warning"><?= $plan['pending_deposits'] ?> pending</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $plan['status'] === 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($plan['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary edit-plan-btn" 
                                                    data-plan-id="<?= $plan['id'] ?>" title="Edit Plan">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-<?= $plan['status'] === 'active' ? 'warning' : 'success' ?> toggle-status-btn"
                                                    data-plan-id="<?= $plan['id'] ?>" 
                                                    data-current-status="<?= $plan['status'] ?>"
                                                    title="<?= $plan['status'] === 'active' ? 'Deactivate' : 'Activate' ?> Plan">
                                                <i class="fas fa-<?= $plan['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger delete-plan-btn" 
                                                    data-plan-id="<?= $plan['id'] ?>" 
                                                    data-plan-name="<?= htmlspecialchars($plan['name']) ?>"
                                                    title="Delete Plan">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Create/Edit Plan Modal -->
        <div class="modal fade" id="createPlanModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New Trading Plan</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="planForm">
                        <div class="modal-body">
                            <input type="hidden" id="planId" name="plan_id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="planName" class="form-label">Plan Name *</label>
                                        <input type="text" class="form-control" id="planName" name="name" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="planStatus" class="form-label">Status</label>
                                        <select class="form-select" id="planStatus" name="status">
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="minDeposit" class="form-label">Minimum Deposit ($) *</label>
                                        <input type="number" class="form-control" id="minDeposit" name="min_deposit" 
                                               step="0.01" min="0.01" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="maxDeposit" class="form-label">Maximum Deposit ($)</label>
                                        <input type="number" class="form-control" id="maxDeposit" name="max_deposit" 
                                               step="0.01" min="0.01">
                                        <small class="form-text text-muted">Leave empty for unlimited</small>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="dailyReturn" class="form-label">Daily Return (%) *</label>
                                        <input type="number" class="form-control" id="dailyReturn" name="daily_return" 
                                               step="0.01" min="0.01" max="100" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="duration" class="form-label">Duration (Days) *</label>
                                        <input type="number" class="form-control" id="duration" name="duration_days" 
                                               min="1" max="3650" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Plan Features</label>
                                <div id="featuresContainer">
                                    <div class="feature-item mb-2">
                                        <div class="input-group">
                                            <input type="text" class="form-control feature-input" placeholder="Enter feature">
                                            <button type="button" class="btn btn-outline-danger remove-feature-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="addFeatureBtn">
                                    <i class="fas fa-plus"></i> Add Feature
                                </button>
                            </div>
                            
                            <!-- Calculated Values Display -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <strong>Total Return:</strong> <span id="totalReturnDisplay">0.00%</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-success">
                                        <strong>Example Profit (on $1000):</strong> $<span id="exampleProfitDisplay">0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                <span class="btn-text">Create Plan</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deletePlanModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete the trading plan "<strong id="deletePlanName"></strong>"?</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            This action cannot be undone. All associated data will be preserved but the plan will no longer be available.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            Delete Plan
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <?php
        return ob_get_clean();
    }
    
    protected function getAdditionalScripts() {
        return '
        <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
        <script src="../../assets/js/admin-trading-plans.js"></script>
        ';
    }
    
    protected function getLayout() {
        return 'admin';
    }
}
?>