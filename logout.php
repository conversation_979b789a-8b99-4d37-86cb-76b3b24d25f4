<?php
require_once 'includes/functions.php';
session_start();

// Get user role before destroying the session
$role = getUserRole();

session_destroy();

// Redirect based on the role
switch ($role) {
    case 'admin':
        redirectTo('admin/login.php');
        break;
    case 'superadmin':
        redirectTo('superadmin/login.php');
        break;
    default:
        redirectTo('login.php');
        break;
}
?>