/* Profile Styles */
.profile-picture-container {
    position: relative;
    display: inline-block;
}

.profile-picture {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #e9ecef;
}

.profile-picture-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 4px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.security-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.security-item:last-child {
    margin-bottom: 0;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.form-check-input:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* File upload styling */
.form-control[type="file"] {
    padding: 0.375rem 0.75rem;
}

.form-control[type="file"]:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Password strength indicator */
.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.password-strength.weak {
    background-color: #dc3545;
    width: 33%;
}

.password-strength.medium {
    background-color: #ffc107;
    width: 66%;
}

.password-strength.strong {
    background-color: #28a745;
    width: 100%;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Toast notifications */
.toast {
    min-width: 300px;
}

.toast-header {
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-picture,
    .profile-picture-placeholder {
        width: 120px;
        height: 120px;
    }
    
    .security-item {
        padding: 0.75rem;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Card hover effects */
.card {
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}