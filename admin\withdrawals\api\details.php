<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/models/Withdrawal.php';
require_once '../../../classes/models/User.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get withdrawal ID
$withdrawalId = (int)($_GET['id'] ?? 0);

if (!$withdrawalId) {
    echo json_encode(['success' => false, 'message' => 'Invalid withdrawal ID']);
    exit;
}

try {
    // Get withdrawal details
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal not found']);
        exit;
    }
    
    // Get user details
    $user = $withdrawal->getUser();
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Get processed by admin if available
    $processedBy = $withdrawal->getProcessedBy();
    
    // Format account details
    $accountDetails = $withdrawal->getAccountDetailsArray();
    
    // Prepare withdrawal data
    $withdrawalData = [
        'id' => $withdrawal->getId(),
        'amount' => $withdrawal->amount,
        'withdrawal_method' => $withdrawal->withdrawal_method,
        'account_details' => $accountDetails,
        'status' => $withdrawal->status,
        'admin_note' => $withdrawal->admin_note,
        'created_at' => $withdrawal->created_at,
        'processed_at' => $withdrawal->processed_at,
        'user_balance' => $user->balance,
        'user' => [
            'id' => $user->getId(),
            'username' => $user->username,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'balance' => $user->balance
        ],
        'processed_by' => $processedBy ? [
            'id' => $processedBy->getId(),
            'username' => $processedBy->username,
            'first_name' => $processedBy->first_name,
            'last_name' => $processedBy->last_name
        ] : null
    ];
    
    // Generate HTML for details modal
    $html = generateWithdrawalDetailsHTML($withdrawalData);
    
    echo json_encode([
        'success' => true,
        'withdrawal' => $withdrawalData,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    error_log("Error fetching withdrawal details: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Failed to load withdrawal details']);
}

function generateWithdrawalDetailsHTML($data) {
    $statusClass = 'badge bg-secondary';
    switch ($data['status']) {
        case 'pending':
            $statusClass = 'badge bg-warning';
            break;
        case 'approved':
            $statusClass = 'badge bg-info';
            break;
        case 'processing':
            $statusClass = 'badge bg-primary';
            break;
        case 'completed':
            $statusClass = 'badge bg-success';
            break;
        case 'rejected':
            $statusClass = 'badge bg-danger';
            break;
    }
    
    $accountDetailsHtml = '';
    if (!empty($data['account_details'])) {
        foreach ($data['account_details'] as $key => $value) {
            if (!empty($value)) {
                $accountDetailsHtml .= '<tr><td><strong>' . ucfirst(htmlspecialchars($key)) . ':</strong></td><td>' . htmlspecialchars($value) . '</td></tr>';
            }
        }
    }
    
    $processedByHtml = '';
    if ($data['processed_by']) {
        $processedByHtml = '
        <tr>
            <td><strong>Processed By:</strong></td>
            <td>' . htmlspecialchars($data['processed_by']['first_name'] . ' ' . $data['processed_by']['last_name']) . ' (@' . htmlspecialchars($data['processed_by']['username']) . ')</td>
        </tr>';
    }
    
    $processedAtHtml = '';
    if ($data['processed_at']) {
        $processedAtHtml = '
        <tr>
            <td><strong>Processed At:</strong></td>
            <td>' . date('M j, Y g:i A', strtotime($data['processed_at'])) . '</td>
        </tr>';
    }
    
    $adminNoteHtml = '';
    if ($data['admin_note']) {
        $adminNoteHtml = '
        <tr>
            <td><strong>Admin Note:</strong></td>
            <td>' . nl2br(htmlspecialchars($data['admin_note'])) . '</td>
        </tr>';
    }
    
    return '
    <div class="row">
        <div class="col-md-6">
            <h6 class="mb-3">Withdrawal Information</h6>
            <table class="table table-borderless">
                <tr>
                    <td><strong>Amount:</strong></td>
                    <td class="text-success fw-bold">$' . number_format($data['amount'], 2) . '</td>
                </tr>
                <tr>
                    <td><strong>Method:</strong></td>
                    <td>' . htmlspecialchars($data['withdrawal_method']) . '</td>
                </tr>
                <tr>
                    <td><strong>Status:</strong></td>
                    <td><span class="' . $statusClass . '">' . ucfirst($data['status']) . '</span></td>
                </tr>
                <tr>
                    <td><strong>Requested:</strong></td>
                    <td>' . date('M j, Y g:i A', strtotime($data['created_at'])) . '</td>
                </tr>
                ' . $processedAtHtml . '
                ' . $processedByHtml . '
                ' . $adminNoteHtml . '
            </table>
        </div>
        <div class="col-md-6">
            <h6 class="mb-3">User Information</h6>
            <table class="table table-borderless">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>' . htmlspecialchars($data['user']['first_name'] . ' ' . $data['user']['last_name']) . '</td>
                </tr>
                <tr>
                    <td><strong>Username:</strong></td>
                    <td>@' . htmlspecialchars($data['user']['username']) . '</td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td>' . htmlspecialchars($data['user']['email']) . '</td>
                </tr>
                <tr>
                    <td><strong>Current Balance:</strong></td>
                    <td class="fw-bold">$' . number_format($data['user']['balance'], 2) . '</td>
                </tr>
            </table>
            
            ' . (!empty($accountDetailsHtml) ? '
            <h6 class="mb-3 mt-4">Account Details</h6>
            <table class="table table-borderless">
                ' . $accountDetailsHtml . '
            </table>' : '') . '
        </div>
    </div>';
}
?>