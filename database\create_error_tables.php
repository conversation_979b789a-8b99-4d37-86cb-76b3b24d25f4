<?php
require_once __DIR__ . '/../includes/db_connect.php';

/**
 * Create Error Logging Tables
 * Creates all necessary tables for comprehensive error handling and system monitoring
 */

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h1>Creating Error Logging Tables</h1>\n";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
    
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/migrations/create_error_logging_tables.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Execute the SQL
    $db->exec($sql);
    echo "✅ Error logging tables created successfully\n";
    
    // Verify tables were created
    $tables = [
        'error_logs',
        'failed_login_attempts', 
        'system_health_checks',
        'error_resolutions',
        'performance_metrics'
    ];
    
    echo "<h2>Verifying tables...</h2>\n";
    
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->fetch()) {
            echo "✅ Table '$table' created successfully\n";
            
            // Show table structure
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "  Columns: " . count($columns) . "\n";
        } else {
            echo "❌ Table '$table' not found\n";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>✅ Error Logging System Setup Complete!</h3>\n";
    echo "<p style='color: #155724;'>All error logging and system monitoring tables have been created successfully.</p>\n";
    echo "<p style='color: #155724;'>The system is now ready for comprehensive error handling and health monitoring.</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Creating Tables</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>