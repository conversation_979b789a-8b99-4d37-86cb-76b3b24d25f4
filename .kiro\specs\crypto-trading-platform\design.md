# Design Document

## Overview

The Crypto Trading Platform is a monolithic PHP application designed with a three-tier architecture: presentation layer (Bootstrap-based UI), business logic layer (PHP classes and functions), and data access layer (MySQL with PDO). The system implements role-based access control (RBAC) with three distinct user types: Users, Admins, and Super Admins, each with specific interfaces and capabilities.

The platform follows a modular directory structure where each functional area is encapsulated in its own folder, promoting maintainability and clean separation of concerns. The design emphasizes security through prepared statements, CSRF protection, password hashing, and optional 2FA implementation.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Presentation Layer"
        UL[User Layout]
        AL[Admin Layout]
        SAL[Super Admin Layout]
        LF[Login Forms]
    end
    
    subgraph "Business Logic Layer"
        AC[Authentication Controller]
        UC[User Controller]
        FC[Financial Controller]
        TC[Trading Controller]
        NC[Notification Controller]
    end
    
    subgraph "Data Access Layer"
        DB[(MySQL Database)]
        DAO[Data Access Objects]
        ES[Email Service]
    end
    
    subgraph "Security Layer"
        CSRF[CSRF Protection]
        2FA[Two-Factor Auth]
        PH[Password Hashing]
        SS[Session Security]
    end
    
    UL --> AC
    AL --> UC
    SAL --> FC
    LF --> AC
    
    AC --> DAO
    UC --> DAO
    FC --> DAO
    TC --> DAO
    NC --> ES
    
    DAO --> DB
    
    AC --> CSRF
    AC --> 2FA
    AC --> PH
    AC --> SS
```

### Directory Structure Design

The application follows a clean, modular directory structure:

```
/ (root)
├── /admin/                    # Admin panel pages
│   ├── /dashboard/           # Admin dashboard
│   ├── /users/               # User management
│   ├── /deposits/            # Deposit management
│   ├── /withdrawals/         # Withdrawal management
│   ├── /plans/               # Trading plan management
│   └── /support/             # Support ticket management
├── /superadmin/              # Super admin panel
│   ├── /settings/            # System configuration
│   ├── /admins/              # Admin management
│   ├── /email-templates/     # Email template editor
│   └── /audit/               # Audit logs
├── /user/                    # User panel pages
│   ├── /dashboard/           # User dashboard
│   ├── /profile/             # Profile management
│   ├── /deposit/             # Deposit interface
│   ├── /withdraw/            # Withdrawal interface
│   ├── /transactions/        # Transaction history
│   └── /support/             # Support tickets
├── /assets/                  # Static resources
├── /includes/                # Reusable PHP components
├── /layouts/                 # Layout templates
├── /classes/                 # PHP classes
└── /test_folder/             # Testing scripts
```

### Database Design

The database schema supports the complete platform functionality with proper relationships and constraints:

```mermaid
erDiagram
    users ||--o{ deposits : "makes"
    users ||--o{ withdrawals : "requests"
    users ||--o{ transactions : "has"
    users ||--o{ support_tickets : "creates"
    trading_plans ||--o{ deposits : "selected_for"
    users {
        int id PK
        string username UK
        string email UK
        string password
        string first_name
        string last_name
        enum role
        enum status
        decimal balance
        decimal bonus
        boolean two_fa_enabled
        timestamp created_at
    }
    deposits {
        int id PK
        int user_id FK
        int plan_id FK
        decimal amount
        decimal bonus_amount
        enum status
        timestamp created_at
    }
    withdrawals {
        int id PK
        int user_id FK
        decimal amount
        enum status
        timestamp created_at
    }
    transactions {
        int id PK
        int user_id FK
        enum type
        decimal amount
        decimal balance_before
        decimal balance_after
        timestamp created_at
    }
```

## Components and Interfaces

### Authentication System

**Class: AuthenticationManager**
- `login($username, $password, $remember = false)`: Authenticates user credentials
- `logout()`: Destroys session and clears authentication
- `register($userData)`: Creates new user account with validation
- `resetPassword($email)`: Initiates password reset process
- `verifyTwoFactor($userId, $code)`: Validates 2FA codes

**Class: SessionManager**
- `startSession()`: Initializes secure session
- `validateSession()`: Checks session validity
- `regenerateSession()`: Regenerates session ID for security
- `destroySession()`: Completely destroys session

### User Management System

**Class: UserManager**
- `createUser($userData)`: Creates new user with validation
- `updateUser($userId, $userData)`: Updates user information
- `getUserById($userId)`: Retrieves user by ID
- `getUserByEmail($email)`: Retrieves user by email
- `suspendUser($userId)`: Suspends user account
- `activateUser($userId)`: Activates user account

**Class: ProfileManager**
- `updateProfile($userId, $profileData)`: Updates user profile
- `uploadProfilePicture($userId, $file)`: Handles profile picture upload
- `changePassword($userId, $oldPassword, $newPassword)`: Changes user password
- `setupTwoFactor($userId)`: Sets up 2FA for user

### Financial Management System

**Class: DepositManager**
- `createDeposit($userId, $planId, $amount, $paymentMethod)`: Creates deposit request
- `approveDeposit($depositId, $adminId)`: Approves deposit and credits account
- `rejectDeposit($depositId, $adminId, $reason)`: Rejects deposit with reason
- `getDepositsByStatus($status)`: Retrieves deposits by status
- `calculateBonus($amount)`: Calculates deposit bonus

**Class: WithdrawalManager**
- `createWithdrawal($userId, $amount, $method, $details)`: Creates withdrawal request
- `approveWithdrawal($withdrawalId, $adminId)`: Approves and processes withdrawal
- `rejectWithdrawal($withdrawalId, $adminId, $reason)`: Rejects withdrawal
- `getWithdrawalsByStatus($status)`: Retrieves withdrawals by status

**Class: TransactionManager**
- `recordTransaction($userId, $type, $amount, $description)`: Records transaction
- `getTransactionHistory($userId, $limit, $offset)`: Gets user transaction history
- `getTransactionsByType($type)`: Gets transactions by type
- `updateUserBalance($userId, $amount, $type)`: Updates user balance

### Trading System

**Class: TradingPlanManager**
- `createPlan($planData)`: Creates new trading plan
- `updatePlan($planId, $planData)`: Updates existing plan
- `deletePlan($planId)`: Deletes trading plan
- `getActivePlans()`: Gets all active plans
- `assignPlanToUser($userId, $planId)`: Assigns plan to user

**Class: TradeManager**
- `createTrade($userId, $asset, $amount, $type)`: Creates trade record
- `updateTradeStatus($tradeId, $status, $profit)`: Updates trade outcome
- `getTradeHistory($userId)`: Gets user's trade history
- `calculateProfitLoss($tradeId)`: Calculates trade P&L

### Notification System

**Class: EmailService**
- `sendWelcomeEmail($userEmail, $userName)`: Sends welcome email
- `sendDepositConfirmation($userEmail, $userName, $amount)`: Sends deposit confirmation
- `sendWithdrawalNotification($userEmail, $userName, $amount)`: Sends withdrawal notification
- `sendPasswordReset($userEmail, $resetToken)`: Sends password reset email
- `testSMTPConnection()`: Tests email server connection

**Class: NotificationManager**
- `createNotification($userId, $message, $type)`: Creates user notification
- `markAsRead($notificationId)`: Marks notification as read
- `getUnreadNotifications($userId)`: Gets unread notifications
- `sendBulkNotification($message, $userIds)`: Sends bulk notifications

### Admin Panel Components

**Class: AdminDashboard**
- `getDashboardStats()`: Gets overview statistics
- `getRecentActivity()`: Gets recent platform activity
- `getPendingActions()`: Gets items requiring admin attention

**Class: UserAdminManager**
- `getAllUsers($filters)`: Gets filtered user list
- `getUserDetails($userId)`: Gets detailed user information
- `creditUserAccount($userId, $amount, $reason)`: Credits user account
- `debitUserAccount($userId, $amount, $reason)`: Debits user account

### Super Admin Components

**Class: SystemConfigManager**
- `updateSystemSetting($key, $value)`: Updates system configuration
- `getSystemSettings()`: Gets all system settings
- `toggleFeature($feature, $enabled)`: Enables/disables features
- `updateThemeSettings($colors, $logo)`: Updates theme configuration

**Class: AdminManager**
- `createAdmin($adminData)`: Creates new admin account
- `updateAdmin($adminId, $adminData)`: Updates admin information
- `deleteAdmin($adminId)`: Deletes admin account
- `getAdminList()`: Gets all admin accounts

## Data Models

### User Model
```php
class User {
    private $id;
    private $username;
    private $email;
    private $password;
    private $firstName;
    private $lastName;
    private $role;
    private $status;
    private $balance;
    private $bonus;
    private $twoFaEnabled;
    private $createdAt;
    
    // Getters and setters
    // Validation methods
    // Business logic methods
}
```

### Deposit Model
```php
class Deposit {
    private $id;
    private $userId;
    private $planId;
    private $amount;
    private $bonusAmount;
    private $paymentMethod;
    private $status;
    private $proofOfPayment;
    private $createdAt;
    
    // Getters and setters
    // Status management methods
    // Validation methods
}
```

### Transaction Model
```php
class Transaction {
    private $id;
    private $userId;
    private $type;
    private $amount;
    private $balanceBefore;
    private $balanceAfter;
    private $description;
    private $status;
    private $createdAt;
    
    // Getters and setters
    // Balance calculation methods
}
```

## Error Handling

### Error Handling Strategy

**Database Errors:**
- All database operations wrapped in try-catch blocks
- PDO exceptions logged to error log
- User-friendly error messages displayed
- Automatic rollback for failed transactions

**Validation Errors:**
- Input validation at multiple layers (client-side, server-side, database)
- Comprehensive error message system
- Form validation with specific field error highlighting
- CSRF token validation on all forms

**Authentication Errors:**
- Failed login attempt logging
- Account lockout after multiple failed attempts
- Session timeout handling
- Unauthorized access redirection

**File Upload Errors:**
- File type validation
- File size limits
- Secure file storage
- Malware scanning for uploaded files

### Error Response Format
```php
class ErrorHandler {
    public static function handleException($exception) {
        error_log($exception->getMessage());
        
        if (DEBUG_MODE) {
            return $exception->getMessage();
        }
        
        return "An error occurred. Please try again.";
    }
    
    public static function handleValidationError($errors) {
        return [
            'success' => false,
            'errors' => $errors,
            'message' => 'Please correct the errors below.'
        ];
    }
}
```

## Testing Strategy

### Unit Testing
- Test all business logic classes independently
- Mock database connections for isolated testing
- Test validation methods with various input scenarios
- Test calculation methods (bonus, balance updates)

### Integration Testing
- Test complete user workflows (registration, deposit, withdrawal)
- Test admin approval processes
- Test email sending functionality
- Test 2FA implementation

### Security Testing
- SQL injection prevention testing
- XSS attack prevention testing
- CSRF protection testing
- Session security testing
- Password hashing verification

### Performance Testing
- Database query optimization testing
- Page load time testing
- Concurrent user testing
- Email delivery performance testing

### Test Environment Setup
```php
class TestDatabase {
    public static function setupTestData() {
        // Create test users
        // Create test trading plans
        // Create test transactions
        // Set up test email configuration
    }
    
    public static function cleanupTestData() {
        // Remove test data
        // Reset sequences
        // Clear test files
    }
}
```

### Automated Testing Framework
- PHPUnit for unit testing
- Selenium for UI testing
- Database fixtures for consistent test data
- Continuous integration setup for automated testing

The design ensures scalability, maintainability, and security while providing a comprehensive trading platform that meets all specified requirements. The modular architecture allows for easy feature additions and modifications while maintaining system integrity.