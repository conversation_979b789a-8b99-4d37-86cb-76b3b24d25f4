/**
 * Transaction History JavaScript
 * Handles transaction filtering, details modal, and export functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeTransactionHistory();
});

function initializeTransactionHistory() {
    // Initialize date inputs with reasonable defaults
    initializeDateFilters();
    
    // Setup form validation
    setupFormValidation();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
}

function initializeDateFilters() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (dateFromInput && !dateFromInput.value) {
        // Default to 30 days ago
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        dateFromInput.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
    
    if (dateToInput && !dateToInput.value) {
        // Default to today
        const today = new Date();
        dateToInput.value = today.toISOString().split('T')[0];
    }
    
    // Validate date range
    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', validateDateRange);
        dateToInput.addEventListener('change', validateDateRange);
    }
}

function validateDateRange() {
    const dateFromInput = document.getElementById('date_from');
    const dateToInput = document.getElementById('date_to');
    
    if (dateFromInput.value && dateToInput.value) {
        const fromDate = new Date(dateFromInput.value);
        const toDate = new Date(dateToInput.value);
        
        if (fromDate > toDate) {
            showAlert('From date cannot be later than to date', 'warning');
            dateFromInput.value = '';
            return false;
        }
        
        // Check if date range is too large (more than 1 year)
        const oneYear = 365 * 24 * 60 * 60 * 1000;
        if (toDate - fromDate > oneYear) {
            showAlert('Date range cannot exceed 1 year', 'warning');
            return false;
        }
    }
    
    return true;
}

function setupFormValidation() {
    const filterForm = document.querySelector('form[method="GET"]');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            if (!validateDateRange()) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            const submitBtn = filterForm.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Filtering...';
                submitBtn.disabled = true;
                
                // Re-enable after a delay (in case of slow response)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F to focus on type filter
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const typeSelect = document.getElementById('type');
            if (typeSelect) {
                typeSelect.focus();
            }
        }
        
        // Escape to clear filters
        if (e.key === 'Escape') {
            const clearBtn = document.querySelector('a[href="?"]');
            if (clearBtn && !document.querySelector('.modal.show')) {
                window.location.href = clearBtn.href;
            }
        }
    });
}

function viewTransactionDetails(transactionId) {
    const modal = new bootstrap.Modal(document.getElementById('transactionDetailsModal'));
    const contentDiv = document.getElementById('transactionDetailsContent');
    
    // Show loading state
    contentDiv.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading transaction details...</p>
        </div>
    `;
    
    modal.show();
    
    // Fetch transaction details
    fetch(`api/transaction-details.php?id=${transactionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTransactionDetails(data.transaction);
            } else {
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading transaction details: ${data.error || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching transaction details:', error);
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load transaction details. Please try again.
                </div>
            `;
        });
}

function displayTransactionDetails(transaction) {
    const contentDiv = document.getElementById('transactionDetailsContent');
    
    const statusClass = getStatusClass(transaction.status);
    const typeColor = getTransactionTypeColor(transaction.type);
    const typeIcon = getTransactionTypeIcon(transaction.type);
    
    contentDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted mb-3">Transaction Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td class="fw-medium">Transaction ID:</td>
                        <td>#${transaction.id}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Type:</td>
                        <td>
                            <span class="badge bg-${typeColor}">
                                ${typeIcon} ${transaction.type_display}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Amount:</td>
                        <td class="fw-bold ${transaction.is_credit ? 'text-success' : 'text-danger'}">
                            ${transaction.formatted_amount}
                        </td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Status:</td>
                        <td>
                            <span class="badge bg-${statusClass}">
                                ${transaction.status_display}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Date:</td>
                        <td>${formatDateTime(transaction.created_at)}</td>
                    </tr>
                </table>
            </div>
            
            <div class="col-md-6">
                <h6 class="text-muted mb-3">Balance Changes</h6>
                <table class="table table-sm">
                    <tr>
                        <td class="fw-medium">Balance Before:</td>
                        <td>$${parseFloat(transaction.balance_before).toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Balance After:</td>
                        <td>$${parseFloat(transaction.balance_after).toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Change:</td>
                        <td class="fw-bold ${transaction.is_credit ? 'text-success' : 'text-danger'}">
                            ${transaction.is_credit ? '+' : '-'}$${Math.abs(parseFloat(transaction.balance_after) - parseFloat(transaction.balance_before)).toFixed(2)}
                        </td>
                    </tr>
                </table>
                
                ${transaction.processed_by ? `
                    <h6 class="text-muted mb-3 mt-4">Processing Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td class="fw-medium">Processed By:</td>
                            <td>${transaction.processed_by_name || 'System'}</td>
                        </tr>
                        ${transaction.processed_at ? `
                        <tr>
                            <td class="fw-medium">Processed At:</td>
                            <td>${formatDateTime(transaction.processed_at)}</td>
                        </tr>
                        ` : ''}
                    </table>
                ` : ''}
            </div>
        </div>
        
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted mb-3">Description</h6>
                <div class="alert alert-light">
                    ${transaction.description}
                </div>
            </div>
        </div>
        
        ${transaction.reference_id ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-muted mb-3">Reference Information</h6>
                    <div class="alert alert-info">
                        <strong>Reference:</strong> ${transaction.reference_type}-${transaction.reference_id}
                        ${getReferenceLinkButton(transaction.reference_type, transaction.reference_id)}
                    </div>
                </div>
            </div>
        ` : ''}
    `;
}

function getReferenceLinkButton(referenceType, referenceId) {
    switch (referenceType) {
        case 'deposit':
            return `<a href="../deposit/details.php?id=${referenceId}" class="btn btn-sm btn-outline-primary ms-2">View Deposit</a>`;
        case 'withdrawal':
            return `<a href="../withdraw/details.php?id=${referenceId}" class="btn btn-sm btn-outline-warning ms-2">View Withdrawal</a>`;
        default:
            return '';
    }
}

function getStatusClass(status) {
    const classes = {
        'pending': 'warning',
        'completed': 'success',
        'failed': 'danger',
        'cancelled': 'secondary'
    };
    return classes[status] || 'secondary';
}

function getTransactionTypeColor(type) {
    const colors = {
        'deposit': 'success',
        'withdrawal': 'warning',
        'bonus': 'info',
        'trade_profit': 'success',
        'trade_loss': 'danger',
        'transfer_in': 'primary',
        'transfer_out': 'secondary',
        'admin_credit': 'success',
        'admin_debit': 'danger'
    };
    return colors[type] || 'secondary';
}

function getTransactionTypeIcon(type) {
    const icons = {
        'deposit': '<i class="fas fa-plus-circle"></i>',
        'withdrawal': '<i class="fas fa-minus-circle"></i>',
        'bonus': '<i class="fas fa-gift"></i>',
        'trade_profit': '<i class="fas fa-arrow-up"></i>',
        'trade_loss': '<i class="fas fa-arrow-down"></i>',
        'transfer_in': '<i class="fas fa-arrow-right"></i>',
        'transfer_out': '<i class="fas fa-arrow-left"></i>',
        'admin_credit': '<i class="fas fa-user-shield"></i>',
        'admin_debit': '<i class="fas fa-user-minus"></i>'
    };
    return icons[type] || '<i class="fas fa-exchange-alt"></i>';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function exportTransactions() {
    const form = document.getElementById('exportForm');
    const formData = new FormData(form);
    
    // Validate export form
    const dateFrom = formData.get('date_from');
    const dateTo = formData.get('date_to');
    
    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
        showAlert('From date cannot be later than to date', 'warning');
        return;
    }
    
    // Add current filters to export
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('type')) formData.append('type', urlParams.get('type'));
    
    // Show loading state
    const exportBtn = document.querySelector('#exportModal .btn-primary');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    exportBtn.disabled = true;
    
    // Create download link
    const params = new URLSearchParams(formData);
    const downloadUrl = `api/export-transactions.php?${params.toString()}`;
    
    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `transactions_${new Date().toISOString().split('T')[0]}.${formData.get('format')}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button state
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();
        
        showAlert('Export completed successfully', 'success');
    }, 2000);
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Utility function to debounce search inputs
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}