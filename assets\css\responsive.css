/* Responsive Design for Coinage Trading Platform */

/* Base responsive utilities */
.container-responsive {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* Mobile First Approach */
@media (max-width: 575.98px) {
    /* Extra small devices (phones) */
    .container-responsive {
        max-width: 100%;
        padding: 0 10px;
    }
    
    .sidebar {
        position: fixed !important;
        top: 0;
        left: -100%;
        width: 280px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease-in-out;
        overflow-y: auto;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease-in-out;
    }
    
    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 10px;
    }
    
    .mobile-header {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: white;
        border-bottom: 1px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 1030;
    }
    
    .mobile-menu-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #495057;
        padding: 5px;
    }
    
    .stats-card {
        margin-bottom: 15px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        font-size: 14px;
        padding: 8px 16px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    .form-control, .form-select {
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .breadcrumb {
        font-size: 14px;
        padding: 8px 0;
    }
    
    .pagination {
        font-size: 14px;
    }
    
    .alert {
        font-size: 14px;
        padding: 10px;
    }
}

@media (min-width: 576px) and (max-width: 767.98px) {
    /* Small devices (landscape phones) */
    .container-responsive {
        max-width: 540px;
    }
    
    .sidebar {
        width: 240px;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .mobile-header {
        display: flex !important;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
}

@media (min-width: 768px) and (max-width: 991.98px) {
    /* Medium devices (tablets) */
    .container-responsive {
        max-width: 720px;
    }
    
    .sidebar {
        position: relative !important;
        width: 220px;
        left: 0 !important;
    }
    
    .main-content {
        margin-left: 220px;
        padding: 20px;
    }
    
    .mobile-header {
        display: none !important;
    }
    
    .sidebar-overlay {
        display: none !important;
    }
}

@media (min-width: 992px) and (max-width: 1199.98px) {
    /* Large devices (desktops) */
    .container-responsive {
        max-width: 960px;
    }
    
    .sidebar {
        position: relative !important;
        width: 250px;
        left: 0 !important;
    }
    
    .main-content {
        margin-left: 250px;
        padding: 25px;
    }
    
    .mobile-header {
        display: none !important;
    }
}

@media (min-width: 1200px) {
    /* Extra large devices (large desktops) */
    .container-responsive {
        max-width: 1140px;
    }
    
    .sidebar {
        position: relative !important;
        width: 260px;
        left: 0 !important;
    }
    
    .main-content {
        margin-left: 260px;
        padding: 30px;
    }
    
    .mobile-header {
        display: none !important;
    }
}

/* Navigation responsive styles */
.nav-responsive {
    flex-direction: column;
}

@media (min-width: 768px) {
    .nav-responsive {
        flex-direction: row;
    }
}

/* Card responsive grid */
.card-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 576px) {
    .card-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .card-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1200px) {
    .card-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Table responsive enhancements */
@media (max-width: 767.98px) {
    .table-responsive table {
        font-size: 0.875rem;
    }
    
    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem 0.25rem;
    }
    
    .table-responsive .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Form responsive styles */
.form-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .form-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-responsive .form-group-full {
        grid-column: 1 / -1;
    }
}

/* Modal responsive adjustments */
@media (max-width: 575.98px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .modal-header {
        padding: 1rem 0.75rem;
    }
    
    .modal-body {
        padding: 0.75rem;
    }
    
    .modal-footer {
        padding: 0.75rem;
    }
}

/* Utility classes for responsive design */
.hide-mobile {
    display: block;
}

@media (max-width: 767.98px) {
    .hide-mobile {
        display: none !important;
    }
}

.show-mobile {
    display: none;
}

@media (max-width: 767.98px) {
    .show-mobile {
        display: block !important;
    }
}

.show-mobile-flex {
    display: none;
}

@media (max-width: 767.98px) {
    .show-mobile-flex {
        display: flex !important;
    }
}

/* Touch-friendly button sizes */
@media (max-width: 767.98px) {
    .btn-touch {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
    }
}

/* Responsive text sizes */
.text-responsive {
    font-size: 1rem;
}

@media (max-width: 575.98px) {
    .text-responsive {
        font-size: 0.875rem;
    }
}

@media (min-width: 1200px) {
    .text-responsive {
        font-size: 1.125rem;
    }
}

/* Responsive spacing */
.spacing-responsive {
    padding: 1rem;
}

@media (max-width: 575.98px) {
    .spacing-responsive {
        padding: 0.5rem;
    }
}

@media (min-width: 1200px) {
    .spacing-responsive {
        padding: 1.5rem;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .mobile-header,
    .btn,
    .pagination,
    .modal {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sidebar {
        border-right: 2px solid #000;
    }
    
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .sidebar,
    .sidebar-overlay,
    .stats-card,
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .main-content {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table-dark {
        background-color: #404040;
    }
}