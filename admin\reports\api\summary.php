<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuthenticationManager.php';
require_once '../../../classes/services/FinancialReportingService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
    $dateTo = $_GET['date_to'] ?? date('Y-m-d');
    
    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $dateFrom) || !DateTime::createFromFormat('Y-m-d', $dateTo)) {
        throw new InvalidArgumentException('Invalid date format');
    }
    
    // Ensure date range is not too large (max 1 year)
    $dateFromObj = new DateTime($dateFrom);
    $dateToObj = new DateTime($dateTo);
    $interval = $dateFromObj->diff($dateToObj);
    
    if ($interval->days > 365) {
        throw new InvalidArgumentException('Date range cannot exceed 365 days');
    }
    
    $reportingService = new FinancialReportingService();
    $summary = $reportingService->getSummaryReport($dateFrom, $dateTo);
    
    echo json_encode([
        'success' => true,
        'data' => $summary
    ]);
    
} catch (Exception $e) {
    error_log("Financial summary API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>