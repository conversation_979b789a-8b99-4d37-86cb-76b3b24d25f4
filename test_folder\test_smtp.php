<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/email_functions.php';

echo "<h2>SMTP Connection and Email Test</h2>";

// Test SMTP connection
echo "<h3>1. SMTP Configuration</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Setting</th><th>Value</th></tr>";
echo "<tr><td>SMTP Host</td><td>" . SMTP_HOST . "</td></tr>";
echo "<tr><td>SMTP Port</td><td>" . SMTP_PORT . "</td></tr>";
echo "<tr><td>SMTP Username</td><td>" . SMTP_USERNAME . "</td></tr>";
echo "<tr><td>From Name</td><td>" . SMTP_FROM_NAME . "</td></tr>";
echo "<tr><td>From Email</td><td>" . SMTP_FROM_EMAIL . "</td></tr>";
echo "</table>";

echo "<h3>2. SMTP Connection Test</h3>";
try {
    $emailService = new EmailService();
    
    if ($emailService->testConnection()) {
        echo "<p style='color: green;'>✓ SMTP connection successful!</p>";
        
        // Send test email
        echo "<h3>3. Sending Test Email</h3>";
        $testEmail = '<EMAIL>';
        $subject = 'Test Email from Coinage Trading Platform';
        
        $body = "
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, " . PRIMARY_COLOR . ", " . SECONDARY_COLOR . "); color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0; }
                .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
                .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
                .button { display: inline-block; background: " . PRIMARY_COLOR . "; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>" . SITE_NAME . "</h1>
                    <p>SMTP Test Email</p>
                </div>
                <div class='content'>
                    <h2>Hello!</h2>
                    <p>This is a test email from the <strong>" . SITE_NAME . "</strong> platform.</p>
                    
                    <p><strong>Test Details:</strong></p>
                    <ul>
                        <li>Sent at: " . date('Y-m-d H:i:s') . "</li>
                        <li>From: " . SMTP_FROM_EMAIL . "</li>
                        <li>SMTP Host: " . SMTP_HOST . "</li>
                        <li>Port: " . SMTP_PORT . "</li>
                    </ul>
                    
                    <p>If you received this email, the SMTP configuration is working correctly!</p>
                    
                    <a href='" . BASE_URL . "' class='button'>Visit Platform</a>
                </div>
                <div class='footer'>
                    <p>&copy; 2025 " . SITE_NAME . ". All rights reserved.</p>
                    <p>This is an automated test email.</p>
                </div>
            </div>
        </body>
        </html>";
        
        if ($emailService->sendEmail($testEmail, $subject, $body)) {
            echo "<p style='color: green;'>✓ Test email sent successfully to: <strong>{$testEmail}</strong></p>";
            echo "<p style='color: blue;'>Please check the inbox (and spam folder) for the test email.</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to send test email!</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ SMTP connection failed!</p>";
        echo "<p>Please check your SMTP settings in config.php</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>SMTP Error: " . $e->getMessage() . "</p>";
}

echo "<h3>4. Email Template Preview</h3>";
echo "<p>Here's how the test email looks:</p>";
echo "<iframe srcdoc='" . htmlspecialchars($body ?? '') . "' width='100%' height='400' style='border: 1px solid #ccc;'></iframe>";

echo "<hr>";
echo "<p><a href='test_database.php'>← Test Database</a> | <a href='test_connections.php'>Test All Connections</a></p>";
?>