<?php
require_once '../../includes/security_init.php';
require_once '../../classes/controllers/AuthController.php';
require_once '../../classes/services/SystemHealthService.php';
require_once '../../classes/services/ErrorHandlingService.php';
require_once '../../classes/views/BaseView.php';

// Require super admin authentication
AuthController::requireRole('superadmin');

$healthService = SystemHealthService::getInstance();
$errorHandler = ErrorHandlingService::getInstance();

// Get system health status
$healthResults = $healthService->runHealthCheck();
$systemSummary = $healthService->getSystemStatusSummary();

// Get error statistics
$errorStats = $errorHandler->getErrorStatistics(7);
$recentErrors = $errorHandler->getRecentErrors(20);

/**
 * System Monitoring Dashboard View
 */
class SystemMonitoringView extends BaseView {
    private $healthResults;
    private $systemSummary;
    private $errorStats;
    private $recentErrors;
    
    public function __construct($healthResults, $systemSummary, $errorStats, $recentErrors) {
        parent::__construct();
        $this->healthResults = $healthResults;
        $this->systemSummary = $systemSummary;
        $this->errorStats = $errorStats;
        $this->recentErrors = $recentErrors;
        $this->setTitle('System Monitoring - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/system-monitoring.css">
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-heartbeat me-2"></i>System Monitoring
                        </h1>
                        <div>
                            <button class="btn btn-primary" onclick="refreshHealthCheck()">
                                <i class="fas fa-sync-alt me-2"></i>Refresh Health Check
                            </button>
                            <button class="btn btn-outline-secondary" onclick="exportReport()">
                                <i class="fas fa-download me-2"></i>Export Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- System Status Overview -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-tachometer-alt me-2"></i>System Status Overview
                                <?php
                                $statusColors = [
                                    'healthy' => 'success',
                                    'warning' => 'warning', 
                                    'critical' => 'danger',
                                    'unknown' => 'secondary'
                                ];
                                $statusColor = $statusColors[$this->systemSummary['overall_status']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusColor ?> ms-2">
                                    <?= ucfirst($this->systemSummary['overall_status']) ?>
                                </span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h2 text-success"><?= $this->systemSummary['healthy_count'] ?></div>
                                        <div class="text-muted">Healthy</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h2 text-warning"><?= $this->systemSummary['warning_count'] ?></div>
                                        <div class="text-muted">Warnings</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h2 text-danger"><?= $this->systemSummary['critical_count'] ?></div>
                                        <div class="text-muted">Critical</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="h2 text-muted"><?= count($this->systemSummary['checks']) ?></div>
                                        <div class="text-muted">Total Checks</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Health Check Details -->
            <div class="row mb-4">
                <?php foreach ($this->healthResults as $checkType => $result): ?>
                    <div class="col-lg-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <?php
                                        $icons = [
                                            'database' => 'fa-database',
                                            'file_system' => 'fa-folder',
                                            'memory' => 'fa-memory',
                                            'disk_space' => 'fa-hdd',
                                            'email' => 'fa-envelope',
                                            'external_api' => 'fa-globe'
                                        ];
                                        $icon = $icons[$checkType] ?? 'fa-cog';
                                        ?>
                                        <i class="fas <?= $icon ?> me-2"></i>
                                        <?= ucwords(str_replace('_', ' ', $checkType)) ?>
                                    </h6>
                                    <?php
                                    $statusColor = $statusColors[$result['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <?= ucfirst($result['status']) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-3"><?= htmlspecialchars($result['message']) ?></p>
                                
                                <?php if (!empty($result['metrics'])): ?>
                                    <div class="metrics">
                                        <?php foreach ($result['metrics'] as $key => $value): ?>
                                            <?php if (is_scalar($value)): ?>
                                                <div class="d-flex justify-content-between mb-1">
                                                    <small class="text-muted"><?= ucwords(str_replace('_', ' ', $key)) ?>:</small>
                                                    <small><strong><?= is_numeric($value) ? number_format($value, 2) : htmlspecialchars($value) ?></strong></small>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        Response time: <?= $result['duration_ms'] ?>ms
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Error Statistics -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Error Statistics (Last 7 Days)
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="errorChart" height="100"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Error Summary
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php
                            $errorCounts = [];
                            foreach ($this->errorStats as $stat) {
                                $key = $stat['severity'];
                                $errorCounts[$key] = ($errorCounts[$key] ?? 0) + $stat['count'];
                            }
                            ?>
                            <?php foreach (['critical', 'high', 'medium', 'low'] as $severity): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="text-<?= $severity === 'critical' ? 'danger' : ($severity === 'high' ? 'warning' : ($severity === 'medium' ? 'info' : 'secondary')) ?>">
                                        <i class="fas fa-circle me-2"></i><?= ucfirst($severity) ?>
                                    </span>
                                    <span class="badge bg-light text-dark"><?= $errorCounts[$severity] ?? 0 ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Errors -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>Recent Errors
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($this->recentErrors)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                    <h5 class="text-success">No Recent Errors</h5>
                                    <p class="text-muted">System is running smoothly with no recent errors.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Error ID</th>
                                                <th>Type</th>
                                                <th>Severity</th>
                                                <th>Category</th>
                                                <th>Message</th>
                                                <th>Time</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($this->recentErrors as $error): ?>
                                                <tr>
                                                    <td>
                                                        <code class="small"><?= htmlspecialchars($error['error_id']) ?></code>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary"><?= htmlspecialchars($error['type']) ?></span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $severityColors = [
                                                            'critical' => 'danger',
                                                            'high' => 'warning',
                                                            'medium' => 'info',
                                                            'low' => 'secondary'
                                                        ];
                                                        $severityColor = $severityColors[$error['severity']] ?? 'secondary';
                                                        ?>
                                                        <span class="badge bg-<?= $severityColor ?>"><?= ucfirst($error['severity']) ?></span>
                                                    </td>
                                                    <td><?= ucfirst($error['category']) ?></td>
                                                    <td>
                                                        <span class="text-truncate d-inline-block" style="max-width: 300px;" title="<?= htmlspecialchars($error['message']) ?>">
                                                            <?= htmlspecialchars($error['message']) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">
                                                            <?= date('M j, H:i', strtotime($error['created_at'])) ?>
                                                        </small>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script>
            // Error statistics chart
            const errorData = <?= json_encode($this->errorStats) ?>;
            
            // Process data for chart
            const dates = [...new Set(errorData.map(item => item.date))].sort();
            const severities = ['critical', 'high', 'medium', 'low'];
            const colors = {
                'critical': '#dc3545',
                'high': '#ffc107', 
                'medium': '#17a2b8',
                'low': '#6c757d'
            };
            
            const datasets = severities.map(severity => {
                const data = dates.map(date => {
                    const item = errorData.find(d => d.date === date && d.severity === severity);
                    return item ? item.count : 0;
                });
                
                return {
                    label: severity.charAt(0).toUpperCase() + severity.slice(1),
                    data: data,
                    borderColor: colors[severity],
                    backgroundColor: colors[severity] + '20',
                    tension: 0.1
                };
            });
            
            const ctx = document.getElementById('errorChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
            
            // Auto-refresh every 30 seconds
            setInterval(function() {
                if (!document.hidden) {
                    refreshHealthCheck();
                }
            }, 30000);
            
            // Refresh health check
            function refreshHealthCheck() {
                location.reload();
            }
            
            // Export report
            function exportReport() {
                window.open('/superadmin/system-monitoring/export.php', '_blank');
            }
        </script>
        <?php
    }
}

$view = new SystemMonitoringView($healthResults, $systemSummary, $errorStats, $recentErrors);
$view->render();
?>