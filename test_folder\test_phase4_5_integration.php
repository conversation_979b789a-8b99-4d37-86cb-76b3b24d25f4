<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/BaseModel.php';
require_once __DIR__ . '/../classes/testing/TestFramework.php';
require_once __DIR__ . '/../classes/services/CacheService.php';
require_once __DIR__ . '/../classes/services/DatabaseOptimizationService.php';
require_once __DIR__ . '/../classes/services/BackupService.php';

/**
 * Phase 4 & 5 Integration Test - Landing Page and System Optimization
 * Tests the final integration components and optimization features
 */

echo "<h1>Phase 4 & 5: Landing Page and System Optimization - Integration Test</h1>";
echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>";

// Initialize test framework
$framework = new TestFramework();

// Test 1: Landing Page Components
echo "<h2>Test 1: Landing Page Components</h2>";
$framework->suite('Landing Page Tests', TestFramework::TYPE_INTEGRATION)
    ->test('Landing Page File Exists', function($test) {
        $landingPageExists = file_exists(__DIR__ . '/../index.php');
        $test->assertTrue($landingPageExists, 'Landing page should exist');
        echo "✅ Landing page file exists<br>";
    })
    ->test('About Page Exists', function($test) {
        $aboutPageExists = file_exists(__DIR__ . '/../about.php');
        $test->assertTrue($aboutPageExists, 'About page should exist');
        echo "✅ About page file exists<br>";
    })
    ->test('Contact Page Exists', function($test) {
        $contactPageExists = file_exists(__DIR__ . '/../contact.php');
        $test->assertTrue($contactPageExists, 'Contact page should exist');
        echo "✅ Contact page file exists<br>";
    })
    ->test('Terms and Privacy Pages Exist', function($test) {
        $termsExists = file_exists(__DIR__ . '/../terms.php');
        $privacyExists = file_exists(__DIR__ . '/../privacy.php');
        $test->assertTrue($termsExists, 'Terms page should exist');
        $test->assertTrue($privacyExists, 'Privacy page should exist');
        echo "✅ Legal pages exist<br>";
    })
    ->test('Landing Page CSS Exists', function($test) {
        $cssExists = file_exists(__DIR__ . '/../assets/css/landing-page.css');
        $test->assertTrue($cssExists, 'Landing page CSS should exist');
        echo "✅ Landing page CSS exists<br>";
    })
    ->end();

// Test 2: Cache Service
echo "<h2>Test 2: Cache Service</h2>";
$framework->suite('Cache Service Tests', TestFramework::TYPE_SYSTEM)
    ->test('Cache Service Initialization', function($test) {
        $cache = CacheService::getInstance();
        $test->assertNotNull($cache, 'Cache service should be initialized');
        echo "✅ Cache service initialized<br>";
    })
    ->test('Cache Set and Get', function($test) {
        $cache = CacheService::getInstance();
        
        $testData = ['test' => 'data', 'number' => 123];
        $result = $cache->set('test_key', $testData, 60);
        $test->assertTrue($result, 'Cache set should succeed');
        
        $retrieved = $cache->get('test_key');
        $test->assertEqual($testData, $retrieved, 'Retrieved data should match stored data');
        echo "✅ Cache set and get working correctly<br>";
    })
    ->test('Cache Expiration', function($test) {
        $cache = CacheService::getInstance();
        
        // Set cache with 1 second TTL
        $cache->set('expire_test', 'test_data', 1);
        
        // Should exist immediately
        $test->assertTrue($cache->has('expire_test'), 'Cache should exist immediately');
        
        // Wait for expiration (simulate)
        sleep(2);
        
        // Should be expired now
        $test->assertFalse($cache->has('expire_test'), 'Cache should be expired');
        echo "✅ Cache expiration working correctly<br>";
    })
    ->test('Cache Namespaces', function($test) {
        $cache = CacheService::getInstance();
        
        $cache->set('same_key', 'data1', 60, 'namespace1');
        $cache->set('same_key', 'data2', 60, 'namespace2');
        
        $data1 = $cache->get('same_key', 'namespace1');
        $data2 = $cache->get('same_key', 'namespace2');
        
        $test->assertEqual('data1', $data1, 'Namespace 1 data should be correct');
        $test->assertEqual('data2', $data2, 'Namespace 2 data should be correct');
        echo "✅ Cache namespaces working correctly<br>";
    })
    ->test('Cache Statistics', function($test) {
        $cache = CacheService::getInstance();
        $stats = $cache->getStats();
        
        $test->assertTrue(is_array($stats), 'Stats should be an array');
        $test->assertTrue(isset($stats['total_files']), 'Stats should include total files');
        $test->assertTrue(isset($stats['namespaces']), 'Stats should include namespaces');
        echo "✅ Cache statistics working correctly<br>";
    })
    ->end();

// Test 3: Database Optimization Service
echo "<h2>Test 3: Database Optimization Service</h2>";
$framework->suite('Database Optimization Tests', TestFramework::TYPE_SYSTEM)
    ->test('Database Optimization Service Initialization', function($test) {
        $dbOpt = DatabaseOptimizationService::getInstance();
        $test->assertNotNull($dbOpt, 'Database optimization service should be initialized');
        echo "✅ Database optimization service initialized<br>";
    })
    ->test('Query Performance Logging', function($test) {
        $dbOpt = DatabaseOptimizationService::getInstance();
        
        // Execute a test query
        $result = $dbOpt->executeQuery("SELECT COUNT(*) as count FROM users", []);
        $test->assertTrue(is_array($result), 'Query should return array result');
        
        // Check query stats
        $stats = $dbOpt->getQueryStats();
        $test->assertTrue($stats['total_queries'] > 0, 'Should have logged queries');
        echo "✅ Query performance logging working<br>";
    })
    ->test('Database Size Information', function($test) {
        $dbOpt = DatabaseOptimizationService::getInstance();
        $sizeInfo = $dbOpt->getDatabaseSize();
        
        $test->assertTrue(isset($sizeInfo['total_size']), 'Should return total size');
        $test->assertTrue($sizeInfo['total_size'] >= 0, 'Size should be non-negative');
        echo "✅ Database size information available<br>";
    })
    ->test('Table Performance Analysis', function($test) {
        $dbOpt = DatabaseOptimizationService::getInstance();
        $analysis = $dbOpt->analyzeTablePerformance();
        
        $test->assertTrue(is_array($analysis), 'Analysis should be an array');
        $test->assertTrue(isset($analysis['users']), 'Should analyze users table');
        echo "✅ Table performance analysis working<br>";
    })
    ->test('Index Optimization', function($test) {
        $dbOpt = DatabaseOptimizationService::getInstance();
        $result = $dbOpt->createOptimizationIndexes();
        
        $test->assertTrue(isset($result['created']), 'Should report created indexes');
        $test->assertTrue($result['created'] >= 0, 'Created count should be non-negative');
        echo "✅ Index optimization completed: {$result['created']} indexes processed<br>";
    })
    ->end();

// Test 4: Backup Service
echo "<h2>Test 4: Backup Service</h2>";
$framework->suite('Backup Service Tests', TestFramework::TYPE_SYSTEM)
    ->test('Backup Service Initialization', function($test) {
        $backup = BackupService::getInstance();
        $test->assertNotNull($backup, 'Backup service should be initialized');
        echo "✅ Backup service initialized<br>";
    })
    ->test('Database Backup Creation', function($test) {
        $backup = BackupService::getInstance();
        $result = $backup->createDatabaseBackup(true, false); // No compression for test
        
        $test->assertTrue($result['success'], 'Database backup should succeed');
        $test->assertNotNull($result['filename'], 'Backup filename should be provided');
        $test->assertTrue($result['size'] > 0, 'Backup file should have content');
        echo "✅ Database backup created: {$result['filename']} ({$backup->formatBytes($result['size'])})<br>";
    })
    ->test('Backup Listing', function($test) {
        $backup = BackupService::getInstance();
        $backups = $backup->listBackups();
        
        $test->assertTrue(is_array($backups), 'Backup list should be an array');
        $test->assertTrue(isset($backups['database']), 'Should list database backups');
        $test->assertTrue(count($backups['database']) > 0, 'Should have at least one database backup');
        echo "✅ Backup listing working: " . count($backups['database']) . " database backups found<br>";
    })
    ->test('Backup Statistics', function($test) {
        $backup = BackupService::getInstance();
        $stats = $backup->getBackupStats();
        
        $test->assertTrue(is_array($stats), 'Stats should be an array');
        $test->assertTrue(isset($stats['total_backups']), 'Should include total backups');
        $test->assertTrue($stats['total_backups'] > 0, 'Should have backups');
        echo "✅ Backup statistics: {$stats['total_backups']} total backups, {$backup->formatBytes($stats['total_size'])} total size<br>";
    })
    ->test('Automated Backup Scheduling', function($test) {
        $backup = BackupService::getInstance();
        $result = $backup->scheduleAutomatedBackup('daily', 30);
        
        $test->assertTrue($result['success'], 'Backup scheduling should succeed');
        $test->assertTrue(isset($result['config']), 'Should return configuration');
        echo "✅ Automated backup scheduled<br>";
    })
    ->end();

// Test 5: System Integration
echo "<h2>Test 5: System Integration</h2>";
$framework->suite('System Integration Tests', TestFramework::TYPE_INTEGRATION)
    ->test('Cache and Database Integration', function($test) {
        $cache = CacheService::getInstance();
        $dbOpt = DatabaseOptimizationService::getInstance();
        
        // Test cached query
        $sql = "SELECT COUNT(*) as count FROM users WHERE status = ?";
        $params = ['active'];
        
        // First query should hit database
        $result1 = $dbOpt->executeQuery($sql, $params, 'user_count_active', 300);
        
        // Second query should hit cache
        $cached = $cache->getCachedQuery($sql, $params);
        
        $test->assertNotNull($cached, 'Query result should be cached');
        $test->assertEqual($result1, $cached, 'Cached result should match database result');
        echo "✅ Cache and database integration working<br>";
    })
    ->test('System Performance Monitoring', function($test) {
        $cache = CacheService::getInstance();
        $dbOpt = DatabaseOptimizationService::getInstance();
        $backup = BackupService::getInstance();
        
        // Get performance metrics
        $cacheStats = $cache->getStats();
        $queryStats = $dbOpt->getQueryStats();
        $backupStats = $backup->getBackupStats();
        
        $test->assertTrue(is_array($cacheStats), 'Cache stats should be available');
        $test->assertTrue(is_array($queryStats), 'Query stats should be available');
        $test->assertTrue(is_array($backupStats), 'Backup stats should be available');
        
        echo "✅ System performance monitoring integrated<br>";
        echo "   - Cache files: {$cacheStats['total_files']}<br>";
        echo "   - Queries executed: {$queryStats['total_queries']}<br>";
        echo "   - Backups available: {$backupStats['total_backups']}<br>";
    })
    ->test('Error Handling Integration', function($test) {
        // Test that optimization services handle errors gracefully
        $cache = CacheService::getInstance();
        
        // Test invalid cache operation
        $result = $cache->get('non_existent_key');
        $test->assertNull($result, 'Non-existent cache key should return null');
        
        // Test cache cleanup
        $cleaned = $cache->cleanExpired();
        $test->assertTrue($cleaned >= 0, 'Cache cleanup should return non-negative count');
        
        echo "✅ Error handling integration working<br>";
    })
    ->end();

// Run all tests
echo "<h2>Running All Tests</h2>";
$results = $framework->run('text');

// Display results
echo "<pre>$results</pre>";

// Save results
$timestamp = date('Y-m-d_H-i-s');
$framework->saveResults("phase4_5_integration_test_$timestamp.html", 'html');
$framework->saveResults("phase4_5_integration_test_$timestamp.json", 'json');

// System optimization summary
echo "<h2>System Optimization Summary</h2>";

$cache = CacheService::getInstance();
$dbOpt = DatabaseOptimizationService::getInstance();
$backup = BackupService::getInstance();

$cacheStats = $cache->getStats();
$dbSize = $dbOpt->getDatabaseSize();
$backupStats = $backup->getBackupStats();

echo "<div style='background: #e8f5e8; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3 style='color: #2d5a2d;'>✅ System Optimization Complete!</h3>";
echo "<p style='color: #2d5a2d;'><strong>Cache System:</strong> {$cacheStats['total_files']} files, {$cache->formatBytes($cacheStats['total_size'])}</p>";
echo "<p style='color: #2d5a2d;'><strong>Database:</strong> {$dbOpt->formatBytes($dbSize['total_size'] ?? 0)} total size</p>";
echo "<p style='color: #2d5a2d;'><strong>Backups:</strong> {$backupStats['total_backups']} backups, {$backup->formatBytes($backupStats['total_size'])}</p>";
echo "<p style='color: #2d5a2d;'>Landing page and optimization systems are fully operational.</p>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3 style='color: #155724;'>🎉 Phase 4 & 5 Integration Test Complete!</h3>";
echo "<p style='color: #155724;'>All landing page components and system optimization features are working correctly.</p>";
echo "<p style='color: #155724;'>Test results saved to test_results directory.</p>";
echo "</div>";

echo "</div>";
?>