<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Admin User Management';
$user = getCurrentUser();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        // Validate CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_admin':
                $userData = [
                    'username' => $_POST['username'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'password' => $_POST['password'] ?? '',
                    'first_name' => $_POST['first_name'] ?? '',
                    'last_name' => $_POST['last_name'] ?? '',
                    'role' => $_POST['role'] ?? 'admin',
                    'status' => 'active'
                ];
                
                // Validate required fields
                if (empty($userData['username']) || empty($userData['email']) || empty($userData['password'])) {
                    throw new Exception('Username, email, and password are required');
                }
                
                // Check if username or email already exists
                if (User::findByUsername($userData['username'])) {
                    throw new Exception('Username already exists');
                }
                
                if (User::findByEmail($userData['email'])) {
                    throw new Exception('Email already exists');
                }
                
                // Hash password
                $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
                
                $newUser = new User($userData);
                if ($newUser->save()) {
                    $response = ['success' => true, 'message' => 'Admin user created successfully'];
                } else {
                    throw new Exception('Failed to create admin user');
                }
                break;
                
            case 'update_admin':
                $userId = intval($_POST['user_id'] ?? 0);
                $adminUser = User::find($userId);
                
                if (!$adminUser) {
                    throw new Exception('Admin user not found');
                }
                
                // Update user data
                $adminUser->first_name = $_POST['first_name'] ?? '';
                $adminUser->last_name = $_POST['last_name'] ?? '';
                $adminUser->email = $_POST['email'] ?? '';
                $adminUser->role = $_POST['role'] ?? 'admin';
                $adminUser->status = $_POST['status'] ?? 'active';
                
                // Update password if provided
                if (!empty($_POST['password'])) {
                    $adminUser->password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                }
                
                if ($adminUser->save()) {
                    $response = ['success' => true, 'message' => 'Admin user updated successfully'];
                } else {
                    throw new Exception('Failed to update admin user');
                }
                break;
                
            case 'delete_admin':
                $userId = intval($_POST['user_id'] ?? 0);
                $adminUser = User::find($userId);
                
                if (!$adminUser) {
                    throw new Exception('Admin user not found');
                }
                
                // Prevent deleting self
                if ($adminUser->id === $user['id']) {
                    throw new Exception('You cannot delete your own account');
                }
                
                if ($adminUser->delete()) {
                    $response = ['success' => true, 'message' => 'Admin user deleted successfully'];
                } else {
                    throw new Exception('Failed to delete admin user');
                }
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for page reload
    $_SESSION['admin_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get response from session if available
$response = $_SESSION['admin_response'] ?? null;
unset($_SESSION['admin_response']);

// Get all admin users
try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE role IN ('admin', 'superadmin') ORDER BY created_at DESC");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $adminUsers = [];
    foreach ($results as $userData) {
        $adminUsers[] = new User($userData);
    }
} catch (Exception $e) {
    $adminUsers = [];
    error_log("Error fetching admin users: " . $e->getMessage());
}

// Get current settings for styling
$primaryColor = SystemSetting::getValue('primary_color', '#007bff');
$sidebarBg = SystemSetting::getValue('sidebar_bg_color', '#343a40');
$sidebarText = SystemSetting::getValue('sidebar_text_color', '#ffffff');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo $primaryColor; ?>;
            --sidebar-bg: <?php echo $sidebarBg; ?>;
            --sidebar-text: <?php echo $sidebarText; ?>;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        
        .admin-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-radius: 16px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: 16px 16px 0 0;
            padding: 20px 24px;
        }
        
        .admin-body {
            padding: 24px;
        }
        
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.15);
        }
        
        .btn {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }
        
        .table {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }
        
        .table tbody tr {
            transition: all 0.3s ease;
        }
        
        .table tbody tr:hover {
            background-color: rgba(0,123,255,0.05);
        }
        
        .badge {
            border-radius: 8px;
            padding: 6px 12px;
        }
        
        .alert {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .modal-content {
            border-radius: 16px;
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            border-radius: 16px 16px 0 0;
            background: var(--primary-color);
            color: white;
            border: none;
        }
        
        .sidebar {
            background: var(--sidebar-bg);
            color: var(--sidebar-text);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: var(--sidebar-text);
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include __DIR__ . '/../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="fas fa-users-cog me-2" style="color: var(--primary-color);"></i><?php echo $pageTitle; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAdminModal">
                            <i class="fas fa-plus me-2"></i>Create New Admin
                        </button>
                    </div>
                </div>

                <?php if ($response): ?>
                    <div class="alert alert-<?php echo $response['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <i class="fas fa-<?php echo $response['success'] ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($response['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Admin Users Table -->
                <div class="admin-card">
                    <div class="admin-header">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Admin Users
                        </h5>
                    </div>
                    <div class="admin-body">
                        <?php if (empty($adminUsers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5>No Admin Users Found</h5>
                                <p class="text-muted">Create your first admin user to get started.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($adminUsers as $adminUser): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-circle me-3">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                        <div>
                                                            <div class="fw-bold"><?php echo htmlspecialchars($adminUser->username); ?></div>
                                                            <small class="text-muted">
                                                                <?php echo htmlspecialchars($adminUser->first_name . ' ' . $adminUser->last_name); ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($adminUser->email); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $adminUser->role === 'superadmin' ? 'danger' : 'primary'; ?>">
                                                        <?php echo ucfirst($adminUser->role); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $adminUser->status === 'active' ? 'success' : 'secondary'; ?>">
                                                        <?php echo ucfirst($adminUser->status); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($adminUser->created_at)); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="editAdmin(<?php echo $adminUser->id; ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <?php if ($adminUser->id !== $user['id']): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteAdmin(<?php echo $adminUser->id; ?>, '<?php echo htmlspecialchars($adminUser->username); ?>')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Create Admin Modal -->
    <div class="modal fade" id="createAdminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Create New Admin User
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createAdminForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                        <input type="hidden" name="action" value="create_admin">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Username *</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Password *</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Role</label>
                                <select class="form-select" name="role">
                                    <option value="admin">Admin</option>
                                    <option value="superadmin">Super Admin</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="createAdminForm" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Create Admin
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Admin Modal -->
    <div class="modal fade" id="editAdminModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>Edit Admin User
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editAdminForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                        <input type="hidden" name="action" value="update_admin">
                        <input type="hidden" name="user_id" id="edit_user_id">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" id="edit_username" readonly>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" id="edit_email" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name" id="edit_first_name">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name" id="edit_last_name">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">New Password (leave blank to keep current)</label>
                                <input type="password" class="form-control" name="password" id="edit_password">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Role</label>
                                <select class="form-select" name="role" id="edit_role">
                                    <option value="admin">Admin</option>
                                    <option value="superadmin">Super Admin</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" name="status" id="edit_status">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="editAdminForm" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Admin
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Admin users data for JavaScript
        const adminUsers = <?php echo json_encode($adminUsers); ?>;

        // Edit admin function
        function editAdmin(userId) {
            const admin = adminUsers.find(u => u.id == userId);
            if (!admin) return;

            document.getElementById('edit_user_id').value = admin.id;
            document.getElementById('edit_username').value = admin.username;
            document.getElementById('edit_email').value = admin.email;
            document.getElementById('edit_first_name').value = admin.first_name || '';
            document.getElementById('edit_last_name').value = admin.last_name || '';
            document.getElementById('edit_role').value = admin.role;
            document.getElementById('edit_status').value = admin.status;
            document.getElementById('edit_password').value = '';

            const modal = new bootstrap.Modal(document.getElementById('editAdminModal'));
            modal.show();
        }

        // Delete admin function
        function deleteAdmin(userId, username) {
            if (confirm(`Are you sure you want to delete admin user "${username}"? This action cannot be undone.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                    <input type="hidden" name="action" value="delete_admin">
                    <input type="hidden" name="user_id" value="${userId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Add avatar circle styling
        document.addEventListener('DOMContentLoaded', function() {
            const style = document.createElement('style');
            style.textContent = `
                .avatar-circle {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    background: var(--primary-color);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 16px;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
