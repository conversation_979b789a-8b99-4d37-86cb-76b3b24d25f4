<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== COINAGE TRADING PLATFORM - COMPLETE TEST ===\n\n";

// 1. Database Test
echo "1. DATABASE CONNECTION TEST\n";
echo "================================\n";

try {
    $pdo = new PDO("mysql:host=localhost;port=3306;dbname=coinage", "root", "root");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Database connection successful!\n";
    
    // Show tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables found: " . count($tables) . "\n";
    
    if (!empty($tables)) {
        foreach ($tables as $table) {
            echo "  - $table\n";
        }
    }
    
    // Get MySQL version
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "MySQL Version: " . $version['version'] . "\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}

echo "\n";

// 2. Vendor/Composer Test
echo "2. COMPOSER/VENDOR TEST\n";
echo "========================\n";

if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    echo "✓ Vendor directory found!\n";
    require_once __DIR__ . '/../vendor/autoload.php';
    
    if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
        echo "✓ PHPMailer class loaded successfully!\n";
    } else {
        echo "✗ PHPMailer class not found!\n";
    }
} else {
    echo "✗ Vendor directory not found!\n";
    echo "Run: composer require phpmailer/phpmailer\n";
}

echo "\n";

// 3. SMTP Test (if PHPMailer is available)
echo "3. SMTP CONNECTION TEST\n";
echo "========================\n";

if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
    
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // SMTP settings
        $mail->isSMTP();
        $mail->Host = 'smtp.hostinger.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'Money2025@Demo#';
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        $mail->Port = 465;
        
        echo "Testing SMTP connection...\n";
        
        // Test connection only
        if ($mail->smtpConnect()) {
            echo "✓ SMTP connection successful!\n";
            $mail->smtpClose();
            
            echo "Attempting to send test email...\n";
            
            // Configure email
            $mail->setFrom('<EMAIL>', 'Coinage Trading');
            $mail->addAddress('<EMAIL>');
            $mail->isHTML(true);
            $mail->Subject = 'Coinage Trading - SMTP Test Success';
            $mail->Body = '<h2>SMTP Test Successful!</h2><p>Your Coinage Trading platform SMTP is working correctly.</p><p>Sent at: ' . date('Y-m-d H:i:s') . '</p>';
            
            if ($mail->send()) {
                echo "✓ Test email sent <NAME_EMAIL>!\n";
                echo "📧 Check your inbox (and spam folder)\n";
            } else {
                echo "✗ Failed to send email: " . $mail->ErrorInfo . "\n";
            }
            
        } else {
            echo "✗ SMTP connection failed!\n";
        }
        
    } catch (PHPMailer\PHPMailer\Exception $e) {
        echo "✗ SMTP Error: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "✗ PHPMailer not available - skipping SMTP test\n";
}

echo "\n";

// 4. File Structure Test
echo "4. FILE STRUCTURE TEST\n";
echo "=======================\n";

$required_files = [
    'config.php',
    'includes/db_connect.php',
    'includes/functions.php',
    'includes/email_functions.php',
    'vendor/autoload.php'
];

foreach ($required_files as $file) {
    $path = __DIR__ . '/../' . $file;
    if (file_exists($path)) {
        echo "✓ $file\n";
    } else {
        echo "✗ $file (missing)\n";
    }
}

echo "\n=== TEST COMPLETED ===\n";
echo "Next steps:\n";
echo "1. Create database schema\n";
echo "2. Set up layout system\n";
echo "3. Build authentication\n";
?>