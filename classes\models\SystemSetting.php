<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * SystemSetting Model - Handles system configuration data and operations
 */
class SystemSetting extends BaseModel {
    protected $table = 'system_settings';
    protected $fillable = [
        'setting_key', 'setting_value', 'setting_type', 'description', 
        'category', 'is_public', 'updated_by'
    ];
    
    // Setting types
    const TYPE_STRING = 'string';
    const TYPE_NUMBER = 'number';
    const TYPE_BOOLEAN = 'boolean';
    const TYPE_JSON = 'json';
    
    // Setting categories
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_FINANCIAL = 'financial';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_NOTIFICATIONS = 'notifications';
    const CATEGORY_APPEARANCE = 'appearance';
    const CATEGORY_CONTACT = 'contact';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // Setting key validation
        if (empty($this->setting_key)) {
            $errors['setting_key'] = 'Setting key is required';
        } elseif (strlen($this->setting_key) > 100) {
            $errors['setting_key'] = 'Setting key must not exceed 100 characters';
        } elseif (!preg_match('/^[a-z0-9_]+$/', $this->setting_key)) {
            $errors['setting_key'] = 'Setting key can only contain lowercase letters, numbers, and underscores';
        } elseif ($this->isSettingKeyTaken()) {
            $errors['setting_key'] = 'Setting key already exists';
        }
        
        // Setting type validation
        if (!empty($this->setting_type) && !in_array($this->setting_type, [
            self::TYPE_STRING, self::TYPE_NUMBER, self::TYPE_BOOLEAN, self::TYPE_JSON
        ])) {
            $errors['setting_type'] = 'Invalid setting type';
        }
        
        // Category validation
        if (!empty($this->category) && !in_array($this->category, [
            self::CATEGORY_GENERAL, self::CATEGORY_FINANCIAL, self::CATEGORY_SECURITY,
            self::CATEGORY_SYSTEM, self::CATEGORY_NOTIFICATIONS, self::CATEGORY_APPEARANCE,
            self::CATEGORY_CONTACT
        ])) {
            $errors['category'] = 'Invalid category';
        }
        
        // Value validation based on type
        if (!empty($this->setting_type) && !empty($this->setting_value)) {
            switch ($this->setting_type) {
                case self::TYPE_NUMBER:
                    if (!is_numeric($this->setting_value)) {
                        $errors['setting_value'] = 'Setting value must be numeric';
                    }
                    break;
                    
                case self::TYPE_BOOLEAN:
                    if (!in_array(strtolower($this->setting_value), ['true', 'false', '1', '0'])) {
                        $errors['setting_value'] = 'Setting value must be true or false';
                    }
                    break;
                    
                case self::TYPE_JSON:
                    if (json_decode($this->setting_value) === null && json_last_error() !== JSON_ERROR_NONE) {
                        $errors['setting_value'] = 'Setting value must be valid JSON';
                    }
                    break;
            }
        }
        
        return $errors;
    }
    
    /**
     * Check if setting key is already taken
     */
    private function isSettingKeyTaken() {
        $sql = "SELECT id FROM {$this->table} WHERE setting_key = :setting_key";
        if ($this->exists) {
            $sql .= " AND id != :id";
        }
        
        $stmt = $this->db->prepare($sql);
        $params = ['setting_key' => $this->setting_key];
        if ($this->exists) {
            $params['id'] = $this->getId();
        }
        
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Override save to set default values
     */
    public function save() {
        // Set default type if not provided
        if (!isset($this->setting_type)) {
            $this->setting_type = self::TYPE_STRING;
        }
        
        // Set default category if not provided
        if (!isset($this->category)) {
            $this->category = self::CATEGORY_GENERAL;
        }
        
        // Set default is_public if not provided
        if (!isset($this->is_public)) {
            $this->is_public = false;
        }
        
        return parent::save();
    }
    
    /**
     * Get typed value based on setting type
     */
    public function getTypedValue() {
        if (empty($this->setting_value)) {
            return null;
        }
        
        switch ($this->setting_type) {
            case self::TYPE_NUMBER:
                return is_numeric($this->setting_value) ? (float) $this->setting_value : 0;
                
            case self::TYPE_BOOLEAN:
                return in_array(strtolower($this->setting_value), ['true', '1']);
                
            case self::TYPE_JSON:
                return json_decode($this->setting_value, true);
                
            case self::TYPE_STRING:
            default:
                return $this->setting_value;
        }
    }
    
    /**
     * Set typed value
     */
    public function setTypedValue($value) {
        switch ($this->setting_type) {
            case self::TYPE_BOOLEAN:
                $this->setting_value = $value ? 'true' : 'false';
                break;
                
            case self::TYPE_JSON:
                $this->setting_value = json_encode($value);
                break;
                
            case self::TYPE_NUMBER:
            case self::TYPE_STRING:
            default:
                $this->setting_value = (string) $value;
                break;
        }
    }
    
    /**
     * Get category display name
     */
    public function getCategoryDisplayName() {
        $categories = [
            self::CATEGORY_GENERAL => 'General',
            self::CATEGORY_FINANCIAL => 'Financial',
            self::CATEGORY_SECURITY => 'Security',
            self::CATEGORY_SYSTEM => 'System',
            self::CATEGORY_NOTIFICATIONS => 'Notifications',
            self::CATEGORY_APPEARANCE => 'Appearance',
            self::CATEGORY_CONTACT => 'Contact'
        ];
        
        return $categories[$this->category] ?? $this->category;
    }
    
    /**
     * Get type display name
     */
    public function getTypeDisplayName() {
        $types = [
            self::TYPE_STRING => 'Text',
            self::TYPE_NUMBER => 'Number',
            self::TYPE_BOOLEAN => 'Yes/No',
            self::TYPE_JSON => 'JSON'
        ];
        
        return $types[$this->setting_type] ?? $this->setting_type;
    }
    
    /**
     * Get setting by key
     */
    public static function getByKey($key) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE setting_key = :key LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['key' => $key]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Get setting value by key
     */
    public static function getValue($key, $default = null) {
        $setting = static::getByKey($key);
        
        if ($setting) {
            return $setting->getTypedValue();
        }
        
        return $default;
    }
    
    /**
     * Set setting value by key
     */
    public static function setValue($key, $value, $type = null, $updatedBy = null) {
        $setting = static::getByKey($key);
        
        if (!$setting) {
            $setting = new static();
            $setting->setting_key = $key;
            $setting->setting_type = $type ?? self::TYPE_STRING;
        }
        
        $setting->setTypedValue($value);
        
        if ($updatedBy) {
            $setting->updated_by = $updatedBy;
        }
        
        return $setting->save();
    }
    
    /**
     * Get settings by category
     */
    public static function getByCategory($category) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE category = :category ORDER BY setting_key ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['category' => $category]);
        $results = $stmt->fetchAll();
        
        $settings = [];
        foreach ($results as $data) {
            $settings[] = new static($data);
        }
        
        return $settings;
    }
    
    /**
     * Get public settings
     */
    public static function getPublicSettings() {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE is_public = 1 ORDER BY category ASC, setting_key ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        $settings = [];
        foreach ($results as $data) {
            $setting = new static($data);
            $settings[$setting->setting_key] = $setting->getTypedValue();
        }
        
        return $settings;
    }
    
    /**
     * Get all settings as key-value pairs
     */
    public static function getAllAsArray() {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} ORDER BY category ASC, setting_key ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        $settings = [];
        foreach ($results as $data) {
            $setting = new static($data);
            $settings[$setting->setting_key] = $setting->getTypedValue();
        }
        
        return $settings;
    }
    
    /**
     * Get settings grouped by category
     */
    public static function getGroupedByCategory() {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} ORDER BY category ASC, setting_key ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();
        
        $grouped = [];
        foreach ($results as $data) {
            $setting = new static($data);
            $grouped[$setting->category][] = $setting;
        }
        
        return $grouped;
    }
    
    /**
     * Update multiple settings at once
     */
    public static function updateMultiple($settings, $updatedBy = null) {
        $instance = new static();
        $instance->beginTransaction();
        
        try {
            foreach ($settings as $key => $value) {
                if (!static::setValue($key, $value, null, $updatedBy)) {
                    throw new Exception("Failed to update setting: {$key}");
                }
            }
            
            $instance->commit();
            return true;
            
        } catch (Exception $e) {
            $instance->rollback();
            error_log("Settings update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Initialize default settings
     */
    public static function initializeDefaults() {
        $defaults = [
            // General settings
            'site_name' => ['value' => 'Coinage Trading', 'type' => 'string', 'category' => 'general', 'public' => true],
            'site_currency' => ['value' => 'USD', 'type' => 'string', 'category' => 'general', 'public' => true],
            'currency_symbol' => ['value' => '$', 'type' => 'string', 'category' => 'general', 'public' => true],
            'base_url' => ['value' => 'http://localhost/coinage/', 'type' => 'string', 'category' => 'general', 'public' => true],
            
            // Financial settings
            'registration_bonus' => ['value' => '0', 'type' => 'number', 'category' => 'financial', 'public' => false],
            'deposit_bonus_percent' => ['value' => '10', 'type' => 'number', 'category' => 'financial', 'public' => false],
            'min_withdrawal_amount' => ['value' => '50', 'type' => 'number', 'category' => 'financial', 'public' => false],
            'max_withdrawal_amount' => ['value' => '10000', 'type' => 'number', 'category' => 'financial', 'public' => false],
            'withdrawal_fee_percent' => ['value' => '2', 'type' => 'number', 'category' => 'financial', 'public' => false],
            
            // Security settings
            'email_verification_required' => ['value' => 'true', 'type' => 'boolean', 'category' => 'security', 'public' => false],
            'two_fa_enforcement' => ['value' => 'false', 'type' => 'boolean', 'category' => 'security', 'public' => false],
            'kyc_verification_required' => ['value' => 'false', 'type' => 'boolean', 'category' => 'security', 'public' => false],
            'max_login_attempts' => ['value' => '5', 'type' => 'number', 'category' => 'security', 'public' => false],
            'account_lockout_minutes' => ['value' => '30', 'type' => 'number', 'category' => 'security', 'public' => false],
            
            // System settings
            'maintenance_mode' => ['value' => 'false', 'type' => 'boolean', 'category' => 'system', 'public' => false],
            'debug_mode' => ['value' => 'false', 'type' => 'boolean', 'category' => 'system', 'public' => false],
            'session_timeout_minutes' => ['value' => '60', 'type' => 'number', 'category' => 'system', 'public' => false],
            
            // Notification settings
            'email_notifications' => ['value' => 'true', 'type' => 'boolean', 'category' => 'notifications', 'public' => false],
            'sms_notifications' => ['value' => 'false', 'type' => 'boolean', 'category' => 'notifications', 'public' => false],
            
            // Appearance settings
            'primary_color' => ['value' => '#007bff', 'type' => 'string', 'category' => 'appearance', 'public' => true],
            'secondary_color' => ['value' => '#6c757d', 'type' => 'string', 'category' => 'appearance', 'public' => true],
            'logo_url' => ['value' => '', 'type' => 'string', 'category' => 'appearance', 'public' => true],
            'favicon_url' => ['value' => '', 'type' => 'string', 'category' => 'appearance', 'public' => true],
            
            // Contact settings
            'contact_email' => ['value' => '<EMAIL>', 'type' => 'string', 'category' => 'contact', 'public' => true],
            'contact_phone' => ['value' => '******-0123', 'type' => 'string', 'category' => 'contact', 'public' => true],
            'company_address' => ['value' => '123 Trading Street, Finance City, FC 12345', 'type' => 'string', 'category' => 'contact', 'public' => true]
        ];
        
        $instance = new static();
        $instance->beginTransaction();
        
        try {
            foreach ($defaults as $key => $config) {
                // Check if setting already exists
                if (!static::getByKey($key)) {
                    $setting = new static();
                    $setting->setting_key = $key;
                    $setting->setting_value = $config['value'];
                    $setting->setting_type = $config['type'];
                    $setting->category = $config['category'];
                    $setting->is_public = $config['public'];
                    
                    if (!$setting->save()) {
                        throw new Exception("Failed to create default setting: {$key}");
                    }
                }
            }
            
            $instance->commit();
            return true;
            
        } catch (Exception $e) {
            $instance->rollback();
            error_log("Default settings initialization error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get settings statistics
     */
    public static function getStatistics() {
        $instance = new static();
        
        $sql = "SELECT 
                    COUNT(*) as total_settings,
                    SUM(CASE WHEN is_public = 1 THEN 1 ELSE 0 END) as public_settings,
                    COUNT(DISTINCT category) as categories_count,
                    COUNT(DISTINCT setting_type) as types_count
                FROM {$instance->table}";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    /**
     * Search settings
     */
    public static function search($query, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE setting_key LIKE :query OR description LIKE :query 
                ORDER BY category ASC, setting_key ASC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['query' => '%' . $query . '%']);
        $results = $stmt->fetchAll();
        
        $settings = [];
        foreach ($results as $data) {
            $settings[] = new static($data);
        }
        
        return $settings;
    }
    
    /**
     * Reset all settings to default values
     */
    public static function resetToDefaults($defaultSettings, $updatedBy = null) {
        $instance = new static();
        $instance->beginTransaction();
        
        try {
            foreach ($defaultSettings as $key => $value) {
                $setting = static::getByKey($key);
                
                if ($setting) {
                    // Update existing setting
                    $setting->setTypedValue($value);
                    if ($updatedBy) {
                        $setting->updated_by = $updatedBy;
                    }
                    
                    if (!$setting->save()) {
                        throw new Exception("Failed to reset setting: {$key}");
                    }
                } else {
                    // Create new setting with default value
                    $newSetting = new static();
                    $newSetting->setting_key = $key;
                    $newSetting->setTypedValue($value);
                    $newSetting->setting_type = self::TYPE_STRING; // Default type
                    $newSetting->category = self::CATEGORY_GENERAL; // Default category
                    
                    if ($updatedBy) {
                        $newSetting->updated_by = $updatedBy;
                    }
                    
                    if (!$newSetting->save()) {
                        throw new Exception("Failed to create default setting: {$key}");
                    }
                }
            }
            
            $instance->commit();
            return true;
            
        } catch (Exception $e) {
            $instance->rollback();
            error_log("Reset to defaults error: " . $e->getMessage());
            return false;
        }
    }
}
?>