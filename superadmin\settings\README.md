# Super Admin System Settings

This module provides comprehensive system configuration management for the Coinage Trading platform.

## Features

### 1. Settings Categories
- **General**: Site name, currency, base URL, contact information
- **Financial**: Bonuses, withdrawal limits, fees
- **Security**: Authentication requirements, login security
- **Appearance**: Colors, logo, branding
- **Email & SMTP**: Email configuration and notifications
- **System**: Maintenance mode, debug settings, session management

### 2. User Interface
- Tabbed interface for organized settings management
- Real-time color preview for appearance settings
- Form validation with immediate feedback
- AJAX form submissions for better user experience
- Responsive design for mobile compatibility

### 3. API Endpoints
- `test-smtp.php` - Test SMTP configuration
- `export.php` - Export all settings as JSON
- `reset-defaults.php` - Reset all settings to default values
- `clear-cache.php` - Clear system cache and temporary files
- `diagnostics.php` - Run system health diagnostics

## File Structure

```
superadmin/settings/
├── index.php                          # Main settings page
├── api/                              # API endpoints
│   ├── test-smtp.php                 # SMTP testing
│   ├── export.php                    # Settings export
│   ├── reset-defaults.php            # Reset to defaults
│   ├── clear-cache.php               # Cache management
│   └── diagnostics.php               # System diagnostics
└── README.md                         # This file

classes/views/
└── SuperAdminSystemSettingsView.php  # View class for rendering

assets/
├── css/
│   └── superadmin-system-settings.css # Styles
└── js/
    └── superadmin-system-settings.js  # JavaScript functionality
```

## Usage

### Accessing Settings
1. Login as a super admin user
2. Navigate to `/superadmin/settings/`
3. Use the tabbed interface to manage different setting categories

### Setting Types
- **String**: Text values (site name, URLs, etc.)
- **Number**: Numeric values (amounts, percentages, etc.)
- **Boolean**: True/false values (feature toggles, etc.)
- **JSON**: Complex data structures

### Security Features
- CSRF protection on all forms
- Role-based access control (super admin only)
- Input validation and sanitization
- Audit logging for all changes

## API Usage

### Test SMTP Configuration
```javascript
fetch('/superadmin/settings/api/test-smtp.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

### Export Settings
```javascript
fetch('/superadmin/settings/api/export.php')
.then(response => response.blob())
.then(blob => {
    // Handle file download
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'system-settings.json';
    a.click();
});
```

### Reset to Defaults
```javascript
fetch('/superadmin/settings/api/reset-defaults.php', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

## Database Schema

The system settings are stored in the `system_settings` table:

```sql
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    category VARCHAR(50) DEFAULT 'general',
    is_public BOOLEAN DEFAULT FALSE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);
```

## Default Settings

The system initializes with the following default settings:

### General
- `site_name`: "Coinage Trading"
- `site_currency`: "USD"
- `currency_symbol`: "$"
- `base_url`: ""
- `contact_email`: ""
- `contact_phone`: ""
- `company_address`: ""

### Financial
- `registration_bonus`: 0
- `deposit_bonus_percent`: 10
- `min_withdrawal_amount`: 50
- `max_withdrawal_amount`: 10000
- `withdrawal_fee_percent`: 2

### Security
- `email_verification_required`: true
- `two_fa_enforcement`: false
- `kyc_verification_required`: false
- `max_login_attempts`: 5
- `account_lockout_minutes`: 30

### System
- `maintenance_mode`: false
- `debug_mode`: false
- `session_timeout_minutes`: 60

### Notifications
- `email_notifications`: true
- `sms_notifications`: false

### Appearance
- `primary_color`: "#007bff"
- `secondary_color`: "#6c757d"
- `logo_url`: ""

## Testing

Run the test suite to verify functionality:

```bash
php test_folder/test_superadmin_settings.php
```

The test covers:
- Default settings initialization
- Settings retrieval by category
- Multiple settings updates
- Public settings access
- Search functionality
- Statistics generation
- Typed value handling
- Reset to defaults

## Customization

### Adding New Settings
1. Add the setting to the default settings array in `SystemSetting::initializeDefaults()`
2. Update the view class to include the new setting in the appropriate tab
3. Add validation rules if needed
4. Update the documentation

### Adding New Categories
1. Define the category constant in `SystemSetting` class
2. Add the category to the display names array
3. Create a new tab in the view class
4. Update the navigation tabs

### Custom Validation
Add validation rules in the `SystemSetting::validate()` method or create custom validators for specific setting types.

## Troubleshooting

### Common Issues
1. **CSRF Token Errors**: Ensure the token is properly included in forms
2. **Permission Denied**: Verify user has super admin role
3. **Database Errors**: Check database connection and table structure
4. **File Upload Issues**: Verify upload directory permissions

### Debug Mode
Enable debug mode in system settings to see detailed error messages and logging information.

### Logs
Check the application logs for detailed error information:
- PHP error logs
- Application audit logs
- Database query logs

## Security Considerations

1. **Access Control**: Only super admin users can access these settings
2. **Input Validation**: All inputs are validated and sanitized
3. **CSRF Protection**: All forms include CSRF tokens
4. **Audit Logging**: All changes are logged for security auditing
5. **File Uploads**: Logo uploads are restricted by type and size
6. **SQL Injection**: All database queries use prepared statements

## Performance

- Settings are cached in memory during request lifecycle
- Database queries are optimized with proper indexing
- AJAX requests reduce page reloads
- Minimal JavaScript footprint for fast loading

## Browser Compatibility

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design for mobile devices
- Progressive enhancement for older browsers
- Graceful degradation when JavaScript is disabled