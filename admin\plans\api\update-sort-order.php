<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $planIds = $input['plan_ids'] ?? [];
    
    if (empty($planIds) || !is_array($planIds)) {
        echo json_encode(['success' => false, 'message' => 'Plan IDs are required']);
        exit;
    }
    
    // Validate that all IDs are integers
    $planIds = array_map('intval', $planIds);
    $planIds = array_filter($planIds, function($id) { return $id > 0; });
    
    if (empty($planIds)) {
        echo json_encode(['success' => false, 'message' => 'Valid plan IDs are required']);
        exit;
    }
    
    // Update sort orders
    if (TradingPlan::updateSortOrders($planIds)) {
        // Log the action
        AuditTrailService::log(
            'trading_plans_reordered',
            'trading_plan',
            'multiple',
            [
                'new_order' => $planIds,
                'total_plans' => count($planIds)
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Plan order updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update plan order'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Update trading plan sort order error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating plan order'
    ]);
}
?>