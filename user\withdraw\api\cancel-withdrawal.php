<?php
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/TransactionManager.php';
require_once '../../../classes/models/Withdrawal.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit;
}

// CSRF protection
if (!hash_equals($_SESSION['csrf_token'] ?? '', $input['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Invalid CSRF token']);
    exit;
}

// Get withdrawal ID
$withdrawalId = (int)($input['withdrawal_id'] ?? 0);

if (!$withdrawalId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Withdrawal ID is required']);
    exit;
}

try {
    // Get withdrawal
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Withdrawal not found']);
        exit;
    }
    
    // Check if withdrawal belongs to current user
    if ($withdrawal->user_id != $user->getId()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
    
    // Check if withdrawal can be cancelled
    if (!$withdrawal->isPending()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Only pending withdrawals can be cancelled']);
        exit;
    }
    
    // Begin transaction
    $withdrawal->beginTransaction();
    
    try {
        // Update withdrawal status to cancelled
        $withdrawal->status = Withdrawal::STATUS_CANCELLED;
        $withdrawal->admin_note = 'Cancelled by user';
        $withdrawal->processed_by = $user->getId(); // User cancelled it themselves
        $withdrawal->processed_at = date('Y-m-d H:i:s');
        
        if (!$withdrawal->save()) {
            throw new Exception('Failed to update withdrawal status');
        }
        
        // Record transaction for audit trail
        $result = TransactionManager::recordTransaction([
            'user_id' => $user->getId(),
            'type' => Transaction::TYPE_ADMIN_DEBIT, // Use admin debit type for cancellation
            'amount' => 0, // No actual balance change since it was never processed
            'description' => "Withdrawal request cancelled - Request ID: {$withdrawal->getId()}",
            'reference_id' => $withdrawal->getId(),
            'reference_type' => Transaction::REF_TYPE_WITHDRAWAL,
            'processed_by' => $user->getId(),
            'status' => Transaction::STATUS_CANCELLED
        ]);
        
        if (!$result['success']) {
            throw new Exception('Failed to record cancellation transaction');
        }
        
        $withdrawal->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal request cancelled successfully'
        ]);
        
    } catch (Exception $e) {
        $withdrawal->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Withdrawal cancellation error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>