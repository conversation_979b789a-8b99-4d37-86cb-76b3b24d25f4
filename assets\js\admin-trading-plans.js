/**
 * Admin Trading Plans Management JavaScript
 */

class AdminTradingPlans {
    constructor() {
        this.currentFilter = 'all';
        this.sortable = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.initSortable();
        this.updateCalculatedValues();
    }
    
    bindEvents() {
        // Form submission
        document.getElementById('planForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });
        
        // Edit plan buttons
        document.querySelectorAll('.edit-plan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const planId = e.currentTarget.dataset.planId;
                this.editPlan(planId);
            });
        });
        
        // Toggle status buttons
        document.querySelectorAll('.toggle-status-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const planId = e.currentTarget.dataset.planId;
                const currentStatus = e.currentTarget.dataset.currentStatus;
                this.togglePlanStatus(planId, currentStatus);
            });
        });
        
        // Delete plan buttons
        document.querySelectorAll('.delete-plan-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const planId = e.currentTarget.dataset.planId;
                const planName = e.currentTarget.dataset.planName;
                this.showDeleteConfirmation(planId, planName);
            });
        });
        
        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.deletePlan();
        });
        
        // Filter options
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const filter = e.currentTarget.dataset.filter;
                this.applyFilter(filter);
            });
        });
        
        // Sort plans button
        document.getElementById('sortPlansBtn').addEventListener('click', () => {
            this.toggleSortMode();
        });
        
        // Feature management
        document.getElementById('addFeatureBtn').addEventListener('click', () => {
            this.addFeatureField();
        });
        
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-feature-btn')) {
                this.removeFeatureField(e.target);
            }
        });
        
        // Real-time calculation updates
        ['dailyReturn', 'duration'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => {
                    this.updateCalculatedValues();
                });
            }
        });
        
        // Modal events
        document.getElementById('createPlanModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm();
        });
        
        // Form validation
        document.querySelectorAll('#planForm input, #planForm textarea, #planForm select').forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
            
            field.addEventListener('input', () => {
                this.clearFieldError(field);
            });
        });
    }
    
    initSortable() {
        const tbody = document.getElementById('sortablePlans');
        if (tbody) {
            this.sortable = Sortable.create(tbody, {
                handle: '.sort-handle',
                disabled: true,
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onEnd: (evt) => {
                    this.updateSortOrder();
                }
            });
        }
    }
    
    toggleSortMode() {
        const btn = document.getElementById('sortPlansBtn');
        const isEnabled = !this.sortable.option('disabled');
        
        this.sortable.option('disabled', isEnabled);
        
        if (isEnabled) {
            btn.innerHTML = '<i class="fas fa-sort"></i> Reorder Plans';
            btn.classList.remove('btn-warning');
            btn.classList.add('btn-outline-secondary');
            this.showNotification('Sort mode disabled', 'info');
        } else {
            btn.innerHTML = '<i class="fas fa-save"></i> Save Order';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-warning');
            this.showNotification('Drag plans to reorder them', 'info');
        }
    }
    
    updateSortOrder() {
        const rows = document.querySelectorAll('#sortablePlans tr');
        const planIds = Array.from(rows).map(row => row.dataset.planId);
        
        fetch('api/update-sort-order.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ plan_ids: planIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Plan order updated successfully', 'success');
            } else {
                this.showNotification(data.message || 'Failed to update plan order', 'error');
                location.reload(); // Reload to restore original order
            }
        })
        .catch(error => {
            console.error('Error updating sort order:', error);
            this.showNotification('Failed to update plan order', 'error');
            location.reload();
        });
    }
    
    handleFormSubmit() {
        const form = document.getElementById('planForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        const btnText = submitBtn.querySelector('.btn-text');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        // Collect features
        const features = this.collectFeatures();
        formData.append('features', JSON.stringify(features));
        
        // Determine if this is create or update
        const planId = document.getElementById('planId').value;
        const url = planId ? 'api/update.php' : 'api/create.php';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(
                    planId ? 'Plan updated successfully' : 'Plan created successfully', 
                    'success'
                );
                
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('createPlanModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.handleFormErrors(data.errors || {});
                this.showNotification(data.message || 'Please correct the errors below', 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }
    
    editPlan(planId) {
        fetch(`api/details.php?id=${planId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.populateForm(data.plan);
                
                // Update modal title
                document.querySelector('#createPlanModal .modal-title').textContent = 'Edit Trading Plan';
                document.querySelector('#planForm .btn-text').textContent = 'Update Plan';
                
                // Show modal
                new bootstrap.Modal(document.getElementById('createPlanModal')).show();
            } else {
                this.showNotification(data.message || 'Failed to load plan details', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading plan:', error);
            this.showNotification('Failed to load plan details', 'error');
        });
    }
    
    populateForm(plan) {
        document.getElementById('planId').value = plan.id;
        document.getElementById('planName').value = plan.name;
        document.getElementById('planStatus').value = plan.status;
        document.getElementById('minDeposit').value = plan.min_deposit;
        document.getElementById('maxDeposit').value = plan.max_deposit || '';
        document.getElementById('dailyReturn').value = plan.daily_return;
        document.getElementById('duration').value = plan.duration_days;
        document.getElementById('description').value = plan.description || '';
        
        // Populate features
        this.populateFeatures(plan.features);
        
        // Update calculated values
        this.updateCalculatedValues();
    }
    
    populateFeatures(features) {
        const container = document.getElementById('featuresContainer');
        container.innerHTML = '';
        
        if (features && features.length > 0) {
            features.forEach(feature => {
                this.addFeatureField(feature);
            });
        } else {
            this.addFeatureField();
        }
    }
    
    addFeatureField(value = '') {
        const container = document.getElementById('featuresContainer');
        const featureItem = document.createElement('div');
        featureItem.className = 'feature-item mb-2';
        
        featureItem.innerHTML = `
            <div class="input-group">
                <input type="text" class="form-control feature-input" placeholder="Enter feature" value="${value}">
                <button type="button" class="btn btn-outline-danger remove-feature-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        container.appendChild(featureItem);
    }
    
    removeFeatureField(button) {
        const featureItem = button.closest('.feature-item');
        featureItem.remove();
        
        // Ensure at least one feature field exists
        const container = document.getElementById('featuresContainer');
        if (container.children.length === 0) {
            this.addFeatureField();
        }
    }
    
    collectFeatures() {
        const inputs = document.querySelectorAll('.feature-input');
        const features = [];
        
        inputs.forEach(input => {
            const value = input.value.trim();
            if (value) {
                features.push(value);
            }
        });
        
        return features;
    }
    
    updateCalculatedValues() {
        const dailyReturn = parseFloat(document.getElementById('dailyReturn').value) || 0;
        const duration = parseInt(document.getElementById('duration').value) || 0;
        
        const totalReturn = dailyReturn * duration;
        const exampleProfit = (1000 * totalReturn) / 100;
        
        document.getElementById('totalReturnDisplay').textContent = totalReturn.toFixed(2) + '%';
        document.getElementById('exampleProfitDisplay').textContent = exampleProfit.toFixed(2);
    }
    
    togglePlanStatus(planId, currentStatus) {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        
        fetch('api/toggle-status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ 
                plan_id: planId, 
                status: newStatus 
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(`Plan ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`, 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification(data.message || 'Failed to update plan status', 'error');
            }
        })
        .catch(error => {
            console.error('Error toggling status:', error);
            this.showNotification('Failed to update plan status', 'error');
        });
    }
    
    showDeleteConfirmation(planId, planName) {
        document.getElementById('deletePlanName').textContent = planName;
        document.getElementById('confirmDeleteBtn').dataset.planId = planId;
        
        new bootstrap.Modal(document.getElementById('deletePlanModal')).show();
    }
    
    deletePlan() {
        const planId = document.getElementById('confirmDeleteBtn').dataset.planId;
        const btn = document.getElementById('confirmDeleteBtn');
        const spinner = btn.querySelector('.spinner-border');
        
        // Show loading state
        spinner.classList.remove('d-none');
        btn.disabled = true;
        
        fetch('api/delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ plan_id: planId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Plan deleted successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deletePlanModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification(data.message || 'Failed to delete plan', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting plan:', error);
            this.showNotification('Failed to delete plan', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            btn.disabled = false;
        });
    }
    
    applyFilter(filter) {
        this.currentFilter = filter;
        const rows = document.querySelectorAll('#sortablePlans tr');
        
        // Update active filter button
        document.querySelectorAll('.filter-option').forEach(option => {
            option.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        // Filter rows
        rows.forEach(row => {
            const status = row.dataset.status;
            const shouldShow = filter === 'all' || status === filter;
            
            row.style.display = shouldShow ? '' : 'none';
        });
        
        // Update visible count
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
        this.updateFilterInfo(visibleRows.length, rows.length);
    }
    
    updateFilterInfo(visible, total) {
        // You can add a filter info display here if needed
        console.log(`Showing ${visible} of ${total} plans`);
    }
    
    validateField(field) {
        const value = field.value.trim();
        const name = field.name;
        let error = '';
        
        switch (name) {
            case 'name':
                if (!value) error = 'Plan name is required';
                else if (value.length > 100) error = 'Plan name must not exceed 100 characters';
                break;
                
            case 'min_deposit':
                if (!value) error = 'Minimum deposit is required';
                else if (isNaN(value) || parseFloat(value) <= 0) error = 'Minimum deposit must be greater than zero';
                break;
                
            case 'max_deposit':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) {
                    error = 'Maximum deposit must be greater than zero';
                } else if (value) {
                    const minDeposit = parseFloat(document.getElementById('minDeposit').value);
                    if (parseFloat(value) < minDeposit) {
                        error = 'Maximum deposit must be greater than minimum deposit';
                    }
                }
                break;
                
            case 'daily_return':
                if (!value) error = 'Daily return is required';
                else if (isNaN(value) || parseFloat(value) <= 0) error = 'Daily return must be greater than zero';
                else if (parseFloat(value) > 100) error = 'Daily return cannot exceed 100%';
                break;
                
            case 'duration_days':
                if (!value) error = 'Duration is required';
                else if (isNaN(value) || parseInt(value) <= 0) error = 'Duration must be greater than zero';
                else if (parseInt(value) > 3650) error = 'Duration cannot exceed 3650 days';
                break;
        }
        
        this.setFieldError(field, error);
        return !error;
    }
    
    setFieldError(field, error) {
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        
        if (error) {
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = error;
        } else {
            field.classList.remove('is-invalid');
            if (feedback) feedback.textContent = '';
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';
    }
    
    handleFormErrors(errors) {
        // Clear all previous errors
        document.querySelectorAll('#planForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Set new errors
        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.setFieldError(field, errors[fieldName]);
            }
        });
    }
    
    resetForm() {
        const form = document.getElementById('planForm');
        form.reset();
        
        // Clear errors
        document.querySelectorAll('#planForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Reset modal title and button text
        document.querySelector('#createPlanModal .modal-title').textContent = 'Create New Trading Plan';
        document.querySelector('#planForm .btn-text').textContent = 'Create Plan';
        
        // Clear plan ID
        document.getElementById('planId').value = '';
        
        // Reset features
        document.getElementById('featuresContainer').innerHTML = '';
        this.addFeatureField();
        
        // Reset calculated values
        document.getElementById('totalReturnDisplay').textContent = '0.00%';
        document.getElementById('exampleProfitDisplay').textContent = '0.00';
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminTradingPlans();
});

// Additional utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatPercentage(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value / 100);
}