<?php
require_once __DIR__ . '/BaseModel.php';

class EmailTemplate extends BaseModel {
    protected $table = 'email_templates';
    
    // Template types
    const TYPE_WELCOME = 'welcome';
    const TYPE_DEPOSIT_CONFIRMATION = 'deposit_confirmation';
    const TYPE_WITHDRAWAL_NOTIFICATION = 'withdrawal_notification';
    const TYPE_PASSWORD_RESET = 'password_reset';
    const TYPE_EMAIL_VERIFICATION = 'email_verification';
    const TYPE_BALANCE_UPDATE = 'balance_update';
    const TYPE_PLAN_ASSIGNMENT = 'plan_assignment';
    
    // Available placeholders for each template type
    private static $placeholders = [
        self::TYPE_WELCOME => [
            '{{user_name}}' => 'User\'s full name',
            '{{first_name}}' => 'User\'s first name',
            '{{email}}' => 'User\'s email address',
            '{{site_name}}' => 'Site name',
            '{{login_url}}' => 'Login page URL',
            '{{registration_bonus}}' => 'Registration bonus amount',
            '{{currency_symbol}}' => 'Currency symbol'
        ],
        self::TYPE_DEPOSIT_CONFIRMATION => [
            '{{user_name}}' => 'User\'s full name',
            '{{amount}}' => 'Deposit amount',
            '{{currency_symbol}}' => 'Currency symbol',
            '{{payment_method}}' => 'Payment method used',
            '{{transaction_id}}' => 'Transaction ID',
            '{{date}}' => 'Transaction date',
            '{{site_name}}' => 'Site name'
        ],
        self::TYPE_WITHDRAWAL_NOTIFICATION => [
            '{{user_name}}' => 'User\'s full name',
            '{{amount}}' => 'Withdrawal amount',
            '{{currency_symbol}}' => 'Currency symbol',
            '{{status}}' => 'Withdrawal status',
            '{{transaction_id}}' => 'Transaction ID',
            '{{date}}' => 'Request date',
            '{{site_name}}' => 'Site name'
        ],
        self::TYPE_PASSWORD_RESET => [
            '{{user_name}}' => 'User\'s full name',
            '{{reset_link}}' => 'Password reset link',
            '{{expiry_time}}' => 'Link expiry time',
            '{{site_name}}' => 'Site name'
        ],
        self::TYPE_EMAIL_VERIFICATION => [
            '{{user_name}}' => 'User\'s full name',
            '{{verification_link}}' => 'Email verification link',
            '{{site_name}}' => 'Site name'
        ],
        self::TYPE_BALANCE_UPDATE => [
            '{{user_name}}' => 'User\'s full name',
            '{{amount}}' => 'Update amount',
            '{{currency_symbol}}' => 'Currency symbol',
            '{{new_balance}}' => 'New account balance',
            '{{update_type}}' => 'Credit or Debit',
            '{{reason}}' => 'Update reason',
            '{{site_name}}' => 'Site name'
        ],
        self::TYPE_PLAN_ASSIGNMENT => [
            '{{user_name}}' => 'User\'s full name',
            '{{plan_name}}' => 'Trading plan name',
            '{{plan_amount}}' => 'Investment amount',
            '{{currency_symbol}}' => 'Currency symbol',
            '{{expected_return}}' => 'Expected return',
            '{{duration}}' => 'Plan duration',
            '{{site_name}}' => 'Site name'
        ]
    ];
    
    protected $fillable = [
        'template_type',
        'template_name',
        'subject',
        'body_html',
        'body_text',
        'is_active',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'is_active' => 'boolean'
    ];
    
    /**
     * Get all available template types
     */
    public static function getTemplateTypes() {
        return [
            self::TYPE_WELCOME => 'Welcome Email',
            self::TYPE_DEPOSIT_CONFIRMATION => 'Deposit Confirmation',
            self::TYPE_WITHDRAWAL_NOTIFICATION => 'Withdrawal Notification',
            self::TYPE_PASSWORD_RESET => 'Password Reset',
            self::TYPE_EMAIL_VERIFICATION => 'Email Verification',
            self::TYPE_BALANCE_UPDATE => 'Balance Update',
            self::TYPE_PLAN_ASSIGNMENT => 'Plan Assignment'
        ];
    }
    
    /**
     * Get placeholders for a specific template type
     */
    public static function getPlaceholders($templateType) {
        return self::$placeholders[$templateType] ?? [];
    }
    
    /**
     * Get all placeholders
     */
    public static function getAllPlaceholders() {
        return self::$placeholders;
    }
    
    /**
     * Replace placeholders in template content
     */
    public function replacePlaceholders($content, $data) {
        foreach ($data as $placeholder => $value) {
            $content = str_replace($placeholder, $value, $content);
        }
        return $content;
    }
    
    /**
     * Get template by type
     */
    public static function getByType($templateType) {
        return self::where('template_type', $templateType)
                  ->where('is_active', true)
                  ->first();
    }
    
    /**
     * Create default templates
     */
    public static function createDefaults() {
        $defaults = [
            [
                'template_type' => self::TYPE_WELCOME,
                'template_name' => 'Welcome Email',
                'subject' => 'Welcome to {{site_name}}!',
                'body_html' => self::getDefaultWelcomeHtml(),
                'body_text' => self::getDefaultWelcomeText(),
                'is_active' => true
            ],
            [
                'template_type' => self::TYPE_DEPOSIT_CONFIRMATION,
                'template_name' => 'Deposit Confirmation',
                'subject' => 'Deposit Confirmation - {{currency_symbol}}{{amount}}',
                'body_html' => self::getDefaultDepositHtml(),
                'body_text' => self::getDefaultDepositText(),
                'is_active' => true
            ],
            [
                'template_type' => self::TYPE_WITHDRAWAL_NOTIFICATION,
                'template_name' => 'Withdrawal Notification',
                'subject' => 'Withdrawal {{status}} - {{currency_symbol}}{{amount}}',
                'body_html' => self::getDefaultWithdrawalHtml(),
                'body_text' => self::getDefaultWithdrawalText(),
                'is_active' => true
            ],
            [
                'template_type' => self::TYPE_PASSWORD_RESET,
                'template_name' => 'Password Reset',
                'subject' => 'Reset Your Password - {{site_name}}',
                'body_html' => self::getDefaultPasswordResetHtml(),
                'body_text' => self::getDefaultPasswordResetText(),
                'is_active' => true
            ]
        ];
        
        foreach ($defaults as $template) {
            $existing = self::where('template_type', $template['template_type'])->first();
            if (!$existing) {
                self::create($template);
            }
        }
    }
    
    private static function getDefaultWelcomeHtml() {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #007bff;">Welcome to {{site_name}}, {{first_name}}!</h2>
            <p>Thank you for joining our trading platform. Your account has been successfully created.</p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Account Details:</h3>
                <p><strong>Email:</strong> {{email}}</p>
                <p><strong>Registration Bonus:</strong> {{currency_symbol}}{{registration_bonus}}</p>
            </div>
            <p>You can now log in to your account and start trading:</p>
            <a href="{{login_url}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a>
            <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
        </div>';
    }
    
    private static function getDefaultWelcomeText() {
        return 'Welcome to {{site_name}}, {{first_name}}!

Thank you for joining our trading platform. Your account has been successfully created.

Account Details:
Email: {{email}}
Registration Bonus: {{currency_symbol}}{{registration_bonus}}

You can now log in to your account and start trading: {{login_url}}

Best regards,
The {{site_name}} Team';
    }
    
    private static function getDefaultDepositHtml() {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #28a745;">Deposit Confirmation</h2>
            <p>Dear {{user_name}},</p>
            <p>Your deposit has been successfully processed.</p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Transaction Details:</h3>
                <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
                <p><strong>Payment Method:</strong> {{payment_method}}</p>
                <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
                <p><strong>Date:</strong> {{date}}</p>
            </div>
            <p>The funds have been added to your account balance and are available for trading.</p>
            <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
        </div>';
    }
    
    private static function getDefaultDepositText() {
        return 'Deposit Confirmation

Dear {{user_name}},

Your deposit has been successfully processed.

Transaction Details:
Amount: {{currency_symbol}}{{amount}}
Payment Method: {{payment_method}}
Transaction ID: {{transaction_id}}
Date: {{date}}

The funds have been added to your account balance and are available for trading.

Best regards,
The {{site_name}} Team';
    }
    
    private static function getDefaultWithdrawalHtml() {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #ffc107;">Withdrawal {{status}}</h2>
            <p>Dear {{user_name}},</p>
            <p>Your withdrawal request has been {{status}}.</p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Withdrawal Details:</h3>
                <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
                <p><strong>Status:</strong> {{status}}</p>
                <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
                <p><strong>Date:</strong> {{date}}</p>
            </div>
            <p>If you have any questions, please contact our support team.</p>
            <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
        </div>';
    }
    
    private static function getDefaultWithdrawalText() {
        return 'Withdrawal {{status}}

Dear {{user_name}},

Your withdrawal request has been {{status}}.

Withdrawal Details:
Amount: {{currency_symbol}}{{amount}}
Status: {{status}}
Transaction ID: {{transaction_id}}
Date: {{date}}

If you have any questions, please contact our support team.

Best regards,
The {{site_name}} Team';
    }
    
    private static function getDefaultPasswordResetHtml() {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">Password Reset Request</h2>
            <p>Dear {{user_name}},</p>
            <p>You have requested to reset your password. Click the link below to create a new password:</p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{reset_link}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">Reset Password</a>
            </div>
            <p><strong>Important:</strong> This link will expire in {{expiry_time}}.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
            <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
        </div>';
    }
    
    private static function getDefaultPasswordResetText() {
        return 'Password Reset Request

Dear {{user_name}},

You have requested to reset your password. Click the link below to create a new password:

{{reset_link}}

Important: This link will expire in {{expiry_time}}.

If you did not request this password reset, please ignore this email.

Best regards,
The {{site_name}} Team';
    }
    
    /**
     * Validate template content
     */
    public function validate() {
        $data = $this->attributes;
        $errors = [];
        
        if (empty($data['template_name'])) {
            $errors[] = 'Template name is required';
        }
        
        if (empty($data['template_type'])) {
            $errors[] = 'Template type is required';
        }
        
        if (empty($data['subject'])) {
            $errors[] = 'Subject is required';
        }
        
        if (empty($data['body_html']) && empty($data['body_text'])) {
            $errors[] = 'At least one body format (HTML or Text) is required';
        }
        
        return $errors;
    }
}