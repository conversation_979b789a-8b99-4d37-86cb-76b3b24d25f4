<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $assetId = intval($input['asset_id'] ?? 0);
    
    if (!$assetId) {
        echo json_encode(['success' => false, 'message' => 'Asset ID is required']);
        exit;
    }
    
    // Load the asset
    $stmt = $pdo->prepare("SELECT * FROM trading_assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();
    
    if (!$asset) {
        echo json_encode(['success' => false, 'message' => 'Asset not found']);
        exit;
    }
    
    // Check if asset is used in any trades
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM trades WHERE asset = ?");
    $stmt->execute([$asset['symbol']]);
    $tradesCount = $stmt->fetch()['count'];
    
    if ($tradesCount > 0) {
        echo json_encode([
            'success' => false,
            'message' => "Cannot delete asset with existing trades. This asset has {$tradesCount} trades."
        ]);
        exit;
    }
    
    // Delete the asset
    $stmt = $pdo->prepare("DELETE FROM trading_assets WHERE id = ?");
    $result = $stmt->execute([$assetId]);
    
    if ($result) {
        // Log the action
        AuditTrailService::log(
            'trading_asset_deleted',
            'trading_asset',
            $assetId,
            [
                'deleted_asset' => [
                    'symbol' => $asset['symbol'],
                    'name' => $asset['name'],
                    'category' => $asset['category'],
                    'status' => $asset['status']
                ],
                'reason' => 'Admin deletion'
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Asset deleted successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to delete asset'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Delete asset error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while deleting the asset'
    ]);
}
?>