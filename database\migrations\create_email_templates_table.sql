-- Create email_templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_type VARCHAR(50) NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body_html TEXT,
    body_text TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_template_type (template_type),
    INDEX idx_template_type (template_type),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default email templates
INSERT INTO email_templates (template_type, template_name, subject, body_html, body_text, is_active) VALUES
('welcome', 'Welcome Email', 'Welcome to {{site_name}}!', 
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #007bff;">Welcome to {{site_name}}, {{first_name}}!</h2>
    <p>Thank you for joining our trading platform. Your account has been successfully created.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Account Details:</h3>
        <p><strong>Email:</strong> {{email}}</p>
        <p><strong>Registration Bonus:</strong> {{currency_symbol}}{{registration_bonus}}</p>
    </div>
    <p>You can now log in to your account and start trading:</p>
    <a href="{{login_url}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Login to Your Account</a>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Welcome to {{site_name}}, {{first_name}}!

Thank you for joining our trading platform. Your account has been successfully created.

Account Details:
Email: {{email}}
Registration Bonus: {{currency_symbol}}{{registration_bonus}}

You can now log in to your account and start trading: {{login_url}}

Best regards,
The {{site_name}} Team', TRUE),

('deposit_confirmation', 'Deposit Confirmation', 'Deposit Confirmation - {{currency_symbol}}{{amount}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #28a745;">Deposit Confirmation</h2>
    <p>Dear {{user_name}},</p>
    <p>Your deposit has been successfully processed.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Transaction Details:</h3>
        <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
        <p><strong>Payment Method:</strong> {{payment_method}}</p>
        <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
        <p><strong>Date:</strong> {{date}}</p>
    </div>
    <p>The funds have been added to your account balance and are available for trading.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Deposit Confirmation

Dear {{user_name}},

Your deposit has been successfully processed.

Transaction Details:
Amount: {{currency_symbol}}{{amount}}
Payment Method: {{payment_method}}
Transaction ID: {{transaction_id}}
Date: {{date}}

The funds have been added to your account balance and are available for trading.

Best regards,
The {{site_name}} Team', TRUE),

('withdrawal_notification', 'Withdrawal Notification', 'Withdrawal {{status}} - {{currency_symbol}}{{amount}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #ffc107;">Withdrawal {{status}}</h2>
    <p>Dear {{user_name}},</p>
    <p>Your withdrawal request has been {{status}}.</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>Withdrawal Details:</h3>
        <p><strong>Amount:</strong> {{currency_symbol}}{{amount}}</p>
        <p><strong>Status:</strong> {{status}}</p>
        <p><strong>Transaction ID:</strong> {{transaction_id}}</p>
        <p><strong>Date:</strong> {{date}}</p>
    </div>
    <p>If you have any questions, please contact our support team.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Withdrawal {{status}}

Dear {{user_name}},

Your withdrawal request has been {{status}}.

Withdrawal Details:
Amount: {{currency_symbol}}{{amount}}
Status: {{status}}
Transaction ID: {{transaction_id}}
Date: {{date}}

If you have any questions, please contact our support team.

Best regards,
The {{site_name}} Team', TRUE),

('password_reset', 'Password Reset', 'Reset Your Password - {{site_name}}',
'<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
    <h2 style="color: #dc3545;">Password Reset Request</h2>
    <p>Dear {{user_name}},</p>
    <p>You have requested to reset your password. Click the link below to create a new password:</p>
    <div style="text-align: center; margin: 30px 0;">
        <a href="{{reset_link}}" style="background: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px;">Reset Password</a>
    </div>
    <p><strong>Important:</strong> This link will expire in {{expiry_time}}.</p>
    <p>If you did not request this password reset, please ignore this email.</p>
    <p style="margin-top: 30px; color: #666;">Best regards,<br>The {{site_name}} Team</p>
</div>',
'Password Reset Request

Dear {{user_name}},

You have requested to reset your password. Click the link below to create a new password:

{{reset_link}}

Important: This link will expire in {{expiry_time}}.

If you did not request this password reset, please ignore this email.

Best regards,
The {{site_name}} Team', TRUE);