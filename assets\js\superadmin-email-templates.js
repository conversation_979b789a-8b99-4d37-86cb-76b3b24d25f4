/**
 * Super Admin Email Templates Management
 */

let currentTemplateId = null;
let isEditMode = false;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    if (typeof DataTable !== 'undefined') {
        new DataTable('#templatesTable', {
            pageLength: 25,
            order: [[0, 'asc']],
            columnDefs: [
                { orderable: false, targets: [5] }
            ]
        });
    }
    
    // Initialize form validation
    initializeFormValidation();
    
    // Show any flash messages
    showFlashMessage();
});

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const form = document.getElementById('templateForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (validateForm()) {
                submitForm();
            }
        });
    }
}

/**
 * Validate template form
 */
function validateForm() {
    const templateName = document.getElementById('templateName').value.trim();
    const templateType = document.getElementById('templateType').value;
    const subject = document.getElementById('subject').value.trim();
    const bodyHtml = document.getElementById('bodyHtml').value.trim();
    const bodyText = document.getElementById('bodyText').value.trim();
    
    if (!templateName) {
        showAlert('Template name is required', 'danger');
        return false;
    }
    
    if (!templateType) {
        showAlert('Template type is required', 'danger');
        return false;
    }
    
    if (!subject) {
        showAlert('Subject is required', 'danger');
        return false;
    }
    
    if (!bodyHtml && !bodyText) {
        showAlert('At least one body format (HTML or Text) is required', 'danger');
        return false;
    }
    
    return true;
}

/**
 * Submit template form
 */
function submitForm() {
    const form = document.getElementById('templateForm');
    const formData = new FormData(form);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('templateModal')).hide();
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while saving the template', 'danger');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

/**
 * Edit template
 */
function editTemplate(templateId) {
    // Fetch template data
    fetch(`/superadmin/email-templates/api/details.php?id=${templateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const template = data.template;
                
                // Populate form
                document.getElementById('templateId').value = template.id;
                document.getElementById('templateName').value = template.template_name;
                document.getElementById('templateType').value = template.template_type;
                document.getElementById('subject').value = template.subject;
                document.getElementById('bodyHtml').value = template.body_html || '';
                document.getElementById('bodyText').value = template.body_text || '';
                document.getElementById('isActive').checked = template.is_active;
                
                // Update form action and modal title
                document.getElementById('formAction').value = 'update_template';
                document.getElementById('templateModalTitle').textContent = 'Edit Email Template';
                
                // Update placeholders
                updatePlaceholders();
                
                // Show modal
                new bootstrap.Modal(document.getElementById('templateModal')).show();
                
                isEditMode = true;
                currentTemplateId = templateId;
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to load template details', 'danger');
        });
}

/**
 * Delete template
 */
function deleteTemplate(templateId) {
    if (!confirm('Are you sure you want to delete this email template? This action cannot be undone.')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'delete_template');
    formData.append('template_id', templateId);
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to delete template', 'danger');
    });
}

/**
 * Toggle template status
 */
function toggleStatus(templateId) {
    const formData = new FormData();
    formData.append('action', 'toggle_status');
    formData.append('template_id', templateId);
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to update template status', 'danger');
    });
}

/**
 * Preview template
 */
function previewTemplate(templateId) {
    const formData = new FormData();
    formData.append('action', 'preview_template');
    formData.append('template_id', templateId);
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPreview(data.preview);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to generate preview', 'danger');
    });
}

/**
 * Preview current template being edited
 */
function previewCurrentTemplate() {
    const templateType = document.getElementById('templateType').value;
    const subject = document.getElementById('subject').value;
    const bodyHtml = document.getElementById('bodyHtml').value;
    const bodyText = document.getElementById('bodyText').value;
    
    if (!templateType) {
        showAlert('Please select a template type first', 'warning');
        return;
    }
    
    // Get sample data for the template type
    const sampleData = getSampleDataForType(templateType);
    
    // Replace placeholders
    const previewSubject = replacePlaceholders(subject, sampleData);
    const previewHtml = replacePlaceholders(bodyHtml, sampleData);
    const previewText = replacePlaceholders(bodyText, sampleData);
    
    showPreview({
        subject: previewSubject,
        html: previewHtml,
        text: previewText
    });
}

/**
 * Show preview modal
 */
function showPreview(preview) {
    document.getElementById('previewSubject').textContent = preview.subject;
    document.getElementById('previewHtmlContent').innerHTML = preview.html;
    document.getElementById('previewTextContent').textContent = preview.text;
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

/**
 * Update placeholders help based on selected template type
 */
function updatePlaceholders() {
    const templateType = document.getElementById('templateType').value;
    const placeholdersHelp = document.getElementById('placeholdersHelp');
    
    if (!templateType || !window.templatePlaceholders[templateType]) {
        placeholdersHelp.innerHTML = '<p class="text-muted">Select a template type to see available placeholders</p>';
        return;
    }
    
    const placeholders = window.templatePlaceholders[templateType];
    let html = '<div class="row">';
    
    Object.entries(placeholders).forEach(([placeholder, description]) => {
        html += `
            <div class="col-md-6 mb-2">
                <code class="placeholder-code" onclick="insertPlaceholder('${placeholder}')" title="Click to insert">
                    ${placeholder}
                </code>
                <small class="text-muted d-block">${description}</small>
            </div>
        `;
    });
    
    html += '</div>';
    placeholdersHelp.innerHTML = html;
}

/**
 * Insert placeholder at cursor position
 */
function insertPlaceholder(placeholder) {
    // Get the currently active textarea
    const activeTab = document.querySelector('#contentTabs .nav-link.active');
    let textarea;
    
    if (activeTab && activeTab.id === 'html-tab') {
        textarea = document.getElementById('bodyHtml');
    } else {
        textarea = document.getElementById('bodyText');
    }
    
    if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const text = textarea.value;
        
        textarea.value = text.substring(0, start) + placeholder + text.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + placeholder.length, start + placeholder.length);
    }
}

/**
 * Reset to default templates
 */
function resetDefaults() {
    if (!confirm('This will restore all default email templates. Any custom changes will be lost. Continue?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'reset_defaults');
    formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to reset templates', 'danger');
    });
}

/**
 * Reset modal when it's hidden
 */
document.getElementById('templateModal').addEventListener('hidden.bs.modal', function() {
    // Reset form
    document.getElementById('templateForm').reset();
    document.getElementById('templateId').value = '';
    document.getElementById('formAction').value = 'create_template';
    document.getElementById('templateModalTitle').textContent = 'Add Email Template';
    
    // Reset placeholders help
    document.getElementById('placeholdersHelp').innerHTML = '<p class="text-muted">Select a template type to see available placeholders</p>';
    
    // Reset flags
    isEditMode = false;
    currentTemplateId = null;
});

/**
 * Get sample data for template type
 */
function getSampleDataForType(templateType) {
    const baseData = {
        '{{site_name}}': 'Coinage Trading',
        '{{currency_symbol}}': '$',
        '{{user_name}}': 'John Doe',
        '{{first_name}}': 'John',
        '{{email}}': '<EMAIL>',
        '{{date}}': new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
        '{{login_url}}': 'https://example.com/login.php'
    };
    
    const typeSpecificData = {
        'welcome': {
            '{{registration_bonus}}': '50.00'
        },
        'deposit_confirmation': {
            '{{amount}}': '500.00',
            '{{payment_method}}': 'Bitcoin',
            '{{transaction_id}}': 'TXN123456789'
        },
        'withdrawal_notification': {
            '{{amount}}': '250.00',
            '{{status}}': 'Approved',
            '{{transaction_id}}': 'WTH987654321'
        },
        'password_reset': {
            '{{reset_link}}': 'https://example.com/reset-password.php?token=abc123',
            '{{expiry_time}}': '24 hours'
        },
        'email_verification': {
            '{{verification_link}}': 'https://example.com/verify-email.php?token=xyz789'
        },
        'balance_update': {
            '{{amount}}': '100.00',
            '{{new_balance}}': '1,250.00',
            '{{update_type}}': 'Credit',
            '{{reason}}': 'Admin adjustment'
        },
        'plan_assignment': {
            '{{plan_name}}': 'Premium Trading Plan',
            '{{plan_amount}}': '1,000.00',
            '{{expected_return}}': '15%',
            '{{duration}}': '30 days'
        }
    };
    
    return { ...baseData, ...(typeSpecificData[templateType] || {}) };
}

/**
 * Replace placeholders in text
 */
function replacePlaceholders(text, data) {
    let result = text;
    Object.entries(data).forEach(([placeholder, value]) => {
        result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
    });
    return result;
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Create alert container if it doesn't exist
 */
function createAlertContainer() {
    let container = document.getElementById('alertContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'alertContainer';
        container.style.position = 'fixed';
        container.style.top = '20px';
        container.style.right = '20px';
        container.style.zIndex = '9999';
        container.style.maxWidth = '400px';
        document.body.appendChild(container);
    }
    return container;
}

/**
 * Show flash message from session
 */
function showFlashMessage() {
    // This would be populated by PHP if there's a session message
    const flashMessage = window.flashMessage;
    if (flashMessage) {
        showAlert(flashMessage.message, flashMessage.type);
    }
}