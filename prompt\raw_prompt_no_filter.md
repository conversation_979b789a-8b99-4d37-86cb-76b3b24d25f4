user register sites opens displays HOME PAGE User homepage should contain user dashboard displaying profile picture, amount,balance bonus all should be  0 BAL user clicks on choose trading plan: different trading plans to choose from BASIC PLAN, GOLD PLAN, VIP PLAN All plans have their different deposit amounts. 3000, 5000, 10000 user chooses a plan, clicks to deposit, another page  opens showing deposit information. deposit information should include crypto, paypal, and other options. USER proceeds to deposit user receives email notification and deposit confirmation mail user account gets automatically credited and deposit bonus of 10% is allocated. should display trading view or trading api  on background should have pages like profile page, deposit page, transaction activities, trading page, settings: for password rest, and to show his information user should also be able to place trades from his side

ADMIN: admin logins to site site displays admin dashboard, admin can create user , admin can edit user, admin can delete user and suspend user admin can credit user, admin can debit user, admin can keep deposit pending admin can send user message notification to display on user dashboard admin can choose trading plan for user admin can create and approve withdrawal for user, approve withdrawal, PEND withdrawal admin can create trade history and backdate admin can EDIT user deposit options admin can EDIT withdrawal options admin can see all user information admin can add different commodity, currency, commodity, options admin can see support ticket admin can reply support system admin can send notification banner admin can create WhatsApp support admin can add and choose payment method admin can access profile settings ADMIN CAN CREATE TRADING PLAN and EDIT PLAN Admin can create and edit kyc complete the English
    lkets me break its down 

its crypto currency trading app ,  the app should have cuyrrency managment for the entire dashboard , user dashboard layout , sidebar , header and footer , we include , header and footer and sidebar in all page , we dont hardcode any header , footer or layout we include , we need this layout system one deicated to usert and another dedicated to admin ,  the layout should have good spacing , no thick border line , we use dark white background so the card can be white with radius , 


appearance , setting , we control primary color and secondary , dont hardcode color , we use primary color dynamic for each section so its easy to control, all link should be dynamic so we can use it any typoe of domain or subdomain , each page must be a folder , dashboard  /index.php then we css, js , php users/index.php we edit , add , delete suspend and so on , css , js and php , we need clean rot folder 

all test must go to test_folder 

clean code no code should be more than 100 line instead we break it into file and include , 

we have user , admin and super admin , super amdin is use to control , system settings, appearance settings, email templates , email template must be beautiful visual appealing ,  with logo and text and the template must well design to hit inbox , 

smtp configuration , 

admin user management , 
Audit logs , audit trauil and top action , 

secutiy is 2fa , 6 digit or otp  for the super admin , this same secuty for admin and user as well , 

we can of and on any of the security , 


super amdin control his own secuty 

normal admin control each user security from admin dasdhboard , 


and for the admin , we have this pages , 



Dashboard
Plan Manage
Time Manage
Plan Manage


Manage Staking
Plan
Staking Invest
Manage Pool
Plan
Pool Invest
Promotion Tool
User Ranking
Manage Referral
Investment Report
Manage Users
All Users
Active Users
Banned Users
KYC Pending
KYC Unverified
9
Payment Gateways
Automatic Gateways
Manual Gateways
Deposits
Pending Deposits
Approved Deposits
Successful Deposits
Rejected Deposits
Initiated Deposits
All Deposits
Withdrawals
Withdrawal Methods
Pending Withdrawals
Approved Withdrawals
Rejected Withdrawals
All Withdrawals
Support Ticket
Pending Ticket
2
Closed Ticket
Answered Ticket
All Ticket
Report
Transaction Log
Invest History
Settings
General Setting
KYC Setting
Holiday Setting
System Configuration
Extensions
Logo & Favicon
Notification Setting
Global Template
Email Setting
SMS Setting
Push Notification Setting
Notification Templates
Report & Request




System Configuration
Email Notification

If you enable this module, the system will send emails to users where needed. Otherwise, no email will be sent. So be sure before disabling this module that, the system doesn't need to send any emails.

SMS Notification

If you enable this module, the system will send SMS to users where needed. Otherwise, no SMS will be sent. So be sure before disabling this module that, the system doesn't need to send any SMS.

Email Verification

If you enable Email Verification, users have to verify their email to access the dashboard. A verification link will be sent to their email to be verified.
Note: Make sure that the Email Notification module is enabled

Registration Bonus

If you enable this module, users will get an amount to their deposit wallet after completing registration, according to the Registration Bonus value set from the General Setting.

User Ranking

If you enable this module, users will get a defined bonus for investment that you can configure from here.

Balance Transfer

If you enable this module, users will be able to transfer the balance to each other. A fixed and a percent charge can be configured for this module from the General Setting.

Promotional Tool

If you enable this module, users will be able to copy some HTML code which contain his/her referral link and an image added by you. Click here to add promotional tool. Promtional Tool

Withdrawal on Holiday

If you enable it, that means the system's users will be able to make withdrawal requests on holiday. Otherwise, they have to wait for the next working days

KYC Verification

If you enable KYC (Know Your Client) module, users must have to submit the required data. Otherwise, any money out transaction will be prevented by this system.

Push Notification

If you enable this module, the system will send push notifications to users. Otherwise, no push notification will be sent. Setting here

Schedule Invest

Enabling this module allows users to set up automated investment schedules. Without enabling it, users are unable to ments.

Staking

Enabling this module allows users to stake their investments. Without enabling it, users will be unable to participate in staking.

Pool

Enabling this module allows users to invest in the pool. Without enabling it, users will not have the option to invest in the pool.