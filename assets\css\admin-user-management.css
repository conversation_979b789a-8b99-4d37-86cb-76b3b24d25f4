/* Admin User Management Styles */

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 600;
}

.user-info {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0;
}

.user-username {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0;
}

.balance-info {
    display: flex;
    flex-direction: column;
}

.balance-main {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0;
}

.balance-bonus {
    font-size: 0.875rem;
    margin-bottom: 0;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 600;
    text-transform: uppercase;
}

.verification-icon {
    font-size: 0.875rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.btn-action {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

.filters-card {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

.table-responsive {
    border-radius: 0.375rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #2d3748;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

/* User details modal */
.user-details-section {
    margin-bottom: 2rem;
}

.user-details-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.user-details-avatar {
    width: 60px;
    height: 60px;
    margin-right: 1rem;
}

.user-details-info h5 {
    margin-bottom: 0.25rem;
    color: #1f2937;
}

.user-details-info p {
    margin-bottom: 0;
    color: #6b7280;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #374151;
}

.detail-value {
    color: #6b7280;
}

.financial-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.financial-summary h6 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
}

.financial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.financial-item {
    text-align: center;
}

.financial-amount {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.financial-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

.transaction-list {
    max-height: 300px;
    overflow-y: auto;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-info {
    flex: 1;
}

.transaction-type {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.transaction-date {
    font-size: 0.875rem;
    color: #6b7280;
}

.transaction-amount {
    font-weight: 600;
    text-align: right;
}

.transaction-amount.positive {
    color: #059669;
}

.transaction-amount.negative {
    color: #dc2626;
}

/* Balance management modal */
.balance-action-selector {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.balance-action-option {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
}

.balance-action-option:hover {
    border-color: #4f46e5;
    background-color: #f8fafc;
}

.balance-action-option.active {
    border-color: #4f46e5;
    background-color: #eef2ff;
}

.balance-action-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.balance-action-icon.credit {
    color: #059669;
}

.balance-action-icon.debit {
    color: #dc2626;
}

/* Pagination */
.pagination {
    margin-top: 1.5rem;
}

.page-link {
    color: #4f46e5;
    border-color: #d1d5db;
}

.page-link:hover {
    color: #3730a3;
    background-color: #f3f4f6;
    border-color: #9ca3af;
}

.page-item.active .page-link {
    background-color: #4f46e5;
    border-color: #4f46e5;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state-icon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #9ca3af;
}

/* Loading states */
.loading-row {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    height: 60px;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-action {
        width: 100%;
    }
    
    .financial-grid {
        grid-template-columns: 1fr;
    }
    
    .balance-action-selector {
        flex-direction: column;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .user-details-header {
        flex-direction: column;
        text-align: center;
    }
    
    .user-details-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .filters-card .row {
        flex-direction: column;
    }
    
    .filters-card .col-md-4,
    .filters-card .col-md-3,
    .filters-card .col-md-2 {
        margin-bottom: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .avatar-sm {
        width: 2rem;
        height: 2rem;
    }
}

/* Print styles */
@media print {
    .btn-toolbar,
    .action-buttons,
    .filters-card {
        display: none !important;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .user-name {
        color: #f7fafc;
    }
    
    .user-username {
        color: #a0aec0;
    }
    
    .balance-main {
        color: #f7fafc;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .table tbody tr:hover {
        background-color: #2d3748;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-header {
        background-color: #1a202c;
        border-bottom-color: #4a5568;
    }
    
    .form-control,
    .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus,
    .form-select:focus {
        background-color: #4a5568;
        border-color: #4f46e5;
        color: #e2e8f0;
    }
}