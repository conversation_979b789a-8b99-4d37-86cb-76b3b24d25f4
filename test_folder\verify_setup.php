<?php
/**
 * Complete Setup Verification Script
 * Verifies that Task 1 has been completed successfully
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h1>🔍 Task 1 Verification - Core Project Structure & Database Schema</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 900px; margin: 0 auto;'>";

$errors = [];
$warnings = [];
$success = [];

// 1. Database Connection Test
echo "<h2>1️⃣ Database Connection Verification</h2>";
try {
    $db = getDB();
    if ($db->getAttribute(PDO::ATTR_CONNECTION_STATUS)) {
        $success[] = "✅ Database connection established successfully";
        
        // Get MySQL version
        $stmt = $db->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        $success[] = "✅ MySQL Version: " . $version['version'];
    } else {
        $errors[] = "❌ Database connection failed";
    }
} catch (Exception $e) {
    $errors[] = "❌ Database connection error: " . $e->getMessage();
}

// 2. Database Schema Verification
echo "<h2>2️⃣ Database Schema Verification</h2>";

$requiredTables = [
    'users' => ['id', 'username', 'email', 'password', 'role', 'status', 'balance', 'bonus'],
    'trading_plans' => ['id', 'name', 'min_deposit', 'max_deposit', 'daily_return', 'duration_days'],
    'deposits' => ['id', 'user_id', 'plan_id', 'amount', 'bonus_amount', 'status'],
    'withdrawals' => ['id', 'user_id', 'amount', 'status'],
    'transactions' => ['id', 'user_id', 'type', 'amount', 'balance_before', 'balance_after'],
    'support_tickets' => ['id', 'user_id', 'subject', 'message', 'status'],
    'system_settings' => ['id', 'setting_key', 'setting_value', 'setting_type'],
    'payment_methods' => ['id', 'name', 'type', 'details', 'status'],
    'audit_logs' => ['id', 'user_id', 'action', 'table_name', 'created_at']
];

foreach ($requiredTables as $table => $columns) {
    try {
        // Check if table exists
        $stmt = $db->query("SELECT COUNT(*) FROM `$table`");
        $count = $stmt->fetchColumn();
        $success[] = "✅ Table '$table' exists with $count records";
        
        // Check if required columns exist
        $stmt = $db->query("DESCRIBE `$table`");
        $existingColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $missingColumns = array_diff($columns, $existingColumns);
        if (empty($missingColumns)) {
            $success[] = "✅ All required columns exist in '$table'";
        } else {
            $warnings[] = "⚠️ Missing columns in '$table': " . implode(', ', $missingColumns);
        }
        
    } catch (Exception $e) {
        $errors[] = "❌ Table '$table' verification failed: " . $e->getMessage();
    }
}

// 3. Foreign Key Constraints Verification
echo "<h2>3️⃣ Foreign Key Constraints Verification</h2>";

$foreignKeys = [
    'deposits' => ['user_id' => 'users(id)', 'plan_id' => 'trading_plans(id)'],
    'withdrawals' => ['user_id' => 'users(id)'],
    'transactions' => ['user_id' => 'users(id)'],
    'support_tickets' => ['user_id' => 'users(id)'],
    'audit_logs' => ['user_id' => 'users(id)']
];

try {
    $stmt = $db->query("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE 
            REFERENCED_TABLE_SCHEMA = '" . DB_NAME . "'
            AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    $existingFKs = $stmt->fetchAll();
    
    if (count($existingFKs) > 0) {
        $success[] = "✅ Found " . count($existingFKs) . " foreign key constraints";
        foreach ($existingFKs as $fk) {
            $success[] = "✅ FK: {$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}";
        }
    } else {
        $warnings[] = "⚠️ No foreign key constraints found - this may affect data integrity";
    }
    
} catch (Exception $e) {
    $warnings[] = "⚠️ Could not verify foreign keys: " . $e->getMessage();
}

// 4. Indexes Verification
echo "<h2>4️⃣ Database Indexes Verification</h2>";

try {
    $stmt = $db->query("
        SELECT 
            TABLE_NAME,
            INDEX_NAME,
            COLUMN_NAME
        FROM 
            INFORMATION_SCHEMA.STATISTICS 
        WHERE 
            TABLE_SCHEMA = '" . DB_NAME . "'
            AND INDEX_NAME != 'PRIMARY'
        ORDER BY TABLE_NAME, INDEX_NAME
    ");
    
    $indexes = $stmt->fetchAll();
    
    if (count($indexes) > 0) {
        $success[] = "✅ Found " . count($indexes) . " database indexes for performance";
        
        $indexesByTable = [];
        foreach ($indexes as $index) {
            $indexesByTable[$index['TABLE_NAME']][] = $index['INDEX_NAME'] . '(' . $index['COLUMN_NAME'] . ')';
        }
        
        foreach ($indexesByTable as $table => $tableIndexes) {
            $success[] = "✅ $table: " . implode(', ', array_unique($tableIndexes));
        }
    } else {
        $warnings[] = "⚠️ No additional indexes found - consider adding for better performance";
    }
    
} catch (Exception $e) {
    $warnings[] = "⚠️ Could not verify indexes: " . $e->getMessage();
}

// 5. Default Data Verification
echo "<h2>5️⃣ Default Data Verification</h2>";

// Check for super admin user
try {
    $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = 'superadmin'");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();
    
    if ($adminCount > 0) {
        $success[] = "✅ Super admin user exists";
        
        // Get admin details
        $stmt = $db->prepare("SELECT username, email, status FROM users WHERE role = 'superadmin' LIMIT 1");
        $stmt->execute();
        $admin = $stmt->fetch();
        $success[] = "✅ Super admin: {$admin['username']} ({$admin['email']}) - Status: {$admin['status']}";
    } else {
        $errors[] = "❌ No super admin user found";
    }
} catch (Exception $e) {
    $errors[] = "❌ Could not verify admin user: " . $e->getMessage();
}

// Check for trading plans
try {
    $stmt = $db->prepare("SELECT COUNT(*) FROM trading_plans WHERE status = 'active'");
    $stmt->execute();
    $planCount = $stmt->fetchColumn();
    
    if ($planCount > 0) {
        $success[] = "✅ $planCount active trading plans found";
        
        // List plans
        $stmt = $db->prepare("SELECT name, min_deposit, max_deposit, daily_return FROM trading_plans WHERE status = 'active' ORDER BY min_deposit");
        $stmt->execute();
        $plans = $stmt->fetchAll();
        
        foreach ($plans as $plan) {
            $success[] = "✅ Plan: {$plan['name']} - $" . number_format($plan['min_deposit']) . " to $" . number_format($plan['max_deposit']) . " ({$plan['daily_return']}% daily)";
        }
    } else {
        $errors[] = "❌ No active trading plans found";
    }
} catch (Exception $e) {
    $errors[] = "❌ Could not verify trading plans: " . $e->getMessage();
}

// Check for payment methods
try {
    $stmt = $db->prepare("SELECT COUNT(*) FROM payment_methods WHERE status = 'active'");
    $stmt->execute();
    $methodCount = $stmt->fetchColumn();
    
    if ($methodCount > 0) {
        $success[] = "✅ $methodCount payment methods configured";
        
        // List methods
        $stmt = $db->prepare("SELECT name, type FROM payment_methods WHERE status = 'active' ORDER BY sort_order");
        $stmt->execute();
        $methods = $stmt->fetchAll();
        
        foreach ($methods as $method) {
            $success[] = "✅ Payment: {$method['name']} ({$method['type']})";
        }
    } else {
        $errors[] = "❌ No payment methods found";
    }
} catch (Exception $e) {
    $errors[] = "❌ Could not verify payment methods: " . $e->getMessage();
}

// Check for system settings
try {
    $stmt = $db->prepare("SELECT COUNT(*) FROM system_settings");
    $stmt->execute();
    $settingsCount = $stmt->fetchColumn();
    
    if ($settingsCount > 0) {
        $success[] = "✅ $settingsCount system settings configured";
    } else {
        $warnings[] = "⚠️ No system settings found";
    }
} catch (Exception $e) {
    $warnings[] = "⚠️ Could not verify system settings: " . $e->getMessage();
}

// 6. Directory Structure Verification
echo "<h2>6️⃣ Directory Structure Verification</h2>";

$requiredDirectories = [
    'admin', 'admin/dashboard', 'admin/users', 'admin/deposits', 'admin/withdrawals',
    'superadmin', 'superadmin/settings', 'superadmin/admins',
    'user', 'user/dashboard', 'user/profile', 'user/deposit', 'user/withdraw',
    'layouts', 'layouts/admin', 'layouts/user', 'layouts/superadmin',
    'classes', 'classes/models', 'classes/controllers', 'classes/services',
    'assets', 'assets/css', 'assets/js', 'assets/images', 'assets/uploads',
    'database', 'database/backups', 'logs'
];

$missingDirs = [];
foreach ($requiredDirectories as $dir) {
    if (is_dir($dir)) {
        $success[] = "✅ Directory exists: $dir";
    } else {
        $missingDirs[] = $dir;
    }
}

if (!empty($missingDirs)) {
    $errors[] = "❌ Missing directories: " . implode(', ', $missingDirs);
}

// 7. Security Files Verification
echo "<h2>7️⃣ Security Files Verification</h2>";

$securityFiles = [
    'database/.htaccess',
    'includes/.htaccess',
    'classes/.htaccess',
    'logs/.htaccess',
    'assets/uploads/.htaccess'
];

foreach ($securityFiles as $file) {
    if (file_exists($file)) {
        $success[] = "✅ Security file exists: $file";
    } else {
        $warnings[] = "⚠️ Missing security file: $file";
    }
}

// 8. Configuration Verification
echo "<h2>8️⃣ Configuration Verification</h2>";

$configChecks = [
    'DB_HOST' => DB_HOST,
    'DB_NAME' => DB_NAME,
    'SITE_NAME' => SITE_NAME,
    'BASE_URL' => BASE_URL,
    'DEFAULT_CURRENCY' => DEFAULT_CURRENCY,
    'ENABLE_2FA' => ENABLE_2FA ? 'Enabled' : 'Disabled',
    'ENABLE_EMAIL_VERIFICATION' => ENABLE_EMAIL_VERIFICATION ? 'Enabled' : 'Disabled'
];

foreach ($configChecks as $setting => $value) {
    $success[] = "✅ $setting: $value";
}

// Display Results
echo "<h2>📊 Verification Results</h2>";

// Success messages
if (!empty($success)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Success (" . count($success) . " items)</h3>";
    foreach ($success as $message) {
        echo "<p style='color: #155724; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Warnings
if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Warnings (" . count($warnings) . " items)</h3>";
    foreach ($warnings as $message) {
        echo "<p style='color: #856404; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Errors
if (!empty($errors)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors (" . count($errors) . " items)</h3>";
    foreach ($errors as $message) {
        echo "<p style='color: #721c24; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Final Status
echo "<h2>🎯 Task 1 Completion Status</h2>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 TASK 1 COMPLETED SUCCESSFULLY!</h3>";
    echo "<p style='color: #155724; margin: 10px 0;'>Core project structure and database schema have been set up correctly.</p>";
    echo "<p style='color: #155724; margin: 10px 0;'><strong>Ready to proceed to Task 2: Implement core PHP classes and data models</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ TASK 1 INCOMPLETE</h3>";
    echo "<p style='color: #721c24; margin: 10px 0;'>Please resolve the errors above before proceeding to the next task.</p>";
    echo "</div>";
}

echo "</div>";
?>