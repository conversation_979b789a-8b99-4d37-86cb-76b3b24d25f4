{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis including suggestions for improvements, code smells detection, design pattern recommendations, and best practices guidance while maintaining existing functionality.", "version": "1", "when": {"type": "fileCreated", "patterns": ["*.php", "includes/*.php", "test_folder/*.php", "config.php"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Recommend PHP best practices for security, performance, and maintainability\n4. **Readability**: Suggest improvements for code readability and documentation\n5. **Performance**: Identify potential performance optimizations\n6. **Maintainability**: Recommend changes that would make the code easier to maintain and extend\n\nProvide specific, actionable suggestions while ensuring the existing functionality remains intact. Format your response with clear sections for each type of improvement and include code examples where helpful."}}