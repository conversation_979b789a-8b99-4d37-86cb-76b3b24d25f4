# Project Structure

## Root Level Files
- `config.php` - Main configuration with database, SMTP, and feature settings
- `login.php`, `register.php`, `forgot-password.php`, `reset-password.php` - Authentication pages
- `verify-email.php` - Email verification handler
- `composer.json` - PHP dependencies
- `robots.txt` - SEO configuration

## Core Directories

### `/classes` - MVC Architecture
- **Models** (`/models`): Extend `BaseModel` with Active Record pattern
  - `BaseModel.php` - Abstract base with CRUD operations, validation, transactions
  - Entity models: `User.php`, `Transaction.php`, `Deposit.php`, etc.
- **Controllers** (`/controllers`): Handle HTTP requests and business logic
  - `AuthController.php` - Authentication workflows
- **Views** (`/views`): Template rendering with `BaseView` pattern
  - `BaseView.php` - Abstract base with head/body/scripts structure
- **Services** (`/services`): Business logic and external integrations
  - `AuthenticationManager.php`, `SessionManager.php`, `CSRFProtection.php`
- **Validators** (`/validators`): Input validation and sanitization
- **Config** (`/config`): Routing and configuration classes

### `/includes` - Shared Utilities
- `db_connect.php` - Database connection with PDO
- `functions.php` - Security, authentication, and utility functions
- `email_functions.php` - Email sending functionality

### `/database` - Data Management
- `schema.sql` - Complete database schema with relationships
- `seed.sql` - Sample data for development
- `migrate.php` - Custom migration runner
- `backup.php` - Database backup utility
- `MigrationLogger.php` - Migration tracking

### User Interfaces
- `/user` - User dashboard and account management
- `/admin` - Admin panel for platform management
- `/superadmin` - System administration interface
- `/public/auth` - Public authentication assets

### `/assets` - Static Resources
- `/css` - Custom stylesheets
- `/js` - JavaScript files
- `/images` - Image assets
- `/uploads` - User-uploaded files

### `/test_folder` - Testing Suite
- Complete test files for authentication, database, SMTP, and models
- `verify_setup.php` - System verification script

## Naming Conventions

### Files and Classes
- **Models**: PascalCase (`User.php`, `TradingPlan.php`)
- **Controllers**: PascalCase with "Controller" suffix (`AuthController.php`)
- **Views**: PascalCase with "View" suffix (`LoginView.php`)
- **Services**: PascalCase with descriptive names (`AuthenticationManager.php`)

### Database
- **Tables**: snake_case plural (`users`, `trading_plans`, `support_tickets`)
- **Columns**: snake_case (`first_name`, `created_at`, `email_verified`)
- **Foreign Keys**: `{table}_id` format (`user_id`, `plan_id`)

### URLs and Routes
- Kebab-case for multi-word endpoints (`forgot-password.php`, `reset-password.php`)
- Directory-based routing (`/user/dashboard/`, `/admin/users/`)

## Architecture Patterns

### MVC Implementation
- **Models**: Active Record pattern with `BaseModel` inheritance
- **Controllers**: Static methods for request handling
- **Views**: Template Method pattern with `BaseView` base class

### Security Patterns
- CSRF tokens for all forms
- Input sanitization in `functions.php`
- Role-based access control with middleware methods
- Session management with timeout and extension

### Database Patterns
- PDO with prepared statements
- Transaction support in `BaseModel`
- Audit logging for all critical operations
- Soft deletes where appropriate (via status fields)