<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';
require_once __DIR__ . '/traits/SecurityTrait.php';
require_once __DIR__ . '/../services/CSRFProtection.php';

/**
 * ProfileView - User profile management interface
 * 
 * Handles user profile display and editing functionality including
 * personal information, profile picture, password changes, and security settings.
 * 
 * @package Views
 * <AUTHOR> Trading Platform
 * @version 1.0.0
 */
class ProfileView extends BaseView {
    use StatusColorTrait, SecurityTrait;
    
    /** @var User User object containing profile information */
    private $user;
    
    public function __construct($user) {
        parent::__construct();
        $this->user = $user;
        $this->setTitle('Profile - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/profile.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1 class="h3 mb-4">My Profile</h1>
                </div>
            </div>
            
            <div class="row">
                <?php $this->renderProfilePictureSection(); ?>
                <?php $this->renderPersonalInformationSection(); ?>
            </div>
            
            <div class="row">
                <?php $this->renderPasswordChangeSection(); ?>
                <?php $this->renderAccountSecuritySection(); ?>
            </div>
            
            <?php $this->renderToastMessages(); ?>
        </div>
        <?php
    }
    
    private function renderProfilePictureSection() {
        ?>
        <!-- Profile Picture Section -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Profile Picture</h5>
                </div>
                <div class="card-body text-center">
                    <div class="profile-picture-container mb-3">
                        <?php if ($this->user->profile_picture && file_exists($this->user->profile_picture)): ?>
                            <img src="<?php echo $this->safeOutput('/' . $this->user->profile_picture); ?>" 
                                 alt="Profile Picture" class="profile-picture">
                        <?php else: ?>
                            <div class="profile-picture-placeholder">
                                <i class="fas fa-user fa-4x text-muted"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <form id="profilePictureForm" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                        <div class="mb-3">
                            <input type="file" class="form-control" id="profilePictureInput" 
                                   name="profile_picture" accept="image/*" required>
                            <div class="form-text">Max size: 2MB. Formats: JPEG, PNG, GIF</div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Upload Picture
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderPersonalInformationSection() {
        ?>
        <!-- Profile Information -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Personal Information</h5>
                </div>
                <div class="card-body">
                    <form id="profileForm">
                        <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo $this->safeOutput($this->user->first_name); ?>" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo $this->safeOutput($this->user->last_name); ?>" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" 
                                       value="<?php echo $this->safeOutput($this->user->email); ?>" readonly>
                                <div class="form-text">Email cannot be changed. Contact support if needed.</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" 
                                       value="<?php echo $this->safeOutput($this->user->username); ?>" readonly>
                                <div class="form-text">Username cannot be changed.</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo $this->safeOutput($this->user->phone ?? ''); ?>">
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <?php $this->renderAccountInformation(); ?>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Profile
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderAccountInformation() {
        ?>
        <div class="mb-3">
            <label class="form-label">Account Information</label>
            <div class="row">
                <div class="col-md-4">
                    <small class="text-muted">Member Since:</small><br>
                    <strong><?php echo $this->formatDate($this->user->created_at); ?></strong>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">Last Login:</small><br>
                    <strong><?php echo $this->formatDate($this->user->last_login, 'M j, Y g:i A'); ?></strong>
                </div>
                <div class="col-md-4">
                    <small class="text-muted">Account Status:</small><br>
                    <span class="badge bg-<?php echo $this->getStatusColor($this->user->status); ?>">
                        <?php echo $this->safeOutput(ucfirst($this->user->status)); ?>
                    </span>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderPasswordChangeSection() {
        ?>
        <!-- Change Password Section -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Change Password</h5>
                </div>
                <div class="card-body">
                    <form id="passwordForm">
                        <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">Current Password *</label>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">New Password *</label>
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" required>
                            <div class="form-text">Minimum 6 characters</div>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password *</label>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderAccountSecuritySection() {
        ?>
        <!-- Account Security -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Account Security</h5>
                </div>
                <div class="card-body">
                    <?php $this->renderSecurityItem(
                        'Email Verification',
                        'Verify your email address',
                        $this->user->email_verified ? 'Verified' : 'Unverified',
                        $this->user->email_verified ? 'success' : 'warning'
                    ); ?>
                    
                    <?php $this->renderSecurityItem(
                        'Two-Factor Authentication',
                        'Add extra security to your account',
                        $this->user->two_fa_enabled ? 'Enabled' : 'Disabled',
                        $this->user->two_fa_enabled ? 'success' : 'secondary'
                    ); ?>
                    
                    <?php $this->renderSecurityItem(
                        'KYC Verification',
                        'Identity verification status',
                        ucfirst($this->user->kyc_status),
                        $this->getKycStatusColor($this->user->kyc_status),
                        false
                    ); ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderSecurityItem($title, $description, $status, $statusColor, $marginBottom = true) {
        ?>
        <div class="security-item <?php echo $marginBottom ? 'mb-3' : ''; ?>">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong><?php echo $this->safeOutput($title); ?></strong>
                    <div class="text-muted small"><?php echo $this->safeOutput($description); ?></div>
                </div>
                <span class="badge bg-<?php echo $statusColor; ?>">
                    <?php echo $this->safeOutput($status); ?>
                </span>
            </div>
        </div>
        <?php
    }
    
    private function renderToastMessages() {
        ?>
        <!-- Success/Error Messages -->
        <div class="toast-container position-fixed bottom-0 end-0 p-3">
            <div id="successToast" class="toast" role="alert">
                <div class="toast-header bg-success text-white">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong class="me-auto">Success</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body"></div>
            </div>
            
            <div id="errorToast" class="toast" role="alert">
                <div class="toast-header bg-danger text-white">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong class="me-auto">Error</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body"></div>
            </div>
        </div>

        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/profile.js"></script>
        <?php
    }

}
?>