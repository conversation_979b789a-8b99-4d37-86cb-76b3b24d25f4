<?php
/**
 * Navigation Component
 * Renders navigation menus for different user roles
 */
class NavigationComponent {
    
    /**
     * Render user navigation
     */
    public static function renderUserNavigation($navigationItems, $currentPath = null) {
        $html = '<ul class="nav flex-column">';
        
        foreach ($navigationItems as $item) {
            $activeClass = $item['active'] ? 'active' : '';
            $html .= sprintf(
                '<li class="nav-item">
                    <a class="nav-link %s" href="%s">
                        <i class="%s me-2"></i>
                        %s
                    </a>
                </li>',
                htmlspecialchars($activeClass),
                htmlspecialchars($item['url']),
                htmlspecialchars($item['icon']),
                htmlspecialchars($item['label'])
            );
        }
        
        // Add logout link
        $html .= '<li class="nav-item mt-3">
                    <a class="nav-link text-warning" href="/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        Logout
                    </a>
                  </li>';
        
        $html .= '</ul>';
        
        return $html;
    }
    
    /**
     * Render breadcrumb navigation
     */
    public static function renderBreadcrumb($items) {
        if (empty($items)) {
            return '';
        }
        
        $html = '<nav aria-label="breadcrumb">
                   <ol class="breadcrumb">';
        
        foreach ($items as $index => $item) {
            $isLast = ($index === count($items) - 1);
            
            if ($isLast) {
                $html .= sprintf(
                    '<li class="breadcrumb-item active" aria-current="page">%s</li>',
                    htmlspecialchars($item['label'])
                );
            } else {
                $html .= sprintf(
                    '<li class="breadcrumb-item"><a href="%s">%s</a></li>',
                    htmlspecialchars($item['url']),
                    htmlspecialchars($item['label'])
                );
            }
        }
        
        $html .= '</ol></nav>';
        
        return $html;
    }
    
    /**
     * Render user dropdown menu
     */
    public static function renderUserDropdown($user) {
        return sprintf(
            '<div class="dropdown">
                <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <div class="user-avatar me-2">%s</div>
                    %s
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/user/profile/"><i class="fas fa-user me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="/user/settings/"><i class="fas fa-cog me-2"></i>Settings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                </ul>
            </div>',
            strtoupper(substr($user['first_name'], 0, 1)),
            htmlspecialchars($user['first_name'])
        );
    }
}