<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $tradeId = intval($_GET['id'] ?? 0);
    
    if (!$tradeId) {
        echo json_encode(['success' => false, 'message' => 'Trade ID is required']);
        exit;
    }
    
    // Get the trade with user and plan information
    $sql = "SELECT 
                t.*,
                u.username,
                u.first_name,
                u.last_name,
                tp.name as plan_name
            FROM trades t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN trading_plans tp ON t.plan_id = tp.id
            WHERE t.id = ?";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$tradeId]);
    $trade = $stmt->fetch();
    
    if (!$trade) {
        echo json_encode(['success' => false, 'message' => 'Trade not found']);
        exit;
    }
    
    // Format the opened_at datetime for the datetime-local input
    $trade['opened_at'] = date('Y-m-d\TH:i', strtotime($trade['opened_at']));
    
    // Format closed_at if it exists
    if ($trade['closed_at']) {
        $trade['closed_at'] = date('Y-m-d\TH:i', strtotime($trade['closed_at']));
    }
    
    echo json_encode([
        'success' => true,
        'trade' => $trade
    ]);
    
} catch (Exception $e) {
    error_log("Get trade details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading trade details'
    ]);
}
?>