<?php
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/views/SuperAdminSystemSettingsView.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    header('Location: ../login.php');
    exit;
}

$pageTitle = 'System Settings';
$view = new SuperAdminSystemSettingsView();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        $currentUser = getCurrentUser();
        
        switch ($action) {
            case 'update_general':
                $settings = [
                    'site_name' => $_POST['site_name'] ?? '',
                    'site_currency' => $_POST['site_currency'] ?? 'USD',
                    'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                    'base_url' => $_POST['base_url'] ?? '',
                    'contact_email' => $_POST['contact_email'] ?? '',
                    'contact_phone' => $_POST['contact_phone'] ?? '',
                    'company_address' => $_POST['company_address'] ?? ''
                ];
                
                if (SystemSetting::updateMultiple($settings, $currentUser['id'])) {
                    $response = ['success' => true, 'message' => 'General settings updated successfully'];
                } else {
                    throw new Exception('Failed to update general settings');
                }
                break;
                
            case 'update_financial':
                $settings = [
                    'registration_bonus' => floatval($_POST['registration_bonus'] ?? 0),
                    'deposit_bonus_percent' => floatval($_POST['deposit_bonus_percent'] ?? 0),
                    'min_withdrawal_amount' => floatval($_POST['min_withdrawal_amount'] ?? 0),
                    'max_withdrawal_amount' => floatval($_POST['max_withdrawal_amount'] ?? 0),
                    'withdrawal_fee_percent' => floatval($_POST['withdrawal_fee_percent'] ?? 0)
                ];
                
                if (SystemSetting::updateMultiple($settings, $currentUser['id'])) {
                    $response = ['success' => true, 'message' => 'Financial settings updated successfully'];
                } else {
                    throw new Exception('Failed to update financial settings');
                }
                break;
                
            case 'update_security':
                $settings = [
                    'email_verification_required' => isset($_POST['email_verification_required']),
                    'two_fa_enforcement' => isset($_POST['two_fa_enforcement']),
                    'kyc_verification_required' => isset($_POST['kyc_verification_required']),
                    'max_login_attempts' => intval($_POST['max_login_attempts'] ?? 5),
                    'account_lockout_minutes' => intval($_POST['account_lockout_minutes'] ?? 30)
                ];
                
                if (SystemSetting::updateMultiple($settings, $currentUser['id'])) {
                    $response = ['success' => true, 'message' => 'Security settings updated successfully'];
                } else {
                    throw new Exception('Failed to update security settings');
                }
                break;
                
            case 'update_features':
                $settings = [
                    'email_notifications' => isset($_POST['email_notifications']),
                    'sms_notifications' => isset($_POST['sms_notifications']),
                    'maintenance_mode' => isset($_POST['maintenance_mode']),
                    'debug_mode' => isset($_POST['debug_mode'])
                ];
                
                if (SystemSetting::updateMultiple($settings, $currentUser['id'])) {
                    $response = ['success' => true, 'message' => 'Feature settings updated successfully'];
                } else {
                    throw new Exception('Failed to update feature settings');
                }
                break;
                
            case 'update_appearance':
                $settings = [
                    'primary_color' => $_POST['primary_color'] ?? '#007bff',
                    'secondary_color' => $_POST['secondary_color'] ?? '#6c757d'
                ];
                
                // Handle main logo upload if provided
                if (isset($_FILES['logo_file']) && $_FILES['logo_file']['error'] === UPLOAD_ERR_OK) {
                    $logoUrl = $view->handleLogoUpload($_FILES['logo_file']);
                    if ($logoUrl) {
                        $settings['logo_url'] = $logoUrl;
                    }
                }

                // Handle dark logo upload if provided
                if (isset($_FILES['logo_dark_file']) && $_FILES['logo_dark_file']['error'] === UPLOAD_ERR_OK) {
                    $logoDarkUrl = $view->handleLogoUpload($_FILES['logo_dark_file']);
                    if ($logoDarkUrl) {
                        $settings['logo_dark_url'] = $logoDarkUrl;
                    }
                }

                // Handle light logo upload if provided
                if (isset($_FILES['logo_light_file']) && $_FILES['logo_light_file']['error'] === UPLOAD_ERR_OK) {
                    $logoLightUrl = $view->handleLogoUpload($_FILES['logo_light_file']);
                    if ($logoLightUrl) {
                        $settings['logo_light_url'] = $logoLightUrl;
                    }
                }

                // Handle favicon upload if provided
                if (isset($_FILES['favicon_file']) && $_FILES['favicon_file']['error'] === UPLOAD_ERR_OK) {
                    $faviconUrl = $view->handleFaviconUpload($_FILES['favicon_file']);
                    if ($faviconUrl) {
                        $settings['favicon_url'] = $faviconUrl;
                    }
                }
                
                if (SystemSetting::updateMultiple($settings, $currentUser['id'])) {
                    $response = ['success' => true, 'message' => 'Appearance settings updated successfully'];
                } else {
                    throw new Exception('Failed to update appearance settings');
                }
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for regular form submissions
    $_SESSION['settings_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get response from session if available
$response = $_SESSION['settings_response'] ?? null;
unset($_SESSION['settings_response']);

// Get all settings grouped by category
$settingsData = SystemSetting::getGroupedByCategory();

// Include layout
require_once __DIR__ . '/../../layouts/superadmin_layout.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 px-0">
            <div class="sidebar d-flex flex-column p-3">
                <div class="text-center mb-4">
                    <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="navbar-brand img-fluid" style="max-height: 50px;">
                    <div class="superadmin-badge mt-2">SUPER ADMIN</div>
                </div>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/superadmin/settings/">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/admins/">
                            <i class="fas fa-user-shield me-2"></i>Admin Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/email-templates/">
                            <i class="fas fa-envelope-open-text me-2"></i>Email Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/audit/">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/system/">
                            <i class="fas fa-server me-2"></i>System Health
                        </a>
                    </li>
                </ul>
                
                <div class="mt-auto">
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($user['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/user/profile/"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">System Settings</h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportSettings()">
                            <i class="fas fa-download me-1"></i>Export Settings
                        </button>
                        <button class="btn btn-outline-secondary" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-1"></i>Reset to Defaults
                        </button>
                    </div>
                </div>
                
                <?php if ($response): ?>
                    <div class="alert alert-<?php echo $response['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($response['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php echo $view->render($settingsData); ?>
            </div>
        </div>
    </div>
</div>

<!-- Include custom CSS and JS -->
<link href="/assets/css/superadmin-system-settings.css" rel="stylesheet">
<script src="/assets/js/superadmin-system-settings.js"></script>

<?php require_once __DIR__ . '/../../layouts/footer.php'; ?>