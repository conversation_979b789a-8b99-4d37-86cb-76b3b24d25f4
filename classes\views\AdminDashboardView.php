<?php

require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

class AdminDashboardView extends BaseView {
    use StatusColorTrait;
    
    private $stats;
    private $recentActivity;
    
    public function __construct($stats = [], $recentActivity = []) {
        parent::__construct();
        $this->stats = $stats;
        $this->recentActivity = $recentActivity;
        $this->setTitle('Admin Dashboard - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link href="/assets/css/admin-dashboard.css" rel="stylesheet">
        <?php
    }
    
    public function renderContent() {
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard Overview
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Users
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($this->stats['total_users'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Active Users
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($this->stats['active_users'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Total Deposits
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        $<?php echo number_format($this->stats['total_deposits'] ?? 0, 2); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Pending Actions
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo ($this->stats['pending_deposits'] ?? 0) + ($this->stats['pending_withdrawals'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row">
                <div class="col-lg-12 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/users/" class="btn btn-primary w-100">
                                        <i class="fas fa-users me-2"></i>
                                        Manage Users
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/deposits/" class="btn btn-success w-100">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        Review Deposits
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/withdrawals/" class="btn btn-warning w-100">
                                        <i class="fas fa-minus-circle me-2"></i>
                                        Review Withdrawals
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/plans/" class="btn btn-info w-100">
                                        <i class="fas fa-chart-line me-2"></i>
                                        Trading Plans
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/support/" class="btn btn-secondary w-100">
                                        <i class="fas fa-headset me-2"></i>
                                        Support Tickets
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="/admin/reports/" class="btn btn-dark w-100">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderBody() {
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard Overview
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshStats()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Users
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($this->stats['total_users'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Active Users
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($this->stats['active_users'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        Total Deposits
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        $<?php echo number_format($this->stats['total_deposits'] ?? 0, 2); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        Pending Actions
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo ($this->stats['pending_deposits'] ?? 0) + ($this->stats['pending_withdrawals'] ?? 0); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions and Recent Activity -->
            <div class="row">
                <!-- Quick Actions -->
                <div class="col-lg-4 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-bolt me-2"></i>
                                Quick Actions
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/admin/users/" class="btn btn-primary">
                                    <i class="fas fa-users me-2"></i>
                                    Manage Users
                                </a>
                                <a href="/admin/deposits/" class="btn btn-success">
                                    <i class="fas fa-plus-circle me-2"></i>
                                    Review Deposits
                                    <?php if (($this->stats['pending_deposits'] ?? 0) > 0): ?>
                                        <span class="badge bg-light text-dark ms-2">
                                            <?php echo $this->stats['pending_deposits']; ?>
                                        </span>
                                    <?php endif; ?>
                                </a>
                                <a href="/admin/withdrawals/" class="btn btn-warning">
                                    <i class="fas fa-minus-circle me-2"></i>
                                    Review Withdrawals
                                    <?php if (($this->stats['pending_withdrawals'] ?? 0) > 0): ?>
                                        <span class="badge bg-light text-dark ms-2">
                                            <?php echo $this->stats['pending_withdrawals']; ?>
                                        </span>
                                    <?php endif; ?>
                                </a>
                                <a href="/admin/plans/" class="btn btn-info">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Trading Plans
                                </a>
                                <a href="/admin/support/" class="btn btn-secondary">
                                    <i class="fas fa-headset me-2"></i>
                                    Support Tickets
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="col-lg-8 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-history me-2"></i>
                                Recent Activity
                            </h6>
                            <a href="/admin/reports/" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (empty($this->recentActivity)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <p>No recent activity</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Type</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($this->recentActivity as $activity): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="avatar-sm me-2">
                                                                <div class="avatar-title bg-light text-primary rounded-circle">
                                                                    <?php echo strtoupper(substr($activity['first_name'] ?? 'U', 0, 1)); ?>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold">
                                                                    <?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?>
                                                                </div>
                                                                <small class="text-muted">
                                                                    @<?php echo htmlspecialchars($activity['username']); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $this->getTypeColor($activity['type']); ?>">
                                                            <?php echo ucfirst(str_replace('_', ' ', $activity['type'])); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php if ($activity['amount'] != 0): ?>
                                                            <span class="<?php echo $activity['amount'] > 0 ? 'text-success' : 'text-danger'; ?>">
                                                                <?php echo $activity['amount'] > 0 ? '+' : ''; ?>$<?php echo number_format($activity['amount'], 2); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $this->getStatusColor($activity['status']); ?>">
                                                            <?php echo ucfirst($activity['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">
                                                            <?php echo date('M j, Y g:i A', strtotime($activity['created_at'])); ?>
                                                        </small>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Statistics -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-chart-bar me-2"></i>
                                Financial Overview
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Total Deposits
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-success">
                                            $<?php echo number_format($this->stats['total_deposits'] ?? 0, 2); ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            Total Withdrawals
                                        </div>
                                        <div class="h6 mb-0 font-weight-bold text-danger">
                                            $<?php echo number_format($this->stats['total_withdrawals'] ?? 0, 2); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    Net Flow
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-primary">
                                    $<?php echo number_format(($this->stats['total_deposits'] ?? 0) - ($this->stats['total_withdrawals'] ?? 0), 2); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-6 mb-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-user-plus me-2"></i>
                                User Growth
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    New Registrations (7 days)
                                </div>
                                <div class="h4 mb-0 font-weight-bold text-info">
                                    <?php echo number_format($this->stats['recent_registrations'] ?? 0); ?>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center">
                                        <small class="text-muted">Active Rate</small>
                                        <div class="font-weight-bold">
                                            <?php 
                                            $activeRate = ($this->stats['total_users'] ?? 0) > 0 
                                                ? (($this->stats['active_users'] ?? 0) / $this->stats['total_users']) * 100 
                                                : 0;
                                            echo number_format($activeRate, 1) . '%';
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center">
                                        <small class="text-muted">Growth Rate</small>
                                        <div class="font-weight-bold text-success">
                                            <?php 
                                            $growthRate = ($this->stats['total_users'] ?? 0) > 0 
                                                ? (($this->stats['recent_registrations'] ?? 0) / $this->stats['total_users']) * 100 
                                                : 0;
                                            echo '+' . number_format($growthRate, 1) . '%';
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/admin-dashboard.js"></script>
        <?php
    }
    
    private function getTypeColor($type) {
        $colors = [
            'deposit' => 'success',
            'withdrawal' => 'warning',
            'bonus' => 'info',
            'trade' => 'primary',
            'admin_credit' => 'success',
            'admin_debit' => 'danger',
            'admin_action' => 'secondary'
        ];
        
        return $colors[$type] ?? 'secondary';
    }
}