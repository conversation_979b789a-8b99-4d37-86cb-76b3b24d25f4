/* Support Tickets Styles */

.support-tickets-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Statistics Cards */
.stats-card {
    transition: transform 0.2s ease-in-out;
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Filter Buttons */
.filter-buttons .btn-group .btn {
    border-radius: 0;
    border-right: none;
    transition: all 0.2s ease;
}

.filter-buttons .btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.filter-buttons .btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    border-right: 1px solid;
}

.filter-buttons .btn-group .btn:hover {
    transform: translateY(-1px);
}

/* Tickets Table */
.tickets-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tickets-table .table {
    margin-bottom: 0;
}

.tickets-table .table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.tickets-table .table tbody tr {
    transition: background-color 0.2s ease;
}

.tickets-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

.tickets-table .table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
}

/* Priority Badges */
.badge.priority-low {
    background-color: #28a745;
}

.badge.priority-medium {
    background-color: #ffc107;
    color: #212529;
}

.badge.priority-high {
    background-color: #fd7e14;
}

.badge.priority-urgent {
    background-color: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Status Badges */
.badge.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge.status-answered {
    background-color: #17a2b8;
}

.badge.status-closed {
    background-color: #28a745;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state h4 {
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 2rem;
}

/* Pagination */
.pagination {
    margin-top: 2rem;
}

.pagination .page-link {
    color: #007bff;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-link:hover {
    color: #0056b3;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card h4 {
        font-size: 1.5rem;
    }
    
    .filter-buttons .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .filter-buttons .btn-group .btn {
        border-radius: 0.375rem !important;
        border-right: 1px solid;
        margin-bottom: 0.25rem;
    }
    
    .tickets-table {
        font-size: 0.875rem;
    }
    
    .tickets-table .table td,
    .tickets-table .table th {
        padding: 0.5rem 0.25rem;
    }
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Auto-refresh indicator */
.auto-refresh-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auto-refresh-indicator.show {
    opacity: 1;
}

.auto-refresh-indicator i {
    animation: spin 1s linear infinite;
}