<?php
require_once __DIR__ . '/../models/User.php';

/**
 * Two-Factor Authentication Service
 * Handles TOTP (Time-based One-Time Password) authentication
 */
class TwoFactorAuthService {
    private static $instance = null;
    private $secretLength = 32;
    private $codeLength = 6;
    private $timeWindow = 30; // seconds
    private $tolerance = 1; // Allow 1 time window before/after
    
    private function __construct() {}
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Generate a new secret for 2FA setup
     */
    public function generateSecret() {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < $this->secretLength; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $secret;
    }
    
    /**
     * Generate QR code URL for authenticator apps
     */
    public function getQRCodeUrl($secret, $userEmail, $issuer = 'Coinage Trading') {
        $label = urlencode($issuer . ':' . $userEmail);
        $issuer = urlencode($issuer);
        
        $otpauth = "otpauth://totp/{$label}?secret={$secret}&issuer={$issuer}&algorithm=SHA1&digits={$this->codeLength}&period={$this->timeWindow}";
        
        // Using Google Charts API for QR code generation
        return "https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=" . urlencode($otpauth);
    }
    
    /**
     * Verify TOTP code
     */
    public function verifyCode($secret, $code, $timestamp = null) {
        if ($timestamp === null) {
            $timestamp = time();
        }
        
        // Remove any spaces or formatting from code
        $code = preg_replace('/\s+/', '', $code);
        
        if (strlen($code) !== $this->codeLength || !ctype_digit($code)) {
            return false;
        }
        
        // Check current time window and adjacent windows for tolerance
        for ($i = -$this->tolerance; $i <= $this->tolerance; $i++) {
            $timeSlice = intval($timestamp / $this->timeWindow) + $i;
            $calculatedCode = $this->generateCode($secret, $timeSlice);
            
            if (hash_equals($calculatedCode, $code)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Generate TOTP code for given time slice
     */
    private function generateCode($secret, $timeSlice) {
        // Convert secret from Base32
        $key = $this->base32Decode($secret);
        
        // Pack time slice as 64-bit big-endian
        $time = pack('N*', 0) . pack('N*', $timeSlice);
        
        // Generate HMAC-SHA1
        $hash = hash_hmac('sha1', $time, $key, true);
        
        // Dynamic truncation
        $offset = ord($hash[19]) & 0xf;
        $code = (
            ((ord($hash[$offset + 0]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % pow(10, $this->codeLength);
        
        return str_pad($code, $this->codeLength, '0', STR_PAD_LEFT);
    }
    
    /**
     * Base32 decode function
     */
    private function base32Decode($input) {
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $v = 0;
        $vbits = 0;
        
        for ($i = 0; $i < strlen($input); $i++) {
            $value = strpos($alphabet, $input[$i]);
            if ($value === false) continue;
            
            $v <<= 5;
            $v += $value;
            $vbits += 5;
            
            if ($vbits >= 8) {
                $output .= chr(($v >> ($vbits - 8)) & 255);
                $vbits -= 8;
            }
        }
        
        return $output;
    }
    
    /**
     * Enable 2FA for user
     */
    public function enable2FA($userId, $secret, $verificationCode) {
        // Verify the code first
        if (!$this->verifyCode($secret, $verificationCode)) {
            return ['success' => false, 'message' => 'Invalid verification code'];
        }
        
        try {
            $db = Database::getInstance()->getConnection();
            
            // Check if user exists
            $stmt = $db->prepare("SELECT id FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            if (!$stmt->fetch()) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Update user with 2FA settings
            $stmt = $db->prepare("UPDATE users SET two_fa_enabled = ?, two_fa_secret = ? WHERE id = ?");
            if ($stmt->execute([1, $secret, $userId])) {
                // Generate backup codes
                $backupCodes = $this->generateBackupCodes($userId);
                
                // Log security event
                $this->logSecurityEvent('2fa_enabled', $userId);
                
                return [
                    'success' => true, 
                    'message' => '2FA enabled successfully',
                    'backup_codes' => $backupCodes
                ];
            }
            
            return ['success' => false, 'message' => 'Failed to enable 2FA'];
            
        } catch (Exception $e) {
            error_log("2FA Enable Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'System error occurred'];
        }
    }
    
    /**
     * Disable 2FA for user
     */
    public function disable2FA($userId, $currentPassword, $twoFACode = null) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get user data
            $stmt = $db->prepare("SELECT password, two_fa_enabled, two_fa_secret FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$userData) {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Verify current password
            if (!password_verify($currentPassword, $userData['password'])) {
                return ['success' => false, 'message' => 'Invalid current password'];
            }
            
            // If 2FA is enabled, verify the code
            if ($userData['two_fa_enabled'] && $twoFACode) {
                if (!$this->verifyCode($userData['two_fa_secret'], $twoFACode)) {
                    return ['success' => false, 'message' => 'Invalid 2FA code'];
                }
            }
            
            // Disable 2FA
            $stmt = $db->prepare("UPDATE users SET two_fa_enabled = ?, two_fa_secret = ? WHERE id = ?");
            if ($stmt->execute([0, null, $userId])) {
                // Remove backup codes
                $this->removeBackupCodes($userId);
                
                // Log security event
                $this->logSecurityEvent('2fa_disabled', $userId);
                
                return ['success' => true, 'message' => '2FA disabled successfully'];
            }
            
            return ['success' => false, 'message' => 'Failed to disable 2FA'];
            
        } catch (Exception $e) {
            error_log("2FA Disable Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'System error occurred'];
        }
    }
    
    /**
     * Verify 2FA during login
     */
    public function verifyLogin($userId, $code, $isBackupCode = false) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get user 2FA data
            $stmt = $db->prepare("SELECT two_fa_enabled, two_fa_secret FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$userData || !$userData['two_fa_enabled']) {
                return ['success' => false, 'message' => '2FA not enabled for this user'];
            }
            
            if ($isBackupCode) {
                return $this->verifyBackupCode($userId, $code);
            }
            
            // Verify TOTP code
            if ($this->verifyCode($userData['two_fa_secret'], $code)) {
                // Log successful 2FA verification
                $this->logSecurityEvent('2fa_login_success', $userId);
                return ['success' => true, 'message' => '2FA verification successful'];
            }
            
            // Log failed 2FA attempt
            $this->logSecurityEvent('2fa_login_failed', $userId);
            return ['success' => false, 'message' => 'Invalid 2FA code'];
            
        } catch (Exception $e) {
            error_log("2FA Login Verification Error: " . $e->getMessage());
            return ['success' => false, 'message' => 'System error occurred'];
        }
    }
    
    /**
     * Generate backup codes for 2FA
     */
    private function generateBackupCodes($userId, $count = 10) {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            $codes[] = strtoupper(bin2hex(random_bytes(4))); // 8-character hex codes
        }
        
        // Store backup codes in database (hashed)
        $this->storeBackupCodes($userId, $codes);
        
        return $codes;
    }
    
    /**
     * Store backup codes in database
     */
    private function storeBackupCodes($userId, $codes) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // First, remove any existing backup codes
            $stmt = $db->prepare("DELETE FROM user_backup_codes WHERE user_id = ?");
            $stmt->execute([$userId]);
            
            // Insert new backup codes
            $stmt = $db->prepare("INSERT INTO user_backup_codes (user_id, code_hash, created_at) VALUES (?, ?, NOW())");
            
            foreach ($codes as $code) {
                $codeHash = password_hash($code, PASSWORD_DEFAULT);
                $stmt->execute([$userId, $codeHash]);
            }
            
        } catch (Exception $e) {
            error_log("Error storing backup codes: " . $e->getMessage());
        }
    }
    
    /**
     * Verify backup code
     */
    private function verifyBackupCode($userId, $code) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("SELECT id, code_hash FROM user_backup_codes WHERE user_id = ? AND used_at IS NULL");
            $stmt->execute([$userId]);
            $backupCodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($backupCodes as $backupCode) {
                if (password_verify(strtoupper($code), $backupCode['code_hash'])) {
                    // Mark backup code as used
                    $updateStmt = $db->prepare("UPDATE user_backup_codes SET used_at = NOW() WHERE id = ?");
                    $updateStmt->execute([$backupCode['id']]);
                    
                    // Log backup code usage
                    $this->logSecurityEvent('backup_code_used', $userId);
                    
                    return ['success' => true, 'message' => 'Backup code verified successfully'];
                }
            }
            
            return ['success' => false, 'message' => 'Invalid backup code'];
            
        } catch (Exception $e) {
            error_log("Backup code verification error: " . $e->getMessage());
            return ['success' => false, 'message' => 'System error occurred'];
        }
    }
    
    /**
     * Remove backup codes for user
     */
    private function removeBackupCodes($userId) {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("DELETE FROM user_backup_codes WHERE user_id = ?");
            $stmt->execute([$userId]);
        } catch (Exception $e) {
            error_log("Error removing backup codes: " . $e->getMessage());
        }
    }
    
    /**
     * Get remaining backup codes count
     */
    public function getRemainingBackupCodes($userId) {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM user_backup_codes WHERE user_id = ? AND used_at IS NULL");
            $stmt->execute([$userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $result['count'] ?? 0;
        } catch (Exception $e) {
            error_log("Error getting backup codes count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Log security events
     */
    private function logSecurityEvent($event, $userId) {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'event' => $event,
                'user_id' => $userId,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ];
            
            $logEntry = json_encode($logData) . "\n";
            
            $logFile = __DIR__ . '/../../logs/2fa.log';
            if (!file_exists(dirname($logFile))) {
                mkdir(dirname($logFile), 0755, true);
            }
            
            file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            error_log("Error logging 2FA event: " . $e->getMessage());
        }
    }
    
    /**
     * Check if user has 2FA enabled
     */
    public function is2FAEnabled($userId) {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT two_fa_enabled FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return $userData && $userData['two_fa_enabled'];
        } catch (Exception $e) {
            error_log("Error checking 2FA status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get 2FA setup status for user
     */
    public function get2FAStatus($userId) {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT two_fa_enabled FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $userData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$userData) {
                return ['enabled' => false, 'backup_codes' => 0];
            }
            
            return [
                'enabled' => (bool)$userData['two_fa_enabled'],
                'backup_codes' => $this->getRemainingBackupCodes($userId)
            ];
        } catch (Exception $e) {
            error_log("Error getting 2FA status: " . $e->getMessage());
            return ['enabled' => false, 'backup_codes' => 0];
        }
    }
}
?>