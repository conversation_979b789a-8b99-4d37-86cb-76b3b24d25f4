/* Admin Dashboard Styles */

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    border: 1px solid #e3e6f0;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
}

.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.quick-action-btn {
    margin-bottom: 0.5rem;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.activity-table {
    font-size: 0.875rem;
}

.activity-table th {
    border-top: none;
    font-weight: 600;
    color: #5a5c69;
    background-color: #f8f9fc;
}

.activity-table td {
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

.financial-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.financial-overview .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.growth-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.growth-card .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.metric-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    font-weight: 600;
    opacity: 0.8;
}

.pending-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-card .h5 {
        font-size: 1.1rem;
    }
    
    .quick-action-btn {
        font-size: 0.875rem;
    }
    
    .activity-table {
        font-size: 0.8rem;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .card-header {
        background-color: #1a202c;
        border-bottom-color: #4a5568;
    }
    
    .text-gray-800 {
        color: #e2e8f0 !important;
    }
    
    .activity-table th {
        background-color: #1a202c;
        color: #e2e8f0;
    }
    
    .activity-table td {
        border-top-color: #4a5568;
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.refresh-btn {
    transition: transform 0.3s ease;
}

.refresh-btn:hover {
    transform: rotate(180deg);
}

/* Status indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-active {
    background-color: #1cc88a;
}

.status-pending {
    background-color: #f6c23e;
}

.status-suspended {
    background-color: #e74a3b;
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
    margin-top: 1rem;
}

/* Print styles */
@media print {
    .btn-toolbar,
    .quick-action-btn {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .stats-card {
        break-inside: avoid;
    }
}