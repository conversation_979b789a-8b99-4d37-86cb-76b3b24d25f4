<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/models/Deposit.php';
require_once '../../../classes/models/User.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/models/PaymentMethod.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    $depositId = (int)($_GET['id'] ?? 0);
    
    if ($depositId <= 0) {
        throw new Exception('Invalid deposit ID');
    }
    
    // Get deposit with full details
    $deposit = Deposit::getWithFullDetails($depositId);
    if (!$deposit) {
        throw new Exception('Deposit not found');
    }
    
    // Format the response data
    $responseData = [
        'id' => $deposit['id'],
        'amount' => (float)$deposit['amount'],
        'bonus_amount' => (float)($deposit['bonus_amount'] ?? 0),
        'status' => $deposit['status'],
        'created_at' => $deposit['created_at'],
        'approved_at' => $deposit['approved_at'],
        'rejected_at' => $deposit['rejected_at'],
        'admin_note' => $deposit['admin_note'],
        'rejection_reason' => $deposit['rejection_reason'],
        'user' => [
            'id' => $deposit['user_id'],
            'username' => $deposit['username'],
            'first_name' => $deposit['first_name'],
            'last_name' => $deposit['last_name'],
            'email' => $deposit['email'],
            'balance' => (float)$deposit['user_balance'],
            'bonus' => (float)$deposit['user_bonus']
        ],
        'payment_method' => [
            'id' => $deposit['payment_method_id'],
            'name' => $deposit['payment_method_name'],
            'type' => $deposit['payment_method_type']
        ],
        'trading_plan' => $deposit['plan_id'] ? [
            'id' => $deposit['plan_id'],
            'name' => $deposit['plan_name'],
            'minimum_amount' => (float)$deposit['plan_minimum_amount'],
            'maximum_amount' => (float)$deposit['plan_maximum_amount'],
            'bonus_percentage' => (float)$deposit['plan_bonus_percentage']
        ] : null,
        'admin_info' => [
            'approved_by' => $deposit['approved_by'],
            'rejected_by' => $deposit['rejected_by'],
            'approver_name' => $deposit['approver_name'],
            'rejecter_name' => $deposit['rejecter_name']
        ]
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $responseData
    ]);
    
} catch (Exception $e) {
    error_log("Deposit details error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>