<?php
require_once __DIR__ . '/EmailTemplateService.php';
require_once __DIR__ . '/SecurityAuditService.php';
require_once __DIR__ . '/../models/SystemSetting.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../../includes/email_functions.php';

/**
 * Email Notification Service
 * Comprehensive email notification system with security integration
 */
class EmailNotificationService {
    private static $instance = null;
    private $auditService;
    
    private function __construct() {
        $this->auditService = SecurityAuditService::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Send welcome email for new registrations
     */
    public function sendWelcomeEmail($userId, $userEmail, $firstName, $verificationToken = null) {
        try {
            $data = [
                'first_name' => $firstName,
                'email' => $userEmail,
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'site_url' => SystemSetting::getValue('site_url', 'https://coinage.trading'),
                'registration_bonus' => SystemSetting::getValue('registration_bonus', '0.00'),
                'verification_link' => $verificationToken ? 
                    SystemSetting::getValue('site_url', '') . '/verify-email.php?token=' . $verificationToken : null
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'welcome',
                $userEmail,
                $firstName,
                $data
            );
            
            // Log email event
            $this->auditService->logSystemEvent('email_sent', [
                'type' => 'welcome',
                'recipient' => $userEmail,
                'user_id' => $userId,
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Welcome email error: " . $e->getMessage());
            $this->auditService->logSystemEvent('email_error', [
                'type' => 'welcome',
                'recipient' => $userEmail,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Send email verification email
     */
    public function sendEmailVerification($userId, $userEmail, $firstName, $verificationToken) {
        try {
            $data = [
                'first_name' => $firstName,
                'email' => $userEmail,
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'verification_link' => SystemSetting::getValue('site_url', '') . '/verify-email.php?token=' . $verificationToken,
                'expiry_hours' => '24'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'email_verification',
                $userEmail,
                $firstName,
                $data
            );
            
            $this->auditService->logAuthEvent('email_verification_sent', $userId, [
                'recipient' => $userEmail,
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Email verification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordReset($userId, $userEmail, $firstName, $resetToken) {
        try {
            $data = [
                'first_name' => $firstName,
                'user_name' => $firstName,
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'reset_link' => SystemSetting::getValue('site_url', '') . '/reset-password.php?token=' . $resetToken,
                'expiry_hours' => '1'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'password_reset',
                $userEmail,
                $firstName,
                $data
            );
            
            $this->auditService->logAuthEvent('password_reset_sent', $userId, [
                'recipient' => $userEmail,
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Password reset email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send deposit confirmation email
     */
    public function sendDepositConfirmation($userId, $depositData) {
        try {
            // Get user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name, last_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            $data = [
                'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                'first_name' => $user['first_name'],
                'amount' => number_format($depositData['amount'], 2),
                'currency' => SystemSetting::getValue('currency', 'USD'),
                'transaction_id' => $depositData['id'],
                'payment_method' => $depositData['payment_method'] ?? 'N/A',
                'status' => ucfirst($depositData['status']),
                'date' => date('Y-m-d H:i:s', strtotime($depositData['created_at'])),
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'bonus_amount' => isset($depositData['bonus']) ? number_format($depositData['bonus'], 2) : '0.00'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'deposit_confirmation',
                $user['email'],
                $user['first_name'],
                $data
            );
            
            $this->auditService->logFinancialEvent('deposit_email_sent', $userId, [
                'deposit_id' => $depositData['id'],
                'amount' => $depositData['amount'],
                'recipient' => $user['email'],
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Deposit confirmation email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send withdrawal notification email
     */
    public function sendWithdrawalNotification($userId, $withdrawalData) {
        try {
            // Get user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name, last_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            $data = [
                'user_name' => $user['first_name'] . ' ' . $user['last_name'],
                'first_name' => $user['first_name'],
                'amount' => number_format($withdrawalData['amount'], 2),
                'currency' => SystemSetting::getValue('currency', 'USD'),
                'transaction_id' => $withdrawalData['id'],
                'payment_method' => $withdrawalData['payment_method'] ?? 'N/A',
                'status' => ucfirst($withdrawalData['status']),
                'date' => date('Y-m-d H:i:s', strtotime($withdrawalData['created_at'])),
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'processing_time' => $this->getProcessingTimeMessage($withdrawalData['status'])
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'withdrawal_notification',
                $user['email'],
                $user['first_name'],
                $data
            );
            
            $this->auditService->logFinancialEvent('withdrawal_email_sent', $userId, [
                'withdrawal_id' => $withdrawalData['id'],
                'amount' => $withdrawalData['amount'],
                'status' => $withdrawalData['status'],
                'recipient' => $user['email'],
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Withdrawal notification email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send 2FA enabled notification
     */
    public function send2FAEnabledNotification($userId) {
        try {
            // Get user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            $data = [
                'first_name' => $user['first_name'],
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'date' => date('Y-m-d H:i:s'),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                '2fa_enabled',
                $user['email'],
                $user['first_name'],
                $data
            );
            
            $this->auditService->logAuthEvent('2fa_email_sent', $userId, [
                'type' => 'enabled',
                'recipient' => $user['email'],
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("2FA enabled email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send 2FA disabled notification
     */
    public function send2FADisabledNotification($userId) {
        try {
            // Get user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            $data = [
                'first_name' => $user['first_name'],
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'date' => date('Y-m-d H:i:s'),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                '2fa_disabled',
                $user['email'],
                $user['first_name'],
                $data
            );
            
            $this->auditService->logAuthEvent('2fa_email_sent', $userId, [
                'type' => 'disabled',
                'recipient' => $user['email'],
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("2FA disabled email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send suspicious login alert
     */
    public function sendSuspiciousLoginAlert($userId, $loginDetails) {
        try {
            // Get user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception("User not found");
            }
            
            $data = [
                'first_name' => $user['first_name'],
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'login_time' => $loginDetails['time'] ?? date('Y-m-d H:i:s'),
                'ip_address' => $loginDetails['ip'] ?? 'Unknown',
                'location' => $loginDetails['location'] ?? 'Unknown',
                'user_agent' => $loginDetails['user_agent'] ?? 'Unknown',
                'secure_account_link' => SystemSetting::getValue('site_url', '') . '/user/security/'
            ];
            
            $result = EmailTemplateService::sendTemplateEmail(
                'suspicious_login',
                $user['email'],
                $user['first_name'],
                $data
            );
            
            $this->auditService->logAuthEvent('suspicious_login_email_sent', $userId, [
                'recipient' => $user['email'],
                'login_details' => $loginDetails,
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Suspicious login email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send support ticket notification to user
     */
    public function sendSupportTicketNotification($ticketId, $type = 'created') {
        try {
            // Get ticket and user data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("
                SELECT st.*, u.email, u.first_name, u.last_name 
                FROM support_tickets st 
                JOIN users u ON st.user_id = u.id 
                WHERE st.id = ?
            ");
            $stmt->execute([$ticketId]);
            $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$ticket) {
                throw new Exception("Ticket not found");
            }
            
            $data = [
                'user_name' => $ticket['first_name'] . ' ' . $ticket['last_name'],
                'first_name' => $ticket['first_name'],
                'ticket_id' => $ticket['id'],
                'subject' => $ticket['subject'],
                'status' => ucfirst($ticket['status']),
                'priority' => ucfirst($ticket['priority']),
                'category' => ucfirst($ticket['category']),
                'created_at' => date('Y-m-d H:i:s', strtotime($ticket['created_at'])),
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'ticket_url' => SystemSetting::getValue('site_url', '') . '/user/support/ticket.php?id=' . $ticket['id']
            ];
            
            // Add reply-specific data if this is a reply notification
            if ($type === 'replied' && !empty($ticket['admin_reply'])) {
                $data['admin_reply'] = $ticket['admin_reply'];
                $data['replied_at'] = date('Y-m-d H:i:s', strtotime($ticket['replied_at']));
            }
            
            $templateType = $type === 'replied' ? 'support_ticket_reply' : 'support_ticket_created';
            
            $result = EmailTemplateService::sendTemplateEmail(
                $templateType,
                $ticket['email'],
                $ticket['first_name'],
                $data
            );
            
            $this->auditService->logSystemEvent('support_email_sent', [
                'ticket_id' => $ticketId,
                'type' => $type,
                'recipient' => $ticket['email'],
                'success' => $result
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Support ticket email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send admin notification for new support ticket
     */
    public function sendAdminSupportNotification($ticketId) {
        try {
            // Get admin emails
            $adminEmails = $this->getAdminEmails();
            if (empty($adminEmails)) {
                return false;
            }
            
            // Get ticket data
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("
                SELECT st.*, u.first_name, u.last_name, u.email as user_email 
                FROM support_tickets st 
                JOIN users u ON st.user_id = u.id 
                WHERE st.id = ?
            ");
            $stmt->execute([$ticketId]);
            $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$ticket) {
                throw new Exception("Ticket not found");
            }
            
            $data = [
                'ticket_id' => $ticket['id'],
                'user_name' => $ticket['first_name'] . ' ' . $ticket['last_name'],
                'user_email' => $ticket['user_email'],
                'subject' => $ticket['subject'],
                'priority' => ucfirst($ticket['priority']),
                'category' => ucfirst($ticket['category']),
                'message' => $ticket['message'],
                'created_at' => date('Y-m-d H:i:s', strtotime($ticket['created_at'])),
                'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
                'admin_url' => SystemSetting::getValue('site_url', '') . '/admin/support/ticket.php?id=' . $ticket['id']
            ];
            
            $results = [];
            foreach ($adminEmails as $adminEmail) {
                $result = EmailTemplateService::sendTemplateEmail(
                    'admin_support_notification',
                    $adminEmail['email'],
                    $adminEmail['first_name'],
                    $data
                );
                $results[] = $result;
            }
            
            $this->auditService->logSystemEvent('admin_support_email_sent', [
                'ticket_id' => $ticketId,
                'admin_count' => count($adminEmails),
                'success' => !in_array(false, $results)
            ]);
            
            return !in_array(false, $results);
            
        } catch (Exception $e) {
            error_log("Admin support notification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get processing time message based on withdrawal status
     */
    private function getProcessingTimeMessage($status) {
        switch ($status) {
            case 'pending':
                return 'Your withdrawal is being reviewed and will be processed within 24-48 hours.';
            case 'processing':
                return 'Your withdrawal is currently being processed and should complete within 1-3 business days.';
            case 'completed':
                return 'Your withdrawal has been completed successfully.';
            case 'rejected':
                return 'Your withdrawal has been rejected. Please contact support for more information.';
            default:
                return 'Please check your account for the latest status updates.';
        }
    }
    
    /**
     * Get admin email addresses
     */
    private function getAdminEmails() {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE role IN ('admin', 'superadmin') AND status = 'active'");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting admin emails: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if email notifications are enabled
     */
    public function areNotificationsEnabled($type = 'general') {
        $settingKey = 'email_notifications_' . $type;
        return SystemSetting::getValue($settingKey, '1') === '1';
    }
    
    /**
     * Send bulk email notifications
     */
    public function sendBulkNotification($userIds, $templateType, $data = []) {
        try {
            $results = [];
            
            foreach ($userIds as $userId) {
                // Get user data
                $db = Database::getInstance()->getConnection();
                $stmt = $db->prepare("SELECT email, first_name FROM users WHERE id = ? AND status = 'active'");
                $stmt->execute([$userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    $result = EmailTemplateService::sendTemplateEmail(
                        $templateType,
                        $user['email'],
                        $user['first_name'],
                        $data
                    );
                    $results[$userId] = $result;
                }
            }
            
            $this->auditService->logSystemEvent('bulk_email_sent', [
                'template_type' => $templateType,
                'user_count' => count($userIds),
                'success_count' => count(array_filter($results)),
                'results' => $results
            ]);
            
            return $results;
            
        } catch (Exception $e) {
            error_log("Bulk email error: " . $e->getMessage());
            return false;
        }
    }
}
?>