/**
 * Withdrawal Request JavaScript
 * Handles dynamic form fields, validation, and user interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeWithdrawalForm();
});

function initializeWithdrawalForm() {
    const withdrawalMethodSelect = document.getElementById('withdrawal_method');
    const amountInput = document.getElementById('amount');
    const form = document.getElementById('withdrawalForm');
    
    if (withdrawalMethodSelect) {
        withdrawalMethodSelect.addEventListener('change', handleMethodChange);
        
        // Initialize if method is already selected
        if (withdrawalMethodSelect.value) {
            handleMethodChange();
        }
    }
    
    if (amountInput) {
        amountInput.addEventListener('input', validateAmount);
        amountInput.addEventListener('blur', formatAmount);
    }
    
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
    
    // Initialize tooltips
    initializeTooltips();
    
    // Setup real-time validation
    setupRealTimeValidation();
}

function handleMethodChange() {
    const select = document.getElementById('withdrawal_method');
    const container = document.getElementById('accountDetailsContainer');
    const fieldsContainer = document.getElementById('accountDetailsFields');
    
    if (!select.value) {
        container.style.display = 'none';
        fieldsContainer.innerHTML = '';
        return;
    }
    
    const selectedOption = select.options[select.selectedIndex];
    const requiredFields = JSON.parse(selectedOption.dataset.fields || '[]');
    const methodType = selectedOption.dataset.type;
    
    if (requiredFields.length > 0) {
        container.style.display = 'block';
        generateAccountDetailsFields(requiredFields, methodType);
    } else {
        container.style.display = 'none';
        fieldsContainer.innerHTML = '';
    }
}

function generateAccountDetailsFields(fields, methodType) {
    const container = document.getElementById('accountDetailsFields');
    container.innerHTML = '';
    
    fields.forEach(field => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'account-detail-field';
        
        const label = document.createElement('label');
        label.className = 'form-label';
        label.textContent = field.label;
        if (field.required) {
            label.innerHTML += ' <span class="text-danger">*</span>';
        }
        
        let input;
        
        switch (field.type) {
            case 'select':
                input = document.createElement('select');
                input.className = 'form-select';
                
                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = `Select ${field.label.toLowerCase()}`;
                input.appendChild(defaultOption);
                
                // Add options
                if (field.options) {
                    field.options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.label;
                        input.appendChild(optionElement);
                    });
                }
                break;
                
            case 'textarea':
                input = document.createElement('textarea');
                input.className = 'form-control';
                input.rows = 3;
                break;
                
            default:
                input = document.createElement('input');
                input.type = field.type || 'text';
                input.className = 'form-control';
        }
        
        input.name = `account_details[${field.name}]`;
        input.id = `account_${field.name}`;
        
        if (field.required) {
            input.required = true;
        }
        
        if (field.placeholder) {
            input.placeholder = field.placeholder;
        }
        
        if (field.pattern) {
            input.pattern = field.pattern;
        }
        
        if (field.maxlength) {
            input.maxLength = field.maxlength;
        }
        
        fieldDiv.appendChild(label);
        fieldDiv.appendChild(input);
        
        // Add help text if available
        if (field.help) {
            const helpText = document.createElement('div');
            helpText.className = 'form-text';
            helpText.textContent = field.help;
            fieldDiv.appendChild(helpText);
        }
        
        container.appendChild(fieldDiv);
    });
    
    // Add method-specific instructions
    addMethodInstructions(methodType, container);
}

function addMethodInstructions(methodType, container) {
    const instructionsDiv = document.createElement('div');
    instructionsDiv.className = 'alert alert-info mt-3';
    
    let instructions = '';
    
    switch (methodType) {
        case 'bank_transfer':
            instructions = '<i class="fas fa-info-circle"></i> <strong>Bank Transfer:</strong> Please ensure all bank details are accurate. Processing may take 1-3 business days.';
            break;
        case 'crypto':
            instructions = '<i class="fas fa-info-circle"></i> <strong>Cryptocurrency:</strong> Double-check your wallet address. Crypto transactions cannot be reversed.';
            break;
        case 'paypal':
            instructions = '<i class="fas fa-info-circle"></i> <strong>PayPal:</strong> Ensure the email address matches your verified PayPal account.';
            break;
        case 'skrill':
            instructions = '<i class="fas fa-info-circle"></i> <strong>Skrill:</strong> Use the email address associated with your Skrill account.';
            break;
        default:
            instructions = '<i class="fas fa-info-circle"></i> Please provide accurate account details for successful processing.';
    }
    
    instructionsDiv.innerHTML = instructions;
    container.appendChild(instructionsDiv);
}

function validateAmount() {
    const input = document.getElementById('amount');
    const value = parseFloat(input.value);
    const min = parseFloat(input.min);
    const max = parseFloat(input.max);
    
    // Remove existing validation classes
    input.classList.remove('is-valid', 'is-invalid');
    
    // Remove existing feedback
    const existingFeedback = input.parentNode.querySelector('.invalid-feedback, .valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    if (input.value === '') {
        return;
    }
    
    let isValid = true;
    let message = '';
    
    if (isNaN(value) || value <= 0) {
        isValid = false;
        message = 'Please enter a valid amount';
    } else if (value < min) {
        isValid = false;
        message = `Minimum withdrawal amount is $${min.toFixed(2)}`;
    } else if (value > max) {
        isValid = false;
        message = `Amount exceeds available balance of $${max.toFixed(2)}`;
    }
    
    if (isValid) {
        input.classList.add('is-valid');
        const feedback = document.createElement('div');
        feedback.className = 'valid-feedback';
        feedback.textContent = 'Amount is valid';
        input.parentNode.appendChild(feedback);
    } else {
        input.classList.add('is-invalid');
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        input.parentNode.appendChild(feedback);
    }
    
    return isValid;
}

function formatAmount() {
    const input = document.getElementById('amount');
    const value = parseFloat(input.value);
    
    if (!isNaN(value) && value > 0) {
        input.value = value.toFixed(2);
    }
}

function setupRealTimeValidation() {
    const form = document.getElementById('withdrawalForm');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            // Clear validation state on input
            this.classList.remove('is-valid', 'is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback, .valid-feedback');
            if (feedback) {
                feedback.remove();
            }
        });
    });
}

function validateField(field) {
    let isValid = true;
    let message = '';
    
    // Remove existing validation classes and feedback
    field.classList.remove('is-valid', 'is-invalid');
    const existingFeedback = field.parentNode.querySelector('.invalid-feedback, .valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }
    
    // Check if field is required and empty
    if (field.required && !field.value.trim()) {
        isValid = false;
        message = 'This field is required';
    }
    
    // Field-specific validation
    if (field.value.trim()) {
        switch (field.type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(field.value)) {
                    isValid = false;
                    message = 'Please enter a valid email address';
                }
                break;
                
            case 'number':
                if (field.id === 'amount') {
                    return validateAmount();
                }
                break;
        }
        
        // Pattern validation
        if (field.pattern && !new RegExp(field.pattern).test(field.value)) {
            isValid = false;
            message = 'Please enter a valid format';
        }
    }
    
    // Apply validation state
    if (isValid && field.value.trim()) {
        field.classList.add('is-valid');
    } else if (!isValid) {
        field.classList.add('is-invalid');
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        field.parentNode.appendChild(feedback);
    }
    
    return isValid;
}

function handleFormSubmit(e) {
    const form = e.target;
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Validate all fields
    let isFormValid = true;
    const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isFormValid = false;
        }
    });
    
    // Additional validation for account details
    const accountDetailsContainer = document.getElementById('accountDetailsContainer');
    if (accountDetailsContainer.style.display !== 'none') {
        const accountFields = accountDetailsContainer.querySelectorAll('input[required], select[required], textarea[required]');
        accountFields.forEach(field => {
            if (!validateField(field)) {
                isFormValid = false;
            }
        });
    }
    
    if (!isFormValid) {
        e.preventDefault();
        showAlert('Please correct the errors in the form', 'danger');
        return false;
    }
    
    // Show loading state
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        submitBtn.disabled = true;
        
        // Re-enable after timeout (in case of error)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    }
    
    return true;
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'danger' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Quick amount buttons functionality
function setQuickAmount(percentage) {
    const amountInput = document.getElementById('amount');
    const maxAmount = parseFloat(amountInput.max);
    const quickAmount = (maxAmount * percentage / 100).toFixed(2);
    
    amountInput.value = quickAmount;
    validateAmount();
}

// Add quick amount buttons if needed
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('amount');
    if (amountInput && parseFloat(amountInput.max) > 0) {
        addQuickAmountButtons();
    }
});

function addQuickAmountButtons() {
    const amountInput = document.getElementById('amount');
    const inputGroup = amountInput.closest('.input-group') || amountInput.parentNode;
    
    const quickButtonsDiv = document.createElement('div');
    quickButtonsDiv.className = 'mt-2';
    quickButtonsDiv.innerHTML = `
        <small class="text-muted me-2">Quick amounts:</small>
        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setQuickAmount(25)">25%</button>
        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setQuickAmount(50)">50%</button>
        <button type="button" class="btn btn-outline-secondary btn-sm me-1" onclick="setQuickAmount(75)">75%</button>
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickAmount(100)">Max</button>
    `;
    
    inputGroup.parentNode.insertBefore(quickButtonsDiv, inputGroup.nextSibling);
}