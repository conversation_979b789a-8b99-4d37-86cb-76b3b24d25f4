<?php
session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../classes/services/AuthenticationManager.php';
require_once __DIR__ . '/../classes/services/CSRFProtection.php';

// If already logged in as admin, redirect to dashboard
if (isLoggedIn() && hasRole(['admin', 'superadmin'])) {
    redirectTo('admin/dashboard/');
}

$errors = [];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $identifier = $_POST['identifier'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($identifier) || empty($password)) {
        $errors['general'] = 'Please enter both username and password';
    } else {
        $result = AuthenticationManager::login($identifier, $password, $remember);
        
        if ($result['success']) {
            $user = $result['user'];
            // Only allow admin and superadmin roles
            if (in_array($user->role, ['admin', 'superadmin'])) {
                redirectTo('admin/dashboard/');
            } else {
                $errors['general'] = 'Access denied. Admin credentials required.';
                // Logout the user since they don't have admin access
                AuthenticationManager::logout();
            }
        } else {
            $errors['general'] = $result['error'];
        }
    }
}

$csrfToken = CSRFProtection::generateToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #6f42c1 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .admin-badge {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-top: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-header">
                        <i class="fas fa-shield-alt fa-3x mb-3"></i>
                        <h2><?php echo SITE_NAME; ?></h2>
                        <p class="mb-0">Admin Panel Access</p>
                        <div class="admin-badge">ADMINISTRATORS ONLY</div>
                    </div>
                    
                    <div class="p-4">
                        <?php if (!empty($errors['general'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($errors['general']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-info">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            
                            <div class="mb-3">
                                <label for="identifier" class="form-label">
                                    <i class="fas fa-user me-2"></i>Admin Username or Email
                                </label>
                                <input type="text" class="form-control" id="identifier" name="identifier" 
                                       value="<?php echo htmlspecialchars($_POST['identifier'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me for 30 days
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-danger w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Access Admin Panel
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="mb-2">
                                <a href="<?php echo url('admin/forgot-password.php'); ?>" class="text-decoration-none">
                                    <i class="fas fa-key me-1"></i>Forgot your password?
                                </a>
                            </p>
                            <p class="mb-0">
                                <a href="<?php echo url(''); ?>" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Back to Main Site
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>