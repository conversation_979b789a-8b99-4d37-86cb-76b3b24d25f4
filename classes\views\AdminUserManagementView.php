<?php

require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

class AdminUserManagementView extends BaseView {
    use StatusColorTrait;
    
    private $users;
    private $pagination;
    private $filters;
    private $tradingPlans;
    
    public function __construct($users = [], $pagination = [], $filters = [], $tradingPlans = []) {
        parent::__construct();
        $this->users = $users;
        $this->pagination = $pagination;
        $this->filters = $filters;
        $this->tradingPlans = $tradingPlans;
        $this->setTitle('User Management - Admin Panel');
    }
    
    /**
     * Render with layout
     */
    public function render($layout = 'admin') {
        ob_start();
        $this->renderContent();
        $content = ob_get_clean();
        
        // Set page title for layout
        $pageTitle = $this->title;
        
        // Include the layout
        include __DIR__ . "/../../layouts/{$layout}_layout.php";
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-users me-2"></i>
                    User Management
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                            <i class="fas fa-plus"></i> Add User
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="exportUsers()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Users</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   placeholder="Username, email, or name..." 
                                   value="<?php echo htmlspecialchars($this->filters['search'] ?? ''); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="active" <?php echo ($this->filters['status'] ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="suspended" <?php echo ($this->filters['status'] ?? '') === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                <option value="pending" <?php echo ($this->filters['status'] ?? '') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="sort" class="form-label">Sort By</label>
                            <select class="form-select" id="sort" name="sort">
                                <option value="created_at">Registration Date</option>
                                <option value="username">Username</option>
                                <option value="balance">Balance</option>
                                <option value="last_login">Last Login</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        Users (<?php echo number_format($this->pagination['total'] ?? 0); ?> total)
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (empty($this->users)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No users found</h5>
                            <p class="text-muted">Try adjusting your search criteria or add a new user.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Balance</th>
                                        <th>Status</th>
                                        <th>Registered</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($this->users as $user): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm me-3">
                                                        <?php if (!empty($user['profile_picture'])): ?>
                                                            <img src="<?php echo htmlspecialchars($user['profile_picture']); ?>" 
                                                                 class="avatar-img rounded-circle" alt="Profile">
                                                        <?php else: ?>
                                                            <div class="avatar-title bg-primary text-white rounded-circle">
                                                                <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <div class="fw-bold">
                                                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                                        </div>
                                                        <small class="text-muted">
                                                            @<?php echo htmlspecialchars($user['username']); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($user['email']); ?>
                                                <?php if ($user['email_verified']): ?>
                                                    <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Unverified"></i>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>$<?php echo number_format($user['balance'], 2); ?></strong>
                                                </div>
                                                <?php if ($user['bonus'] > 0): ?>
                                                    <small class="text-success">
                                                        +$<?php echo number_format($user['bonus'], 2); ?> bonus
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $this->getStatusColor($user['status']); ?>">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewUser(<?php echo $user['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                            onclick="editUser(<?php echo $user['id']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                data-bs-toggle="dropdown">
                                                            <i class="fas fa-cog"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <?php if ($user['status'] === 'active'): ?>
                                                                <li>
                                                                    <a class="dropdown-item text-warning" href="#" 
                                                                       onclick="suspendUser(<?php echo $user['id']; ?>)">
                                                                        <i class="fas fa-ban me-2"></i>Suspend
                                                                    </a>
                                                                </li>
                                                            <?php else: ?>
                                                                <li>
                                                                    <a class="dropdown-item text-success" href="#" 
                                                                       onclick="activateUser(<?php echo $user['id']; ?>)">
                                                                        <i class="fas fa-check me-2"></i>Activate
                                                                    </a>
                                                                </li>
                                                            <?php endif; ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item" href="#" 
                                                                   onclick="manageBalance(<?php echo $user['id']; ?>)">
                                                                    <i class="fas fa-wallet me-2"></i>Manage Balance
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item" href="#" 
                                                                   onclick="assignPlan(<?php echo $user['id']; ?>)">
                                                                    <i class="fas fa-chart-line me-2"></i>Assign Plan
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($this->pagination['pages'] > 1): ?>
                            <nav aria-label="Users pagination">
                                <ul class="pagination justify-content-center">
                                    <?php if ($this->pagination['current_page'] > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $this->pagination['current_page'] - 1; ?>&<?php echo http_build_query($this->filters); ?>">
                                                Previous
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = 1; $i <= $this->pagination['pages']; $i++): ?>
                                        <li class="page-item <?php echo $i === $this->pagination['current_page'] ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($this->filters); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($this->pagination['current_page'] < $this->pagination['pages']): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $this->pagination['current_page'] + 1; ?>&<?php echo http_build_query($this->filters); ?>">
                                                Next
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Create User Modal -->
        <div class="modal fade" id="createUserModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="createUserForm">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">First Name</label>
                                    <input type="text" class="form-control" id="firstName" name="first_name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Last Name</label>
                                    <input type="text" class="form-control" id="lastName" name="last_name" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Create User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- User Details Modal -->
        <div class="modal fade" id="userDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">User Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="userDetailsContent">
                        <!-- Content loaded via AJAX -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Edit User Modal -->
        <div class="modal fade" id="editUserModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit User</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="editUserForm">
                        <div class="modal-body" id="editUserContent">
                            <!-- Content loaded via AJAX -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Update User</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Balance Management Modal -->
        <div class="modal fade" id="balanceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Manage Balance</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="balanceForm">
                        <div class="modal-body">
                            <input type="hidden" id="balanceUserId" name="user_id">
                            <div class="mb-3">
                                <label class="form-label">Action</label>
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="action" id="credit" value="credit" checked>
                                        <label class="form-check-label" for="credit">Credit</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="action" id="debit" value="debit">
                                        <label class="form-check-label" for="debit">Debit</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="amount" class="form-label">Amount</label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label for="reason" class="form-label">Reason</label>
                                <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="Reason for balance adjustment..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Update Balance</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Plan Assignment Modal -->
        <div class="modal fade" id="planModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Assign Trading Plan</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="planForm">
                        <div class="modal-body">
                            <input type="hidden" id="planUserId" name="user_id">
                            <div class="mb-3">
                                <label for="planId" class="form-label">Trading Plan</label>
                                <select class="form-select" id="planId" name="plan_id" required>
                                    <option value="">Select a plan...</option>
                                    <?php foreach ($this->tradingPlans as $plan): ?>
                                        <option value="<?php echo $plan['id']; ?>">
                                            <?php echo htmlspecialchars($plan['name']); ?> - 
                                            $<?php echo number_format($plan['min_amount'], 2); ?> min
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Assign Plan</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Include CSS and JS -->
        <link href="/assets/css/admin-user-management.css" rel="stylesheet">
        <script src="/assets/js/admin-user-management.js"></script>
        <?php
    }
}