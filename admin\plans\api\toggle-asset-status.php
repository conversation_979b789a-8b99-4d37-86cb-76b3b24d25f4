<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $assetId = intval($input['asset_id'] ?? 0);
    $newStatus = $input['status'] ?? '';
    
    if (!$assetId) {
        echo json_encode(['success' => false, 'message' => 'Asset ID is required']);
        exit;
    }
    
    if (!in_array($newStatus, ['active', 'inactive'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit;
    }
    
    // Load the asset
    $stmt = $pdo->prepare("SELECT * FROM trading_assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();
    
    if (!$asset) {
        echo json_encode(['success' => false, 'message' => 'Asset not found']);
        exit;
    }
    
    $oldStatus = $asset['status'];
    
    // Update status
    $stmt = $pdo->prepare("UPDATE trading_assets SET status = ?, updated_at = NOW() WHERE id = ?");
    $result = $stmt->execute([$newStatus, $assetId]);
    
    if ($result) {
        // Log the action
        AuditTrailService::log(
            'trading_asset_status_changed',
            'trading_asset',
            $assetId,
            [
                'symbol' => $asset['symbol'],
                'old_status' => $oldStatus,
                'new_status' => $newStatus
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => "Asset {$newStatus} successfully"
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update asset status'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Toggle asset status error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating asset status'
    ]);
}
?>