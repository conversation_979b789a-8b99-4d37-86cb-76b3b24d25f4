<?php
require_once 'includes/db_connect.php';
require_once 'classes/models/SystemSetting.php';
require_once 'classes/views/BaseView.php';
require_once 'classes/services/CSRFProtection.php';

/**
 * Contact Page - Contact form and company information
 */
class ContactPageView extends BaseView {
    private $settings;
    private $message = '';
    private $messageType = '';
    
    public function __construct() {
        parent::__construct();
        $this->settings = SystemSetting::getAllAsArray();
        $this->setTitle('Contact Us - ' . ($this->settings['site_name'] ?? 'Coinage Trading'));
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $this->handleContactForm();
        }
    }
    
    private function handleContactForm() {
        // Verify CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            $this->message = 'Security token validation failed. Please try again.';
            $this->messageType = 'danger';
            return;
        }
        
        // Validate form data
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'Name is required';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Valid email address is required';
        }
        
        if (empty($subject)) {
            $errors[] = 'Subject is required';
        }
        
        if (empty($message)) {
            $errors[] = 'Message is required';
        }
        
        if (!empty($errors)) {
            $this->message = implode('<br>', $errors);
            $this->messageType = 'danger';
            return;
        }
        
        // Send email (in a real application, you'd use a proper email service)
        $to = $this->settings['contact_email'] ?? '<EMAIL>';
        $emailSubject = 'Contact Form: ' . $subject;
        $emailBody = "
            Name: $name
            Email: $email
            Subject: $subject
            
            Message:
            $message
        ";
        
        $headers = [
            'From' => $email,
            'Reply-To' => $email,
            'Content-Type' => 'text/plain; charset=UTF-8'
        ];
        
        if (mail($to, $emailSubject, $emailBody, $headers)) {
            $this->message = 'Thank you for your message! We\'ll get back to you soon.';
            $this->messageType = 'success';
        } else {
            $this->message = 'Sorry, there was an error sending your message. Please try again later.';
            $this->messageType = 'danger';
        }
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <meta name="description" content="Contact <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?> - Get in touch with our support team for help with your trading account.">
        <link rel="stylesheet" href="/assets/css/landing-page.css">
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-coins me-2"></i>
                    <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/about.php">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/contact.php">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="/register.php">Sign Up</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <section class="hero-section" style="min-height: 50vh;">
            <div class="hero-background">
                <div class="hero-overlay"></div>
                <div class="container">
                    <div class="row align-items-center" style="min-height: 50vh;">
                        <div class="col-12 text-center" data-aos="fade-up">
                            <div class="hero-content">
                                <h1 class="hero-title">Contact Us</h1>
                                <p class="hero-subtitle">
                                    Get in touch with our team - we're here to help with all your trading needs.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mb-5" data-aos="fade-right">
                        <div class="card shadow">
                            <div class="card-body p-4">
                                <h3 class="mb-4">Send us a Message</h3>
                                
                                <?php if (!empty($this->message)): ?>
                                    <div class="alert alert-<?= $this->messageType ?> alert-dismissible fade show" role="alert">
                                        <?= $this->message ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST" action="/contact.php">
                                    <?= CSRFProtection::generateTokenField() ?>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Full Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?= htmlspecialchars($_POST['name'] ?? '') ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="email" class="form-label">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Subject *</label>
                                        <select class="form-select" id="subject" name="subject" required>
                                            <option value="">Choose a subject...</option>
                                            <option value="General Inquiry" <?= ($_POST['subject'] ?? '') === 'General Inquiry' ? 'selected' : '' ?>>General Inquiry</option>
                                            <option value="Account Support" <?= ($_POST['subject'] ?? '') === 'Account Support' ? 'selected' : '' ?>>Account Support</option>
                                            <option value="Trading Issues" <?= ($_POST['subject'] ?? '') === 'Trading Issues' ? 'selected' : '' ?>>Trading Issues</option>
                                            <option value="Deposit/Withdrawal" <?= ($_POST['subject'] ?? '') === 'Deposit/Withdrawal' ? 'selected' : '' ?>>Deposit/Withdrawal</option>
                                            <option value="Technical Support" <?= ($_POST['subject'] ?? '') === 'Technical Support' ? 'selected' : '' ?>>Technical Support</option>
                                            <option value="Partnership" <?= ($_POST['subject'] ?? '') === 'Partnership' ? 'selected' : '' ?>>Partnership</option>
                                            <option value="Other" <?= ($_POST['subject'] ?? '') === 'Other' ? 'selected' : '' ?>>Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-4">
                                        <label for="message" class="form-label">Message *</label>
                                        <textarea class="form-control" id="message" name="message" rows="6" 
                                                  placeholder="Please describe your inquiry in detail..." required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane me-2"></i>Send Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-4" data-aos="fade-left">
                        <div class="row">
                            <div class="col-12 mb-4">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <h5>Email Support</h5>
                                    <p><EMAIL></p>
                                    <small class="text-muted">Response within 24 hours</small>
                                </div>
                            </div>
                            
                            <div class="col-12 mb-4">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <h5>Phone Support</h5>
                                    <p>+****************</p>
                                    <small class="text-muted">Mon-Fri, 9AM-6PM EST</small>
                                </div>
                            </div>
                            
                            <div class="col-12 mb-4">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-comments"></i>
                                    </div>
                                    <h5>Live Chat</h5>
                                    <p>Available 24/7</p>
                                    <a href="/user/support/index.php" class="btn btn-outline-primary btn-sm">Start Chat</a>
                                </div>
                            </div>
                            
                            <div class="col-12 mb-4">
                                <div class="feature-card">
                                    <div class="feature-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <h5>Office Address</h5>
                                    <p>123 Trading Street<br>Crypto City, CC 12345<br>United States</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-5 bg-light">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center mb-5" data-aos="fade-up">
                        <h2 class="section-title">Frequently Asked Questions</h2>
                        <p class="section-subtitle">
                            Quick answers to common questions
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item" data-aos="fade-up" data-aos-delay="100">
                                <h2 class="accordion-header" id="faq1">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                        How do I create an account?
                                    </button>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Creating an account is simple! Click the "Sign Up" button, fill out the registration form with your details, verify your email address, and you're ready to start trading.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item" data-aos="fade-up" data-aos-delay="200">
                                <h2 class="accordion-header" id="faq2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                        What payment methods do you accept?
                                    </button>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        We accept various payment methods including bank transfers, credit/debit cards, and popular cryptocurrencies. All transactions are secured with bank-level encryption.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item" data-aos="fade-up" data-aos-delay="300">
                                <h2 class="accordion-header" id="faq3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                        How long do withdrawals take?
                                    </button>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Withdrawal processing times vary by method: cryptocurrency withdrawals typically take 1-2 hours, while bank transfers may take 1-3 business days depending on your bank.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item" data-aos="fade-up" data-aos-delay="400">
                                <h2 class="accordion-header" id="faq4">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                        Is my money safe with you?
                                    </button>
                                </h2>
                                <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Absolutely! We use bank-level security measures including SSL encryption, two-factor authentication, and cold storage for cryptocurrencies. Your funds are protected by industry-leading security protocols.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item" data-aos="fade-up" data-aos-delay="500">
                                <h2 class="accordion-header" id="faq5">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse5">
                                        Do you offer customer support?
                                    </button>
                                </h2>
                                <div id="collapse5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        Yes! We offer 24/7 customer support through live chat, email support with responses within 24 hours, and phone support during business hours. Our team is here to help with any questions or issues.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer-section bg-dark text-white py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <h5><?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?></h5>
                        <p>Professional cryptocurrency trading platform with advanced security and cutting-edge technology.</p>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-telegram"></i></a>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6>Platform</h6>
                        <ul class="footer-links">
                            <li><a href="/login.php">Login</a></li>
                            <li><a href="/register.php">Register</a></li>
                            <li><a href="/about.php">About Us</a></li>
                            <li><a href="/contact.php">Contact</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6>Support</h6>
                        <ul class="footer-links">
                            <li><a href="/user/support/index.php">Help Center</a></li>
                            <li><a href="/terms.php">Terms of Service</a></li>
                            <li><a href="/privacy.php">Privacy Policy</a></li>
                            <li><a href="/security.php">Security</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <h6>Contact Info</h6>
                        <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                        <p><i class="fas fa-phone me-2"></i>+****************</p>
                        <p><i class="fas fa-map-marker-alt me-2"></i>123 Trading Street, Crypto City, CC 12345</p>
                    </div>
                </div>
                <hr class="my-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">&copy; <?= date('Y') ?> <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?>. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0">
                            <i class="fas fa-shield-alt me-1"></i>
                            Secured by SSL encryption
                        </p>
                    </div>
                </div>
            </div>
        </footer>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
        <script>
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true
            });
        </script>
        <?php
    }
}

$view = new ContactPageView();
$view->render();
?>