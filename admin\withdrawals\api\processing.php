<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/services/NotificationService.php';
require_once '../../../classes/models/Withdrawal.php';
require_once '../../../classes/models/User.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get form data
$withdrawalId = (int)($_POST['withdrawal_id'] ?? 0);
$adminNote = trim($_POST['admin_note'] ?? '');

if (!$withdrawalId) {
    echo json_encode(['success' => false, 'message' => 'Invalid withdrawal ID']);
    exit;
}

try {
    // Get withdrawal
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal not found']);
        exit;
    }
    
    // Check if withdrawal is approved
    if (!$withdrawal->isApproved()) {
        echo json_encode(['success' => false, 'message' => 'Only approved withdrawals can be marked as processing']);
        exit;
    }
    
    // Get current admin
    $currentUser = SessionManager::getCurrentUser();
    
    // Add admin note if provided
    if ($adminNote) {
        $withdrawal->admin_note = $adminNote;
    }
    
    // Mark as processing
    if ($withdrawal->markAsProcessing($currentUser->getId())) {
        // Send notification to user
        try {
            $user = $withdrawal->getUser();
            if ($user) {
                NotificationService::sendWithdrawalProcessingNotification($user, $withdrawal);
            }
        } catch (Exception $e) {
            error_log("Failed to send withdrawal processing notification: " . $e->getMessage());
            // Don't fail the update if notification fails
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal marked as processing successfully. User has been notified.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update withdrawal status. Please try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error marking withdrawal as processing: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while updating the withdrawal status']);
}
?>