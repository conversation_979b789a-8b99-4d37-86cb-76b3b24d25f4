<?php
session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../classes/services/AuthenticationManager.php';
require_once __DIR__ . '/../classes/services/CSRFProtection.php';

// If already logged in as superadmin, redirect to dashboard
if (isLoggedIn() && hasRole('superadmin')) {
    redirectTo('superadmin/dashboard/');
}

$errors = [];
$message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $identifier = $_POST['identifier'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($identifier) || empty($password)) {
        $errors['general'] = 'Please enter both username and password';
    } else {
        $result = AuthenticationManager::login($identifier, $password, $remember);
        
        if ($result['success']) {
            $user = $result['user'];
            // Only allow superadmin role
            if ($user->role === 'superadmin') {
                redirectTo('superadmin/dashboard/');
            } else {
                $errors['general'] = 'Access denied. Super Admin credentials required.';
                // Logout the user since they don't have superadmin access
                AuthenticationManager::logout();
            }
        } else {
            $errors['general'] = $result['error'];
        }
    }
}

$csrfToken = CSRFProtection::generateToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Super Admin Login - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #6f42c1 0%, #343a40 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #6f42c1, #5a32a3);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .superadmin-badge {
            background: rgba(255,255,255,0.2);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-top: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-header">
                        <i class="fas fa-crown fa-3x mb-3"></i>
                        <h2><?php echo SITE_NAME; ?></h2>
                        <p class="mb-0">System Administration</p>
                        <div class="superadmin-badge">SUPER ADMIN ONLY</div>
                    </div>
                    
                    <div class="p-4">
                        <?php if (!empty($errors['general'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($errors['general']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($message): ?>
                            <div class="alert alert-info">
                                <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                            
                            <div class="mb-3">
                                <label for="identifier" class="form-label">
                                    <i class="fas fa-user-shield me-2"></i>Super Admin Username
                                </label>
                                <input type="text" class="form-control" id="identifier" name="identifier" 
                                       value="<?php echo htmlspecialchars($_POST['identifier'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-key me-2"></i>Master Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me for 30 days
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-dark w-100 mb-3">
                                <i class="fas fa-unlock-alt me-2"></i>Access System Control
                            </button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <p class="mb-0">
                                <a href="<?php echo url(''); ?>" class="text-decoration-none">
                                    <i class="fas fa-home me-1"></i>Back to Main Site
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>