<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $userId = intval($_POST['user_id'] ?? 0);
    $planId = !empty($_POST['plan_id']) ? intval($_POST['plan_id']) : null;
    $asset = trim($_POST['asset'] ?? '');
    $tradeType = $_POST['trade_type'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    $entryPrice = floatval($_POST['entry_price'] ?? 0);
    $openedAt = $_POST['opened_at'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    
    // Validation
    $errors = [];
    
    if (!$userId) {
        $errors['user_id'] = 'User is required';
    } else {
        // Check if user exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND role = 'user'");
        $stmt->execute([$userId]);
        if (!$stmt->fetch()) {
            $errors['user_id'] = 'Invalid user selected';
        }
    }
    
    if (empty($asset)) {
        $errors['asset'] = 'Asset is required';
    } elseif (strlen($asset) > 50) {
        $errors['asset'] = 'Asset name is too long';
    }
    
    if (!in_array($tradeType, ['buy', 'sell'])) {
        $errors['trade_type'] = 'Invalid trade type';
    }
    
    if ($amount <= 0) {
        $errors['amount'] = 'Amount must be greater than zero';
    }
    
    if ($entryPrice <= 0) {
        $errors['entry_price'] = 'Entry price must be greater than zero';
    }
    
    if (empty($openedAt)) {
        $errors['opened_at'] = 'Opened date is required';
    } elseif (strtotime($openedAt) > time()) {
        $errors['opened_at'] = 'Opened date cannot be in the future';
    }
    
    if ($planId) {
        // Check if plan exists
        $stmt = $pdo->prepare("SELECT id FROM trading_plans WHERE id = ?");
        $stmt->execute([$planId]);
        if (!$stmt->fetch()) {
            $errors['plan_id'] = 'Invalid trading plan selected';
        }
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Create the trade
    $sql = "INSERT INTO trades (user_id, plan_id, asset, trade_type, amount, entry_price, opened_at, notes, created_by, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        $userId,
        $planId,
        $asset,
        $tradeType,
        $amount,
        $entryPrice,
        $openedAt,
        $notes,
        getCurrentUser()['id']
    ]);
    
    if ($result) {
        $tradeId = $pdo->lastInsertId();
        
        // Log the action
        AuditTrailService::log(
            'trade_created',
            'trade',
            $tradeId,
            [
                'user_id' => $userId,
                'plan_id' => $planId,
                'asset' => $asset,
                'trade_type' => $tradeType,
                'amount' => $amount,
                'entry_price' => $entryPrice,
                'opened_at' => $openedAt
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Trade created successfully',
            'trade_id' => $tradeId
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create trade'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Create trade error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while creating the trade'
    ]);
}
?>