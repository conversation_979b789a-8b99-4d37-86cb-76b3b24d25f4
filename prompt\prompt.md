Host	localhost
Port	3306
User	root
Password	root
Database	coinage
---

smtp configutation 
Sender name : Coinage Trading
<EMAIL>
smtp.hostinger.com
password: Money2025@Demo#
465


### **Project Requirements Document (PRD): Crypto Trading & Investment Platform**

This document details the full scope, features, and technical architecture for a direct-access, PHP-based trading platform with distinct User, Admin, and Super Admin roles.

### **1. Core Philosophy & Architecture**

The application will be a monolithic PHP application, avoiding external APIs for core logic and providing direct backend access. The design prioritizes modularity, clean code, and a highly configurable system managed by a Super Admin.

**1.1. Technology Stack**
*   **Backend:** PHP (7.4+)
*   **Frontend:** Bootstrap 5, HTML5, CSS3, JavaScript (vanilla or with a lightweight library like Alpine.js for reactivity)
*   **Database:** MySQL / MariaDB
*   **Local Development:** MAMP / XAMPP / WAMP
*   **Core Libraries:**
    *   **PHPMailer:** For robust SMTP email delivery.
    *   **PHP 2FA Library (e.g., `pragmarx/google2fa-php`):** For OTP and QR code generation.
    *   **QR Code Generator Library:** For displaying QR codes for deposits and 2FA setup.

**1.2. Directory Structure**
A clean root folder is essential. Each functional page will reside in its own folder to encapsulate its specific logic, styles, and scripts.

```
/ (root)
|-- /admin/                   # Admin Panel Root
|   |-- /dashboard/
|   |   |-- index.php
|   |-- /users/               # Manage Users Section
|   |   |-- index.php         # List users
|   |   |-- create.php        # Create new user form
|   |   |-- edit.php          # Edit user form
|   |   |-- view.php          # View user details
|   |   |-- handle_actions.php # PHP logic for create/edit/delete
|   |-- /plans/               # Manage Trading Plans
|   |-- /deposits/            # Manage Deposits
|   |-- ... (other admin pages)
|
|-- /superadmin/              # Super Admin Panel Root
|   |-- /settings/
|   |-- /email-templates/
|   |-- ... (other super admin pages)
|
|-- /user/                    # User Panel Root
|   |-- /dashboard/
|   |   |-- index.php
|   |-- /deposit/
|   |-- /trade/
|   |-- ... (other user pages)
|
|-- /assets/                  # Global assets
|   |-- /css/
|   |   |-- style.css
|   |   |-- user.css
|   |   |-- admin.css
|   |-- /js/
|   |-- /img/
|
|-- /includes/                # Reusable PHP components
|   |-- db_connect.php
|   |-- functions.php
|   |-- user_auth_check.php
|   |-- admin_auth_check.php
|
|-- /layouts/                 # Main layout templates
|   |-- user_layout.php
|   |-- admin_layout.php
|   |-- superadmin_layout.php
|   |-- mail_template.php
|
|-- /test_folder/             # All testing scripts and files
|
|-- config.php                # Core configuration (DB, Base URL, Site Name)
|-- login.php
|-- register.php
|-- logout.php
|-- index.php                 # Landing Page
```

**1.3. Layout & Theming System**
*   **No Hardcoding:** Headers, footers, and sidebars will be defined in dedicated layout files (`/layouts/user_layout.php`, etc.).
*   **Inclusion:** Each page (`/user/dashboard/index.php`) will include its corresponding layout file. This ensures consistency and easy updates.
*   **Dynamic Colors:** A configuration file (`config.php` or a dedicated `theme.php`) will define `PRIMARY_COLOR` and `SECONDARY_COLOR`. These PHP constants will be echoed into a `<style>` block in the head of the layout files to dynamically set CSS variables.
    ```css
    /* style.css */
    :root {
      --primary-color: #007bff; /* Default */
      --secondary-color: #6c757d; /* Default */
    }
    ```
    ```php
    <!-- layouts/user_layout.php -->
    <style>
      :root {
        --primary-color: <?php echo PRIMARY_COLOR; ?>;
        --secondary-color: <?php echo SECONDARY_COLOR; ?>;
      }
    </style>
    ```
*   **Dynamic Links:** A `BASE_URL` will be defined in `config.php`. All links (`<a href="...">`, `<img src="...">`) must be constructed using this base URL to ensure portability across domains.

**1.4. Code Modularity**
*   **100-Line Rule:** No single PHP file should exceed 100-150 lines of code. Complex logic will be broken down into smaller, single-purpose functions and placed in `/includes/functions.php` or feature-specific include files.

---

### **2. Role-Based Access Control (RBAC)**

**2.1. User**
The standard client of the platform. Can manage their own account and trading activities.

**2.2. Admin**
A staff member who manages users, finances, and platform content. They operate within the rules set by the Super Admin.

**2.3. Super Admin**
The owner of the system. Has root-level access to configure the entire platform, including security, system features, and admin accounts.

---

### **3. User-Facing Features**

**3.1. User Dashboard (`/user/dashboard/`)**
*   **Initial State:** On first login after registration, displays:
    *   Profile Picture (default avatar)
    *   Amount: 0
    *   Balance: 0
    *   Bonus: 0
*   **Layout:** Includes the standard user header, footer, and sidebar.
*   **Content:** Main dashboard view with key stats. It can display notifications from the admin and a trading view chart/widget (e.g., from TradingView) in the background or as a primary element.

**3.2. Trading Plans & Deposit Flow**
1.  User clicks on "Choose Trading Plan".
2.  A page displays available plans (e.g., BASIC, GOLD, VIP) with their names, deposit amounts (e.g., $3,000, $5,000, $10,000), and benefits. These plans are managed by the Admin.
3.  User selects a plan and clicks "Deposit".
4.  The deposit page (`/user/deposit/`) opens, showing available methods (e.g., Crypto Address, PayPal Link, Bank Info). These methods are managed by the Admin.
5.  User proceeds to make a deposit (manually, outside the system). The system will show a "pending confirmation" status.
6.  **Admin Action:** Admin approves the deposit from their panel.
7.  **System Automation:** Upon admin approval:
    *   User receives a confirmation email (using a beautiful template).
    *   User's account is credited with the deposit amount.
    *   A 10% bonus is automatically calculated and added to the 'Bonus' wallet.

**3.3. Core User Pages**
*   **Profile (`/user/profile/`):** View personal information.
*   **Settings (`/user/settings/`):**
    *   Change password.
    *   Update personal information.
    *   Set up 2FA security.
*   **Transaction Activities (`/user/transactions/`):** Log of all deposits, withdrawals, bonuses, and trade outcomes.
*   **Trading Page (`/user/trade/`):** An interface where the user can initiate or view trades based on their balance and chosen plan.

---

### **4. Admin Panel Features**

The Admin panel is the central hub for managing platform operations.

*   **Dashboard:** Overview of platform activity (new users, pending deposits/withdrawals, support tickets).
*   **User Management:**
    *   CRUD operations: Create, Edit, View, Delete, and Suspend users.
    *   Credit/Debit User accounts directly (for corrections or manual bonuses).
    *   Manage User KYC status (Pending, Approved, Rejected).
    *   Choose/assign a trading plan to a user.
*   **Financial Management:**
    *   **Deposits:** View all deposits, filter by status (Pending, Approved, Rejected). Approve or reject pending deposits.
    *   **Withdrawals:** View all withdrawal requests. Approve, Pend, or Reject requests.
    *   **Payment Gateways:** Edit deposit options (e.g., update crypto wallet address, PayPal email). Edit withdrawal methods.
*   **Trade & Investment Management:**
    *   **Plan Management:** Create, Edit, and Delete trading plans (Basic, Gold, etc.).
    *   **Trade History:** Create and backdate trade history for users.
    *   **Staking & Pool Management:** Create and manage staking/pool plans and view investments.
    *   **Add Assets:** Add new trading options (Currencies, Commodities, Stocks).
*   **Communication & Support:**
    *   **Notifications:** Send custom notification messages to individual user dashboards. Send a site-wide banner notification.
    *   **Support Tickets:** View and reply to all user support tickets. Filter by status (Pending, Closed, Answered).
    *   **WhatsApp Support:** Set the official WhatsApp support link/number.
*   **Reporting:**
    *   View detailed transaction logs and investment history for all users.
*   **Profile Settings:** Manage their own admin profile and password.

---

### **5. Super Admin Panel Features**

The Super Admin has ultimate control over the system's configuration and integrity.

*   **System Configuration (Feature Toggles):**
    *   A central page to enable/disable major features with a simple switch:
        *   Email Notification (On/Off)
        *   SMS Notification (On/Off)
        *   Email Verification at Registration (On/Off)
        *   Registration Bonus (On/Off)
        *   User Ranking System (On/Off)
        *   Balance Transfer Between Users (On/Off)
        *   Promotional Tool (On/Off)
        *   Withdrawal on Holidays (On/Off)
        *   KYC Verification Requirement (On/Off)
        *   Push Notification (On/Off)
        *   Schedule Invest Feature (On/Off)
        *   Staking Module (On/Off)
        *   Pool Module (On/Off)
*   **General Settings:** Set Site Name, **Currency Management** (set site's default currency and symbol), registration bonus amount, transfer fees, etc.
*   **Appearance Settings:**
    *   Upload Logo & Favicon.
    *   Use a color picker to set the `PRIMARY_COLOR` and `SECONDARY_COLOR`.
*   **Email Management:**
    *   **SMTP Configuration:** Securely input SMTP host, port, username, and password.
    *   **Email Templates:** A visual editor (or HTML code view) to modify all outbound email templates (welcome, deposit confirmation, password reset, etc.). Templates must be visually appealing with logo and dynamic placeholders (e.g., `{{username}}`, `{{amount}}`).
*   **Admin User Management:** Create, edit, and delete Admin accounts.
*   **Security Settings:**
    *   Enforce/disable 2FA for Admins and Users globally.
    *   Configure 2FA settings.
    *   Manage their own Super Admin 2FA.
*   **Audit Logs:** A detailed, immutable log of critical actions performed by Admins and the Super Admin (e.g., "Admin 'JohnDoe' credited user 'JaneRoe' with $500").
*   **KYC Settings:** Define the fields and documents required for KYC verification.

---

### **6. Security & Notifications**

**6.1. Security**
*   **2FA/OTP:** All three roles (User, Admin, Super Admin) will have the option for 6-digit Time-based One-Time Password (TOTP) security. The system will generate a QR code for easy setup with apps like Google Authenticator.
*   **Control:** Super Admin can enforce 2FA for all users/admins. Admin can see a user's 2FA status and prompt them to enable it.
*   **Standard Practices:**
    *   **Password Hashing:** Use `password_hash()` and `password_verify()`.
    *   **Prepared Statements:** All database queries must use prepared statements to prevent SQL injection.
    *   **Input/Output Sanitization:** Protect against XSS attacks.
    *   **CSRF Protection:** Implement tokens in all forms.

**6.2. Notifications**
*   **Email:** Sent via SMTP using PHPMailer for reliability.
*   **SMS:** Requires a third-party SMS gateway API (this is an exception to the "no API" rule, as it's externally necessary). Can be toggled on/off by Super Admin.
*   **Push Notifications:** Can be implemented using a service like Firebase Cloud Messaging (FCM) and toggled on/off.


Project Requirements Document (PRD): Crypto Trading & Investment Platform
This document details the scope, features, and technical architecture for a PHP-based cryptocurrency trading and investment platform with direct backend access and no external API reliance for core logic.

1. Core Philosophy & Architecture
The application is a monolithic PHP system designed for modularity, clean code, and configurability, managed primarily by a Super Admin. It avoids external APIs for core functionality and uses a beautiful Bootstrap-based dashboard framework compatible with HTML, JS, and PHP.
1.1. Technology Stack

Backend: PHP (7.4+)
Frontend: Bootstrap 5, HTML5, CSS3, JavaScript (vanilla)
Database: MySQL / MariaDB
Local Development: MAMP / XAMPP / WAMP
Core Libraries:
PHPMailer: For SMTP email delivery.
PHP 2FA Library (e.g., pragmarx/google2fa-php): For OTP and 2FA functionality.
QR Code Generator Library: For deposit addresses and 2FA setup.



1.2. Directory Structure
The root folder is organized cleanly, with each functional page encapsulated in its own directory.
/ (root)
|-- /admin/                   # Admin Panel
|   |-- /dashboard/           # Admin dashboard
|   |-- /users/               # User management
|   |-- /plans/               # Trading plan management
|   |-- /deposits/            # Deposit management
|
|-- /superadmin/              # Super Admin Panel
|   |-- /settings/            # System settings
|   |-- /email-templates/     # Email configuration
|
|-- /user/                    # User Panel
|   |-- /dashboard/           # User dashboard
|   |-- /deposit/             # Deposit flow
|   |-- /trade/               # Trading interface
|
|-- /assets/                  # Static assets
|   |-- /css/                 # Stylesheets
|   |-- /js/                  # Scripts
|   |-- /img/                 # Images
|
|-- /includes/                # Reusable PHP components
|   |-- db_connect.php        # Database connection
|   |-- functions.php         # Helper functions
|   |-- auth_check.php        # Authentication logic
|
|-- /layouts/                 # Layout templates
|   |-- user_layout.php       # User layout
|   |-- admin_layout.php      # Admin layout
|   |-- superadmin_layout.php # Super Admin layout
|
|-- /test_folder/             # Testing scripts
|
|-- config.php                # Core configuration
|-- index.php                 # Landing page
|-- login.php                 # Login page
|-- register.php              # Registration page
|-- logout.php                # Logout functionality

1.3. Layout & Theming System

Layout Files: Headers, footers, and sidebars are defined in /layouts/ for consistency.
Dynamic Theming: Colors (PRIMARY_COLOR, SECONDARY_COLOR) are set in config.php and applied via CSS variables in layout files.
Dynamic Links: A BASE_URL constant in config.php ensures portable URLs.

1.4. Code Modularity

100-Line Rule: PHP files are limited to 100-150 lines, with complex logic moved to functions in /includes/functions.php.


2. Role-Based Access Control (RBAC)

User: Standard client managing their account and trades.
Admin: Staff member overseeing users, finances, and content within Super Admin-defined rules.
Super Admin: System owner with full configuration control.


3. User-Facing Features
3.1. User Dashboard (/user/dashboard/)

Displays profile picture, amount, balance, bonus, and notifications.
Uses a Bootstrap-based layout with a TradingView widget option.

3.2. Trading Plans & Deposit Flow

User selects a plan (e.g., Basic, Gold) from a list managed by Admin.
Proceeds to /user/deposit/ to view payment methods (e.g., crypto address).
Submits deposit (manual process), awaiting Admin approval.
Post-approval: User receives email confirmation, deposit credited, and 10% bonus added.

3.3. Core User Pages

Profile: View/edit personal info.
Settings: Update password, info, and 2FA.
Transactions: View deposit/withdrawal/trade logs.
Trading: Initiate/view trades based on plan.


4. Admin Panel Features

Dashboard: Overview of platform activity.
User Management: CRUD operations, KYC status, plan assignment.
Financial Management: Approve deposits/withdrawals, configure payment methods.
Trade & Investment: Manage plans, trade history, and assets.
Communication: Send notifications, handle support tickets, set WhatsApp link.
Reporting: View transaction/investment logs.
Profile Settings: Manage admin account.


5. Super Admin Panel Features

System Configuration: Toggle features (e.g., email notifications, KYC).
General Settings: Set site name, currency, bonus amounts, fees.
Appearance: Upload logo/favicon, set colors.
Email Management: Configure SMTP, edit templates.
Admin Management: CRUD for admin accounts.
Security: Enforce 2FA, view audit logs.
KYC Settings: Define verification requirements.


6. Security & Notifications
6.1. Security

2FA/OTP: Optional for all roles, configurable by Super Admin.
Practices:
Password hashing with password_hash().
Prepared statements for SQL queries.
Input/output sanitization.
Universal CSRF protection with tokens on all pages.



6.2. Notifications

Email: Delivered via PHPMailer (SMTP).
SMS: Disabled by default (toggleable with third-party API).
Push Notifications: Disabled by default (toggleable with FCM).


7. Additional Notes

Uses a Bootstrap-based dashboard framework for a modern, responsive UI.
No API backend or SMS connection by default, ensuring self-contained operation.
