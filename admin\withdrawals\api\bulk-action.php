<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/services/NotificationService.php';
require_once '../../../classes/models/Withdrawal.php';
require_once '../../../classes/models/User.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get form data
$action = $_POST['action'] ?? '';
$withdrawalIds = $_POST['withdrawal_ids'] ?? [];
$reason = trim($_POST['reason'] ?? '');
$adminNote = trim($_POST['admin_note'] ?? '');

if (empty($action)) {
    echo json_encode(['success' => false, 'message' => 'Action is required']);
    exit;
}

if (empty($withdrawalIds) || !is_array($withdrawalIds)) {
    echo json_encode(['success' => false, 'message' => 'No withdrawals selected']);
    exit;
}

if ($action === 'reject' && empty($reason)) {
    echo json_encode(['success' => false, 'message' => 'Rejection reason is required']);
    exit;
}

try {
    $currentUser = SessionManager::getCurrentUser();
    $successCount = 0;
    $failCount = 0;
    $errors = [];
    
    foreach ($withdrawalIds as $withdrawalId) {
        $withdrawalId = (int)$withdrawalId;
        
        if (!$withdrawalId) {
            $failCount++;
            continue;
        }
        
        try {
            $withdrawal = Withdrawal::find($withdrawalId);
            
            if (!$withdrawal) {
                $failCount++;
                $errors[] = "Withdrawal ID {$withdrawalId} not found";
                continue;
            }
            
            $success = false;
            
            switch ($action) {
                case 'approve':
                    if ($withdrawal->isPending()) {
                        if ($adminNote) {
                            $withdrawal->admin_note = $adminNote;
                        }
                        $success = $withdrawal->approve($currentUser->getId());
                        
                        if ($success) {
                            // Send notification
                            try {
                                $user = $withdrawal->getUser();
                                if ($user) {
                                    NotificationService::sendWithdrawalApprovalNotification($user, $withdrawal);
                                }
                            } catch (Exception $e) {
                                error_log("Failed to send approval notification for withdrawal {$withdrawalId}: " . $e->getMessage());
                            }
                        }
                    } else {
                        $errors[] = "Withdrawal ID {$withdrawalId} is not pending";
                    }
                    break;
                    
                case 'reject':
                    if ($withdrawal->isPending()) {
                        $success = $withdrawal->reject($currentUser->getId(), $reason);
                        
                        if ($success) {
                            // Send notification
                            try {
                                $user = $withdrawal->getUser();
                                if ($user) {
                                    NotificationService::sendWithdrawalRejectionNotification($user, $withdrawal, $reason);
                                }
                            } catch (Exception $e) {
                                error_log("Failed to send rejection notification for withdrawal {$withdrawalId}: " . $e->getMessage());
                            }
                        }
                    } else {
                        $errors[] = "Withdrawal ID {$withdrawalId} is not pending";
                    }
                    break;
                    
                case 'processing':
                    if ($withdrawal->isApproved()) {
                        if ($adminNote) {
                            $withdrawal->admin_note = $adminNote;
                        }
                        $success = $withdrawal->markAsProcessing($currentUser->getId());
                        
                        if ($success) {
                            // Send notification
                            try {
                                $user = $withdrawal->getUser();
                                if ($user) {
                                    NotificationService::sendWithdrawalProcessingNotification($user, $withdrawal);
                                }
                            } catch (Exception $e) {
                                error_log("Failed to send processing notification for withdrawal {$withdrawalId}: " . $e->getMessage());
                            }
                        }
                    } else {
                        $errors[] = "Withdrawal ID {$withdrawalId} is not approved";
                    }
                    break;
                    
                case 'completed':
                    if ($withdrawal->isProcessing()) {
                        if ($adminNote) {
                            $withdrawal->admin_note = $adminNote;
                        }
                        $success = $withdrawal->markAsCompleted($currentUser->getId());
                        
                        if ($success) {
                            // Send notification
                            try {
                                $user = $withdrawal->getUser();
                                if ($user) {
                                    NotificationService::sendWithdrawalCompletionNotification($user, $withdrawal);
                                }
                            } catch (Exception $e) {
                                error_log("Failed to send completion notification for withdrawal {$withdrawalId}: " . $e->getMessage());
                            }
                        }
                    } else {
                        $errors[] = "Withdrawal ID {$withdrawalId} is not processing";
                    }
                    break;
                    
                default:
                    $errors[] = "Invalid action: {$action}";
                    break;
            }
            
            if ($success) {
                $successCount++;
            } else {
                $failCount++;
            }
            
        } catch (Exception $e) {
            $failCount++;
            $errors[] = "Error processing withdrawal ID {$withdrawalId}: " . $e->getMessage();
            error_log("Bulk action error for withdrawal {$withdrawalId}: " . $e->getMessage());
        }
    }
    
    // Prepare response message
    $message = "Bulk action completed. ";
    if ($successCount > 0) {
        $message .= "{$successCount} withdrawal(s) processed successfully. ";
    }
    if ($failCount > 0) {
        $message .= "{$failCount} withdrawal(s) failed to process.";
    }
    
    $response = [
        'success' => $successCount > 0,
        'message' => $message,
        'details' => [
            'success_count' => $successCount,
            'fail_count' => $failCount,
            'total_count' => count($withdrawalIds)
        ]
    ];
    
    if (!empty($errors)) {
        $response['errors'] = $errors;
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Bulk action error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while processing the bulk action']);
}
?>