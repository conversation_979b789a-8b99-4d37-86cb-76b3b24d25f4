<?php
require_once __DIR__ . '/../includes/db_connect.php';

/**
 * Create Additional Email Templates
 * Adds comprehensive email templates for the communication system
 */

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h1>Creating Additional Email Templates</h1>\n";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
    
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/migrations/create_additional_email_templates.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Split by semicolons and execute each statement
    $statements = explode(';', $sql);
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->exec($statement);
            $successCount++;
            echo "✅ Executed statement successfully\n";
        } catch (Exception $e) {
            $errorCount++;
            echo "❌ Error executing statement: " . $e->getMessage() . "\n";
        }
    }
    
    echo "<h2>Verifying email templates...</h2>\n";
    
    // Verify templates were created
    $stmt = $db->query("SELECT template_type, template_name, is_active FROM email_templates ORDER BY template_type");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Available Email Templates:</h3>\n";
    foreach ($templates as $template) {
        $status = $template['is_active'] ? '✅ Active' : '❌ Inactive';
        echo "- {$template['template_type']}: {$template['template_name']} ($status)\n";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>✅ Email Templates Setup Complete!</h3>\n";
    echo "<p style='color: #155724;'>Successfully executed $successCount statements with $errorCount errors.</p>\n";
    echo "<p style='color: #155724;'>Total templates available: " . count($templates) . "</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Creating Email Templates</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>