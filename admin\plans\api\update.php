<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get plan ID
    $planId = intval($_POST['plan_id'] ?? 0);
    
    if (!$planId) {
        echo json_encode(['success' => false, 'message' => 'Plan ID is required']);
        exit;
    }
    
    // Load existing plan
    $plan = TradingPlan::find($planId);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'Trading plan not found']);
        exit;
    }
    
    // Store original values for audit
    $originalData = [
        'name' => $plan->name,
        'min_deposit' => $plan->min_deposit,
        'max_deposit' => $plan->max_deposit,
        'daily_return' => $plan->daily_return,
        'duration_days' => $plan->duration_days,
        'status' => $plan->status,
        'description' => $plan->description,
        'features' => $plan->getFeaturesArray()
    ];
    
    // Get form data
    $name = trim($_POST['name'] ?? '');
    $minDeposit = floatval($_POST['min_deposit'] ?? 0);
    $maxDeposit = !empty($_POST['max_deposit']) ? floatval($_POST['max_deposit']) : null;
    $dailyReturn = floatval($_POST['daily_return'] ?? 0);
    $durationDays = intval($_POST['duration_days'] ?? 0);
    $status = $_POST['status'] ?? 'active';
    $description = trim($_POST['description'] ?? '');
    $features = json_decode($_POST['features'] ?? '[]', true);
    
    // Update plan properties
    $plan->name = $name;
    $plan->min_deposit = $minDeposit;
    $plan->max_deposit = $maxDeposit;
    $plan->daily_return = $dailyReturn;
    $plan->duration_days = $durationDays;
    $plan->status = $status;
    $plan->description = $description;
    $plan->features = $features;
    
    // Validate the plan
    $errors = $plan->validate();
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Save the plan
    if ($plan->save()) {
        // Prepare new data for audit
        $newData = [
            'name' => $name,
            'min_deposit' => $minDeposit,
            'max_deposit' => $maxDeposit,
            'daily_return' => $dailyReturn,
            'duration_days' => $durationDays,
            'status' => $status,
            'description' => $description,
            'features' => $features
        ];
        
        // Log the action
        AuditTrailService::log(
            'trading_plan_updated',
            'trading_plan',
            $plan->getId(),
            [
                'original' => $originalData,
                'updated' => $newData,
                'changes' => array_diff_assoc($newData, $originalData)
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Trading plan updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update trading plan'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Update trading plan error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the trading plan'
    ]);
}
?>