<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = getDB();
    
    try {
        $db->exec('CREATE INDEX idx_entity_type ON audit_logs (entity_type)');
        echo "Added idx_entity_type index\n";
    } catch (Exception $e) {
        echo "idx_entity_type index already exists or error: " . $e->getMessage() . "\n";
    }
    
    try {
        $db->exec('CREATE INDEX idx_entity_id ON audit_logs (entity_id)');
        echo "Added idx_entity_id index\n";
    } catch (Exception $e) {
        echo "idx_entity_id index already exists or error: " . $e->getMessage() . "\n";
    }
    
    echo "Index creation completed\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>