<?php
require_once '../../includes/security_init.php';
require_once '../../classes/controllers/AuthController.php';
require_once '../../classes/services/SupportTicketService.php';
require_once '../../classes/views/BaseView.php';

// Require authentication
AuthController::requireAuth();

$ticketId = $_GET['id'] ?? null;
if (!$ticketId) {
    header('Location: /user/support/');
    exit();
}

$supportService = SupportTicketService::getInstance();
$userId = $_SESSION['user_id'];

// Get ticket details
$ticketResult = $supportService->getTicketDetails($ticketId, $userId);

if (!$ticketResult['success']) {
    header('Location: /user/support/');
    exit();
}

$ticket = $ticketResult['ticket'];

/**
 * Support Ticket Detail View
 */
class SupportTicketDetailView extends BaseView {
    private $ticket;
    
    public function __construct($ticket) {
        parent::__construct();
        $this->ticket = $ticket;
        $this->setTitle('Ticket #' . $ticket['id'] . ' - ' . $ticket['subject'] . ' - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/support-ticket.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container mt-4">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/user/dashboard/">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="/user/support/">Support</a></li>
                            <li class="breadcrumb-item active">Ticket #<?= htmlspecialchars($this->ticket['id']) ?></li>
                        </ol>
                    </nav>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-8">
                    <!-- Ticket Details -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h4 class="mb-0">
                                    <i class="fas fa-ticket-alt me-2"></i>
                                    <?= htmlspecialchars($this->ticket['subject']) ?>
                                </h4>
                                <?php
                                $statusColors = [
                                    'pending' => 'warning',
                                    'answered' => 'info',
                                    'closed' => 'success'
                                ];
                                $statusColor = $statusColors[$this->ticket['status']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?= $statusColor ?> fs-6">
                                    <?= ucfirst(htmlspecialchars($this->ticket['status'])) ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <!-- Original Message -->
                            <div class="message-container mb-4">
                                <div class="message-header">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="avatar bg-primary text-white rounded-circle me-3">
                                            <?= strtoupper(substr($this->ticket['first_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <strong><?= htmlspecialchars($this->ticket['first_name'] . ' ' . $this->ticket['last_name']) ?></strong>
                                            <small class="text-muted d-block">
                                                <?= date('M j, Y g:i A', strtotime($this->ticket['created_at'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="message-content">
                                    <div class="bg-light p-3 rounded">
                                        <?= nl2br(htmlspecialchars($this->ticket['message'])) ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Admin Reply -->
                            <?php if (!empty($this->ticket['admin_reply'])): ?>
                                <div class="message-container admin-reply">
                                    <div class="message-header">
                                        <div class="d-flex align-items-center mb-2">
                                            <div class="avatar bg-success text-white rounded-circle me-3">
                                                <i class="fas fa-user-shield"></i>
                                            </div>
                                            <div>
                                                <strong>
                                                    <?php if ($this->ticket['admin_first_name']): ?>
                                                        <?= htmlspecialchars($this->ticket['admin_first_name'] . ' ' . $this->ticket['admin_last_name']) ?>
                                                    <?php else: ?>
                                                        Support Team
                                                    <?php endif; ?>
                                                </strong>
                                                <span class="badge bg-success ms-2">Support</span>
                                                <small class="text-muted d-block">
                                                    <?= date('M j, Y g:i A', strtotime($this->ticket['replied_at'])) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="message-content">
                                        <div class="bg-success bg-opacity-10 p-3 rounded border-start border-success border-3">
                                            <?= nl2br(htmlspecialchars($this->ticket['admin_reply'])) ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Status Messages -->
                            <?php if ($this->ticket['status'] === 'pending'): ?>
                                <div class="alert alert-info mt-4">
                                    <i class="fas fa-clock me-2"></i>
                                    <strong>Ticket Status:</strong> Your ticket is pending review. Our support team will respond within 24 hours.
                                </div>
                            <?php elseif ($this->ticket['status'] === 'answered'): ?>
                                <div class="alert alert-success mt-4">
                                    <i class="fas fa-reply me-2"></i>
                                    <strong>Ticket Answered:</strong> Our support team has responded to your ticket. If you need further assistance, please reply via email or create a new ticket.
                                </div>
                            <?php elseif ($this->ticket['status'] === 'closed'): ?>
                                <div class="alert alert-secondary mt-4">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <strong>Ticket Closed:</strong> This ticket has been resolved and closed. If you need further assistance, please create a new ticket.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- Ticket Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Ticket Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Ticket ID:</strong></div>
                                <div class="col-sm-8">#<?= htmlspecialchars($this->ticket['id']) ?></div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Category:</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge bg-secondary">
                                        <?= ucfirst(htmlspecialchars($this->ticket['category'])) ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Priority:</strong></div>
                                <div class="col-sm-8">
                                    <?php
                                    $priorityColors = [
                                        'low' => 'success',
                                        'medium' => 'warning',
                                        'high' => 'danger',
                                        'urgent' => 'dark'
                                    ];
                                    $priorityColor = $priorityColors[$this->ticket['priority']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $priorityColor ?>">
                                        <?= ucfirst(htmlspecialchars($this->ticket['priority'])) ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Created:</strong></div>
                                <div class="col-sm-8">
                                    <small><?= date('M j, Y g:i A', strtotime($this->ticket['created_at'])) ?></small>
                                </div>
                            </div>
                            
                            <?php if ($this->ticket['replied_at']): ?>
                                <div class="row mb-3">
                                    <div class="col-sm-4"><strong>Last Reply:</strong></div>
                                    <div class="col-sm-8">
                                        <small><?= date('M j, Y g:i A', strtotime($this->ticket['replied_at'])) ?></small>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-sm-4"><strong>Status:</strong></div>
                                <div class="col-sm-8">
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <?= ucfirst(htmlspecialchars($this->ticket['status'])) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Actions -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="/user/support/" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>All Tickets
                                </a>
                                
                                <?php if ($this->ticket['status'] !== 'closed'): ?>
                                    <a href="/user/support/create-ticket.php" class="btn btn-outline-success">
                                        <i class="fas fa-plus me-2"></i>New Ticket
                                    </a>
                                <?php endif; ?>
                                
                                <button class="btn btn-outline-secondary" onclick="window.print()">
                                    <i class="fas fa-print me-2"></i>Print Ticket
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Help -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">Need More Help?</h5>
                        </div>
                        <div class="card-body">
                            <p class="small text-muted">
                                If you need immediate assistance or have additional questions, you can:
                            </p>
                            <ul class="small">
                                <li>Create a new support ticket</li>
                                <li>Check our FAQ section</li>
                                <li>Contact us via email</li>
                            </ul>
                            
                            <div class="alert alert-info small">
                                <i class="fas fa-info-circle me-2"></i>
                                You will receive email notifications when your ticket is updated.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script>
            // Auto-refresh for pending tickets
            <?php if ($this->ticket['status'] === 'pending'): ?>
            setInterval(function() {
                if (!document.hidden) {
                    location.reload();
                }
            }, 30000); // Refresh every 30 seconds
            <?php endif; ?>
            
            // Print styles
            const printStyles = `
                @media print {
                    .card { border: 1px solid #ddd !important; box-shadow: none !important; }
                    .btn, .breadcrumb, nav { display: none !important; }
                    .container { max-width: none !important; }
                }
            `;
            
            const styleSheet = document.createElement("style");
            styleSheet.type = "text/css";
            styleSheet.innerText = printStyles;
            document.head.appendChild(styleSheet);
        </script>
        <?php
    }
}

$view = new SupportTicketDetailView($ticket);
$view->render();
?>