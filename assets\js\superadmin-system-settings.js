/**
 * Super Admin System Settings JavaScript
 * Handles form submissions, color previews, and AJAX functionality
 */

document.addEventListener('DOMContentLoaded', function () {
    initializeSystemSettings();
});

/**
 * Initialize system settings functionality
 */
function initializeSystemSettings() {
    // Initialize color pickers
    initializeColorPickers();

    // Initialize form handlers
    initializeFormHandlers();

    // Initialize file upload handlers
    initializeFileUploadHandlers();

    // Initialize validation
    initializeFormValidation();

    // Initialize tooltips
    initializeTooltips();
}

/**
 * Initialize color picker functionality
 */
function initializeColorPickers() {
    const primaryColorPicker = document.getElementById('primary_color');
    const secondaryColorPicker = document.getElementById('secondary_color');

    if (primaryColorPicker) {
        primaryColorPicker.addEventListener('input', function () {
            updateColorPreview('primary', this.value);
            this.nextElementSibling.value = this.value;
        });
    }

    if (secondaryColorPicker) {
        secondaryColorPicker.addEventListener('input', function () {
            updateColorPreview('secondary', this.value);
            this.nextElementSibling.value = this.value;
        });
    }
}

/**
 * Update color preview
 */
function updateColorPreview(type, color) {
    const previewButton = document.getElementById(`${type}-preview`);
    if (previewButton) {
        previewButton.style.backgroundColor = color;
        previewButton.style.borderColor = color;
    }

    // Update CSS custom property
    document.documentElement.style.setProperty(`--${type}-color`, color);
}

/**
 * Initialize form handlers
 */
function initializeFormHandlers() {
    // General settings form
    const generalForm = document.getElementById('generalSettingsForm');
    if (generalForm) {
        generalForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'general');
        });
    }

    // Financial settings form
    const financialForm = document.getElementById('financialSettingsForm');
    if (financialForm) {
        financialForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'financial');
        });
    }

    // Security settings form
    const securityForm = document.getElementById('securitySettingsForm');
    if (securityForm) {
        securityForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'security');
        });
    }

    // Appearance settings form
    const appearanceForm = document.getElementById('appearanceSettingsForm');
    if (appearanceForm) {
        appearanceForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'appearance');
        });
    }

    // Email settings form
    const emailForm = document.getElementById('emailSettingsForm');
    if (emailForm) {
        emailForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'email');
        });
    }

    // System settings form
    const systemForm = document.getElementById('systemSettingsForm');
    if (systemForm) {
        systemForm.addEventListener('submit', function (e) {
            handleFormSubmission(e, 'system');
        });
    }
}


/**
 * Handle form submission with AJAX
 */
function handleFormSubmission(e, formType) {
    e.preventDefault();

    const form = e.target;
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;

    // Show loading state
    submitButton.classList.add('loading');
    submitButton.disabled = true;

    // Prepare form data
    const formData = new FormData(form);

    // Send AJAX request
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);

                // Apply appearance changes immediately if it's appearance form
                if (formType === 'appearance') {
                    applyAppearanceChanges(formData);
                }
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'An error occurred while saving settings. Please try again.');
        })
        .finally(() => {
            // Reset button state
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
}

/**
 * Initialize file upload handlers
 */
function initializeFileUploadHandlers() {
    const logoFileInput = document.getElementById('logo_file');
    if (logoFileInput) {
        logoFileInput.addEventListener('change', function () {
            previewLogoUpload(this);
        });
    }
}

/**
 * Preview logo upload
 */
function previewLogoUpload(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function (e) {
            // Create or update preview
            let previewContainer = document.querySelector('.logo-upload-preview');
            if (!previewContainer) {
                previewContainer = document.createElement('div');
                previewContainer.className = 'logo-upload-preview mt-3';
                input.parentNode.appendChild(previewContainer);
            }

            previewContainer.innerHTML = `
                <label class="form-label">Preview:</label>
                <div class="logo-preview">
                    <img src="${e.target.result}" alt="Logo Preview" class="img-thumbnail" style="max-height: 100px;">
                </div>
            `;
        };

        reader.readAsDataURL(input.files[0]);
    }
}

/**
 * Apply appearance changes immediately
 */
function applyAppearanceChanges(formData) {
    const primaryColor = formData.get('primary_color');
    const secondaryColor = formData.get('secondary_color');

    if (primaryColor) {
        document.documentElement.style.setProperty('--primary-color', primaryColor);
        updateColorPreview('primary', primaryColor);
    }

    if (secondaryColor) {
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);
        updateColorPreview('secondary', secondaryColor);
    }
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    // Add real-time validation for numeric inputs
    const numericInputs = document.querySelectorAll('input[type="number"]');
    numericInputs.forEach(input => {
        input.addEventListener('input', function () {
            validateNumericInput(this);
        });
    });

    // Add validation for email inputs
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        input.addEventListener('blur', function () {
            validateEmailInput(this);
        });
    });

    // Add validation for URL inputs
    const urlInputs = document.querySelectorAll('input[type="url"]');
    urlInputs.forEach(input => {
        input.addEventListener('blur', function () {
            validateUrlInput(this);
        });
    });
}

/**
 * Validate numeric input
 */
function validateNumericInput(input) {
    const value = parseFloat(input.value);
    const min = parseFloat(input.min);
    const max = parseFloat(input.max);

    let isValid = true;
    let message = '';

    if (isNaN(value)) {
        isValid = false;
        message = 'Please enter a valid number';
    } else if (min !== undefined && value < min) {
        isValid = false;
        message = `Value must be at least ${min}`;
    } else if (max !== undefined && value > max) {
        isValid = false;
        message = `Value must be no more than ${max}`;
    }

    updateInputValidation(input, isValid, message);
}

/**
 * Validate email input
 */
function validateEmailInput(input) {
    const email = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    let isValid = true;
    let message = '';

    if (email && !emailRegex.test(email)) {
        isValid = false;
        message = 'Please enter a valid email address';
    }

    updateInputValidation(input, isValid, message);
}

/**
 * Validate URL input
 */
function validateUrlInput(input) {
    const url = input.value.trim();

    let isValid = true;
    let message = '';

    if (url) {
        try {
            new URL(url);
        } catch {
            isValid = false;
            message = 'Please enter a valid URL';
        }
    }

    updateInputValidation(input, isValid, message);
}

/**
 * Update input validation state
 */
function updateInputValidation(input, isValid, message) {
    // Remove existing validation classes
    input.classList.remove('is-valid', 'is-invalid');

    // Remove existing feedback
    const existingFeedback = input.parentNode.querySelector('.invalid-feedback, .valid-feedback');
    if (existingFeedback) {
        existingFeedback.remove();
    }

    if (!isValid && message) {
        input.classList.add('is-invalid');

        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        input.parentNode.appendChild(feedback);
    } else if (input.value.trim()) {
        input.classList.add('is-valid');
    }
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    // Initialize Bootstrap tooltips if available
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * Show alert message
 */
function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert.auto-dismiss');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show auto-dismiss`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of main content
    const mainContent = document.querySelector('.main-content');
    const firstChild = mainContent.querySelector('h1').nextElementSibling;
    mainContent.insertBefore(alertDiv, firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * Test SMTP connection
 */
function testSMTPConnection() {
    const button = document.querySelector('[onclick="testSMTPConnection()"]');
    const originalText = button.innerHTML;

    button.classList.add('loading');
    button.disabled = true;

    fetch('/superadmin/settings/api/test-smtp.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'SMTP connection test successful! Test email sent.');
            } else {
                showAlert('danger', `SMTP connection failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to test SMTP connection. Please try again.');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

/**
 * Export settings
 */
function exportSettings() {
    const button = document.querySelector('[onclick="exportSettings()"]');
    const originalText = button.innerHTML;

    button.classList.add('loading');
    button.disabled = true;

    fetch('/superadmin/settings/api/export.php', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => {
            if (response.ok) {
                return response.blob();
            }
            throw new Error('Export failed');
        })
        .then(blob => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `system-settings-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showAlert('success', 'Settings exported successfully!');
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to export settings. Please try again.');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

/**
 * Reset settings to defaults
 */
function resetToDefaults() {
    if (!confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
        return;
    }

    const button = document.querySelector('[onclick="resetToDefaults()"]');
    const originalText = button.innerHTML;

    button.classList.add('loading');
    button.disabled = true;

    fetch('/superadmin/settings/api/reset-defaults.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Settings reset to defaults successfully! Page will reload in 3 seconds.');
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                showAlert('danger', `Failed to reset settings: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to reset settings. Please try again.');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

/**
 * Clear system cache
 */
function clearSystemCache() {
    if (!confirm('Are you sure you want to clear the system cache?')) {
        return;
    }

    const button = document.querySelector('[onclick="clearSystemCache()"]');
    const originalText = button.innerHTML;

    button.classList.add('loading');
    button.disabled = true;

    fetch('/superadmin/settings/api/clear-cache.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'System cache cleared successfully!');
            } else {
                showAlert('danger', `Failed to clear cache: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to clear cache. Please try again.');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

/**
 * Run system diagnostics
 */
function runSystemDiagnostics() {
    const button = document.querySelector('[onclick="runSystemDiagnostics()"]');
    const originalText = button.innerHTML;

    button.classList.add('loading');
    button.disabled = true;

    fetch('/superadmin/settings/api/diagnostics.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDiagnosticsResults(data.results);
            } else {
                showAlert('danger', `Diagnostics failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'Failed to run diagnostics. Please try again.');
        })
        .finally(() => {
            button.classList.remove('loading');
            button.disabled = false;
            button.innerHTML = originalText;
        });
}

/**
 * Display diagnostics results
 */
function displayDiagnosticsResults(results) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">System Diagnostics Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="diagnostics-results">
                        ${formatDiagnosticsResults(results)}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }
}

/**
 * Format diagnostics results for display
 */
function formatDiagnosticsResults(results) {
    let html = '';

    for (const [category, tests] of Object.entries(results)) {
        html += `<h6 class="mt-3 mb-2">${category}</h6>`;

        for (const [test, result] of Object.entries(tests)) {
            const statusClass = result.status === 'pass' ? 'text-success' :
                result.status === 'warning' ? 'text-warning' : 'text-danger';
            const icon = result.status === 'pass' ? 'fa-check-circle' :
                result.status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle';

            html += `
                <div class="d-flex align-items-center mb-2">
                    <i class="fas ${icon} ${statusClass} me-2"></i>
                    <span class="me-auto">${test}</span>
                    <span class="${statusClass}">${result.message}</span>
                </div>
            `;
        }
    }

    return html;
}