<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/controllers/AdminController.php';
require_once '../../classes/views/AdminPaymentMethodsView.php';

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    header('Location: ../../login.php');
    exit;
}

// Initialize controller and view
$controller = new AdminController();
$view = new AdminPaymentMethodsView();

// Handle page rendering
try {
    $view->render();
} catch (Exception $e) {
    error_log("Payment methods page error: " . $e->getMessage());
    header('Location: ../dashboard/?error=system_error');
    exit;
}
?>