<?php
/**
 * LazyLoading trait - Provides lazy loading functionality for model relationships
 */
trait LazyLoading {
    private $loadedRelations = [];
    
    /**
     * Load a relationship lazily
     */
    protected function loadRelation($relationName, $callback) {
        if (!isset($this->loadedRelations[$relationName])) {
            $this->loadedRelations[$relationName] = $callback();
        }
        
        return $this->loadedRelations[$relationName];
    }
    
    /**
     * Check if a relation is loaded
     */
    protected function isRelationLoaded($relationName) {
        return isset($this->loadedRelations[$relationName]);
    }
    
    /**
     * Clear loaded relations (useful for testing)
     */
    protected function clearLoadedRelations() {
        $this->loadedRelations = [];
    }
    
    /**
     * Eager load multiple relations
     */
    protected function eagerLoad(array $relations) {
        foreach ($relations as $relationName => $callback) {
            if (!$this->isRelationLoaded($relationName)) {
                $this->loadedRelations[$relationName] = $callback();
            }
        }
    }
}
?>