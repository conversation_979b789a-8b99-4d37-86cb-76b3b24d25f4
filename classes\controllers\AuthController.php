<?php
require_once __DIR__ . '/../services/AuthenticationManager.php';
require_once __DIR__ . '/../services/SessionManager.php';
require_once __DIR__ . '/../services/CSRFProtection.php';
require_once __DIR__ . '/../services/TwoFactorAuthService.php';
require_once __DIR__ . '/../services/SecurityAuditService.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';
require_once __DIR__ . '/../validators/SecurityValidator.php';

/**
 * AuthController - Handles authentication-related requests
 */
class AuthController {
    
    /**
     * Handle login request
     */
    public static function login() {
        // If already logged in, redirect to dashboard
        if (AuthenticationManager::isAuthenticated()) {
            $user = AuthenticationManager::getCurrentUser();
            header('Location: ' . self::getRedirectUrl($user->role));
            exit();
        }
        
        $errors = [];
        $message = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                // Check rate limiting
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
                if (AuthenticationManager::isIPRateLimited($ipAddress)) {
                    $errors['rate_limit'] = 'Too many login attempts. Please try again later.';
                } else {
                    // Process login
                    $identifier = $_POST['identifier'] ?? '';
                    $password = $_POST['password'] ?? '';
                    $remember = isset($_POST['remember']);
                    
                    $result = AuthenticationManager::login($identifier, $password, $remember);
                    
                    if ($result['success']) {
                        // Redirect to appropriate dashboard
                        header('Location: ' . $result['redirect']);
                        exit();
                    } else {
                        if (isset($result['field'])) {
                            $errors[$result['field']] = $result['error'];
                        } else {
                            $errors['general'] = $result['error'];
                        }
                        
                        // Log failed login attempt
                        self::logFailedLogin($identifier, $ipAddress);
                    }
                }
            }
        }
        
        // Check for flash messages
        $flashMessage = SessionManager::getMessage();
        if ($flashMessage) {
            $message = $flashMessage['message'];
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Handle registration request
     */
    public static function register() {
        // If already logged in, redirect to dashboard
        if (AuthenticationManager::isAuthenticated()) {
            $user = AuthenticationManager::getCurrentUser();
            header('Location: ' . self::getRedirectUrl($user->role));
            exit();
        }
        
        $errors = [];
        $message = '';
        $success = false;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                // Process registration
                $userData = [
                    'username' => $_POST['username'] ?? '',
                    'email' => $_POST['email'] ?? '',
                    'password' => $_POST['password'] ?? '',
                    'confirm_password' => $_POST['confirm_password'] ?? '',
                    'first_name' => $_POST['first_name'] ?? '',
                    'last_name' => $_POST['last_name'] ?? '',
                    'phone' => $_POST['phone'] ?? '',
                    'csrf_token' => $_POST['csrf_token'] ?? ''
                ];
                
                // Validate password confirmation
                if ($userData['password'] !== $userData['confirm_password']) {
                    $errors['confirm_password'] = 'Passwords do not match';
                }
                
                if (empty($errors)) {
                    $result = AuthenticationManager::register($userData);
                    
                    if ($result['success']) {
                        $success = true;
                        $message = $result['message'];
                    } else {
                        $errors = $result['errors'] ?? ['general' => 'Registration failed'];
                    }
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Handle logout request
     */
    public static function logout() {
        AuthenticationManager::logout();
        SessionManager::setMessage('You have been logged out successfully.', 'success');
        header('Location: /login.php');
        exit();
    }
    
    /**
     * Handle forgot password request
     */
    public static function forgotPassword() {
        $errors = [];
        $message = '';
        $success = false;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $email = $_POST['email'] ?? '';
                
                $result = AuthenticationManager::requestPasswordReset($email);
                
                if ($result['success']) {
                    $success = true;
                    $message = $result['message'];
                } else {
                    $errors['email'] = $result['error'];
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Handle password reset request
     */
    public static function resetPassword() {
        $token = $_GET['token'] ?? '';
        $errors = [];
        $message = '';
        $success = false;
        $validToken = false;
        
        // Validate token
        if ($token) {
            $user = User::findByPasswordResetToken($token);
            $validToken = $user !== null;
        }
        
        if (!$validToken) {
            $errors['token'] = 'Invalid or expired reset token';
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $newPassword = $_POST['password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                $result = AuthenticationManager::resetPassword($token, $newPassword, $confirmPassword);
                
                if ($result['success']) {
                    $success = true;
                    $message = $result['message'];
                } else {
                    $errors = $result['errors'] ?? ['general' => 'Password reset failed'];
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'valid_token' => $validToken,
            'token' => $token,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Handle email verification
     */
    public static function verifyEmail() {
        $token = $_GET['token'] ?? '';
        $errors = [];
        $message = '';
        $success = false;
        
        if ($token) {
            $result = AuthenticationManager::verifyEmail($token);
            
            if ($result['success']) {
                $success = true;
                $message = $result['message'];
                SessionManager::setMessage($message, 'success');
            } else {
                $errors['token'] = $result['error'];
            }
        } else {
            $errors['token'] = 'Verification token is required';
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success
        ];
    }
    
    /**
     * Handle resend email verification
     */
    public static function resendVerification() {
        $errors = [];
        $message = '';
        $success = false;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $email = $_POST['email'] ?? '';
                
                // Find user by email
                $user = User::findByEmail($email);
                if ($user && !$user->email_verified) {
                    // Generate new verification token
                    $user->generateEmailVerificationToken();
                    
                    // Send verification email (this would be implemented in AuthenticationManager)
                    $success = true;
                    $message = 'Verification email has been resent. Please check your inbox.';
                } else {
                    $errors['email'] = 'Email not found or already verified';
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Check authentication status (AJAX endpoint)
     */
    public static function checkAuth() {
        header('Content-Type: application/json');
        
        $isAuthenticated = AuthenticationManager::isAuthenticated();
        $user = null;
        
        if ($isAuthenticated) {
            $currentUser = AuthenticationManager::getCurrentUser();
            $user = [
                'id' => $currentUser->getId(),
                'username' => $currentUser->username,
                'email' => $currentUser->email,
                'role' => $currentUser->role,
                'first_name' => $currentUser->first_name,
                'last_name' => $currentUser->last_name
            ];
        }
        
        echo json_encode([
            'authenticated' => $isAuthenticated,
            'user' => $user,
            'csrf_token' => CSRFProtection::generateToken()
        ]);
        exit();
    }
    
    /**
     * Extend session (AJAX endpoint)
     */
    public static function extendSession() {
        header('Content-Type: application/json');
        
        // Validate CSRF token for AJAX
        CSRFProtection::validateAjaxRequest();
        
        $extended = SessionManager::extendSession();
        $timeLeft = SessionManager::getTimeUntilExpiry();
        
        echo json_encode([
            'success' => $extended,
            'time_left' => $timeLeft,
            'expires_at' => time() + $timeLeft
        ]);
        exit();
    }
    
    /**
     * Get redirect URL based on user role
     */
    private static function getRedirectUrl($role) {
        $baseUrl = defined('AUTO_BASE_URL') ? AUTO_BASE_URL : BASE_URL;

        switch ($role) {
            case User::ROLE_SUPERADMIN:
                return $baseUrl . 'superadmin/dashboard/';
            case User::ROLE_ADMIN:
                return $baseUrl . 'admin/dashboard/';
            case User::ROLE_USER:
            default:
                return $baseUrl . 'user/dashboard/';
        }
    }
    
    /**
     * Log failed login attempt
     */
    private static function logFailedLogin($identifier, $ipAddress) {
        try {
            $db = getDB();
            $stmt = $db->prepare("
                INSERT INTO audit_logs (user_id, action, ip_address, user_agent, old_values, created_at) 
                VALUES (NULL, 'login_failed', ?, ?, ?, NOW())
            ");
            
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $details = json_encode(['identifier' => $identifier]);
            
            $stmt->execute([$ipAddress, $userAgent, $details]);
        } catch (Exception $e) {
            error_log("Failed login logging error: " . $e->getMessage());
        }
    }
    
    /**
     * Require authentication middleware
     */
    public static function requireAuth($redirectUrl = null) {
        if (!AuthenticationManager::isAuthenticated()) {
            // Store intended URL for redirect after login
            SessionManager::startSession();
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];

            $baseUrl = defined('AUTO_BASE_URL') ? AUTO_BASE_URL : BASE_URL;
            $redirectUrl = $redirectUrl ?: $baseUrl . 'login.php';
            header('Location: ' . $redirectUrl);
            exit();
        }
    }

    /**
     * Require role middleware
     */
    public static function requireRole($role, $redirectUrl = null) {
        self::requireAuth();

        if (!AuthenticationManager::hasRole($role)) {
            $baseUrl = defined('AUTO_BASE_URL') ? AUTO_BASE_URL : BASE_URL;
            $redirectUrl = $redirectUrl ?: $baseUrl . 'unauthorized.php';
            header('Location: ' . $redirectUrl);
            exit();
        }
    }

    /**
     * Require any of the specified roles
     */
    public static function requireAnyRole($roles, $redirectUrl = null) {
        self::requireAuth();

        if (!AuthenticationManager::hasAnyRole($roles)) {
            $baseUrl = defined('AUTO_BASE_URL') ? AUTO_BASE_URL : BASE_URL;
            $redirectUrl = $redirectUrl ?: $baseUrl . 'unauthorized.php';
            header('Location: ' . $redirectUrl);
            exit();
        }
    }
    
    /**
     * Get intended URL after login
     */
    public static function getIntendedUrl($default = null) {
        SessionManager::startSession();
        
        if (isset($_SESSION['intended_url'])) {
            $url = $_SESSION['intended_url'];
            unset($_SESSION['intended_url']);
            return $url;
        }
        
        return $default;
    }
    
    /**
     * Handle 2FA verification during login
     */
    public static function verify2FA() {
        $errors = [];
        $message = '';
        $success = false;
        
        // Check if user is in 2FA verification state
        if (!isset($_SESSION['2fa_user_id'])) {
            header('Location: /login.php');
            exit();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $code = $_POST['code'] ?? '';
                $isBackupCode = isset($_POST['use_backup_code']);
                $userId = $_SESSION['2fa_user_id'];
                
                $twoFA = TwoFactorAuthService::getInstance();
                $result = $twoFA->verifyLogin($userId, $code, $isBackupCode);
                
                if ($result['success']) {
                    // Complete login process
                    $user = new User();
                    $userData = $user->find($userId);
                    
                    if ($userData) {
                        // Set session data
                        $_SESSION['user_id'] = $userId;
                        $_SESSION['user_role'] = $userData['role'];
                        $_SESSION['authenticated'] = true;
                        
                        // Clear 2FA session data
                        unset($_SESSION['2fa_user_id']);
                        unset($_SESSION['2fa_required']);
                        
                        // Log successful login
                        SecurityAuditService::getInstance()->logAuthEvent('login_success_2fa', $userId);
                        
                        // Redirect to dashboard
                        $redirectUrl = self::getIntendedUrl(self::getRedirectUrl($userData['role']));
                        header('Location: ' . $redirectUrl);
                        exit();
                    }
                } else {
                    $errors['code'] = $result['message'];
                    
                    // Log failed 2FA attempt
                    SecurityAuditService::getInstance()->logAuthEvent('2fa_verification_failed', $userId);
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Setup 2FA for user
     */
    public static function setup2FA() {
        self::requireAuth();
        
        $errors = [];
        $message = '';
        $success = false;
        $qrCodeUrl = '';
        $secret = '';
        $backupCodes = [];
        
        $userId = $_SESSION['user_id'];
        $twoFA = TwoFactorAuthService::getInstance();
        
        // Check if 2FA is already enabled
        if ($twoFA->is2FAEnabled($userId)) {
            $message = '2FA is already enabled for your account.';
            return [
                'errors' => $errors,
                'message' => $message,
                'success' => false,
                'already_enabled' => true,
                'csrf_token' => CSRFProtection::generateToken()
            ];
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $secret = $_POST['secret'] ?? '';
                $verificationCode = $_POST['code'] ?? '';
                
                if (empty($secret) || empty($verificationCode)) {
                    $errors['code'] = 'Verification code is required.';
                } else {
                    $result = $twoFA->enable2FA($userId, $secret, $verificationCode);
                    
                    if ($result['success']) {
                        $success = true;
                        $message = $result['message'];
                        $backupCodes = $result['backup_codes'];
                        
                        // Log 2FA setup
                        SecurityAuditService::getInstance()->logAuthEvent('2fa_enabled', $userId);
                    } else {
                        $errors['code'] = $result['message'];
                    }
                }
            }
        } else {
            // Generate new secret and QR code
            $secret = $twoFA->generateSecret();
            $user = new User();
            $userData = $user->find($userId);
            
            if ($userData) {
                $qrCodeUrl = $twoFA->getQRCodeUrl($secret, $userData['email']);
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'secret' => $secret,
            'qr_code_url' => $qrCodeUrl,
            'backup_codes' => $backupCodes,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Disable 2FA for user
     */
    public static function disable2FA() {
        self::requireAuth();
        
        $errors = [];
        $message = '';
        $success = false;
        
        $userId = $_SESSION['user_id'];
        $twoFA = TwoFactorAuthService::getInstance();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $currentPassword = $_POST['current_password'] ?? '';
                $twoFACode = $_POST['code'] ?? '';
                
                $result = $twoFA->disable2FA($userId, $currentPassword, $twoFACode);
                
                if ($result['success']) {
                    $success = true;
                    $message = $result['message'];
                    
                    // Log 2FA disable
                    SecurityAuditService::getInstance()->logAuthEvent('2fa_disabled', $userId);
                } else {
                    $errors['general'] = $result['message'];
                }
            }
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'success' => $success,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
    
    /**
     * Get 2FA status for user
     */
    public static function get2FAStatus() {
        self::requireAuth();
        
        header('Content-Type: application/json');
        
        $userId = $_SESSION['user_id'];
        $twoFA = TwoFactorAuthService::getInstance();
        $status = $twoFA->get2FAStatus($userId);
        
        echo json_encode([
            'success' => true,
            'enabled' => $status['enabled'],
            'backup_codes_remaining' => $status['backup_codes']
        ]);
        exit();
    }
    
    /**
     * Enhanced login with 2FA support
     */
    public static function loginWithSecurity() {
        // If already logged in, redirect to dashboard
        if (AuthenticationManager::isAuthenticated()) {
            $user = AuthenticationManager::getCurrentUser();
            header('Location: ' . self::getRedirectUrl($user->role));
            exit();
        }
        
        $errors = [];
        $message = '';
        $require2FA = false;
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            $csrfResult = CSRFProtection::validateRequest();
            if (!$csrfResult['valid']) {
                $errors['csrf'] = $csrfResult['error'];
            } else {
                $identifier = $_POST['identifier'] ?? '';
                $password = $_POST['password'] ?? '';
                $remember = isset($_POST['remember']);
                $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
                $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                
                // Enhanced security validation
                $securityValidator = new SecurityValidator();
                if (!$securityValidator->validateLoginAttempt($identifier, $password, $ipAddress, $userAgent)) {
                    $errors = array_merge($errors, $securityValidator->getErrors());
                } else {
                    // Attempt login
                    $result = AuthenticationManager::login($identifier, $password, $remember);
                    
                    if ($result['success']) {
                        $user = AuthenticationManager::getCurrentUser();
                        $twoFA = TwoFactorAuthService::getInstance();
                        
                        // Check if 2FA is enabled
                        if ($twoFA->is2FAEnabled($user->getId())) {
                            // Set 2FA session state
                            $_SESSION['2fa_user_id'] = $user->getId();
                            $_SESSION['2fa_required'] = true;
                            
                            // Clear regular authentication
                            unset($_SESSION['user_id']);
                            unset($_SESSION['authenticated']);
                            
                            $require2FA = true;
                            $message = 'Please enter your 2FA code to complete login.';
                            
                            // Log 2FA required
                            SecurityAuditService::getInstance()->logAuthEvent('2fa_required', $user->getId());
                        } else {
                            // Regular login successful
                            SecurityAuditService::getInstance()->logAuthEvent('login_success', $user->getId());
                            header('Location: ' . $result['redirect']);
                            exit();
                        }
                    } else {
                        if (isset($result['field'])) {
                            $errors[$result['field']] = $result['error'];
                        } else {
                            $errors['general'] = $result['error'];
                        }
                        
                        // Log failed login attempt with enhanced details
                        SecurityAuditService::getInstance()->monitorFailedLogins($identifier, $ipAddress);
                    }
                }
            }
        }
        
        // Check for flash messages
        $flashMessage = SessionManager::getMessage();
        if ($flashMessage) {
            $message = $flashMessage['message'];
        }
        
        return [
            'errors' => $errors,
            'message' => $message,
            'require_2fa' => $require2FA,
            'csrf_token' => CSRFProtection::generateToken()
        ];
    }
}
?>