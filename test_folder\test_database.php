<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>Database Analysis and Test Table Creation</h2>";

try {
    $db = getDB();
    
    // Test database connection
    echo "<h3>1. Database Connection Status</h3>";
    if (Database::getInstance()->testConnection()) {
        echo "<p style='color: green;'>✓ Database connection successful!</p>";
        echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
        echo "<p><strong>Host:</strong> " . DB_HOST . ":" . DB_PORT . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Database connection failed!</p>";
        exit;
    }
    
    // Show existing tables
    echo "<h3>2. Existing Tables</h3>";
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>No tables found in database.</p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>{$table}</li>";
        }
        echo "</ul>";
    }
    
    // Create test table
    echo "<h3>3. Creating Test Table</h3>";
    $sql_test_table = "CREATE TABLE IF NOT EXISTS test_coinage (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_name VARCHAR(100) NOT NULL,
        test_email VARCHAR(100) NOT NULL,
        test_amount DECIMAL(10,2) DEFAULT 0.00,
        test_status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($db->exec($sql_test_table) !== false) {
        echo "<p style='color: green;'>✓ Test table 'test_coinage' created successfully!</p>";
        
        // Insert test data
        $test_data = [
            ['John Doe', '<EMAIL>', 1000.50],
            ['Jane Smith', '<EMAIL>', 2500.75],
            ['Bob Johnson', '<EMAIL>', 500.25]
        ];
        
        $sql_insert = "INSERT INTO test_coinage (test_name, test_email, test_amount) VALUES (?, ?, ?)";
        $stmt = $db->prepare($sql_insert);
        
        foreach ($test_data as $data) {
            if ($stmt->execute($data)) {
                echo "<p style='color: blue;'>→ Inserted: {$data[0]} - {$data[1]} - $" . number_format($data[2], 2) . "</p>";
            }
        }
        
        // Query test data
        echo "<h4>Test Data Retrieved:</h4>";
        $stmt = $db->query("SELECT * FROM test_coinage ORDER BY id");
        $results = $stmt->fetchAll();
        
        if ($results) {
            echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Amount</th><th>Status</th><th>Created</th></tr>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['test_name']}</td>";
                echo "<td>{$row['test_email']}</td>";
                echo "<td>$" . number_format($row['test_amount'], 2) . "</td>";
                echo "<td>{$row['test_status']}</td>";
                echo "<td>{$row['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Failed to create test table!</p>";
    }
    
    // Database info
    echo "<h3>4. Database Information</h3>";
    $stmt = $db->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "<p><strong>MySQL Version:</strong> " . $version['version'] . "</p>";
    
    $stmt = $db->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch();
    echo "<p><strong>Current Database:</strong> " . $db_info['db_name'] . "</p>";
    
    // Clean up test table (optional)
    echo "<h3>5. Cleanup</h3>";
    echo "<p><a href='?cleanup=1' style='color: red;'>Click here to drop test table</a></p>";
    
    if (isset($_GET['cleanup']) && $_GET['cleanup'] == '1') {
        if ($db->exec("DROP TABLE IF EXISTS test_coinage") !== false) {
            echo "<p style='color: green;'>✓ Test table dropped successfully!</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>