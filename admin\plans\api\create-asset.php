<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $symbol = strtoupper(trim($_POST['symbol'] ?? ''));
    $name = trim($_POST['name'] ?? '');
    $category = $_POST['category'] ?? '';
    $description = trim($_POST['description'] ?? '');
    $currentPrice = !empty($_POST['current_price']) ? floatval($_POST['current_price']) : null;
    $priceChange24h = !empty($_POST['price_change_24h']) ? floatval($_POST['price_change_24h']) : null;
    $status = $_POST['status'] ?? 'active';
    
    // Validation
    $errors = [];
    
    if (empty($symbol)) {
        $errors['symbol'] = 'Symbol is required';
    } elseif (strlen($symbol) > 20) {
        $errors['symbol'] = 'Symbol must not exceed 20 characters';
    } else {
        // Check if symbol already exists
        $stmt = $pdo->prepare("SELECT id FROM trading_assets WHERE symbol = ?");
        $stmt->execute([$symbol]);
        if ($stmt->fetch()) {
            $errors['symbol'] = 'Symbol already exists';
        }
    }
    
    if (empty($name)) {
        $errors['name'] = 'Name is required';
    } elseif (strlen($name) > 100) {
        $errors['name'] = 'Name must not exceed 100 characters';
    }
    
    if (!in_array($category, ['crypto', 'forex', 'stocks', 'commodities', 'indices'])) {
        $errors['category'] = 'Invalid category';
    }
    
    if ($currentPrice !== null && $currentPrice < 0) {
        $errors['current_price'] = 'Current price must be positive';
    }
    
    if (!in_array($status, ['active', 'inactive'])) {
        $errors['status'] = 'Invalid status';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Get next sort order
    $stmt = $pdo->prepare("SELECT MAX(sort_order) as max_order FROM trading_assets");
    $stmt->execute();
    $result = $stmt->fetch();
    $sortOrder = ($result['max_order'] ?? 0) + 1;
    
    // Create the asset
    $sql = "INSERT INTO trading_assets (symbol, name, category, description, current_price, price_change_24h, status, sort_order) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute([
        $symbol,
        $name,
        $category,
        $description,
        $currentPrice,
        $priceChange24h,
        $status,
        $sortOrder
    ]);
    
    if ($result) {
        $assetId = $pdo->lastInsertId();
        
        // Log the action
        AuditTrailService::log(
            'trading_asset_created',
            'trading_asset',
            $assetId,
            [
                'symbol' => $symbol,
                'name' => $name,
                'category' => $category,
                'status' => $status,
                'current_price' => $currentPrice,
                'price_change_24h' => $priceChange24h
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Asset created successfully',
            'asset_id' => $assetId
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create asset'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Create asset error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while creating the asset'
    ]);
}
?>