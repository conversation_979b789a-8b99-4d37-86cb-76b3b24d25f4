<?php
/**
 * ValidationHelper - Centralized validation utilities
 */
class ValidationHelper {
    
    /**
     * Validate email address
     */
    public static function validateEmail($email) {
        if (empty($email)) {
            return 'Email is required';
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return 'Invalid email format';
        }
        
        if (strlen($email) > 100) {
            return 'Email must not exceed 100 characters';
        }
        
        return null;
    }
    
    /**
     * Validate password strength
     */
    public static function validatePassword($password) {
        if (empty($password)) {
            return 'Password is required';
        }
        
        if (strlen($password) < 6) {
            return 'Password must be at least 6 characters';
        }
        
        if (strlen($password) > 255) {
            return 'Password is too long';
        }
        
        // Check for at least one letter and one number (only for passwords longer than 6 chars)
        if (strlen($password) >= 6 && (!preg_match('/[A-Za-z]/', $password) || !preg_match('/[0-9]/', $password))) {
            return 'Password must contain at least one letter and one number';
        }
        
        return null;
    }
    
    /**
     * Validate username
     */
    public static function validateUsername($username) {
        if (empty($username)) {
            return 'Username is required';
        }
        
        if (strlen($username) < 3) {
            return 'Username must be at least 3 characters';
        }
        
        if (strlen($username) > 50) {
            return 'Username must not exceed 50 characters';
        }
        
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            return 'Username can only contain letters, numbers, and underscores';
        }
        
        return null;
    }
    
    /**
     * Validate amount (for deposits, withdrawals, etc.)
     */
    public static function validateAmount($amount, $min = 0.01, $max = 999999999.99) {
        if (empty($amount) && $amount !== 0 && $amount !== '0') {
            return 'Amount is required';
        }
        
        if (!is_numeric($amount)) {
            return 'Amount must be numeric';
        }
        
        $amount = (float) $amount;
        
        if ($amount < $min) {
            return "Amount must be at least $" . number_format($min, 2);
        }
        
        if ($amount > $max) {
            return "Amount cannot exceed $" . number_format($max, 2);
        }
        
        return null;
    }
    
    /**
     * Validate phone number
     */
    public static function validatePhone($phone) {
        if (empty($phone)) {
            return null; // Phone is optional
        }
        
        if (strlen($phone) > 20) {
            return 'Phone number must not exceed 20 characters';
        }
        
        // Basic phone validation - allows various formats
        if (!preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $phone)) {
            return 'Invalid phone number format';
        }
        
        return null;
    }
    
    /**
     * Validate name (first name, last name)
     */
    public static function validateName($name, $fieldName = 'Name') {
        if (empty($name)) {
            return "$fieldName is required";
        }
        
        if (strlen($name) > 50) {
            return "$fieldName must not exceed 50 characters";
        }
        
        if (!preg_match('/^[a-zA-Z\s\-\'\.]+$/', $name)) {
            return "$fieldName can only contain letters, spaces, hyphens, apostrophes, and periods";
        }
        
        return null;
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCSRFToken($token) {
        if (!isset($_SESSION['csrf_token'])) {
            return 'CSRF token not found in session';
        }
        
        if (empty($token)) {
            return 'CSRF token is required';
        }
        
        if (!hash_equals($_SESSION['csrf_token'], $token)) {
            return 'Invalid CSRF token';
        }
        
        return null;
    }
    
    /**
     * Validate file upload
     */
    public static function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) { // 5MB default
        if (!isset($file) || $file['error'] === UPLOAD_ERR_NO_FILE) {
            return 'No file uploaded';
        }
        
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return 'File upload error: ' . self::getUploadErrorMessage($file['error']);
        }
        
        if ($file['size'] > $maxSize) {
            return 'File size exceeds maximum allowed size of ' . self::formatBytes($maxSize);
        }
        
        if (!empty($allowedTypes)) {
            $fileType = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if (!in_array($fileType, $allowedTypes)) {
                return 'File type not allowed. Allowed types: ' . implode(', ', $allowedTypes);
            }
        }
        
        return null;
    }
    
    /**
     * Validate JSON string
     */
    public static function validateJSON($json) {
        if (empty($json)) {
            return 'JSON data is required';
        }
        
        json_decode($json);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return 'Invalid JSON format: ' . json_last_error_msg();
        }
        
        return null;
    }
    
    /**
     * Validate URL
     */
    public static function validateURL($url) {
        if (empty($url)) {
            return null; // URL is optional
        }
        
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            return 'Invalid URL format';
        }
        
        return null;
    }
    
    /**
     * Validate date
     */
    public static function validateDate($date, $format = 'Y-m-d') {
        if (empty($date)) {
            return 'Date is required';
        }
        
        $d = DateTime::createFromFormat($format, $date);
        if (!$d || $d->format($format) !== $date) {
            return "Invalid date format. Expected format: $format";
        }
        
        return null;
    }
    
    /**
     * Validate required field
     */
    public static function validateRequired($value, $fieldName = 'Field') {
        if (empty($value) && $value !== 0 && $value !== '0') {
            return "$fieldName is required";
        }
        
        return null;
    }
    
    /**
     * Validate string length
     */
    public static function validateLength($value, $min = null, $max = null, $fieldName = 'Field') {
        if ($min !== null && strlen($value) < $min) {
            return "$fieldName must be at least $min characters";
        }
        
        if ($max !== null && strlen($value) > $max) {
            return "$fieldName must not exceed $max characters";
        }
        
        return null;
    }
    
    /**
     * Validate numeric value
     */
    public static function validateNumeric($value, $min = null, $max = null, $fieldName = 'Value') {
        if (!is_numeric($value)) {
            return "$fieldName must be numeric";
        }
        
        $value = (float) $value;
        
        if ($min !== null && $value < $min) {
            return "$fieldName must be at least $min";
        }
        
        if ($max !== null && $value > $max) {
            return "$fieldName cannot exceed $max";
        }
        
        return null;
    }
    
    /**
     * Validate enum value
     */
    public static function validateEnum($value, $allowedValues, $fieldName = 'Value') {
        if (!in_array($value, $allowedValues)) {
            return "$fieldName must be one of: " . implode(', ', $allowedValues);
        }
        
        return null;
    }
    
    /**
     * Get upload error message
     */
    private static function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
    
    /**
     * Format bytes to human readable format
     */
    private static function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Sanitize input data with enhanced security
     */
    public static function sanitize($data) {
        if (is_array($data)) {
            return array_map([self::class, 'sanitize'], $data);
        }
        
        // Remove null bytes and control characters
        $data = str_replace(chr(0), '', $data);
        $data = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $data);
        
        // Trim whitespace
        $data = trim($data);
        
        // Remove HTML tags and encode special characters
        $data = strip_tags($data);
        $data = htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        
        return $data;
    }
    
    /**
     * Advanced XSS protection
     */
    public static function preventXSS($data) {
        if (is_array($data)) {
            return array_map([self::class, 'preventXSS'], $data);
        }
        
        // Remove dangerous patterns
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
            '/onmouseover\s*=/i'
        ];
        
        foreach ($patterns as $pattern) {
            $data = preg_replace($pattern, '', $data);
        }
        
        return self::sanitize($data);
    }
    
    /**
     * SQL injection prevention (additional layer)
     */
    public static function preventSQLInjection($data) {
        if (is_array($data)) {
            return array_map([self::class, 'preventSQLInjection'], $data);
        }
        
        // Remove SQL keywords and dangerous characters
        $sqlPatterns = [
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute)\s/i',
            '/(\s|^)(or|and)\s+\d+\s*=\s*\d+/i',
            '/(\s|^)(or|and)\s+[\'"].*[\'"](\s*=\s*[\'"].*[\'"])?/i',
            '/[\'";\\\\]/',
            '/--/',
            '/\/\*.*\*\//'
        ];
        
        foreach ($sqlPatterns as $pattern) {
            $data = preg_replace($pattern, '', $data);
        }
        
        return trim($data);
    }
    
    /**
     * Validate and sanitize file path
     */
    public static function sanitizeFilePath($path) {
        // Remove directory traversal attempts
        $path = str_replace(['../', '..\\', '../', '..\\'], '', $path);
        
        // Remove null bytes
        $path = str_replace(chr(0), '', $path);
        
        // Only allow alphanumeric, dash, underscore, dot, and forward slash
        $path = preg_replace('/[^a-zA-Z0-9\-_\.\/]/', '', $path);
        
        return $path;
    }
    
    /**
     * Validate multiple fields at once
     */
    public static function validateFields($data, $rules) {
        $errors = [];
        
        foreach ($rules as $field => $fieldRules) {
            $value = $data[$field] ?? null;
            
            foreach ($fieldRules as $rule => $params) {
                $error = null;
                
                switch ($rule) {
                    case 'required':
                        if ($params) {
                            $error = self::validateRequired($value, $field);
                        }
                        break;
                        
                    case 'email':
                        if ($params && !empty($value)) {
                            $error = self::validateEmail($value);
                        }
                        break;
                        
                    case 'length':
                        if (!empty($value)) {
                            $error = self::validateLength($value, $params['min'] ?? null, $params['max'] ?? null, $field);
                        }
                        break;
                        
                    case 'numeric':
                        if (!empty($value)) {
                            $error = self::validateNumeric($value, $params['min'] ?? null, $params['max'] ?? null, $field);
                        }
                        break;
                        
                    case 'enum':
                        if (!empty($value)) {
                            $error = self::validateEnum($value, $params, $field);
                        }
                        break;
                }
                
                if ($error) {
                    $errors[$field] = $error;
                    break; // Stop at first error for this field
                }
            }
        }
        
        return $errors;
    }
}
?>