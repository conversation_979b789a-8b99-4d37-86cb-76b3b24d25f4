<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * WithdrawalStatusView - Displays withdrawal status and history
 */
class WithdrawalStatusView extends BaseView {
    use StatusColorTrait;
    
    protected function getTitle() {
        return 'Withdrawal Status - Coinage Trading';
    }
    
    protected function getAdditionalCSS() {
        return [
            '/assets/css/withdrawal-status.css'
        ];
    }
    
    protected function getAdditionalJS() {
        return [
            '/assets/js/withdrawal-status.js'
        ];
    }
    
    protected function renderBody() {
        $user = $this->data['user'];
        $withdrawals = $this->data['withdrawals'];
        $statistics = $this->data['statistics'];
        $recentTransactions = $this->data['recent_transactions'];
        $filters = $this->data['filters'];
        $pagination = $this->data['pagination'];
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0">Withdrawal Status</h1>
                            <p class="text-muted">Track your withdrawal requests and history</p>
                        </div>
                        <div>
                            <a href="request.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> New Withdrawal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-primary mb-2">
                                <i class="fas fa-list fa-2x"></i>
                            </div>
                            <h4 class="mb-1"><?= $statistics['total'] ?></h4>
                            <small class="text-muted">Total</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-warning mb-2">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                            <h4 class="mb-1"><?= $statistics['pending'] ?></h4>
                            <small class="text-muted">Pending</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-info mb-2">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                            <h4 class="mb-1"><?= $statistics['approved'] ?></h4>
                            <small class="text-muted">Approved</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-success mb-2">
                                <i class="fas fa-check-double fa-2x"></i>
                            </div>
                            <h4 class="mb-1"><?= $statistics['completed'] ?></h4>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-danger mb-2">
                                <i class="fas fa-times-circle fa-2x"></i>
                            </div>
                            <h4 class="mb-1"><?= $statistics['rejected'] ?></h4>
                            <small class="text-muted">Rejected</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2 col-sm-4 col-6 mb-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="text-secondary mb-2">
                                <i class="fas fa-dollar-sign fa-2x"></i>
                            </div>
                            <h6 class="mb-1">$<?= number_format($user->total_withdrawal, 2) ?></h6>
                            <small class="text-muted">Total Withdrawn</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3 align-items-end">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Filter by Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" <?= $filters['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="approved" <?= $filters['status'] === 'approved' ? 'selected' : '' ?>>Approved</option>
                                <option value="processing" <?= $filters['status'] === 'processing' ? 'selected' : '' ?>>Processing</option>
                                <option value="completed" <?= $filters['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="rejected" <?= $filters['status'] === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="?" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Withdrawals Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        Withdrawal History
                        <span class="badge bg-secondary"><?= number_format($pagination['total_records']) ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($withdrawals)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>No Withdrawals Found</h5>
                        <p class="text-muted">You haven't made any withdrawal requests yet.</p>
                        <a href="request.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Request Withdrawal
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Processed</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($withdrawals as $withdrawal): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium">
                                            <?= date('M j, Y', strtotime($withdrawal->created_at)) ?>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('g:i A', strtotime($withdrawal->created_at)) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="fw-bold text-danger">
                                            $<?= number_format($withdrawal->amount, 2) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="method-info">
                                            <div class="fw-medium"><?= htmlspecialchars($withdrawal->withdrawal_method) ?></div>
                                            <?php 
                                            $accountDetails = $withdrawal->getAccountDetailsArray();
                                            if (!empty($accountDetails)):
                                            ?>
                                            <small class="text-muted">
                                                <?php
                                                // Show first account detail as preview
                                                $firstDetail = array_values($accountDetails)[0] ?? '';
                                                if (strlen($firstDetail) > 20) {
                                                    echo htmlspecialchars(substr($firstDetail, 0, 20) . '...');
                                                } else {
                                                    echo htmlspecialchars($firstDetail);
                                                }
                                                ?>
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $this->getStatusColor($withdrawal->status) ?>">
                                            <?= $this->getStatusIcon($withdrawal->status) ?>
                                            <?= ucfirst($withdrawal->status) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($withdrawal->processed_at): ?>
                                        <div class="processed-info">
                                            <div class="fw-medium">
                                                <?= date('M j, Y', strtotime($withdrawal->processed_at)) ?>
                                            </div>
                                            <small class="text-muted">
                                                <?= date('g:i A', strtotime($withdrawal->processed_at)) ?>
                                            </small>
                                        </div>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="viewWithdrawalDetails(<?= $withdrawal->getId() ?>)"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            
                                            <?php if ($withdrawal->isPending()): ?>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="cancelWithdrawal(<?= $withdrawal->getId() ?>)"
                                                    title="Cancel Request">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <?php endif; ?>
                                            
                                            <a href="../transactions/?type=withdrawal" 
                                               class="btn btn-outline-info" 
                                               title="View Transaction">
                                                <i class="fas fa-receipt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="Withdrawal pagination">
                            <ul class="pagination justify-content-center mb-0">
                                <?php if ($pagination['current_page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] - 1])) ?>">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] + 1])) ?>">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing <?= (($pagination['current_page'] - 1) * $pagination['limit']) + 1 ?> to 
                                <?= min($pagination['current_page'] * $pagination['limit'], $pagination['total_records']) ?> 
                                of <?= number_format($pagination['total_records']) ?> withdrawals
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Withdrawal Details Modal -->
        <div class="modal fade" id="withdrawalDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Withdrawal Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="withdrawalDetailsContent">
                        <div class="text-center py-3">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cancel Confirmation Modal -->
        <div class="modal fade" id="cancelConfirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Cancel Withdrawal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to cancel this withdrawal request?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Request</button>
                        <button type="button" class="btn btn-danger" id="confirmCancelBtn">
                            <i class="fas fa-times"></i> Cancel Withdrawal
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function getStatusIcon($status) {
        $icons = [
            'pending' => '<i class="fas fa-clock"></i>',
            'approved' => '<i class="fas fa-check"></i>',
            'processing' => '<i class="fas fa-cog fa-spin"></i>',
            'completed' => '<i class="fas fa-check-double"></i>',
            'rejected' => '<i class="fas fa-times"></i>'
        ];
        
        return $icons[$status] ?? '<i class="fas fa-question"></i>';
    }
}
?>