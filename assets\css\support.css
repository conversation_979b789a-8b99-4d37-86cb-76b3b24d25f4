/* Support System Styles */

.support-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Create Ticket Form */
.create-ticket-form {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.create-ticket-form .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    padding: 1.5rem;
}

.create-ticket-form .card-header h4 {
    margin: 0;
    font-weight: 600;
}

.create-ticket-form .card-body {
    padding: 2rem;
}

/* Form Elements */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Character Counter */
#charCount {
    font-weight: 600;
    transition: color 0.2s ease;
}

/* Priority Select Styling */
.form-select.border-danger {
    border-color: #dc3545 !important;
}

.form-select.border-warning {
    border-color: #ffc107 !important;
}

.form-select.border-info {
    border-color: #17a2b8 !important;
}

.form-select.border-secondary {
    border-color: #6c757d !important;
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.5rem;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0277bd;
    border-left: 4px solid #2196f3;
}

.alert-success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
}

/* Button Styling */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
    border: none;
}

/* Success State */
.success-state {
    text-align: center;
    padding: 2rem;
}

.success-state .alert {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.success-state .btn {
    margin: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .create-ticket-form .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .success-state .btn {
        width: auto;
        margin: 0.25rem;
    }
}

/* Form Validation States */
.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.94-.94 1.38 1.38 3.72-3.72.94.94-4.66 4.66z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Textarea Auto-resize */
.form-control.auto-resize {
    resize: vertical;
    min-height: 120px;
}

/* Help Text */
.help-text {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    margin: 1rem 0;
}

.help-text ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.help-text li {
    margin-bottom: 0.25rem;
}

/* Category Icons */
.category-icon {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    opacity: 0.7;
}

/* Priority Indicators */
.priority-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.priority-indicator.low {
    background-color: #28a745;
}

.priority-indicator.medium {
    background-color: #ffc107;
}

.priority-indicator.high {
    background-color: #fd7e14;
}

.priority-indicator.urgent {
    background-color: #dc3545;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}