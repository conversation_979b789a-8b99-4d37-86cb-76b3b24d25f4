-- Database Backup
-- Generated: 2025-07-27 20:05:43
-- Database: coinage

SET FOREIGN_KEY_CHECKS=0;

-- Table: audit_logs
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `entity_id` varchar(50) NOT NULL,
  `changes` json DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `additional_data` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_entity_type` (`entity_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_ip_address` (`ip_address`),
  CONSTRAINT `audit_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: deposits
DROP TABLE IF EXISTS `deposits`;
CREATE TABLE `deposits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `amount` decimal(15,2) NOT NULL,
  `bonus_amount` decimal(15,2) DEFAULT '0.00',
  `payment_method_id` int(11) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `proof_of_payment` varchar(255) DEFAULT NULL,
  `admin_note` text,
  `approved_by` int(11) DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `payment_method_id` (`payment_method_id`),
  KEY `approved_by` (`approved_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_approved_at` (`approved_at`),
  KEY `idx_transaction_id` (`transaction_id`),
  CONSTRAINT `deposits_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `deposits_ibfk_2` FOREIGN KEY (`plan_id`) REFERENCES `trading_plans` (`id`) ON DELETE SET NULL,
  CONSTRAINT `deposits_ibfk_3` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE SET NULL,
  CONSTRAINT `deposits_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: email_templates
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `template_key` varchar(100) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `body` text NOT NULL,
  `variables` json DEFAULT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `template_key` (`template_key`),
  KEY `idx_template_key` (`template_key`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: error_logs
DROP TABLE IF EXISTS `error_logs`;
CREATE TABLE `error_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `error_id` varchar(50) NOT NULL,
  `type` enum('php_error','exception','fatal_error','application_error','database_error','authentication_error','file_upload_error','financial_error','validation_error') NOT NULL,
  `severity` enum('low','medium','high','critical') NOT NULL,
  `category` enum('database','authentication','financial','file_upload','validation','system','security','api') NOT NULL,
  `message` text NOT NULL,
  `file` varchar(500) DEFAULT NULL,
  `line` int(11) DEFAULT NULL,
  `context` json DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `resolved` tinyint(1) DEFAULT '0',
  `resolved_by` int(11) DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `error_id` (`error_id`),
  KEY `resolved_by` (`resolved_by`),
  KEY `idx_error_id` (`error_id`),
  KEY `idx_type` (`type`),
  KEY `idx_severity` (`severity`),
  KEY `idx_category` (`category`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_resolved` (`resolved`),
  KEY `idx_severity_created` (`severity`,`created_at`),
  KEY `idx_category_created` (`category`,`created_at`),
  CONSTRAINT `error_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `error_logs_ibfk_2` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=98 DEFAULT CHARSET=utf8;

INSERT INTO `error_logs` (`id`, `error_id`, `type`, `severity`, `category`, `message`, `file`, `line`, `context`, `user_id`, `ip_address`, `user_agent`, `resolved`, `resolved_by`, `resolved_at`, `created_at`) VALUES
('1', 'ERR_20250727_68fdff52', 'php_error', 'high', 'system', 'Undefined array key \"pass\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php', '173', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:35:51'),
('2', 'ERR_20250727_2ab22e10', 'exception', 'high', 'system', 'Call to undefined method SystemSetting::get()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '370', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:35:51'),
('3', 'ERR_20250727_9c079be0', 'fatal_error', 'critical', 'system', 'Uncaught Error: Call to undefined method SystemSetting::get() in C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php:370\nStack trace:\n#0 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(306): ErrorHandlingService->sendToExternalMonitoring(Array)\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(89): ErrorHandlingService->logError(Array)\n#2 [internal function]: ErrorHandlingService->handleException(Object(Error))\n#3 {main}\n  thrown', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '370', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:35:52'),
('4', 'ERR_20250727_35c4a72a', 'php_error', 'high', 'system', 'Undefined array key \"pass\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php', '173', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:43'),
('5', 'ERR_20250727_2643fa03', 'php_error', 'high', 'system', 'Undefined array key \"pass\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php', '175', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:43'),
('6', 'ERR_20250727_73d3fb43', 'application_error', 'medium', 'system', 'Test application error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '34', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:43'),
('7', 'ERR_20250727_8eb62e51', 'database_error', 'high', 'database', 'Test database error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '44', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('8', 'ERR_20250727_733a4120', 'authentication_error', 'medium', 'authentication', 'Invalid credentials', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('9', 'ERR_20250727_c3db5477', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('10', 'ERR_20250727_36bfb344', 'financial_error', 'high', 'financial', 'Transaction failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('11', 'ERR_20250727_a1a831d5', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('12', 'ERR_20250727_7aedb5fd', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:44'),
('13', 'ERR_20250727_0659652b', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:46'),
('14', 'ERR_20250727_405d36fb', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:46'),
('15', 'ERR_20250727_775922cb', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:46'),
('16', 'ERR_20250727_f1430564', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('17', 'ERR_20250727_91f2a17b', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('18', 'ERR_20250727_9afee3e7', 'php_error', 'high', 'system', 'Undefined array key \"pass\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php', '173', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('19', 'ERR_20250727_3cece122', 'exception', 'high', 'system', 'Call to undefined method SystemSetting::get()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SystemHealthService.php', '343', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('20', 'ERR_20250727_03ba7f8a', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('21', 'ERR_20250727_ae3d794f', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:36:48'),
('22', 'ERR_20250727_ae792a87', 'application_error', 'medium', 'system', 'Test application error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '34', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:51'),
('23', 'ERR_20250727_385e6792', 'database_error', 'high', 'database', 'Test database error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '44', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('24', 'ERR_20250727_dc25e68a', 'authentication_error', 'medium', 'authentication', 'Invalid credentials', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('25', 'ERR_20250727_3e0d09c6', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('26', 'ERR_20250727_8b0c1d65', 'financial_error', 'high', 'financial', 'Transaction failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('27', 'ERR_20250727_6c45ac27', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('28', 'ERR_20250727_bbc2c529', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:52'),
('29', 'ERR_20250727_41417930', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:54'),
('30', 'ERR_20250727_e8c51f74', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:54'),
('31', 'ERR_20250727_03af2648', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:54'),
('32', 'ERR_20250727_1e1f17c2', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:56'),
('33', 'ERR_20250727_c265c360', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:38:56'),
('34', 'ERR_20250727_f7b163d9', 'exception', 'high', 'security', 'Class \"SecurityMiddleware\" not found', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '185', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:00'),
('35', 'ERR_20250727_6d4a3440', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:00'),
('36', 'ERR_20250727_2c253549', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:00'),
('37', 'ERR_20250727_cd305dcc', 'application_error', 'medium', 'system', 'Test application error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '35', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:19'),
('38', 'ERR_20250727_38a4cc45', 'database_error', 'high', 'database', 'Test database error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '45', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:19'),
('39', 'ERR_20250727_7fcfd88e', 'authentication_error', 'medium', 'authentication', 'Invalid credentials', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:19'),
('40', 'ERR_20250727_e36674be', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:19'),
('41', 'ERR_20250727_49237a85', 'financial_error', 'high', 'financial', 'Transaction failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:19'),
('42', 'ERR_20250727_3924e484', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:20'),
('43', 'ERR_20250727_d2c4f56c', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:20'),
('44', 'ERR_20250727_517a1945', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:22'),
('45', 'ERR_20250727_61a2c0de', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:22'),
('46', 'ERR_20250727_955e7a76', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:22'),
('47', 'ERR_20250727_298bc45b', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:24'),
('48', 'ERR_20250727_e90c8921', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:24'),
('49', 'ERR_20250727_507a93ba', 'exception', 'high', 'system', 'Call to undefined method SystemSetting::get()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\EmailNotificationService.php', '535', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:27'),
('50', 'ERR_20250727_3183a046', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:27'),
('51', 'ERR_20250727_c09dd323', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:39:27'),
('52', 'ERR_20250727_073c6614', 'application_error', 'medium', 'system', 'Test application error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '35', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:23'),
('53', 'ERR_20250727_edbaf5f2', 'database_error', 'high', 'database', 'Test database error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '45', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:23'),
('54', 'ERR_20250727_a8cd9edd', 'authentication_error', 'medium', 'authentication', 'Invalid credentials', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:24'),
('55', 'ERR_20250727_76b632b4', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:24'),
('56', 'ERR_20250727_08ff6f17', 'financial_error', 'high', 'financial', 'Transaction failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:24'),
('57', 'ERR_20250727_61390cab', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:24'),
('58', 'ERR_20250727_7ae0a14b', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:24'),
('59', 'ERR_20250727_6f938a00', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:26'),
('60', 'ERR_20250727_1904e4d9', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:27'),
('61', 'ERR_20250727_ad29d0dc', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:27'),
('62', 'ERR_20250727_c6baa90d', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:29'),
('63', 'ERR_20250727_283f0632', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:29'),
('64', 'ERR_20250727_8f1ddb71', 'exception', 'high', 'system', 'Call to undefined method SupportTicket::create()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SupportTicketService.php', '55', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:32'),
('65', 'ERR_20250727_b48c2eff', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:32'),
('66', 'ERR_20250727_4226eba0', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:42:32'),
('67', 'ERR_20250727_eac422bf', 'application_error', 'medium', 'system', 'Test application error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '35', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('68', 'ERR_20250727_fc7fc5ff', 'database_error', 'high', 'database', 'Test database error', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php', '45', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('69', 'ERR_20250727_7bf61c8c', 'authentication_error', 'medium', 'authentication', 'Invalid credentials', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('70', 'ERR_20250727_5cf7ea95', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('71', 'ERR_20250727_dc5d51c7', 'financial_error', 'high', 'financial', 'Transaction failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('72', 'ERR_20250727_8f7c8934', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('73', 'ERR_20250727_1ef4eb19', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:13'),
('74', 'ERR_20250727_b02f64bd', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:15'),
('75', 'ERR_20250727_0289601c', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '449', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:15'),
('76', 'ERR_20250727_6ea57953', 'php_error', 'high', 'system', 'Undefined array key \"timestamp\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '453', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:15'),
('77', 'ERR_20250727_de852a8f', 'php_error', 'high', 'system', 'mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '463', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:17'),
('78', 'ERR_20250727_8ab1b101', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', '1', '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:17'),
('79', 'ERR_20250727_17aa0135', 'exception', 'high', 'system', 'Call to a member function where() on array', 'C:\\MAMP\\htdocs\\Coinage\\classes\\models\\EmailTemplate.php', '134', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:20'),
('80', 'ERR_20250727_42755f8a', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:20'),
('81', 'ERR_20250727_1ca51598', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, '127.0.0.1', 'Test Agent', '0', NULL, NULL, '2025-07-27 21:43:20'),
('82', 'ERR_20250727_681d6589', 'application_error', 'low', 'system', 'Test application error for Phase 3', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php', '29', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:48'),
('83', 'ERR_20250727_7a206846', 'database_error', 'high', 'database', 'Test database error for Phase 3', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php', '40', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:48'),
('84', 'ERR_20250727_99d684f2', 'authentication_error', 'medium', 'authentication', 'Invalid credentials test', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('85', 'ERR_20250727_2a8012d9', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('86', 'ERR_20250727_29c05e08', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('87', 'ERR_20250727_eb7bc360', 'exception', 'high', 'system', 'Call to private method SystemHealthService::checkMemory() from global scope', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php', '91', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('88', 'ERR_20250727_0a6fbc56', 'php_error', 'high', 'system', 'http_response_code(): Cannot set response code - headers already sent (output started at C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php:13)', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '495', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('89', 'ERR_20250727_84136c38', 'php_error', 'high', 'system', 'Undefined array key \"error_id\"', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '497', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('90', 'ERR_20250727_0de626c8', 'php_error', 'low', 'system', 'htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php', '524', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:45:49'),
('91', 'ERR_20250727_c2cc89ed', 'application_error', 'low', 'system', 'Test application error for Phase 3', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php', '29', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:48:53'),
('92', 'ERR_20250727_fd6aa29c', 'database_error', 'high', 'database', 'Test database error for Phase 3', 'C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php', '40', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:48:53'),
('93', 'ERR_20250727_d017ce3c', 'authentication_error', 'medium', 'authentication', 'Invalid credentials test', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:48:53'),
('94', 'ERR_20250727_06cc8c51', 'file_upload_error', 'medium', 'file_upload', 'File exceeds maximum allowed size', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:48:53'),
('95', 'ERR_20250727_d4538bb9', 'validation_error', 'low', 'validation', 'Form validation failed', NULL, NULL, '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 21:48:54'),
('96', 'ERR_20250727_6ae10cfa', 'php_error', 'high', 'file_upload', 'file_put_contents(C:\\MAMP\\htdocs\\Coinage\\classes\\services/../../cache/namespace1/namespace1_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\CacheService.php', '72', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 22:05:43'),
('97', 'ERR_20250727_34a9bbfe', 'php_error', 'high', 'file_upload', 'file_put_contents(C:\\MAMP\\htdocs\\Coinage\\classes\\services/../../cache/namespace2/namespace2_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory', 'C:\\MAMP\\htdocs\\Coinage\\classes\\services\\CacheService.php', '72', '{\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}', NULL, 'unknown', 'unknown', '0', NULL, NULL, '2025-07-27 22:05:43');

-- Table: error_resolutions
DROP TABLE IF EXISTS `error_resolutions`;
CREATE TABLE `error_resolutions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `error_id` varchar(50) NOT NULL,
  `resolution_type` enum('fixed','ignored','duplicate','wont_fix') NOT NULL,
  `resolution_notes` text,
  `resolved_by` int(11) NOT NULL,
  `resolved_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_error_id` (`error_id`),
  KEY `idx_resolution_type` (`resolution_type`),
  KEY `idx_resolved_by` (`resolved_by`),
  KEY `idx_resolved_at` (`resolved_at`),
  CONSTRAINT `error_resolutions_ibfk_1` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: failed_login_attempts
DROP TABLE IF EXISTS `failed_login_attempts`;
CREATE TABLE `failed_login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(255) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_identifier` (`identifier`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_attempt_time` (`attempt_time`),
  KEY `idx_identifier_time` (`identifier`,`attempt_time`),
  KEY `idx_ip_time` (`ip_address`,`attempt_time`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8;

INSERT INTO `failed_login_attempts` (`id`, `identifier`, `ip_address`, `attempt_time`) VALUES
('1', '<EMAIL>', '127.0.0.1', '2025-07-27 21:36:44'),
('2', '<EMAIL>', '127.0.0.1', '2025-07-27 21:38:52'),
('3', '<EMAIL>', '127.0.0.1', '2025-07-27 21:39:19'),
('4', '<EMAIL>', '127.0.0.1', '2025-07-27 21:42:24'),
('5', '<EMAIL>', '127.0.0.1', '2025-07-27 21:43:13'),
('6', '<EMAIL>', '127.0.0.1', '2025-07-27 21:45:49'),
('7', '<EMAIL>', '127.0.0.1', '2025-07-27 21:48:53');

-- Table: notifications
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `type` enum('info','success','warning','error') DEFAULT 'info',
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: payment_methods
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` enum('crypto','bank','paypal','other') NOT NULL,
  `details` json NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `sort_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

INSERT INTO `payment_methods` (`id`, `name`, `type`, `details`, `status`, `sort_order`, `created_at`, `updated_at`) VALUES
('1', 'Bitcoin (BTC)', 'crypto', '{\"fee\": \"0.0005 BTC\", \"address\": \"******************************************\", \"network\": \"Bitcoin\", \"confirmations\": 3}', 'active', '1', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('2', 'Ethereum (ETH)', 'crypto', '{\"fee\": \"0.01 ETH\", \"address\": \"******************************************\", \"network\": \"Ethereum\", \"confirmations\": 12}', 'active', '2', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('3', 'Tether (USDT)', 'crypto', '{\"fee\": \"1 USDT\", \"address\": \"TQn9Y2khEsLJW1ChVWFMSMeRDow5oRP8V\", \"network\": \"Tron (TRC20)\", \"confirmations\": 20}', 'active', '3', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('4', 'USD Coin (USDC)', 'crypto', '{\"fee\": \"2 USDC\", \"address\": \"******************************************\", \"network\": \"Ethereum (ERC20)\", \"confirmations\": 12}', 'active', '4', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('5', 'PayPal', 'paypal', '{\"email\": \"<EMAIL>\", \"fee_percent\": \"3.5\", \"processing_time\": \"1-2 business days\"}', 'active', '5', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('6', 'Bank Transfer', 'bank', '{\"fee\": \"$25\", \"bank_name\": \"Chase Bank\", \"swift_code\": \"CHASUS33\", \"account_name\": \"Coinage Trading Ltd\", \"account_number\": \"**********\", \"routing_number\": \"*********\"}', 'active', '6', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('7', 'Skrill', 'other', '{\"email\": \"<EMAIL>\", \"fee_percent\": \"2.9\", \"processing_time\": \"Instant\"}', 'active', '7', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('8', 'Neteller', 'other', '{\"email\": \"<EMAIL>\", \"fee_percent\": \"2.5\", \"processing_time\": \"Instant\"}', 'active', '8', '2025-07-27 16:59:42', '2025-07-27 16:59:42');

-- Table: performance_metrics
DROP TABLE IF EXISTS `performance_metrics`;
CREATE TABLE `performance_metrics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `metric_type` enum('page_load','database_query','api_response','memory_usage','cpu_usage') NOT NULL,
  `metric_name` varchar(100) NOT NULL,
  `value` decimal(10,4) NOT NULL,
  `unit` varchar(20) NOT NULL,
  `context` json DEFAULT NULL,
  `recorded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_metric_type` (`metric_type`),
  KEY `idx_metric_name` (`metric_name`),
  KEY `idx_recorded_at` (`recorded_at`),
  KEY `idx_type_name` (`metric_type`,`metric_name`),
  KEY `idx_type_recorded` (`metric_type`,`recorded_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: security_audit_logs
DROP TABLE IF EXISTS `security_audit_logs`;
CREATE TABLE `security_audit_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` enum('auth','financial','admin','system') NOT NULL,
  `event` varchar(100) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text,
  `request_uri` varchar(500) DEFAULT NULL,
  `http_method` varchar(10) DEFAULT NULL,
  `session_id` varchar(128) DEFAULT NULL,
  `details` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_event` (`event`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_category_event` (`category`,`event`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  CONSTRAINT `security_audit_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8;

INSERT INTO `security_audit_logs` (`id`, `category`, `event`, `user_id`, `ip_address`, `user_agent`, `request_uri`, `http_method`, `session_id`, `details`, `created_at`) VALUES
('1', 'auth', 'test_login', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\"}', '2025-07-27 17:08:20'),
('2', 'financial', 'test_deposit', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"amount\": 100}', '2025-07-27 17:08:20'),
('3', 'admin', 'test_admin_action', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"action\": \"test\", \"target_user_id\": null}', '2025-07-27 17:08:20'),
('4', 'system', 'test_system_event', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"component\": \"test\"}', '2025-07-27 17:08:20'),
('5', 'auth', 'login_failed', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"email\": \"<EMAIL>\"}', '2025-07-27 17:08:20'),
('6', 'financial', 'deposit_request', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"amount\": 500}', '2025-07-27 17:08:20'),
('7', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php\", \"line\": 173, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"pass\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_68fdff52\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:35:51\"}', '2025-07-27 21:35:51'),
('8', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 370, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php(306): ErrorHandlingService->sendToExternalMonitoring(Array)\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php(68): ErrorHandlingService->logError(Array)\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(173): ErrorHandlingService->handleError(2, \'Undefined array...\', \'C:\\\\\\\\MAMP\\\\\\\\htdocs\\\\\\\\...\', 173)\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#4 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(339): TestFramework->run(\'html\')\\n#5 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to undefined method SystemSetting::get()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_2ab22e10\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:35:51\"}', '2025-07-27 21:35:51'),
('9', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 370, \"type\": \"fatal_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Uncaught Error: Call to undefined method SystemSetting::get() in C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php:370\\nStack trace:\\n#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php(306): ErrorHandlingService->sendToExternalMonitoring(Array)\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php(89): ErrorHandlingService->logError(Array)\\n#2 [internal function]: ErrorHandlingService->handleException(Object(Error))\\n#3 {main}\\n  thrown\", \"category\": \"system\", \"error_id\": \"ERR_20250727_9c079be0\", \"severity\": \"critical\", \"timestamp\": \"2025-07-27 19:35:51\"}', '2025-07-27 21:35:51'),
('10', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php\", \"line\": 173, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"pass\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_35c4a72a\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:43\"}', '2025-07-27 21:36:43'),
('11', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php\", \"line\": 175, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"pass\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_2643fa03\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:43\"}', '2025-07-27 21:36:43'),
('12', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 34, \"type\": \"application_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error\", \"category\": \"system\", \"error_id\": \"ERR_20250727_73d3fb43\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:36:43\"}', '2025-07-27 21:36:43'),
('13', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 44, \"type\": \"database_error\", \"query\": \"SELECT * FROM test\", \"params\": [], \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error\", \"category\": \"database\", \"error_id\": \"ERR_20250727_8eb62e51\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:44\"}', '2025-07-27 21:36:44'),
('14', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_733a4120\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:36:44\"}', '2025-07-27 21:36:44'),
('15', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_c3db5477\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:36:44\", \"upload_error_code\": 1}', '2025-07-27 21:36:44'),
('16', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"financial_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Transaction failed\", \"category\": \"financial\", \"error_id\": \"ERR_20250727_36bfb344\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:44\", \"transaction_data\": {\"amount\": 100}}', '2025-07-27 21:36:44'),
('17', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_a1a831d5\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:44\"}', '2025-07-27 21:36:44'),
('18', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_7aedb5fd\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:44\"}', '2025-07-27 21:36:44'),
('19', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_0659652b\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:46\"}', '2025-07-27 21:36:46'),
('20', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_405d36fb\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:46\"}', '2025-07-27 21:36:46'),
('21', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_775922cb\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:46\"}', '2025-07-27 21:36:46'),
('22', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_f1430564\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:48\"}', '2025-07-27 21:36:48'),
('23', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_91f2a17b\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:36:48\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:36:48'),
('24', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php\", \"line\": 173, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"pass\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_9afee3e7\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:48\"}', '2025-07-27 21:36:48'),
('25', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\SystemHealthService.php\", \"line\": 343, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\SystemHealthService.php(51): SystemHealthService->checkEmail()\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(98): SystemHealthService->runHealthCheck()\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#4 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#5 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(339): TestFramework->run(\'html\')\\n#6 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to undefined method SystemSetting::get()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_3cece122\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:48\"}', '2025-07-27 21:36:48'),
('26', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_03ba7f8a\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:36:48\"}', '2025-07-27 21:36:48'),
('27', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_ae3d794f\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:36:48\"}', '2025-07-27 21:36:48'),
('28', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 34, \"type\": \"application_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error\", \"category\": \"system\", \"error_id\": \"ERR_20250727_ae792a87\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:38:51\"}', '2025-07-27 21:38:51'),
('29', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 44, \"type\": \"database_error\", \"query\": \"SELECT * FROM test\", \"params\": [], \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error\", \"category\": \"database\", \"error_id\": \"ERR_20250727_385e6792\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:51\"}', '2025-07-27 21:38:51'),
('30', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_dc25e68a\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:38:52\"}', '2025-07-27 21:38:52'),
('31', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_3e0d09c6\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:38:52\", \"upload_error_code\": 1}', '2025-07-27 21:38:52'),
('32', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"financial_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Transaction failed\", \"category\": \"financial\", \"error_id\": \"ERR_20250727_8b0c1d65\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:52\", \"transaction_data\": {\"amount\": 100}}', '2025-07-27 21:38:52'),
('33', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_6c45ac27\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:52\"}', '2025-07-27 21:38:52'),
('34', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_bbc2c529\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:52\"}', '2025-07-27 21:38:52'),
('35', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_41417930\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:54\"}', '2025-07-27 21:38:54'),
('36', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_e8c51f74\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:54\"}', '2025-07-27 21:38:54'),
('37', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_03af2648\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:54\"}', '2025-07-27 21:38:54'),
('38', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_1e1f17c2\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:38:56\"}', '2025-07-27 21:38:56'),
('39', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_c265c360\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:38:56\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:38:56'),
('40', 'auth', 'test_login', '1', '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\"}', '2025-07-27 21:39:00'),
('41', 'system', 'test_system_event', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"component\": \"test\"}', '2025-07-27 21:39:00'),
('42', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 185, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(339): TestFramework->run(\'html\')\\n#4 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Class \\\"SecurityMiddleware\\\" not found\", \"category\": \"security\", \"error_id\": \"ERR_20250727_f7b163d9\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:00\"}', '2025-07-27 21:39:00'),
('43', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_6d4a3440\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:00\"}', '2025-07-27 21:39:00'),
('44', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_2c253549\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:39:00\"}', '2025-07-27 21:39:00'),
('45', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 35, \"type\": \"application_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error\", \"category\": \"system\", \"error_id\": \"ERR_20250727_cd305dcc\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:39:19\"}', '2025-07-27 21:39:19'),
('46', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 45, \"type\": \"database_error\", \"query\": \"SELECT * FROM test\", \"params\": [], \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error\", \"category\": \"database\", \"error_id\": \"ERR_20250727_38a4cc45\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:19\"}', '2025-07-27 21:39:19'),
('47', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_7fcfd88e\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:39:19\"}', '2025-07-27 21:39:19'),
('48', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_e36674be\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:39:19\", \"upload_error_code\": 1}', '2025-07-27 21:39:19'),
('49', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"financial_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Transaction failed\", \"category\": \"financial\", \"error_id\": \"ERR_20250727_49237a85\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:19\", \"transaction_data\": {\"amount\": 100}}', '2025-07-27 21:39:19'),
('50', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_3924e484\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:19\"}', '2025-07-27 21:39:19'),
('51', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_d2c4f56c\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:20\"}', '2025-07-27 21:39:20'),
('52', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_517a1945\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:22\"}', '2025-07-27 21:39:22'),
('53', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_61a2c0de\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:22\"}', '2025-07-27 21:39:22'),
('54', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_955e7a76\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:22\"}', '2025-07-27 21:39:22'),
('55', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_298bc45b\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:24\"}', '2025-07-27 21:39:24'),
('56', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_e90c8921\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:39:24\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:39:24'),
('57', 'auth', 'test_login', '1', '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\"}', '2025-07-27 21:39:27'),
('58', 'system', 'test_system_event', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"component\": \"test\"}', '2025-07-27 21:39:27'),
('59', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\EmailNotificationService.php\", \"line\": 535, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(237): EmailNotificationService->areNotificationsEnabled(\'general\')\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#4 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(340): TestFramework->run(\'html\')\\n#5 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to undefined method SystemSetting::get()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_507a93ba\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:27\"}', '2025-07-27 21:39:27'),
('60', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_3183a046\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:39:27\"}', '2025-07-27 21:39:27'),
('61', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_c09dd323\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:39:27\"}', '2025-07-27 21:39:27'),
('62', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 35, \"type\": \"application_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error\", \"category\": \"system\", \"error_id\": \"ERR_20250727_073c6614\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:42:23\"}', '2025-07-27 21:42:23'),
('63', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 45, \"type\": \"database_error\", \"query\": \"SELECT * FROM test\", \"params\": [], \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error\", \"category\": \"database\", \"error_id\": \"ERR_20250727_edbaf5f2\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:23\"}', '2025-07-27 21:42:23'),
('64', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_a8cd9edd\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:42:24\"}', '2025-07-27 21:42:24'),
('65', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_76b632b4\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:42:24\", \"upload_error_code\": 1}', '2025-07-27 21:42:24'),
('66', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"financial_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Transaction failed\", \"category\": \"financial\", \"error_id\": \"ERR_20250727_08ff6f17\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:24\", \"transaction_data\": {\"amount\": 100}}', '2025-07-27 21:42:24'),
('67', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_61390cab\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:24\"}', '2025-07-27 21:42:24'),
('68', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_7ae0a14b\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:24\"}', '2025-07-27 21:42:24'),
('69', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_6f938a00\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:26\"}', '2025-07-27 21:42:26'),
('70', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_1904e4d9\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:27\"}', '2025-07-27 21:42:27'),
('71', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_ad29d0dc\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:27\"}', '2025-07-27 21:42:27'),
('72', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_c6baa90d\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:29\"}', '2025-07-27 21:42:29'),
('73', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_283f0632\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:42:29\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:42:29'),
('74', 'auth', 'test_login', '1', '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\"}', '2025-07-27 21:42:32'),
('75', 'system', 'test_system_event', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"component\": \"test\"}', '2025-07-27 21:42:32'),
('76', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\SupportTicketService.php\", \"line\": 55, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(253): SupportTicketService->createTicket(1, \'Integration Tes...\', \'This is a test ...\', \'technical\', \'medium\')\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#4 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(340): TestFramework->run(\'html\')\\n#5 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to undefined method SupportTicket::create()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_8f1ddb71\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:32\"}', '2025-07-27 21:42:32'),
('77', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_b48c2eff\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:42:32\"}', '2025-07-27 21:42:32'),
('78', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_4226eba0\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:42:32\"}', '2025-07-27 21:42:32'),
('79', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 35, \"type\": \"application_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error\", \"category\": \"system\", \"error_id\": \"ERR_20250727_eac422bf\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:43:12\"}', '2025-07-27 21:43:12'),
('80', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php\", \"line\": 45, \"type\": \"database_error\", \"query\": \"SELECT * FROM test\", \"params\": [], \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error\", \"category\": \"database\", \"error_id\": \"ERR_20250727_fc7fc5ff\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:13\"}', '2025-07-27 21:43:13'),
('81', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_7bf61c8c\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:43:13\"}', '2025-07-27 21:43:13'),
('82', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_5cf7ea95\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:43:13\", \"upload_error_code\": 1}', '2025-07-27 21:43:13'),
('83', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"financial_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Transaction failed\", \"category\": \"financial\", \"error_id\": \"ERR_20250727_dc5d51c7\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:13\", \"transaction_data\": {\"amount\": 100}}', '2025-07-27 21:43:13'),
('84', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_8f7c8934\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:13\"}', '2025-07-27 21:43:13'),
('85', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_1ef4eb19\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:13\"}', '2025-07-27 21:43:13'),
('86', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_b02f64bd\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:15\"}', '2025-07-27 21:43:15'),
('87', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 449, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_0289601c\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:15\"}', '2025-07-27 21:43:15'),
('88', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 453, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"timestamp\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_6ea57953\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:15\"}', '2025-07-27 21:43:15'),
('89', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 463, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"mail(): Failed to connect to mailserver at \\\"localhost\\\" port 25, verify your \\\"SMTP\\\" and \\\"smtp_port\\\" setting in php.ini or use ini_set()\", \"category\": \"system\", \"error_id\": \"ERR_20250727_de852a8f\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:17\"}', '2025-07-27 21:43:17'),
('90', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": 1, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_8ab1b101\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:43:17\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:43:17'),
('91', 'auth', 'test_login', '1', '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\"}', '2025-07-27 21:43:19'),
('92', 'system', 'test_system_event', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"component\": \"test\"}', '2025-07-27 21:43:19'),
('93', 'system', 'support_ticket_created', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"user_id\": 1, \"category\": \"technical\", \"priority\": \"medium\", \"ticket_id\": true}', '2025-07-27 21:43:20'),
('94', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\models\\\\EmailTemplate.php\", \"line\": 134, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\EmailTemplateService.php(15): EmailTemplate::getByType(\'support_ticket_...\')\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\EmailNotificationService.php(411): EmailTemplateService::sendTemplateEmail(\'support_ticket_...\', \'admin@coinage.c...\', \'Super\', Array)\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\SupportTicketService.php(68): EmailNotificationService->sendSupportTicketNotification(true, \'created\')\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(253): SupportTicketService->createTicket(1, \'Integration Tes...\', \'This is a test ...\', \'technical\', \'medium\')\\n#4 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#5 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#6 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#7 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_comprehensive_system.php(340): TestFramework->run(\'html\')\\n#8 {main}\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to a member function where() on array\", \"category\": \"system\", \"error_id\": \"ERR_20250727_17aa0135\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:20\"}', '2025-07-27 21:43:20'),
('95', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_42755f8a\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:43:20\"}', '2025-07-27 21:43:20'),
('96', 'system', 'error_occurred', NULL, '127.0.0.1', 'Test Agent', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"127.0.0.1\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"Test Agent\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_1ca51598\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:43:20\"}', '2025-07-27 21:43:20'),
('97', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php\", \"line\": 29, \"type\": \"application_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error for Phase 3\", \"category\": \"system\", \"error_id\": \"ERR_20250727_681d6589\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:45:48\"}', '2025-07-27 21:45:48'),
('98', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php\", \"line\": 40, \"type\": \"database_error\", \"query\": \"SELECT * FROM test_table\", \"params\": [], \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error for Phase 3\", \"category\": \"database\", \"error_id\": \"ERR_20250727_7a206846\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:45:48\"}', '2025-07-27 21:45:48'),
('99', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials test\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_99d684f2\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:45:48\"}', '2025-07-27 21:45:48'),
('100', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_2a8012d9\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:45:49\", \"upload_error_code\": 1}', '2025-07-27 21:45:49'),
('101', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_29c05e08\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:45:49\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:45:49'),
('102', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php\", \"line\": 91, \"type\": \"exception\", \"trace\": \"#0 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(231): {closure}(Object(TestContext))\\n#1 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(171): TestFramework->runTest(Array, false)\\n#2 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\testing\\\\TestFramework.php(118): TestFramework->runSuite(Array)\\n#3 C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php(147): TestFramework->run(\'text\')\\n#4 {main}\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Call to private method SystemHealthService::checkMemory() from global scope\", \"category\": \"system\", \"error_id\": \"ERR_20250727_eb7bc360\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:45:49\"}', '2025-07-27 21:45:49'),
('103', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 495, \"type\": \"php_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"http_response_code(): Cannot set response code - headers already sent (output started at C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php:13)\", \"category\": \"system\", \"error_id\": \"ERR_20250727_0a6fbc56\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:45:49\"}', '2025-07-27 21:45:49'),
('104', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 497, \"type\": \"php_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Undefined array key \\\"error_id\\\"\", \"category\": \"system\", \"error_id\": \"ERR_20250727_84136c38\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:45:49\"}', '2025-07-27 21:45:49'),
('105', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\ErrorHandlingService.php\", \"line\": 524, \"type\": \"php_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated\", \"category\": \"system\", \"error_id\": \"ERR_20250727_0de626c8\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:45:49\"}', '2025-07-27 21:45:49'),
('106', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php\", \"line\": 29, \"type\": \"application_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test application error for Phase 3\", \"category\": \"system\", \"error_id\": \"ERR_20250727_c2cc89ed\", \"severity\": \"low\", \"timestamp\": \"2025-07-27 19:48:53\"}', '2025-07-27 21:48:53'),
('107', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\test_folder\\\\test_phase3_basic.php\", \"line\": 40, \"type\": \"database_error\", \"query\": \"SELECT * FROM test_table\", \"params\": [], \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Test database error for Phase 3\", \"category\": \"database\", \"error_id\": \"ERR_20250727_fd6aa29c\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 19:48:53\"}', '2025-07-27 21:48:53'),
('108', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"ip\": \"127.0.0.1\", \"type\": \"authentication_error\", \"email\": \"<EMAIL>\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Invalid credentials test\", \"user_id\": null, \"category\": \"authentication\", \"error_id\": \"ERR_20250727_d017ce3c\", \"severity\": \"medium\", \"timestamp\": \"2025-07-27 19:48:53\"}', '2025-07-27 21:48:53'),
('109', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"type\": \"file_upload_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"File exceeds maximum allowed size\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_06cc8c51\", \"severity\": \"medium\", \"file_name\": \"test.jpg\", \"file_size\": 5000000, \"timestamp\": \"2025-07-27 19:48:53\", \"upload_error_code\": 1}', '2025-07-27 21:48:53'),
('110', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"type\": \"validation_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"Form validation failed\", \"category\": \"validation\", \"error_id\": \"ERR_20250727_d4538bb9\", \"severity\": \"low\", \"form_data\": {\"email\": \"invalid-email\"}, \"timestamp\": \"2025-07-27 19:48:54\", \"validation_errors\": {\"email\": \"Invalid email format\", \"password\": \"Password too short\"}}', '2025-07-27 21:48:54'),
('111', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\CacheService.php\", \"line\": 72, \"type\": \"php_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"file_put_contents(C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services/../../cache/namespace1/namespace1_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_6ae10cfa\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 20:05:43\"}', '2025-07-27 22:05:43'),
('112', 'system', 'error_occurred', NULL, '0.0.0.0', 'Unknown', 'Unknown', 'Unknown', '', '{\"file\": \"C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services\\\\CacheService.php\", \"line\": 72, \"type\": \"php_error\", \"context\": {\"ip\": \"unknown\", \"referer\": null, \"user_id\": null, \"session_id\": \"\", \"user_agent\": \"unknown\", \"peak_memory\": 2097152, \"request_uri\": \"unknown\", \"memory_usage\": 2097152, \"request_method\": \"unknown\"}, \"message\": \"file_put_contents(C:\\\\MAMP\\\\htdocs\\\\Coinage\\\\classes\\\\services/../../cache/namespace2/namespace2_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory\", \"category\": \"file_upload\", \"error_id\": \"ERR_20250727_34a9bbfe\", \"severity\": \"high\", \"timestamp\": \"2025-07-27 20:05:43\"}', '2025-07-27 22:05:43');

-- Table: support_tickets
DROP TABLE IF EXISTS `support_tickets`;
CREATE TABLE `support_tickets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `message` text NOT NULL,
  `status` enum('pending','answered','closed') DEFAULT 'pending',
  `priority` enum('low','medium','high','urgent') DEFAULT 'medium',
  `category` enum('general','deposit','withdrawal','technical','account') DEFAULT 'general',
  `admin_reply` text,
  `replied_by` int(11) DEFAULT NULL,
  `replied_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `replied_by` (`replied_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_category` (`category`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `support_tickets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `support_tickets_ibfk_2` FOREIGN KEY (`replied_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

INSERT INTO `support_tickets` (`id`, `user_id`, `subject`, `message`, `status`, `priority`, `category`, `admin_reply`, `replied_by`, `replied_at`, `created_at`, `updated_at`) VALUES
('1', '1', 'Integration Test Ticket', 'This is a test ticket to verify email-support integration works correctly.', 'pending', 'medium', 'technical', NULL, NULL, NULL, '2025-07-27 19:43:20', '2025-07-27 19:43:20');

-- Table: system_health_checks
DROP TABLE IF EXISTS `system_health_checks`;
CREATE TABLE `system_health_checks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `check_type` enum('database','file_system','memory','disk_space','email','external_api') NOT NULL,
  `status` enum('healthy','warning','critical','unknown') NOT NULL,
  `message` text,
  `metrics` json DEFAULT NULL,
  `check_duration_ms` int(11) DEFAULT NULL,
  `checked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_check_type` (`check_type`),
  KEY `idx_status` (`status`),
  KEY `idx_checked_at` (`checked_at`),
  KEY `idx_type_status` (`check_type`,`status`),
  KEY `idx_type_checked` (`check_type`,`checked_at`)
) ENGINE=InnoDB AUTO_INCREMENT=79 DEFAULT CHARSET=utf8;

INSERT INTO `system_health_checks` (`id`, `check_type`, `status`, `message`, `metrics`, `check_duration_ms`, `checked_at`) VALUES
('1', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.91\", \"response_time_ms\": 152.23}', '152', '2025-07-27 21:38:58'),
('2', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 6.33}', '6', '2025-07-27 21:38:58'),
('3', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:38:58'),
('4', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62734.91, \"used_gb\": 175.91, \"used_mb\": 180132.39, \"total_gb\": 237.18, \"free_bytes\": 65782321152, \"used_bytes\": 188882497536, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:38:58'),
('5', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:38:58'),
('6', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 923.78}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 404.67}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 74.93}}', '1403', '2025-07-27 21:38:58'),
('7', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.91\", \"response_time_ms\": 149.92}', '150', '2025-07-27 21:38:59'),
('8', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 6.33}', '6', '2025-07-27 21:38:59'),
('9', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:38:59'),
('10', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62734.91, \"used_gb\": 175.91, \"used_mb\": 180132.39, \"total_gb\": 237.18, \"free_bytes\": 65782321152, \"used_bytes\": 188882497536, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:38:59'),
('11', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:38:59'),
('12', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 125.5}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 291.16}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 66.49}}', '483', '2025-07-27 21:38:59'),
('13', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.91\", \"response_time_ms\": 164.62}', '165', '2025-07-27 21:38:59'),
('14', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.26}', '5', '2025-07-27 21:38:59'),
('15', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:38:59'),
('16', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62734.85, \"used_gb\": 175.91, \"used_mb\": 180132.45, \"total_gb\": 237.18, \"free_bytes\": 65782255616, \"used_bytes\": 188882563072, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:38:59'),
('17', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:38:59'),
('18', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 104.24}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 285.79}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 72.2}}', '462', '2025-07-27 21:38:59'),
('19', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.92\", \"response_time_ms\": 149.4}', '149', '2025-07-27 21:39:25'),
('20', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.99}', '6', '2025-07-27 21:39:25'),
('21', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:39:25'),
('22', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62731.4, \"used_gb\": 175.91, \"used_mb\": 180135.89, \"total_gb\": 237.18, \"free_bytes\": 65778642944, \"used_bytes\": 188886175744, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:39:25'),
('23', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '4', '2025-07-27 21:39:25'),
('24', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 429.6}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 344.5}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 50.73}}', '825', '2025-07-27 21:39:25'),
('25', 'database', 'warning', 'Database response time is slow', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.95\", \"response_time_ms\": 608.29}', '608', '2025-07-27 21:39:26'),
('26', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 4.95}', '5', '2025-07-27 21:39:26'),
('27', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:39:26'),
('28', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62731.39, \"used_gb\": 175.91, \"used_mb\": 180135.91, \"total_gb\": 237.18, \"free_bytes\": 65778630656, \"used_bytes\": 188886188032, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:39:26'),
('29', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:39:26'),
('30', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 71.35}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 298.67}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 45.98}}', '416', '2025-07-27 21:39:26'),
('31', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.95\", \"response_time_ms\": 149.21}', '149', '2025-07-27 21:39:27'),
('32', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.92}', '6', '2025-07-27 21:39:27'),
('33', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:39:27'),
('34', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.26, \"free_mb\": 62731.39, \"used_gb\": 175.91, \"used_mb\": 180135.91, \"total_gb\": 237.18, \"free_bytes\": 65778630656, \"used_bytes\": 188886188032, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:39:27'),
('35', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '4', '2025-07-27 21:39:27'),
('36', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 100.13}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 284.88}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 47.73}}', '433', '2025-07-27 21:39:27'),
('37', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.97\", \"response_time_ms\": 141.13}', '141', '2025-07-27 21:42:30'),
('38', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.52}', '6', '2025-07-27 21:42:30'),
('39', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:42:30'),
('40', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.25, \"free_mb\": 62717.31, \"used_gb\": 175.93, \"used_mb\": 180149.98, \"total_gb\": 237.18, \"free_bytes\": 65763868672, \"used_bytes\": 188900950016, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:42:30'),
('41', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:42:30'),
('42', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 564.83}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 345.91}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 54.56}}', '965', '2025-07-27 21:42:30'),
('43', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.97\", \"response_time_ms\": 144.02}', '144', '2025-07-27 21:42:31'),
('44', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.68}', '6', '2025-07-27 21:42:31'),
('45', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:42:31'),
('46', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.25, \"free_mb\": 62717.31, \"used_gb\": 175.93, \"used_mb\": 180149.98, \"total_gb\": 237.18, \"free_bytes\": 65763868672, \"used_bytes\": 188900950016, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:42:31'),
('47', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:42:31'),
('48', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 564.03}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 288.24}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 43.01}}', '895', '2025-07-27 21:42:31'),
('49', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.97\", \"response_time_ms\": 149.61}', '150', '2025-07-27 21:42:32'),
('50', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 7.2}', '7', '2025-07-27 21:42:32'),
('51', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:42:32'),
('52', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.25, \"free_mb\": 62717.25, \"used_gb\": 175.93, \"used_mb\": 180150.05, \"total_gb\": 237.18, \"free_bytes\": 65763803136, \"used_bytes\": 188901015552, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:42:32'),
('53', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:42:32'),
('54', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 590.83}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 283.98}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 58.67}}', '934', '2025-07-27 21:42:32'),
('55', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.98\", \"response_time_ms\": 154.73}', '155', '2025-07-27 21:43:18'),
('56', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.69}', '6', '2025-07-27 21:43:18'),
('57', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:43:18'),
('58', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.24, \"free_mb\": 62714.59, \"used_gb\": 175.93, \"used_mb\": 180152.71, \"total_gb\": 237.18, \"free_bytes\": 65761013760, \"used_bytes\": 188903804928, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:43:18'),
('59', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:43:18'),
('60', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 116.85}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 376.62}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 59.05}}', '553', '2025-07-27 21:43:18'),
('61', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.98\", \"response_time_ms\": 246.13}', '246', '2025-07-27 21:43:19'),
('62', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 6.18}', '6', '2025-07-27 21:43:19'),
('63', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:43:19'),
('64', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.24, \"free_mb\": 62714.59, \"used_gb\": 175.93, \"used_mb\": 180152.71, \"total_gb\": 237.18, \"free_bytes\": 65761013760, \"used_bytes\": 188903804928, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:43:19'),
('65', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:43:19'),
('66', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 92.58}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 283.01}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 61.98}}', '438', '2025-07-27 21:43:19'),
('67', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"1.98\", \"response_time_ms\": 147.4}', '147', '2025-07-27 21:43:19'),
('68', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 5.85}', '6', '2025-07-27 21:43:19'),
('69', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:43:19'),
('70', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.24, \"free_mb\": 62714.59, \"used_gb\": 175.93, \"used_mb\": 180152.71, \"total_gb\": 237.18, \"free_bytes\": 65761013760, \"used_bytes\": 188903804928, \"total_bytes\": 254664818688, \"usage_percent\": 74.18}', '0', '2025-07-27 21:43:19'),
('71', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:43:19'),
('72', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 92.01}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 286.31}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 51.39}}', '430', '2025-07-27 21:43:19'),
('73', 'database', 'healthy', 'Database is healthy', '{\"connections\": \"1\", \"slow_queries\": \"0\", \"database_size_mb\": \"2.00\", \"response_time_ms\": 172.02}', '172', '2025-07-27 21:48:54'),
('74', 'file_system', 'healthy', 'File system is healthy', '{\"directories\": {\"logs\": {\"exists\": true, \"readable\": true, \"writable\": true}, \"cache\": {\"exists\": false, \"readable\": false, \"writable\": false}, \"uploads\": {\"exists\": true, \"readable\": true, \"writable\": true}}, \"response_time_ms\": 6.13}', '6', '2025-07-27 21:48:54'),
('75', 'memory', 'healthy', 'Memory usage is normal', '{\"peak_percent\": 0.2, \"peak_usage_mb\": 2, \"usage_percent\": 0.2, \"memory_limit_mb\": 1024, \"current_usage_mb\": 2, \"peak_usage_bytes\": 2097152, \"memory_limit_bytes\": **********, \"current_usage_bytes\": 2097152}', '0', '2025-07-27 21:48:54'),
('76', 'disk_space', 'healthy', 'Disk space is sufficient', '{\"free_gb\": 61.27, \"free_mb\": 62737.61, \"used_gb\": 175.91, \"used_mb\": 180129.69, \"total_gb\": 237.18, \"free_bytes\": 65785147392, \"used_bytes\": 188879671296, \"total_bytes\": 254664818688, \"usage_percent\": 74.17}', '0', '2025-07-27 21:48:54'),
('77', 'email', 'warning', 'SMTP not configured', '{\"configured\": false}', '3', '2025-07-27 21:48:54'),
('78', 'external_api', 'warning', 'Some external APIs are unreachable', '{\"bootstrap_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 132.08}, \"google_charts\": {\"status\": \"unreachable\", \"response_time_ms\": 345.55}, \"fontawesome_cdn\": {\"status\": \"reachable\", \"response_time_ms\": 69.04}}', '547', '2025-07-27 21:48:54');

-- Table: system_settings
DROP TABLE IF EXISTS `system_settings`;
CREATE TABLE `system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','number','boolean','json') DEFAULT 'string',
  `description` text,
  `category` varchar(50) DEFAULT 'general',
  `is_public` tinyint(1) DEFAULT '0',
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`),
  CONSTRAINT `system_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8;

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `category`, `is_public`, `updated_by`, `updated_at`) VALUES
('1', 'site_name', 'Coinage Trading', 'string', 'Website name displayed across the platform', 'general', '1', NULL, '2025-07-27 16:59:42'),
('2', 'site_currency', 'USD', 'string', 'Default currency for the platform', 'general', '1', NULL, '2025-07-27 16:59:42'),
('3', 'currency_symbol', '$', 'string', 'Currency symbol to display', 'general', '1', NULL, '2025-07-27 16:59:42'),
('4', 'registration_bonus', '0', 'number', 'Bonus amount given to new users', 'financial', '0', NULL, '2025-07-27 16:59:42'),
('5', 'deposit_bonus_percent', '10', 'number', 'Percentage bonus on deposits', 'financial', '0', NULL, '2025-07-27 16:59:42'),
('6', 'min_withdrawal_amount', '50', 'number', 'Minimum withdrawal amount', 'financial', '0', NULL, '2025-07-27 16:59:42'),
('7', 'max_withdrawal_amount', '10000', 'number', 'Maximum withdrawal amount per transaction', 'financial', '0', NULL, '2025-07-27 16:59:42'),
('8', 'withdrawal_fee_percent', '2', 'number', 'Withdrawal fee percentage', 'financial', '0', NULL, '2025-07-27 16:59:42'),
('9', 'email_verification_required', 'true', 'boolean', 'Require email verification for new accounts', 'security', '0', NULL, '2025-07-27 16:59:42'),
('10', 'two_fa_enforcement', 'false', 'boolean', 'Force all users to enable 2FA', 'security', '0', NULL, '2025-07-27 16:59:42'),
('11', 'kyc_verification_required', 'false', 'boolean', 'Require KYC verification for withdrawals', 'security', '0', NULL, '2025-07-27 16:59:42'),
('12', 'maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 'system', '0', NULL, '2025-07-27 16:59:42'),
('13', 'email_notifications', 'true', 'boolean', 'Enable email notifications', 'notifications', '0', NULL, '2025-07-27 16:59:42'),
('14', 'sms_notifications', 'false', 'boolean', 'Enable SMS notifications', 'notifications', '0', NULL, '2025-07-27 16:59:42'),
('15', 'primary_color', '#007bff', 'string', 'Primary theme color', 'appearance', '1', NULL, '2025-07-27 16:59:42'),
('16', 'secondary_color', '#6c757d', 'string', 'Secondary theme color', 'appearance', '1', NULL, '2025-07-27 16:59:42'),
('17', 'logo_url', '', 'string', 'Platform logo URL', 'appearance', '1', NULL, '2025-07-27 16:59:42'),
('18', 'favicon_url', '', 'string', 'Platform favicon URL', 'appearance', '1', NULL, '2025-07-27 16:59:42'),
('19', 'contact_email', '<EMAIL>', 'string', 'Contact email address', 'contact', '1', NULL, '2025-07-27 16:59:42'),
('20', 'contact_phone', '******-0123', 'string', 'Contact phone number', 'contact', '1', NULL, '2025-07-27 16:59:42'),
('21', 'company_address', '123 Trading Street, Finance City, FC 12345', 'string', 'Company address', 'contact', '1', NULL, '2025-07-27 16:59:42');

-- Table: test_coinage
DROP TABLE IF EXISTS `test_coinage`;
CREATE TABLE `test_coinage` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `test_name` varchar(100) NOT NULL,
  `test_email` varchar(100) NOT NULL,
  `test_amount` decimal(10,2) DEFAULT '0.00',
  `test_status` enum('active','inactive') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

INSERT INTO `test_coinage` (`id`, `test_name`, `test_email`, `test_amount`, `test_status`, `created_at`) VALUES
('1', 'John Doe', '<EMAIL>', '1000.50', 'active', '2025-07-25 13:26:32'),
('2', 'Jane Smith', '<EMAIL>', '2500.75', 'active', '2025-07-25 13:26:32'),
('3', 'Bob Johnson', '<EMAIL>', '500.25', 'active', '2025-07-25 13:26:32');

-- Table: test_coinage_simple
DROP TABLE IF EXISTS `test_coinage_simple`;
CREATE TABLE `test_coinage_simple` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `amount` decimal(10,2) DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

INSERT INTO `test_coinage_simple` (`id`, `name`, `email`, `amount`, `created_at`) VALUES
('1', 'John Doe', '<EMAIL>', '1000.50', '2025-07-25 13:27:16'),
('2', 'Jane Smith', '<EMAIL>', '2500.75', '2025-07-25 13:27:16'),
('3', 'Bob Wilson', '<EMAIL>', '750.25', '2025-07-25 13:27:16');

-- Table: trading_plans
DROP TABLE IF EXISTS `trading_plans`;
CREATE TABLE `trading_plans` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `min_deposit` decimal(15,2) NOT NULL,
  `max_deposit` decimal(15,2) DEFAULT NULL,
  `daily_return` decimal(5,2) NOT NULL,
  `duration_days` int(11) NOT NULL,
  `status` enum('active','inactive') DEFAULT 'active',
  `description` text,
  `features` json DEFAULT NULL,
  `sort_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;

INSERT INTO `trading_plans` (`id`, `name`, `min_deposit`, `max_deposit`, `daily_return`, `duration_days`, `status`, `description`, `features`, `sort_order`, `created_at`, `updated_at`) VALUES
('1', 'STARTER PLAN', '100.00', '999.99', '1.50', '30', 'active', 'Perfect for beginners looking to start their trading journey', '[\"24/7 Support\", \"Basic Analytics\", \"Mobile App Access\"]', '1', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('2', 'BASIC PLAN', '1000.00', '4999.99', '2.50', '30', 'active', 'Ideal for regular traders with moderate investment capacity', '[\"24/7 Support\", \"Advanced Analytics\", \"Mobile App Access\", \"Priority Support\"]', '2', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('3', 'GOLD PLAN', '5000.00', '19999.99', '3.50', '30', 'active', 'Premium plan for serious traders with higher returns', '[\"24/7 Support\", \"Advanced Analytics\", \"Mobile App Access\", \"Priority Support\", \"Personal Account Manager\"]', '3', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('4', 'PLATINUM PLAN', '20000.00', '49999.99', '4.50', '30', 'active', 'Elite plan for professional traders', '[\"24/7 Support\", \"Advanced Analytics\", \"Mobile App Access\", \"Priority Support\", \"Personal Account Manager\", \"Exclusive Market Insights\"]', '4', '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('5', 'VIP PLAN', '50000.00', '999999.99', '6.00', '30', 'active', 'Ultimate plan for high-net-worth individuals', '[\"24/7 Support\", \"Advanced Analytics\", \"Mobile App Access\", \"Priority Support\", \"Personal Account Manager\", \"Exclusive Market Insights\", \"VIP Events Access\"]', '5', '2025-07-27 16:59:42', '2025-07-27 16:59:42');

-- Table: transactions
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','bonus','trade_profit','trade_loss','transfer_in','transfer_out','admin_credit','admin_debit') NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `balance_before` decimal(15,2) NOT NULL,
  `balance_after` decimal(15,2) NOT NULL,
  `description` text,
  `reference_id` varchar(100) DEFAULT NULL,
  `reference_type` enum('deposit','withdrawal','trade','manual') DEFAULT 'manual',
  `status` enum('pending','completed','failed','cancelled') DEFAULT 'completed',
  `processed_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_reference_id` (`reference_id`),
  KEY `idx_reference_type` (`reference_type`),
  CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transactions_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

INSERT INTO `transactions` (`id`, `user_id`, `type`, `amount`, `balance_before`, `balance_after`, `description`, `reference_id`, `reference_type`, `status`, `processed_by`, `created_at`) VALUES
('1', '3', 'admin_credit', '1100.00', '0.00', '1100.00', 'Initial demo balance and bonus', 'DEMO_INIT_001', 'manual', 'completed', NULL, '2025-07-27 16:59:42');

-- Table: user_backup_codes
DROP TABLE IF EXISTS `user_backup_codes`;
CREATE TABLE `user_backup_codes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `code_hash` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_used_at` (`used_at`),
  CONSTRAINT `user_backup_codes_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: user_sessions
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table: users
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role` enum('user','admin','superadmin') DEFAULT 'user',
  `status` enum('active','suspended','pending') DEFAULT 'pending',
  `balance` decimal(15,2) DEFAULT '0.00',
  `bonus` decimal(15,2) DEFAULT '0.00',
  `total_deposit` decimal(15,2) DEFAULT '0.00',
  `total_withdrawal` decimal(15,2) DEFAULT '0.00',
  `kyc_status` enum('unverified','pending','verified','rejected') DEFAULT 'unverified',
  `two_fa_enabled` tinyint(1) DEFAULT '0',
  `two_fa_secret` varchar(32) DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT '0',
  `email_verification_token` varchar(64) DEFAULT NULL,
  `password_reset_token` varchar(64) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `profile_picture` varchar(255) DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `login_attempts` int(11) DEFAULT '0',
  `locked_until` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_email_verification_token` (`email_verification_token`),
  KEY `idx_password_reset_token` (`password_reset_token`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8;

INSERT INTO `users` (`id`, `username`, `email`, `password`, `first_name`, `last_name`, `phone`, `role`, `status`, `balance`, `bonus`, `total_deposit`, `total_withdrawal`, `kyc_status`, `two_fa_enabled`, `two_fa_secret`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `profile_picture`, `last_login`, `login_attempts`, `locked_until`, `created_at`, `updated_at`) VALUES
('1', 'superadmin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Super', 'Admin', NULL, 'superadmin', 'active', '0.00', '0.00', '0.00', '0.00', 'unverified', '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', NULL, '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('2', 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', NULL, 'admin', 'active', '0.00', '0.00', '0.00', '0.00', 'unverified', '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', NULL, '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('3', 'demo', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Demo', 'User', NULL, 'user', 'active', '1000.00', '100.00', '0.00', '0.00', 'unverified', '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', NULL, '2025-07-27 16:59:42', '2025-07-27 16:59:42'),
('8', 'test_comm_user', '<EMAIL>', '$2y$10$.6fQ.VOAQK.YJT2BfO.RYehg/zLFJdnM8.TLCyEZ5GXcKywqKzhce', 'Communication', 'Test', NULL, 'user', 'active', '0.00', '0.00', '0.00', '0.00', 'unverified', '0', NULL, '1', NULL, NULL, NULL, NULL, NULL, '0', NULL, '2025-07-27 21:39:27', '2025-07-27 21:39:27');

-- Table: withdrawals
DROP TABLE IF EXISTS `withdrawals`;
CREATE TABLE `withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `withdrawal_method` varchar(50) NOT NULL,
  `account_details` json NOT NULL,
  `status` enum('pending','approved','rejected','processing','completed') DEFAULT 'pending',
  `admin_note` text,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_processed_at` (`processed_at`),
  CONSTRAINT `withdrawals_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `withdrawals_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;
