<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * UserService - Business logic for user operations
 */
class UserService {
    
    /**
     * Create a new user
     */
    public static function createUser($userData) {
        // Validate input data
        $rules = [
            'username' => ['required' => true, 'length' => ['min' => 3, 'max' => 50]],
            'email' => ['required' => true, 'email' => true],
            'password' => ['required' => true, 'length' => ['min' => 6, 'max' => 255]],
            'first_name' => ['required' => true, 'length' => ['max' => 50]],
            'last_name' => ['required' => true, 'length' => ['max' => 50]],
            'phone' => ['length' => ['max' => 20]],
            'role' => ['enum' => [User::ROLE_USER, User::ROLE_ADMIN, User::ROLE_SUPERADMIN]]
        ];
        
        $errors = ValidationHelper::validateFields($userData, $rules);
        
        // Additional custom validations
        if (empty($errors['username']) && User::findByUsername($userData['username'])) {
            $errors['username'] = 'Username is already taken';
        }
        
        if (empty($errors['email']) && User::findByEmail($userData['email'])) {
            $errors['email'] = 'Email is already registered';
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Create user
        $user = new User();
        $user->fill($userData);
        
        // Set defaults
        if (!isset($userData['role'])) {
            $user->role = User::ROLE_USER;
        }
        
        if (!isset($userData['status'])) {
            $user->status = User::STATUS_PENDING;
        }
        
        // Generate email verification token if email verification is enabled
        if (defined('ENABLE_EMAIL_VERIFICATION') && ENABLE_EMAIL_VERIFICATION) {
            $user->generateEmailVerificationToken();
        } else {
            $user->email_verified = true;
            $user->status = User::STATUS_ACTIVE;
        }
        
        if ($user->save()) {
            // Add registration bonus if configured
            $registrationBonus = defined('REGISTRATION_BONUS') ? REGISTRATION_BONUS : 0;
            if ($registrationBonus > 0) {
                self::addBonus($user->getId(), $registrationBonus, 'Registration bonus');
            }
            
            return ['success' => true, 'user' => $user];
        }
        
        return ['success' => false, 'errors' => ['general' => 'Failed to create user']];
    }
    
    /**
     * Update user profile
     */
    public static function updateProfile($userId, $profileData) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'errors' => ['general' => 'User not found']];
        }
        
        // Validate input data
        $rules = [
            'first_name' => ['required' => true, 'length' => ['max' => 50]],
            'last_name' => ['required' => true, 'length' => ['max' => 50]],
            'phone' => ['length' => ['max' => 20]]
        ];
        
        $errors = ValidationHelper::validateFields($profileData, $rules);
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Update allowed fields only
        $allowedFields = ['first_name', 'last_name', 'phone'];
        foreach ($allowedFields as $field) {
            if (isset($profileData[$field])) {
                $user->$field = $profileData[$field];
            }
        }
        
        if ($user->save()) {
            return ['success' => true, 'user' => $user];
        }
        
        return ['success' => false, 'errors' => ['general' => 'Failed to update profile']];
    }
    
    /**
     * Change user password
     */
    public static function changePassword($userId, $currentPassword, $newPassword) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'errors' => ['general' => 'User not found']];
        }
        
        // Verify current password
        if (!$user->verifyPassword($currentPassword)) {
            return ['success' => false, 'errors' => ['current_password' => 'Current password is incorrect']];
        }
        
        // Validate new password
        $passwordError = ValidationHelper::validatePassword($newPassword);
        if ($passwordError) {
            return ['success' => false, 'errors' => ['new_password' => $passwordError]];
        }
        
        // Update password
        $user->password = $newPassword; // Will be hashed in save()
        
        if ($user->save()) {
            return ['success' => true];
        }
        
        return ['success' => false, 'errors' => ['general' => 'Failed to change password']];
    }
    
    /**
     * Update user balance
     */
    public static function updateBalance($userId, $amount, $type = 'credit', $description = '', $adminId = null) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        $db = getDB();
        $db->beginTransaction();
        
        try {
            $balanceBefore = $user->balance;
            
            if ($type === 'credit') {
                $user->balance += $amount;
                $transactionType = Transaction::TYPE_ADMIN_CREDIT;
            } else {
                if ($user->balance < $amount) {
                    throw new Exception('Insufficient balance');
                }
                $user->balance -= $amount;
                $transactionType = Transaction::TYPE_ADMIN_DEBIT;
                $amount = -$amount; // Make negative for debit
            }
            
            if (!$user->save()) {
                throw new Exception('Failed to update user balance');
            }
            
            // Create transaction record
            $transaction = new Transaction();
            $transaction->user_id = $userId;
            $transaction->type = $transactionType;
            $transaction->amount = $amount;
            $transaction->balance_before = $balanceBefore;
            $transaction->balance_after = $user->balance;
            $transaction->description = $description ?: ($type === 'credit' ? 'Admin credit' : 'Admin debit');
            $transaction->processed_by = $adminId;
            
            if (!$transaction->save()) {
                throw new Exception('Failed to create transaction record');
            }
            
            $db->commit();
            return ['success' => true, 'new_balance' => $user->balance];
            
        } catch (Exception $e) {
            $db->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Add bonus to user account
     */
    public static function addBonus($userId, $amount, $description = 'Bonus') {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        $db = getDB();
        $db->beginTransaction();
        
        try {
            $balanceBefore = $user->balance;
            $user->balance += $amount;
            $user->bonus += $amount;
            
            if (!$user->save()) {
                throw new Exception('Failed to update user balance');
            }
            
            // Create transaction record
            $transaction = new Transaction();
            $transaction->user_id = $userId;
            $transaction->type = Transaction::TYPE_BONUS;
            $transaction->amount = $amount;
            $transaction->balance_before = $balanceBefore;
            $transaction->balance_after = $user->balance;
            $transaction->description = $description;
            
            if (!$transaction->save()) {
                throw new Exception('Failed to create transaction record');
            }
            
            $db->commit();
            return ['success' => true, 'new_balance' => $user->balance, 'new_bonus' => $user->bonus];
            
        } catch (Exception $e) {
            $db->rollback();
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Suspend user account
     */
    public static function suspendUser($userId, $adminId, $reason = '') {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        if ($user->role === User::ROLE_SUPERADMIN) {
            return ['success' => false, 'error' => 'Cannot suspend super admin'];
        }
        
        $user->status = User::STATUS_SUSPENDED;
        
        if ($user->save()) {
            // Log the action (you could create an audit log here)
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Failed to suspend user'];
    }
    
    /**
     * Activate user account
     */
    public static function activateUser($userId, $adminId) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'error' => 'User not found'];
        }
        
        $user->status = User::STATUS_ACTIVE;
        
        if ($user->save()) {
            return ['success' => true];
        }
        
        return ['success' => false, 'error' => 'Failed to activate user'];
    }
    
    /**
     * Get user statistics
     */
    public static function getUserStats($userId) {
        $user = User::find($userId);
        if (!$user) {
            return null;
        }
        
        $stats = [
            'total_deposits' => 0,
            'total_withdrawals' => 0,
            'pending_deposits' => 0,
            'pending_withdrawals' => 0,
            'total_transactions' => 0,
            'account_age_days' => 0
        ];
        
        // Calculate account age
        if ($user->created_at) {
            $createdDate = new DateTime($user->created_at);
            $now = new DateTime();
            $stats['account_age_days'] = $now->diff($createdDate)->days;
        }
        
        // Get deposit stats
        $deposits = $user->getDeposits();
        foreach ($deposits as $deposit) {
            if ($deposit->isApproved()) {
                $stats['total_deposits'] += $deposit->amount;
            } elseif ($deposit->isPending()) {
                $stats['pending_deposits']++;
            }
        }
        
        // Get withdrawal stats
        $withdrawals = $user->getWithdrawals();
        foreach ($withdrawals as $withdrawal) {
            if ($withdrawal->isApproved() || $withdrawal->isCompleted()) {
                $stats['total_withdrawals'] += $withdrawal->amount;
            } elseif ($withdrawal->isPending()) {
                $stats['pending_withdrawals']++;
            }
        }
        
        // Get transaction count
        $stats['total_transactions'] = count($user->getTransactions());
        
        return $stats;
    }
    
    /**
     * Search users
     */
    public static function searchUsers($query, $filters = [], $limit = 50, $offset = 0) {
        $db = getDB();
        
        $sql = "SELECT * FROM users WHERE 1=1";
        $params = [];
        
        // Search query
        if (!empty($query)) {
            $sql .= " AND (username LIKE :query OR email LIKE :query OR first_name LIKE :query OR last_name LIKE :query)";
            $params['query'] = '%' . $query . '%';
        }
        
        // Filters
        if (!empty($filters['role'])) {
            $sql .= " AND role = :role";
            $params['role'] = $filters['role'];
        }
        
        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['email_verified'])) {
            $sql .= " AND email_verified = :email_verified";
            $params['email_verified'] = $filters['email_verified'] ? 1 : 0;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $params['limit'] = $limit;
        $params['offset'] = $offset;
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        $users = [];
        foreach ($results as $data) {
            $users[] = new User($data);
        }
        
        return $users;
    }
    
    /**
     * Get user activity summary
     */
    public static function getUserActivity($userId, $days = 30) {
        $user = User::find($userId);
        if (!$user) {
            return null;
        }
        
        $dateFrom = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $activity = [
            'recent_deposits' => [],
            'recent_withdrawals' => [],
            'recent_transactions' => [],
            'login_history' => []
        ];
        
        // Get recent deposits
        $db = getDB();
        $stmt = $db->prepare("SELECT * FROM deposits WHERE user_id = :user_id AND created_at >= :date_from ORDER BY created_at DESC LIMIT 10");
        $stmt->execute(['user_id' => $userId, 'date_from' => $dateFrom]);
        $deposits = $stmt->fetchAll();
        
        foreach ($deposits as $data) {
            $activity['recent_deposits'][] = new Deposit($data);
        }
        
        // Get recent withdrawals
        $stmt = $db->prepare("SELECT * FROM withdrawals WHERE user_id = :user_id AND created_at >= :date_from ORDER BY created_at DESC LIMIT 10");
        $stmt->execute(['user_id' => $userId, 'date_from' => $dateFrom]);
        $withdrawals = $stmt->fetchAll();
        
        foreach ($withdrawals as $data) {
            $activity['recent_withdrawals'][] = new Withdrawal($data);
        }
        
        // Get recent transactions
        $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = :user_id AND created_at >= :date_from ORDER BY created_at DESC LIMIT 20");
        $stmt->execute(['user_id' => $userId, 'date_from' => $dateFrom]);
        $transactions = $stmt->fetchAll();
        
        foreach ($transactions as $data) {
            $activity['recent_transactions'][] = new Transaction($data);
        }
        
        return $activity;
    }
}
?>