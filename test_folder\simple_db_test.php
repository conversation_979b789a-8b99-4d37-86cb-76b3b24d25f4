<?php
// Simple database test
echo "=== Database Connection Test ===\n";

// Database config
$host = 'localhost';
$port = '3306';
$dbname = 'coinage';
$username = 'root';
$password = 'root';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Database connection successful!\n";
    echo "Database: $dbname\n";
    echo "Host: $host:$port\n\n";
    
    // Show existing tables
    echo "=== Existing Tables ===\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "No tables found.\n\n";
    } else {
        foreach ($tables as $table) {
            echo "- $table\n";
        }
        echo "\n";
    }
    
    // Create test table
    echo "=== Creating Test Table ===\n";
    $sql = "CREATE TABLE IF NOT EXISTS test_coinage_simple (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) NOT NULL,
        amount DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✓ Test table created successfully!\n\n";
    
    // Insert test data
    echo "=== Inserting Test Data ===\n";
    $stmt = $pdo->prepare("INSERT INTO test_coinage_simple (name, email, amount) VALUES (?, ?, ?)");
    
    $testData = [
        ['John Doe', '<EMAIL>', 1000.50],
        ['Jane Smith', '<EMAIL>', 2500.75],
        ['Bob Wilson', '<EMAIL>', 750.25]
    ];
    
    foreach ($testData as $data) {
        $stmt->execute($data);
        echo "→ Inserted: {$data[0]} - {$data[1]} - $" . number_format($data[2], 2) . "\n";
    }
    echo "\n";
    
    // Query data
    echo "=== Retrieving Test Data ===\n";
    $stmt = $pdo->query("SELECT * FROM test_coinage_simple ORDER BY id");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($results as $row) {
        echo "ID: {$row['id']} | Name: {$row['name']} | Email: {$row['email']} | Amount: $" . number_format($row['amount'], 2) . " | Created: {$row['created_at']}\n";
    }
    echo "\n";
    
    // Database info
    echo "=== Database Information ===\n";
    $stmt = $pdo->query("SELECT VERSION() as version");
    $version = $stmt->fetch();
    echo "MySQL Version: " . $version['version'] . "\n";
    
    echo "\n✓ Database test completed successfully!\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
}
?>