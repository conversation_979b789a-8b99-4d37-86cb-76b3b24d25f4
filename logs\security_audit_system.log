{"timestamp":"2025-07-27 15:08:20","category":"system","event":"test_system_event","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"component":"test"}}
{"timestamp":"2025-07-27 19:35:51","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"pass\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php","line":173,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_68fdff52","timestamp":"2025-07-27 19:35:51"}}
{"timestamp":"2025-07-27 19:35:51","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Call to undefined method SystemSetting::get()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":370,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(306): ErrorHandlingService->sendToExternalMonitoring(Array)\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(68): ErrorHandlingService->logError(Array)\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(173): ErrorHandlingService->handleError(2, 'Undefined array...', 'C:\\\\MAMP\\\\htdocs\\\\...', 173)\n#3 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#4 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(339): TestFramework->run('html')\n#5 {main}","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_2ab22e10","timestamp":"2025-07-27 19:35:51"}}
{"timestamp":"2025-07-27 19:35:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"fatal_error","severity":"critical","message":"Uncaught Error: Call to undefined method SystemSetting::get() in C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php:370\nStack trace:\n#0 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(306): ErrorHandlingService->sendToExternalMonitoring(Array)\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php(89): ErrorHandlingService->logError(Array)\n#2 [internal function]: ErrorHandlingService->handleException(Object(Error))\n#3 {main}\n  thrown","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":370,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_9c079be0","timestamp":"2025-07-27 19:35:51"}}
{"timestamp":"2025-07-27 19:36:43","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"pass\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php","line":173,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_35c4a72a","timestamp":"2025-07-27 19:36:43"}}
{"timestamp":"2025-07-27 19:36:43","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"pass\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php","line":175,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_2643fa03","timestamp":"2025-07-27 19:36:43"}}
{"timestamp":"2025-07-27 19:36:43","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"application_error","severity":"medium","message":"Test application error","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":34,"error_id":"ERR_20250727_73d3fb43","timestamp":"2025-07-27 19:36:43"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"database_error","severity":"high","message":"Test database error","category":"database","query":"SELECT * FROM test","params":[],"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":44,"error_id":"ERR_20250727_8eb62e51","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_733a4120","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_c3db5477","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"financial_error","severity":"high","message":"Transaction failed","category":"financial","transaction_data":{"amount":100},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_36bfb344","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_a1a831d5","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:44","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_7aedb5fd","timestamp":"2025-07-27 19:36:44"}}
{"timestamp":"2025-07-27 19:36:46","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_0659652b","timestamp":"2025-07-27 19:36:46"}}
{"timestamp":"2025-07-27 19:36:46","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_405d36fb","timestamp":"2025-07-27 19:36:46"}}
{"timestamp":"2025-07-27 19:36:46","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_775922cb","timestamp":"2025-07-27 19:36:46"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_f1430564","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_91f2a17b","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"pass\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php","line":173,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_9afee3e7","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Call to undefined method SystemSetting::get()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SystemHealthService.php","line":343,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SystemHealthService.php(51): SystemHealthService->checkEmail()\n#1 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(98): SystemHealthService->runHealthCheck()\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#3 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#4 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#5 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(339): TestFramework->run('html')\n#6 {main}","category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_3cece122","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_03ba7f8a","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:36:48","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_ae3d794f","timestamp":"2025-07-27 19:36:48"}}
{"timestamp":"2025-07-27 19:38:51","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"application_error","severity":"medium","message":"Test application error","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":34,"error_id":"ERR_20250727_ae792a87","timestamp":"2025-07-27 19:38:51"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"database_error","severity":"high","message":"Test database error","category":"database","query":"SELECT * FROM test","params":[],"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":44,"error_id":"ERR_20250727_385e6792","timestamp":"2025-07-27 19:38:51"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_dc25e68a","timestamp":"2025-07-27 19:38:52"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_3e0d09c6","timestamp":"2025-07-27 19:38:52"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"financial_error","severity":"high","message":"Transaction failed","category":"financial","transaction_data":{"amount":100},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_8b0c1d65","timestamp":"2025-07-27 19:38:52"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_6c45ac27","timestamp":"2025-07-27 19:38:52"}}
{"timestamp":"2025-07-27 19:38:52","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_bbc2c529","timestamp":"2025-07-27 19:38:52"}}
{"timestamp":"2025-07-27 19:38:54","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_41417930","timestamp":"2025-07-27 19:38:54"}}
{"timestamp":"2025-07-27 19:38:54","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_e8c51f74","timestamp":"2025-07-27 19:38:54"}}
{"timestamp":"2025-07-27 19:38:54","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_03af2648","timestamp":"2025-07-27 19:38:54"}}
{"timestamp":"2025-07-27 19:38:56","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_1e1f17c2","timestamp":"2025-07-27 19:38:56"}}
{"timestamp":"2025-07-27 19:38:56","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_c265c360","timestamp":"2025-07-27 19:38:56"}}
{"timestamp":"2025-07-27 19:39:00","category":"system","event":"test_system_event","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"component":"test"}}
{"timestamp":"2025-07-27 19:39:00","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Class \"SecurityMiddleware\" not found","file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":185,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#3 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(339): TestFramework->run('html')\n#4 {main}","category":"security","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_f7b163d9","timestamp":"2025-07-27 19:39:00"}}
{"timestamp":"2025-07-27 19:39:00","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_6d4a3440","timestamp":"2025-07-27 19:39:00"}}
{"timestamp":"2025-07-27 19:39:00","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_2c253549","timestamp":"2025-07-27 19:39:00"}}
{"timestamp":"2025-07-27 19:39:19","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"application_error","severity":"medium","message":"Test application error","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":35,"error_id":"ERR_20250727_cd305dcc","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:19","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"database_error","severity":"high","message":"Test database error","category":"database","query":"SELECT * FROM test","params":[],"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":45,"error_id":"ERR_20250727_38a4cc45","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:19","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_7fcfd88e","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:19","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_e36674be","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:19","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"financial_error","severity":"high","message":"Transaction failed","category":"financial","transaction_data":{"amount":100},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_49237a85","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:20","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_3924e484","timestamp":"2025-07-27 19:39:19"}}
{"timestamp":"2025-07-27 19:39:20","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_d2c4f56c","timestamp":"2025-07-27 19:39:20"}}
{"timestamp":"2025-07-27 19:39:22","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_517a1945","timestamp":"2025-07-27 19:39:22"}}
{"timestamp":"2025-07-27 19:39:22","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_61a2c0de","timestamp":"2025-07-27 19:39:22"}}
{"timestamp":"2025-07-27 19:39:22","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_955e7a76","timestamp":"2025-07-27 19:39:22"}}
{"timestamp":"2025-07-27 19:39:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_298bc45b","timestamp":"2025-07-27 19:39:24"}}
{"timestamp":"2025-07-27 19:39:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_e90c8921","timestamp":"2025-07-27 19:39:24"}}
{"timestamp":"2025-07-27 19:39:27","category":"system","event":"test_system_event","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"component":"test"}}
{"timestamp":"2025-07-27 19:39:27","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Call to undefined method SystemSetting::get()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\EmailNotificationService.php","line":535,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(237): EmailNotificationService->areNotificationsEnabled('general')\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#3 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#4 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(340): TestFramework->run('html')\n#5 {main}","category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_507a93ba","timestamp":"2025-07-27 19:39:27"}}
{"timestamp":"2025-07-27 19:39:27","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_3183a046","timestamp":"2025-07-27 19:39:27"}}
{"timestamp":"2025-07-27 19:39:27","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_c09dd323","timestamp":"2025-07-27 19:39:27"}}
{"timestamp":"2025-07-27 19:42:23","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"application_error","severity":"medium","message":"Test application error","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":35,"error_id":"ERR_20250727_073c6614","timestamp":"2025-07-27 19:42:23"}}
{"timestamp":"2025-07-27 19:42:23","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"database_error","severity":"high","message":"Test database error","category":"database","query":"SELECT * FROM test","params":[],"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":45,"error_id":"ERR_20250727_edbaf5f2","timestamp":"2025-07-27 19:42:23"}}
{"timestamp":"2025-07-27 19:42:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_a8cd9edd","timestamp":"2025-07-27 19:42:24"}}
{"timestamp":"2025-07-27 19:42:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_76b632b4","timestamp":"2025-07-27 19:42:24"}}
{"timestamp":"2025-07-27 19:42:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"financial_error","severity":"high","message":"Transaction failed","category":"financial","transaction_data":{"amount":100},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_08ff6f17","timestamp":"2025-07-27 19:42:24"}}
{"timestamp":"2025-07-27 19:42:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_61390cab","timestamp":"2025-07-27 19:42:24"}}
{"timestamp":"2025-07-27 19:42:24","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_7ae0a14b","timestamp":"2025-07-27 19:42:24"}}
{"timestamp":"2025-07-27 19:42:26","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_6f938a00","timestamp":"2025-07-27 19:42:26"}}
{"timestamp":"2025-07-27 19:42:27","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_1904e4d9","timestamp":"2025-07-27 19:42:27"}}
{"timestamp":"2025-07-27 19:42:27","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_ad29d0dc","timestamp":"2025-07-27 19:42:27"}}
{"timestamp":"2025-07-27 19:42:29","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_c6baa90d","timestamp":"2025-07-27 19:42:29"}}
{"timestamp":"2025-07-27 19:42:29","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_283f0632","timestamp":"2025-07-27 19:42:29"}}
{"timestamp":"2025-07-27 19:42:32","category":"system","event":"test_system_event","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"component":"test"}}
{"timestamp":"2025-07-27 19:42:32","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Call to undefined method SupportTicket::create()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SupportTicketService.php","line":55,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(253): SupportTicketService->createTicket(1, 'Integration Tes...', 'This is a test ...', 'technical', 'medium')\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#3 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#4 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(340): TestFramework->run('html')\n#5 {main}","category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_8f1ddb71","timestamp":"2025-07-27 19:42:32"}}
{"timestamp":"2025-07-27 19:42:32","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_b48c2eff","timestamp":"2025-07-27 19:42:32"}}
{"timestamp":"2025-07-27 19:42:32","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_4226eba0","timestamp":"2025-07-27 19:42:32"}}
{"timestamp":"2025-07-27 19:43:12","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"application_error","severity":"medium","message":"Test application error","category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":35,"error_id":"ERR_20250727_eac422bf","timestamp":"2025-07-27 19:43:12"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"database_error","severity":"high","message":"Test database error","category":"database","query":"SELECT * FROM test","params":[],"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php","line":45,"error_id":"ERR_20250727_fc7fc5ff","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_7bf61c8c","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_5cf7ea95","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"financial_error","severity":"high","message":"Transaction failed","category":"financial","transaction_data":{"amount":100},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_dc5d51c7","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_8f7c8934","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:13","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_1ef4eb19","timestamp":"2025-07-27 19:43:13"}}
{"timestamp":"2025-07-27 19:43:15","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_b02f64bd","timestamp":"2025-07-27 19:43:15"}}
{"timestamp":"2025-07-27 19:43:15","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":449,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_0289601c","timestamp":"2025-07-27 19:43:15"}}
{"timestamp":"2025-07-27 19:43:15","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"timestamp\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":453,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_6ea57953","timestamp":"2025-07-27 19:43:15"}}
{"timestamp":"2025-07-27 19:43:17","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"mail(): Failed to connect to mailserver at \"localhost\" port 25, verify your \"SMTP\" and \"smtp_port\" setting in php.ini or use ini_set()","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":463,"category":"system","context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_de852a8f","timestamp":"2025-07-27 19:43:17"}}
{"timestamp":"2025-07-27 19:43:17","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":1,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_8ab1b101","timestamp":"2025-07-27 19:43:17"}}
{"timestamp":"2025-07-27 19:43:19","category":"system","event":"test_system_event","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"component":"test"}}
{"timestamp":"2025-07-27 19:43:20","category":"system","event":"support_ticket_created","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"ticket_id":true,"user_id":1,"category":"technical","priority":"medium"}}
{"timestamp":"2025-07-27 19:43:20","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"exception","severity":"high","message":"Call to a member function where() on array","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\models\\EmailTemplate.php","line":134,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\EmailTemplateService.php(15): EmailTemplate::getByType('support_ticket_...')\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\EmailNotificationService.php(411): EmailTemplateService::sendTemplateEmail('support_ticket_...', 'admin@coinage.c...', 'Super', Array)\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\services\\SupportTicketService.php(68): EmailNotificationService->sendSupportTicketNotification(true, 'created')\n#3 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(253): SupportTicketService->createTicket(1, 'Integration Tes...', 'This is a test ...', 'technical', 'medium')\n#4 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#5 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#6 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#7 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_comprehensive_system.php(340): TestFramework->run('html')\n#8 {main}","category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_17aa0135","timestamp":"2025-07-27 19:43:20"}}
{"timestamp":"2025-07-27 19:43:20","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_42755f8a","timestamp":"2025-07-27 19:43:20"}}
{"timestamp":"2025-07-27 19:43:20","category":"system","event":"error_occurred","user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"127.0.0.1","user_agent":"Test Agent","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_1ca51598","timestamp":"2025-07-27 19:43:20"}}
{"timestamp":"2025-07-27 19:45:48","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"application_error","severity":"low","message":"Test application error for Phase 3","category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php","line":29,"error_id":"ERR_20250727_681d6589","timestamp":"2025-07-27 19:45:48"}}
{"timestamp":"2025-07-27 19:45:48","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"database_error","severity":"high","message":"Test database error for Phase 3","category":"database","query":"SELECT * FROM test_table","params":[],"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php","line":40,"error_id":"ERR_20250727_7a206846","timestamp":"2025-07-27 19:45:48"}}
{"timestamp":"2025-07-27 19:45:48","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials test","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_99d684f2","timestamp":"2025-07-27 19:45:48"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_2a8012d9","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_29c05e08","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"exception","severity":"high","message":"Call to private method SystemHealthService::checkMemory() from global scope","file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php","line":91,"trace":"#0 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(231): {closure}(Object(TestContext))\n#1 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(171): TestFramework->runTest(Array, false)\n#2 C:\\MAMP\\htdocs\\Coinage\\classes\\testing\\TestFramework.php(118): TestFramework->runSuite(Array)\n#3 C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php(147): TestFramework->run('text')\n#4 {main}","category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_eb7bc360","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"php_error","severity":"high","message":"http_response_code(): Cannot set response code - headers already sent (output started at C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php:13)","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":495,"category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_0a6fbc56","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"php_error","severity":"high","message":"Undefined array key \"error_id\"","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":497,"category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_84136c38","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:45:49","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"php_error","severity":"low","message":"htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\ErrorHandlingService.php","line":524,"category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_0de626c8","timestamp":"2025-07-27 19:45:49"}}
{"timestamp":"2025-07-27 19:48:53","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"application_error","severity":"low","message":"Test application error for Phase 3","category":"system","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php","line":29,"error_id":"ERR_20250727_c2cc89ed","timestamp":"2025-07-27 19:48:53"}}
{"timestamp":"2025-07-27 19:48:53","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"database_error","severity":"high","message":"Test database error for Phase 3","category":"database","query":"SELECT * FROM test_table","params":[],"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"file":"C:\\MAMP\\htdocs\\Coinage\\test_folder\\test_phase3_basic.php","line":40,"error_id":"ERR_20250727_fd6aa29c","timestamp":"2025-07-27 19:48:53"}}
{"timestamp":"2025-07-27 19:48:53","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"authentication_error","severity":"medium","message":"Invalid credentials test","category":"authentication","user_id":null,"email":"<EMAIL>","ip":"127.0.0.1","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_d017ce3c","timestamp":"2025-07-27 19:48:53"}}
{"timestamp":"2025-07-27 19:48:53","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"file_upload_error","severity":"medium","message":"File exceeds maximum allowed size","category":"file_upload","upload_error_code":1,"file_name":"test.jpg","file_size":5000000,"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_06cc8c51","timestamp":"2025-07-27 19:48:53"}}
{"timestamp":"2025-07-27 19:48:54","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"validation_error","severity":"low","message":"Form validation failed","category":"validation","validation_errors":{"email":"Invalid email format","password":"Password too short"},"form_data":{"email":"invalid-email"},"context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_d4538bb9","timestamp":"2025-07-27 19:48:54"}}
{"timestamp":"2025-07-27 20:05:43","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"php_error","severity":"high","message":"file_put_contents(C:\\MAMP\\htdocs\\Coinage\\classes\\services\/..\/..\/cache\/namespace1\/namespace1_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\CacheService.php","line":72,"category":"file_upload","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_6ae10cfa","timestamp":"2025-07-27 20:05:43"}}
{"timestamp":"2025-07-27 20:05:43","category":"system","event":"error_occurred","user_id":null,"ip":"0.0.0.0","user_agent":"Unknown","details":{"type":"php_error","severity":"high","message":"file_put_contents(C:\\MAMP\\htdocs\\Coinage\\classes\\services\/..\/..\/cache\/namespace2\/namespace2_23567c800d9a71681dea32f9501ae58e.cache): Failed to open stream: No such file or directory","file":"C:\\MAMP\\htdocs\\Coinage\\classes\\services\\CacheService.php","line":72,"category":"file_upload","context":{"user_id":null,"ip":"unknown","user_agent":"unknown","request_uri":"unknown","request_method":"unknown","referer":null,"session_id":"","memory_usage":2097152,"peak_memory":2097152},"error_id":"ERR_20250727_34a9bbfe","timestamp":"2025-07-27 20:05:43"}}
