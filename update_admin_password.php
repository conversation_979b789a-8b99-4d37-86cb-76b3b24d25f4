<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

try {
    $db = getDB();
    $password = hashPassword('admin123');
    
    // Update admin user password
    $stmt = $db->prepare('UPDATE users SET password = ? WHERE username = ?');
    $result = $stmt->execute([$password, 'admin']);
    
    if ($result) {
        echo "Admin password updated successfully!\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
        echo "New hash: " . $password . "\n";
    } else {
        echo "Failed to update admin password.\n";
    }
    
    // Also update superadmin password
    $stmt2 = $db->prepare('UPDATE users SET password = ? WHERE username = ?');
    $result2 = $stmt2->execute([$password, 'superadmin']);
    
    if ($result2) {
        echo "Superadmin password updated successfully!\n";
        echo "Username: superadmin\n";
        echo "Password: admin123\n";
    } else {
        echo "Failed to update superadmin password.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>