<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/../services/FinancialReportingService.php';
require_once __DIR__ . '/../services/AuditTrailService.php';

/**
 * AdminFinancialReportsView - Financial reporting and audit trail interface
 */
class AdminFinancialReportsView extends BaseView {
    
    protected function getTitle() {
        return 'Financial Reports & Audit Trail';
    }
    
    protected function getBodyContent() {
        $reportingService = new FinancialReportingService();
        $auditService = new AuditTrailService();
        
        // Get default date range (last 30 days)
        $dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
        $dateTo = $_GET['date_to'] ?? date('Y-m-d');
        $reportType = $_GET['report_type'] ?? 'summary';
        
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Financial Reports & Audit Trail
                            </h4>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="exportReport(\'pdf\')">
                                    <i class="fas fa-file-pdf me-1"></i>Export PDF
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="exportReport(\'excel\')">
                                    <i class="fas fa-file-excel me-1"></i>Export Excel
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            ' . $this->renderReportFilters($dateFrom, $dateTo, $reportType) . '
                            
                            <div class="row mt-4">
                                <div class="col-12">
                                    <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" 
                                                    data-bs-target="#summary" type="button" role="tab">
                                                <i class="fas fa-chart-pie me-1"></i>Summary Report
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="transactions-tab" data-bs-toggle="tab" 
                                                    data-bs-target="#transactions" type="button" role="tab">
                                                <i class="fas fa-exchange-alt me-1"></i>Transaction Details
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="audit-tab" data-bs-toggle="tab" 
                                                    data-bs-target="#audit" type="button" role="tab">
                                                <i class="fas fa-shield-alt me-1"></i>Audit Trail
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" 
                                                    data-bs-target="#analytics" type="button" role="tab">
                                                <i class="fas fa-analytics me-1"></i>Analytics
                                            </button>
                                        </li>
                                    </ul>
                                    
                                    <div class="tab-content mt-3" id="reportTabContent">
                                        <div class="tab-pane fade show active" id="summary" role="tabpanel">
                                            ' . $this->renderSummaryReport($dateFrom, $dateTo) . '
                                        </div>
                                        <div class="tab-pane fade" id="transactions" role="tabpanel">
                                            ' . $this->renderTransactionDetails($dateFrom, $dateTo) . '
                                        </div>
                                        <div class="tab-pane fade" id="audit" role="tabpanel">
                                            ' . $this->renderAuditTrail($dateFrom, $dateTo) . '
                                        </div>
                                        <div class="tab-pane fade" id="analytics" role="tabpanel">
                                            ' . $this->renderAnalytics($dateFrom, $dateTo) . '
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderReportFilters($dateFrom, $dateTo, $reportType) {
        return '
        <form id="reportFilters" class="row g-3">
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="' . $dateFrom . '">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="' . $dateTo . '">
            </div>
            <div class="col-md-3">
                <label for="report_type" class="form-label">Report Type</label>
                <select class="form-select" id="report_type" name="report_type">
                    <option value="summary"' . ($reportType === 'summary' ? ' selected' : '') . '>Summary</option>
                    <option value="detailed"' . ($reportType === 'detailed' ? ' selected' : '') . '>Detailed</option>
                    <option value="user_activity"' . ($reportType === 'user_activity' ? ' selected' : '') . '>User Activity</option>
                    <option value="payment_methods"' . ($reportType === 'payment_methods' ? ' selected' : '') . '>Payment Methods</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" onclick="updateReports()">
                        <i class="fas fa-search me-1"></i>Generate Report
                    </button>
                </div>
            </div>
        </form>';
    }
    
    private function renderSummaryReport($dateFrom, $dateTo) {
        $reportingService = new FinancialReportingService();
        $summary = $reportingService->getSummaryReport($dateFrom, $dateTo);
        
        return '
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white metric-card" data-metric="deposits">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title metric-label">Total Deposits</h6>
                                <h4 class="metric-value">$' . number_format($summary['deposits']['total'], 2) . '</h4>
                                <small class="metric-subtitle">' . $summary['deposits']['count'] . ' transactions</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-arrow-down fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white metric-card" data-metric="withdrawals">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title metric-label">Total Withdrawals</h6>
                                <h4 class="metric-value">$' . number_format($summary['withdrawals']['total'], 2) . '</h4>
                                <small class="metric-subtitle">' . $summary['withdrawals']['count'] . ' transactions</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-arrow-up fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white metric-card" data-metric="net_flow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title metric-label">Net Flow</h6>
                                <h4 class="metric-value">$' . number_format($summary['net_flow'], 2) . '</h4>
                                <small class="metric-subtitle">' . ($summary['net_flow'] >= 0 ? 'Positive' : 'Negative') . ' flow</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-balance-scale fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white metric-card" data-metric="pending_actions">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title metric-label">Pending Actions</h6>
                                <h4 class="metric-value">' . $summary['pending_actions'] . '</h4>
                                <small class="metric-subtitle">Require attention</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Deposit Status Breakdown</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="depositStatusChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Withdrawal Status Breakdown</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="withdrawalStatusChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Top Users by Transaction Volume</h6>
                    </div>
                    <div class="card-body">
                        ' . $this->renderTopUsersTable($summary['top_users']) . '
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderTransactionDetails($dateFrom, $dateTo) {
        return '
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Transaction Details</h6>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="filterTransactions(\'all\')">All</button>
                    <button type="button" class="btn btn-outline-success" onclick="filterTransactions(\'deposit\')">Deposits</button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterTransactions(\'withdrawal\')">Withdrawals</button>
                    <button type="button" class="btn btn-outline-info" onclick="filterTransactions(\'bonus\')">Bonuses</button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="transactionDetailsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Date</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Balance Before</th>
                                <th>Balance After</th>
                                <th>Status</th>
                                <th>Processed By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
                <nav aria-label="Transaction pagination">
                    <ul class="pagination justify-content-center" id="transactionPagination">
                        <!-- Pagination loaded via AJAX -->
                    </ul>
                </nav>
            </div>
        </div>';
    }
    
    private function renderAuditTrail($dateFrom, $dateTo) {
        return '
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">Audit Trail</h6>
                <div class="input-group" style="width: 300px;">
                    <input type="text" class="form-control form-control-sm" id="auditSearch" 
                           placeholder="Search audit logs...">
                    <button class="btn btn-outline-secondary btn-sm" type="button" onclick="searchAuditLogs()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="auditTrailTable" class="table table-sm">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>User</th>
                                <th>Action</th>
                                <th>Entity</th>
                                <th>Entity ID</th>
                                <th>Changes</th>
                                <th>IP Address</th>
                                <th>User Agent</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data loaded via AJAX -->
                        </tbody>
                    </table>
                </div>
                <nav aria-label="Audit pagination">
                    <ul class="pagination justify-content-center" id="auditPagination">
                        <!-- Pagination loaded via AJAX -->
                    </ul>
                </nav>
            </div>
        </div>';
    }
    
    private function renderAnalytics($dateFrom, $dateTo) {
        return '
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Transaction Volume Trends</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="volumeTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Payment Method Usage</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="paymentMethodChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">User Activity Heatmap</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="activityHeatmap" width="600" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Key Metrics</h6>
                    </div>
                    <div class="card-body">
                        <div id="keyMetrics">
                            <!-- Metrics loaded via AJAX -->
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Suspicious Activity</h6>
                    </div>
                    <div class="card-body">
                        <div id="suspiciousActivity">
                            <!-- Suspicious activity loaded via AJAX -->
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderTopUsersTable($topUsers) {
        if (empty($topUsers)) {
            return '<p class="text-muted">No user data available for the selected period.</p>';
        }
        
        $html = '
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>User</th>
                        <th>Total Deposits</th>
                        <th>Total Withdrawals</th>
                        <th>Net Volume</th>
                        <th>Transaction Count</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>';
        
        $rank = 1;
        foreach ($topUsers as $user) {
            $netVolume = $user['total_deposits'] - $user['total_withdrawals'];
            $html .= '
                    <tr>
                        <td>' . $rank . '</td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        ' . strtoupper(substr($user['first_name'], 0, 1)) . '
                                    </div>
                                </div>
                                <div>
                                    <div class="fw-medium">' . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . '</div>
                                    <small class="text-muted">@' . htmlspecialchars($user['username']) . '</small>
                                </div>
                            </div>
                        </td>
                        <td class="text-success">$' . number_format($user['total_deposits'], 2) . '</td>
                        <td class="text-danger">$' . number_format($user['total_withdrawals'], 2) . '</td>
                        <td class="' . ($netVolume >= 0 ? 'text-success' : 'text-danger') . '">
                            $' . number_format($netVolume, 2) . '
                        </td>
                        <td>' . $user['transaction_count'] . '</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                    onclick="viewUserDetails(' . $user['id'] . ')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>';
            $rank++;
        }
        
        $html .= '
                </tbody>
            </table>
        </div>';
        
        return $html;
    }
    
    protected function getAdditionalCSS() {
        return [
            '/assets/css/admin-financial-reports.css'
        ];
    }
    
    protected function getAdditionalJS() {
        return [
            'https://cdn.jsdelivr.net/npm/chart.js',
            '/assets/js/admin-financial-reports.js'
        ];
    }
}
?>