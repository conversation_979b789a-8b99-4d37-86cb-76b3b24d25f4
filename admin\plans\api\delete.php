<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $planId = intval($input['plan_id'] ?? 0);
    
    if (!$planId) {
        echo json_encode(['success' => false, 'message' => 'Plan ID is required']);
        exit;
    }
    
    // Load the plan
    $plan = TradingPlan::find($planId);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'Trading plan not found']);
        exit;
    }
    
    // Check if plan has any deposits
    $depositsCount = $plan->getDepositsCount();
    
    if ($depositsCount > 0) {
        echo json_encode([
            'success' => false,
            'message' => "Cannot delete plan with existing deposits. This plan has {$depositsCount} deposits."
        ]);
        exit;
    }
    
    // Store plan data for audit log
    $planData = [
        'id' => $plan->getId(),
        'name' => $plan->name,
        'min_deposit' => $plan->min_deposit,
        'max_deposit' => $plan->max_deposit,
        'daily_return' => $plan->daily_return,
        'duration_days' => $plan->duration_days,
        'status' => $plan->status,
        'description' => $plan->description,
        'features' => $plan->getFeaturesArray()
    ];
    
    // Delete the plan
    if ($plan->delete()) {
        // Log the action
        AuditTrailService::log(
            'trading_plan_deleted',
            'trading_plan',
            $planId,
            [
                'deleted_plan' => $planData,
                'reason' => 'Admin deletion'
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Trading plan deleted successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to delete trading plan'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Delete trading plan error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while deleting the trading plan'
    ]);
}
?>