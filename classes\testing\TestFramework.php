<?php
require_once __DIR__ . '/../services/ErrorHandlingService.php';
require_once __DIR__ . '/../services/SystemHealthService.php';

/**
 * Comprehensive Testing Framework
 * Provides unit testing, integration testing, and system testing capabilities
 */
class TestFramework {
    private $tests = [];
    private $results = [];
    private $currentSuite = null;
    private $errorHandler;
    private $healthService;
    
    // Test result constants
    const RESULT_PASS = 'pass';
    const RESULT_FAIL = 'fail';
    const RESULT_SKIP = 'skip';
    const RESULT_ERROR = 'error';
    
    // Test types
    const TYPE_UNIT = 'unit';
    const TYPE_INTEGRATION = 'integration';
    const TYPE_SYSTEM = 'system';
    const TYPE_PERFORMANCE = 'performance';
    const TYPE_SECURITY = 'security';
    
    public function __construct() {
        $this->errorHandler = ErrorHandlingService::getInstance();
        $this->healthService = SystemHealthService::getInstance();
    }
    
    /**
     * Create a new test suite
     */
    public function suite($name, $type = self::TYPE_UNIT) {
        $this->currentSuite = [
            'name' => $name,
            'type' => $type,
            'tests' => [],
            'setup' => null,
            'teardown' => null,
            'started_at' => null,
            'completed_at' => null
        ];
        
        return $this;
    }
    
    /**
     * Add setup function for current suite
     */
    public function setup($callback) {
        if ($this->currentSuite) {
            $this->currentSuite['setup'] = $callback;
        }
        return $this;
    }
    
    /**
     * Add teardown function for current suite
     */
    public function teardown($callback) {
        if ($this->currentSuite) {
            $this->currentSuite['teardown'] = $callback;
        }
        return $this;
    }
    
    /**
     * Add a test to the current suite
     */
    public function test($name, $callback, $timeout = 30) {
        if ($this->currentSuite) {
            $this->currentSuite['tests'][] = [
                'name' => $name,
                'callback' => $callback,
                'timeout' => $timeout,
                'result' => null,
                'message' => null,
                'duration' => null,
                'memory_usage' => null,
                'assertions' => []
            ];
        }
        return $this;
    }
    
    /**
     * Finalize current suite and add to tests
     */
    public function end() {
        if ($this->currentSuite) {
            $this->tests[] = $this->currentSuite;
            $this->currentSuite = null;
        }
        return $this;
    }
    
    /**
     * Run all test suites
     */
    public function run($outputFormat = 'html') {
        $this->results = [
            'started_at' => date('Y-m-d H:i:s'),
            'completed_at' => null,
            'total_suites' => count($this->tests),
            'total_tests' => 0,
            'pass' => 0,
            'fail' => 0,
            'skip' => 0,
            'error' => 0,
            'suites' => []
        ];
        
        foreach ($this->tests as $suite) {
            $this->runSuite($suite);
        }
        
        $this->results['completed_at'] = date('Y-m-d H:i:s');
        
        // Output results
        switch ($outputFormat) {
            case 'json':
                return $this->outputJSON();
            case 'xml':
                return $this->outputXML();
            case 'text':
                return $this->outputText();
            default:
                return $this->outputHTML();
        }
    }
    
    /**
     * Run a single test suite
     */
    private function runSuite($suite) {
        $suiteResult = [
            'name' => $suite['name'],
            'type' => $suite['type'],
            'started_at' => date('Y-m-d H:i:s'),
            'completed_at' => null,
            'tests' => [],
            'pass' => 0,
            'fail' => 0,
            'skip' => 0,
            'error' => 0,
            'setup_error' => null,
            'teardown_error' => null
        ];
        
        // Run setup
        if ($suite['setup']) {
            try {
                call_user_func($suite['setup']);
            } catch (Exception $e) {
                $suiteResult['setup_error'] = $e->getMessage();
                $this->errorHandler->logApplicationError(
                    "Test suite setup failed: " . $e->getMessage(),
                    ErrorHandlingService::CATEGORY_SYSTEM,
                    ErrorHandlingService::SEVERITY_MEDIUM,
                    ['suite' => $suite['name']]
                );
            }
        }
        
        // Run tests
        foreach ($suite['tests'] as $test) {
            $testResult = $this->runTest($test, $suiteResult['setup_error'] !== null);
            $suiteResult['tests'][] = $testResult;
            $suiteResult[$testResult['result']]++;
            $this->results['total_tests']++;
            $this->results[$testResult['result']]++;
        }
        
        // Run teardown
        if ($suite['teardown']) {
            try {
                call_user_func($suite['teardown']);
            } catch (Exception $e) {
                $suiteResult['teardown_error'] = $e->getMessage();
                $this->errorHandler->logApplicationError(
                    "Test suite teardown failed: " . $e->getMessage(),
                    ErrorHandlingService::CATEGORY_SYSTEM,
                    ErrorHandlingService::SEVERITY_MEDIUM,
                    ['suite' => $suite['name']]
                );
            }
        }
        
        $suiteResult['completed_at'] = date('Y-m-d H:i:s');
        $this->results['suites'][] = $suiteResult;
    }
    
    /**
     * Run a single test
     */
    private function runTest($test, $skipDueToSetupError = false) {
        $testResult = [
            'name' => $test['name'],
            'result' => self::RESULT_SKIP,
            'message' => null,
            'duration' => 0,
            'memory_usage' => 0,
            'assertions' => [],
            'started_at' => date('Y-m-d H:i:s'),
            'completed_at' => null
        ];
        
        if ($skipDueToSetupError) {
            $testResult['message'] = 'Skipped due to setup error';
            $testResult['completed_at'] = date('Y-m-d H:i:s');
            return $testResult;
        }
        
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        try {
            // Set timeout
            if (function_exists('set_time_limit')) {
                set_time_limit($test['timeout']);
            }
            
            // Create test context
            $testContext = new TestContext();
            
            // Run test
            call_user_func($test['callback'], $testContext);
            
            // Check if test passed
            if ($testContext->hasFailures()) {
                $testResult['result'] = self::RESULT_FAIL;
                $testResult['message'] = implode('; ', $testContext->getFailures());
            } else {
                $testResult['result'] = self::RESULT_PASS;
                $testResult['message'] = 'Test passed';
            }
            
            $testResult['assertions'] = $testContext->getAssertions();
            
        } catch (Exception $e) {
            $testResult['result'] = self::RESULT_ERROR;
            $testResult['message'] = $e->getMessage();
            
            // Log test error
            $this->errorHandler->logApplicationError(
                "Test error: " . $e->getMessage(),
                ErrorHandlingService::CATEGORY_SYSTEM,
                ErrorHandlingService::SEVERITY_LOW,
                ['test' => $test['name']]
            );
        }
        
        $testResult['duration'] = round((microtime(true) - $startTime) * 1000, 2);
        $testResult['memory_usage'] = memory_get_usage(true) - $startMemory;
        $testResult['completed_at'] = date('Y-m-d H:i:s');
        
        return $testResult;
    }
    
    /**
     * Output results as HTML
     */
    private function outputHTML() {
        $html = "<!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Test Results - " . date('Y-m-d H:i:s') . "</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
            <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
            <style>
                .test-pass { color: #28a745; }
                .test-fail { color: #dc3545; }
                .test-skip { color: #ffc107; }
                .test-error { color: #fd7e14; }
                .suite-header { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .test-item { padding: 10px; border-left: 4px solid #e9ecef; margin: 5px 0; }
                .test-item.pass { border-left-color: #28a745; }
                .test-item.fail { border-left-color: #dc3545; }
                .test-item.skip { border-left-color: #ffc107; }
                .test-item.error { border-left-color: #fd7e14; }
            </style>
        </head>
        <body class='bg-light'>
            <div class='container mt-4'>
                <h1><i class='fas fa-vial me-2'></i>Test Results</h1>
                
                <div class='row mb-4'>
                    <div class='col-md-3'>
                        <div class='card bg-success text-white'>
                            <div class='card-body text-center'>
                                <h3>{$this->results['pass']}</h3>
                                <p class='mb-0'>Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-danger text-white'>
                            <div class='card-body text-center'>
                                <h3>{$this->results['fail']}</h3>
                                <p class='mb-0'>Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-warning text-white'>
                            <div class='card-body text-center'>
                                <h3>{$this->results['skip']}</h3>
                                <p class='mb-0'>Skipped</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-info text-white'>
                            <div class='card-body text-center'>
                                <h3>{$this->results['error']}</h3>
                                <p class='mb-0'>Errors</p>
                            </div>
                        </div>
                    </div>
                </div>";
        
        foreach ($this->results['suites'] as $suite) {
            $html .= "<div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>{$suite['name']} ({$suite['type']})</h4>
                    <small class='text-muted'>
                        Passed: {$suite['pass']} | Failed: {$suite['fail']} | 
                        Skipped: {$suite['skip']} | Errors: {$suite['error']}
                    </small>
                </div>
                <div class='card-body'>";
            
            foreach ($suite['tests'] as $test) {
                $iconClass = [
                    self::RESULT_PASS => 'fa-check-circle test-pass',
                    self::RESULT_FAIL => 'fa-times-circle test-fail',
                    self::RESULT_SKIP => 'fa-minus-circle test-skip',
                    self::RESULT_ERROR => 'fa-exclamation-circle test-error'
                ];
                
                $html .= "<div class='test-item {$test['result']}'>
                    <i class='fas {$iconClass[$test['result']]} me-2'></i>
                    <strong>{$test['name']}</strong>
                    <span class='float-end'>
                        <small class='text-muted'>{$test['duration']}ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>{$test['message']}</small>
                </div>";
            }
            
            $html .= "</div></div>";
        }
        
        $html .= "</div>
        <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
        </body>
        </html>";
        
        return $html;
    }
    
    /**
     * Output results as JSON
     */
    private function outputJSON() {
        return json_encode($this->results, JSON_PRETTY_PRINT);
    }
    
    /**
     * Output results as XML
     */
    private function outputXML() {
        $xml = new SimpleXMLElement('<testsuites/>');
        $xml->addAttribute('name', 'Test Results');
        $xml->addAttribute('tests', $this->results['total_tests']);
        $xml->addAttribute('failures', $this->results['fail']);
        $xml->addAttribute('errors', $this->results['error']);
        $xml->addAttribute('time', strtotime($this->results['completed_at']) - strtotime($this->results['started_at']));
        
        foreach ($this->results['suites'] as $suite) {
            $suiteXml = $xml->addChild('testsuite');
            $suiteXml->addAttribute('name', $suite['name']);
            $suiteXml->addAttribute('tests', count($suite['tests']));
            $suiteXml->addAttribute('failures', $suite['fail']);
            $suiteXml->addAttribute('errors', $suite['error']);
            
            foreach ($suite['tests'] as $test) {
                $testXml = $suiteXml->addChild('testcase');
                $testXml->addAttribute('name', $test['name']);
                $testXml->addAttribute('time', $test['duration'] / 1000);
                
                if ($test['result'] === self::RESULT_FAIL) {
                    $failure = $testXml->addChild('failure', htmlspecialchars($test['message']));
                    $failure->addAttribute('message', $test['message']);
                } elseif ($test['result'] === self::RESULT_ERROR) {
                    $error = $testXml->addChild('error', htmlspecialchars($test['message']));
                    $error->addAttribute('message', $test['message']);
                } elseif ($test['result'] === self::RESULT_SKIP) {
                    $testXml->addChild('skipped');
                }
            }
        }
        
        return $xml->asXML();
    }
    
    /**
     * Output results as plain text
     */
    private function outputText() {
        $output = "Test Results\n";
        $output .= "============\n\n";
        $output .= "Total Tests: {$this->results['total_tests']}\n";
        $output .= "Passed: {$this->results['pass']}\n";
        $output .= "Failed: {$this->results['fail']}\n";
        $output .= "Skipped: {$this->results['skip']}\n";
        $output .= "Errors: {$this->results['error']}\n\n";
        
        foreach ($this->results['suites'] as $suite) {
            $output .= "Suite: {$suite['name']} ({$suite['type']})\n";
            $output .= str_repeat('-', 50) . "\n";
            
            foreach ($suite['tests'] as $test) {
                $status = strtoupper($test['result']);
                $output .= "[$status] {$test['name']} ({$test['duration']}ms)\n";
                if ($test['message']) {
                    $output .= "  {$test['message']}\n";
                }
            }
            $output .= "\n";
        }
        
        return $output;
    }
    
    /**
     * Save results to file
     */
    public function saveResults($filename, $format = 'json') {
        $content = '';
        
        switch ($format) {
            case 'html':
                $content = $this->outputHTML();
                break;
            case 'xml':
                $content = $this->outputXML();
                break;
            case 'text':
                $content = $this->outputText();
                break;
            default:
                $content = $this->outputJSON();
                break;
        }
        
        $dir = __DIR__ . '/../../test_results';
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        
        return file_put_contents($dir . '/' . $filename, $content) !== false;
    }
    
    /**
     * Get test results
     */
    public function getResults() {
        return $this->results;
    }
}

/**
 * Test Context - Provides assertion methods for tests
 */
class TestContext {
    private $assertions = [];
    private $failures = [];
    
    /**
     * Assert that a condition is true
     */
    public function assertTrue($condition, $message = 'Assertion failed') {
        $this->assertions[] = [
            'type' => 'assertTrue',
            'condition' => $condition,
            'message' => $message,
            'result' => (bool)$condition
        ];
        
        if (!$condition) {
            $this->failures[] = $message;
        }
        
        return $this;
    }
    
    /**
     * Assert that a condition is false
     */
    public function assertFalse($condition, $message = 'Assertion failed') {
        return $this->assertTrue(!$condition, $message);
    }
    
    /**
     * Assert that two values are equal
     */
    public function assertEqual($expected, $actual, $message = 'Values are not equal') {
        $condition = $expected === $actual;
        
        $this->assertions[] = [
            'type' => 'assertEqual',
            'expected' => $expected,
            'actual' => $actual,
            'message' => $message,
            'result' => $condition
        ];
        
        if (!$condition) {
            $this->failures[] = "$message (Expected: " . var_export($expected, true) . ", Actual: " . var_export($actual, true) . ")";
        }
        
        return $this;
    }
    
    /**
     * Assert that two values are not equal
     */
    public function assertNotEqual($expected, $actual, $message = 'Values should not be equal') {
        $condition = $expected !== $actual;
        
        $this->assertions[] = [
            'type' => 'assertNotEqual',
            'expected' => $expected,
            'actual' => $actual,
            'message' => $message,
            'result' => $condition
        ];
        
        if (!$condition) {
            $this->failures[] = "$message (Both values are: " . var_export($expected, true) . ")";
        }
        
        return $this;
    }
    
    /**
     * Assert that a value is null
     */
    public function assertNull($value, $message = 'Value is not null') {
        return $this->assertTrue($value === null, $message);
    }
    
    /**
     * Assert that a value is not null
     */
    public function assertNotNull($value, $message = 'Value is null') {
        return $this->assertTrue($value !== null, $message);
    }
    
    /**
     * Assert that an array contains a value
     */
    public function assertContains($needle, $haystack, $message = 'Array does not contain value') {
        $condition = in_array($needle, $haystack, true);
        
        $this->assertions[] = [
            'type' => 'assertContains',
            'needle' => $needle,
            'haystack' => $haystack,
            'message' => $message,
            'result' => $condition
        ];
        
        if (!$condition) {
            $this->failures[] = $message;
        }
        
        return $this;
    }
    
    /**
     * Assert that a string contains a substring
     */
    public function assertStringContains($needle, $haystack, $message = 'String does not contain substring') {
        $condition = strpos($haystack, $needle) !== false;
        
        $this->assertions[] = [
            'type' => 'assertStringContains',
            'needle' => $needle,
            'haystack' => $haystack,
            'message' => $message,
            'result' => $condition
        ];
        
        if (!$condition) {
            $this->failures[] = $message;
        }
        
        return $this;
    }
    
    /**
     * Assert that an exception is thrown
     */
    public function assertThrows($callback, $expectedExceptionClass = 'Exception', $message = 'Expected exception was not thrown') {
        $exceptionThrown = false;
        $actualException = null;
        
        try {
            call_user_func($callback);
        } catch (Exception $e) {
            $exceptionThrown = true;
            $actualException = $e;
        }
        
        $condition = $exceptionThrown && ($actualException instanceof $expectedExceptionClass);
        
        $this->assertions[] = [
            'type' => 'assertThrows',
            'expected_exception' => $expectedExceptionClass,
            'actual_exception' => $actualException ? get_class($actualException) : null,
            'message' => $message,
            'result' => $condition
        ];
        
        if (!$condition) {
            if (!$exceptionThrown) {
                $this->failures[] = $message . ' (No exception thrown)';
            } else {
                $this->failures[] = $message . ' (Wrong exception type: ' . get_class($actualException) . ')';
            }
        }
        
        return $this;
    }
    
    /**
     * Get all assertions
     */
    public function getAssertions() {
        return $this->assertions;
    }
    
    /**
     * Get all failures
     */
    public function getFailures() {
        return $this->failures;
    }
    
    /**
     * Check if there are any failures
     */
    public function hasFailures() {
        return !empty($this->failures);
    }
}
?>