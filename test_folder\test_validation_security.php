<?php
/**
 * Test Validation and Security Features
 * Run this script to test all validation and security components
 */

require_once __DIR__ . '/../includes/security_init.php';
require_once __DIR__ . '/../classes/validators/RegistrationValidator.php';
require_once __DIR__ . '/../classes/validators/FinancialValidator.php';
require_once __DIR__ . '/../classes/validators/SecurityValidator.php';

class ValidationSecurityTestSuite {
    private $testResults = [];
    private $totalTests = 0;
    private $passedTests = 0;
    
    public function __construct() {
        echo "<h1>Validation and Security Test Suite</h1>\n";
    }
    
    public function runAllTests() {
        $this->testValidationHelper();
        $this->testInputSanitization();
        $this->testRegistrationValidator();
        $this->testFinancialValidator();
        $this->testSecurityValidator();
        $this->testInputSanitizerService();
        $this->testFileUploadValidation();
        $this->testSecurityHeaders();
        $this->testBulkFieldValidation();
        $this->displaySummary();
    }
    
    private function assertTest($condition, $testName) {
        $this->totalTests++;
        if ($condition) {
            $this->passedTests++;
            echo "✅ $testName: PASS\n";
        } else {
            echo "❌ $testName: FAIL\n";
        }
        $this->testResults[] = ['name' => $testName, 'passed' => $condition];
    }

    private function testValidationHelper() {
        echo "<h2>1. Testing ValidationHelper</h2>\n";
        
        $testCases = [
            'email' => [
                ['<EMAIL>', null, 'Valid email validation'],
                ['invalid-email', 'not_null', 'Invalid email validation'],
                ['', 'not_null', 'Empty email validation'],
                ['test@', 'not_null', 'Incomplete email validation']
            ],
            'password' => [
                ['SecurePass123!', null, 'Strong password validation'],
                ['123', 'not_null', 'Weak password validation'],
                ['', 'not_null', 'Empty password validation'],
                ['password', 'not_null', 'Password without numbers validation']
            ],
            'username' => [
                ['testuser', null, 'Valid username validation'],
                ['test user!', 'not_null', 'Invalid username with spaces validation'],
                ['ab', 'not_null', 'Too short username validation'],
                ['', 'not_null', 'Empty username validation']
            ]
        ];
        
        foreach ($testCases as $method => $cases) {
            $methodName = 'validate' . ucfirst($method);
            
            foreach ($cases as [$input, $expectedResult, $testName]) {
                $result = ValidationHelper::$methodName($input);
                
                if ($expectedResult === null) {
                    $this->assertTest($result === null, $testName);
                } else {
                    $this->assertTest($result !== null, $testName);
                }
            }
        }
    }

// Test 2: Input Sanitization
echo "<h2>2. Testing Input Sanitization</h2>\n";

$maliciousData = [
    'xss_attempt' => '<script>alert("XSS")</script>',
    'sql_injection' => "'; DROP TABLE users; --",
    'normal_text' => 'This is normal text',
    'html_content' => '<p>This is <strong>bold</strong> text</p>'
];

echo "Testing XSS prevention:\n";
foreach ($maliciousData as $key => $value) {
    $sanitized = ValidationHelper::preventXSS($value);
    echo "- $key: " . ($sanitized !== $value ? 'SANITIZED' : 'UNCHANGED') . "\n";
    echo "  Original: $value\n";
    echo "  Sanitized: $sanitized\n";
}

// Test 3: Registration Validator
echo "<h2>3. Testing Registration Validator</h2>\n";

$registrationData = [
    'first_name' => 'John',
    'last_name' => 'Doe',
    'username' => 'johndoe',
    'email' => '<EMAIL>',
    'password' => 'SecurePass123!',
    'password_confirmation' => 'SecurePass123!',
    'phone' => '******-567-8900',
    'terms_accepted' => true,
    'csrf_token' => 'dummy_token'
];

$regValidator = new RegistrationValidator();
$sanitizedData = RegistrationValidator::sanitize($registrationData);

echo "Sanitized registration data:\n";
foreach ($sanitizedData as $key => $value) {
    echo "- $key: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
}

// Test 4: Financial Validator
echo "<h2>4. Testing Financial Validator</h2>\n";

$depositData = [
    'amount' => '100.50',
    'payment_method' => 'bank_transfer',
    'csrf_token' => 'dummy_token'
];

$withdrawalData = [
    'amount' => '50.00',
    'withdrawal_method' => 'bank_transfer',
    'bank_name' => 'Test Bank',
    'account_number' => '*********0',
    'routing_number' => '*********',
    'account_holder_name' => 'John Doe',
    'csrf_token' => 'dummy_token'
];

$finValidator = new FinancialValidator();

echo "Testing deposit validation:\n";
$isValidDeposit = $finValidator->validateDeposit($depositData);
echo "- Deposit validation: " . ($isValidDeposit ? 'PASS' : 'FAIL') . "\n";
if (!$isValidDeposit) {
    foreach ($finValidator->getErrors() as $field => $error) {
        echo "  Error in $field: $error\n";
    }
}

echo "Testing withdrawal validation:\n";
$isValidWithdrawal = $finValidator->validateWithdrawal($withdrawalData, 1000.00);
echo "- Withdrawal validation: " . ($isValidWithdrawal ? 'PASS' : 'FAIL') . "\n";
if (!$isValidWithdrawal) {
    foreach ($finValidator->getErrors() as $field => $error) {
        echo "  Error in $field: $error\n";
    }
}

// Test 5: Security Validator
echo "<h2>5. Testing Security Validator</h2>\n";

$secValidator = new SecurityValidator();

echo "Testing rate limiting:\n";
$rateLimitTest = $secValidator->validateRateLimit('test_operation', '127.0.0.1', 3, 60);
echo "- First attempt: " . ($rateLimitTest ? 'ALLOWED' : 'BLOCKED') . "\n";

$rateLimitTest = $secValidator->validateRateLimit('test_operation', '127.0.0.1', 3, 60);
echo "- Second attempt: " . ($rateLimitTest ? 'ALLOWED' : 'BLOCKED') . "\n";

$rateLimitTest = $secValidator->validateRateLimit('test_operation', '127.0.0.1', 3, 60);
echo "- Third attempt: " . ($rateLimitTest ? 'ALLOWED' : 'BLOCKED') . "\n";

$rateLimitTest = $secValidator->validateRateLimit('test_operation', '127.0.0.1', 3, 60);
echo "- Fourth attempt (should be blocked): " . ($rateLimitTest ? 'ALLOWED' : 'BLOCKED') . "\n";

// Test 6: Input Sanitizer Service
echo "<h2>6. Testing Input Sanitizer Service</h2>\n";

$formData = [
    'username' => 'test_user',
    'email' => '<EMAIL>',
    'amount' => '100.50',
    'description' => '<script>alert("xss")</script>Normal text',
    'password' => 'DontSanitizeThis!'
];

$sanitizedForm = InputSanitizer::sanitizeForm($formData, 'financial');

echo "Financial context sanitization:\n";
foreach ($sanitizedForm as $key => $value) {
    echo "- $key: '$value'\n";
}

// Test 7: File Upload Validation
echo "<h2>7. Testing File Upload Validation</h2>\n";

$mockFile = [
    'name' => 'test_document.pdf',
    'type' => 'application/pdf',
    'size' => 1024000, // 1MB
    'tmp_name' => '/tmp/phpupload',
    'error' => UPLOAD_ERR_OK
];

$fileValidation = ValidationHelper::validateFileUpload($mockFile, ['pdf', 'jpg', 'png'], 5242880);
echo "- Valid file upload: " . ($fileValidation ? 'FAIL' : 'PASS') . "\n";

$mockLargeFile = [
    'name' => 'large_file.pdf',
    'type' => 'application/pdf',
    'size' => 10485760, // 10MB
    'tmp_name' => '/tmp/phpupload',
    'error' => UPLOAD_ERR_OK
];

$largeFileValidation = ValidationHelper::validateFileUpload($mockLargeFile, ['pdf'], 5242880);
echo "- Large file upload (should fail): " . ($largeFileValidation ? 'FAIL' : 'PASS') . "\n";

// Test 8: Security Headers
echo "<h2>8. Testing Security Headers</h2>\n";

$securityMiddleware = SecurityMiddleware::getInstance();
echo "Security middleware initialized: PASS\n";

$config = $securityMiddleware->getConfig();
echo "Security configuration loaded:\n";
foreach ($config as $key => $value) {
    echo "- $key: " . (is_bool($value) ? ($value ? 'enabled' : 'disabled') : $value) . "\n";
}

// Test 9: Bulk Field Validation
echo "<h2>9. Testing Bulk Field Validation</h2>\n";

$bulkData = [
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'age' => '25',
    'status' => 'active'
];

$validationRules = [
    'name' => [
        'required' => true,
        'length' => ['min' => 2, 'max' => 50]
    ],
    'email' => [
        'required' => true,
        'email' => true
    ],
    'age' => [
        'required' => true,
        'numeric' => ['min' => 18, 'max' => 120]
    ],
    'status' => [
        'required' => true,
        'enum' => ['active', 'inactive', 'pending']
    ]
];

$bulkErrors = ValidationHelper::validateFields($bulkData, $validationRules);
echo "Bulk validation result: " . (empty($bulkErrors) ? 'PASS' : 'FAIL') . "\n";
if (!empty($bulkErrors)) {
    foreach ($bulkErrors as $field => $error) {
        echo "- Error in $field: $error\n";
    }
}

echo "<h2>Test Summary</h2>\n";
echo "All validation and security components have been tested.\n";
echo "Check the output above for any failures that need attention.\n";
echo "Security logging is active and events are being recorded.\n";

// Log this test run
logSecurityEvent('validation_test_completed', [
    'test_timestamp' => date('Y-m-d H:i:s'),
    'components_tested' => [
        'ValidationHelper',
        'InputSanitizer',
        'RegistrationValidator',
        'FinancialValidator',
        'SecurityValidator',
        'SecurityMiddleware'
    ]
]);

    }
    
    private function testInputSanitization() {
        echo "<h2>2. Testing Input Sanitization</h2>\n";
        
        $maliciousInputs = [
            '<script>alert("XSS")</script>' => 'XSS script tag',
            "'; DROP TABLE users; --" => 'SQL injection attempt',
            '<img src="x" onerror="alert(1)">' => 'XSS via image tag',
            'javascript:alert(1)' => 'JavaScript protocol'
        ];
        
        foreach ($maliciousInputs as $input => $description) {
            $sanitized = ValidationHelper::preventXSS($input);
            $this->assertTest(
                $sanitized !== $input && !preg_match('/<script|javascript:|onerror=/i', $sanitized),
                "Sanitization of $description"
            );
        }
    }
    
    private function testRegistrationValidator() {
        echo "<h2>3. Testing Registration Validator</h2>\n";
        
        $validData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'username' => 'johndoe',
            'email' => '<EMAIL>',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
            'terms_accepted' => true
        ];
        
        $regValidator = new RegistrationValidator();
        $isValid = $regValidator->validate($validData);
        $this->assertTest($isValid, 'Valid registration data');
        
        // Test invalid data
        $invalidData = $validData;
        $invalidData['email'] = 'invalid-email';
        $isInvalid = !$regValidator->validate($invalidData);
        $this->assertTest($isInvalid, 'Invalid registration data rejection');
    }
    
    private function testFinancialValidator() {
        echo "<h2>4. Testing Financial Validator</h2>\n";
        
        $finValidator = new FinancialValidator();
        
        $validDeposit = [
            'amount' => '100.50',
            'payment_method' => 'bank_transfer'
        ];
        
        $this->assertTest(
            $finValidator->validateDeposit($validDeposit),
            'Valid deposit validation'
        );
        
        $invalidDeposit = [
            'amount' => 'invalid',
            'payment_method' => ''
        ];
        
        $this->assertTest(
            !$finValidator->validateDeposit($invalidDeposit),
            'Invalid deposit rejection'
        );
    }
    
    private function testSecurityValidator() {
        echo "<h2>5. Testing Security Validator</h2>\n";
        
        $secValidator = new SecurityValidator();
        
        // Test rate limiting
        $allowed1 = $secValidator->validateRateLimit('test_op', '127.0.0.1', 2, 60);
        $allowed2 = $secValidator->validateRateLimit('test_op', '127.0.0.1', 2, 60);
        $blocked = $secValidator->validateRateLimit('test_op', '127.0.0.1', 2, 60);
        
        $this->assertTest($allowed1, 'First rate limit attempt allowed');
        $this->assertTest($allowed2, 'Second rate limit attempt allowed');
        $this->assertTest(!$blocked, 'Third rate limit attempt blocked');
    }
    
    private function testInputSanitizerService() {
        echo "<h2>6. Testing Input Sanitizer Service</h2>\n";
        
        $formData = [
            'username' => 'TEST_USER',
            'email' => '<EMAIL>',
            'amount' => '100.50'
        ];
        
        $sanitized = InputSanitizer::sanitizeForm($formData, 'financial');
        
        $this->assertTest(
            isset($sanitized['username']) && isset($sanitized['email']),
            'Form data sanitization'
        );
    }
    
    private function testFileUploadValidation() {
        echo "<h2>7. Testing File Upload Validation</h2>\n";
        
        $validFile = [
            'name' => 'document.pdf',
            'type' => 'application/pdf',
            'size' => 1024000,
            'error' => UPLOAD_ERR_OK
        ];
        
        $result = ValidationHelper::validateFileUpload($validFile, ['pdf'], 5242880);
        $this->assertTest($result === null, 'Valid file upload');
        
        $invalidFile = [
            'name' => 'large.pdf',
            'type' => 'application/pdf',
            'size' => 10485760,
            'error' => UPLOAD_ERR_OK
        ];
        
        $result = ValidationHelper::validateFileUpload($invalidFile, ['pdf'], 5242880);
        $this->assertTest($result !== null, 'Invalid large file rejection');
    }
    
    private function testSecurityHeaders() {
        echo "<h2>8. Testing Security Headers</h2>\n";
        
        $middleware = SecurityMiddleware::getInstance();
        $config = $middleware->getConfig();
        
        $this->assertTest(
            is_array($config) && !empty($config),
            'Security middleware configuration loaded'
        );
    }
    
    private function testBulkFieldValidation() {
        echo "<h2>9. Testing Bulk Field Validation</h2>\n";
        
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'age' => '25'
        ];
        
        $rules = [
            'name' => ['required' => true, 'length' => ['min' => 2, 'max' => 50]],
            'email' => ['required' => true, 'email' => true],
            'age' => ['required' => true, 'numeric' => ['min' => 18, 'max' => 120]]
        ];
        
        $errors = ValidationHelper::validateFields($data, $rules);
        $this->assertTest(empty($errors), 'Bulk field validation');
    }
    
    private function displaySummary() {
        echo "<h2>📊 Test Summary</h2>\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: " . ($this->totalTests - $this->passedTests) . "\n";
        echo "Success Rate: " . round(($this->passedTests / $this->totalTests) * 100, 2) . "%\n";
        
        if ($this->passedTests === $this->totalTests) {
            echo "🎉 All tests passed!\n";
        } else {
            echo "⚠️ Some tests failed. Please review the output above.\n";
        }
        
        // Log test results
        logSecurityEvent('validation_test_completed', [
            'total_tests' => $this->totalTests,
            'passed_tests' => $this->passedTests,
            'success_rate' => ($this->passedTests / $this->totalTests) * 100,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}

// Run the test suite
$testSuite = new ValidationSecurityTestSuite();
$testSuite->runAllTests();
?>