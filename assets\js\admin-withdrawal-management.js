/**
 * Admin Withdrawal Management JavaScript
 */

// Global variables
let selectedWithdrawals = [];

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeWithdrawalManagement();
});

/**
 * Initialize withdrawal management functionality
 */
function initializeWithdrawalManagement() {
    // Initialize select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllWithdrawals');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }
    
    // Initialize individual checkboxes
    const withdrawalCheckboxes = document.querySelectorAll('.withdrawal-checkbox');
    withdrawalCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedWithdrawals);
    });
    
    // Initialize search functionality
    const searchInput = document.getElementById('searchWithdrawals');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchWithdrawals();
            }
        });
    }
    
    // Initialize form submissions
    initializeFormSubmissions();
    
    // Initialize bulk actions
    initializeBulkActions();
}

/**
 * Toggle select all checkboxes
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllWithdrawals');
    const withdrawalCheckboxes = document.querySelectorAll('.withdrawal-checkbox');
    
    withdrawalCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedWithdrawals();
}

/**
 * Update selected withdrawals array
 */
function updateSelectedWithdrawals() {
    selectedWithdrawals = [];
    const withdrawalCheckboxes = document.querySelectorAll('.withdrawal-checkbox:checked');
    
    withdrawalCheckboxes.forEach(checkbox => {
        selectedWithdrawals.push(parseInt(checkbox.value));
    });
    
    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAllWithdrawals');
    const allCheckboxes = document.querySelectorAll('.withdrawal-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = selectedWithdrawals.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedWithdrawals.length > 0 && selectedWithdrawals.length < allCheckboxes.length;
    }
    
    // Update selected count in bulk actions modal
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = selectedWithdrawals.length;
    }
}

/**
 * Filter withdrawals by status
 */
function filterWithdrawals(status) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('status', status);
    currentUrl.searchParams.delete('page'); // Reset to first page
    window.location.href = currentUrl.toString();
}

/**
 * Search withdrawals
 */
function searchWithdrawals() {
    const searchTerm = document.getElementById('searchWithdrawals').value.trim();
    if (searchTerm) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('search', searchTerm);
        currentUrl.searchParams.delete('page'); // Reset to first page
        window.location.href = currentUrl.toString();
    }
}

/**
 * Refresh withdrawals page
 */
function refreshWithdrawals() {
    window.location.reload();
}

/**
 * Show bulk actions modal
 */
function showBulkActions() {
    if (selectedWithdrawals.length === 0) {
        showAlert('Please select at least one withdrawal to perform bulk actions.', 'warning');
        return;
    }
    
    const modal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
    modal.show();
}
/
**
 * View withdrawal details
 */
function viewWithdrawalDetails(withdrawalId) {
    fetch(`api/details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('withdrawalDetailsContent').innerHTML = data.html;
                const modal = new bootstrap.Modal(document.getElementById('withdrawalDetailsModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load withdrawal details', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading withdrawal details', 'error');
        });
}

/**
 * Approve withdrawal
 */
function approveWithdrawal(withdrawalId) {
    // Load withdrawal data first
    fetch(`api/details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const withdrawal = data.withdrawal;
                document.getElementById('approveWithdrawalId').value = withdrawalId;
                document.getElementById('approveWithdrawalAmount').value = '$' + parseFloat(withdrawal.amount).toFixed(2);
                document.getElementById('approveUserBalance').value = '$' + parseFloat(withdrawal.user_balance).toFixed(2);
                
                const modal = new bootstrap.Modal(document.getElementById('approveWithdrawalModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load withdrawal data', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading withdrawal data', 'error');
        });
}

/**
 * Reject withdrawal
 */
function rejectWithdrawal(withdrawalId) {
    // Load withdrawal data first
    fetch(`api/details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const withdrawal = data.withdrawal;
                document.getElementById('rejectWithdrawalId').value = withdrawalId;
                document.getElementById('rejectWithdrawalAmount').value = '$' + parseFloat(withdrawal.amount).toFixed(2);
                
                const modal = new bootstrap.Modal(document.getElementById('rejectWithdrawalModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load withdrawal data', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading withdrawal data', 'error');
        });
}

/**
 * Mark withdrawal as processing
 */
function markAsProcessing(withdrawalId) {
    // Load withdrawal data first
    fetch(`api/details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const withdrawal = data.withdrawal;
                document.getElementById('processingWithdrawalId').value = withdrawalId;
                document.getElementById('processingWithdrawalAmount').value = '$' + parseFloat(withdrawal.amount).toFixed(2);
                
                const modal = new bootstrap.Modal(document.getElementById('processingModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load withdrawal data', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading withdrawal data', 'error');
        });
}

/**
 * Mark withdrawal as completed
 */
function markAsCompleted(withdrawalId) {
    // Load withdrawal data first
    fetch(`api/details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const withdrawal = data.withdrawal;
                document.getElementById('completedWithdrawalId').value = withdrawalId;
                document.getElementById('completedWithdrawalAmount').value = '$' + parseFloat(withdrawal.amount).toFixed(2);
                
                const modal = new bootstrap.Modal(document.getElementById('completedModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load withdrawal data', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading withdrawal data', 'error');
        });
}

/**
 * Initialize form submissions
 */
function initializeFormSubmissions() {
    // Approve withdrawal form
    const approveForm = document.getElementById('approveWithdrawalForm');
    if (approveForm) {
        approveForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitWithdrawalAction('approve', new FormData(this));
        });
    }
    
    // Reject withdrawal form
    const rejectForm = document.getElementById('rejectWithdrawalForm');
    if (rejectForm) {
        rejectForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitWithdrawalAction('reject', new FormData(this));
        });
    }
    
    // Processing form
    const processingForm = document.getElementById('processingForm');
    if (processingForm) {
        processingForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitWithdrawalAction('processing', new FormData(this));
        });
    }
    
    // Completed form
    const completedForm = document.getElementById('completedForm');
    if (completedForm) {
        completedForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitWithdrawalAction('completed', new FormData(this));
        });
    }
    
    // Bulk actions form
    const bulkForm = document.getElementById('bulkActionsForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBulkAction(new FormData(this));
        });
    }
}

/**
 * Initialize bulk actions functionality
 */
function initializeBulkActions() {
    const actionSelect = document.querySelector('#bulkActionsModal select[name="action"]');
    if (actionSelect) {
        actionSelect.addEventListener('change', function() {
            const reasonField = document.getElementById('bulkReasonField');
            const noteField = document.getElementById('bulkNoteField');
            
            if (this.value === 'reject') {
                reasonField.style.display = 'block';
                noteField.style.display = 'none';
            } else if (this.value === 'approve' || this.value === 'processing' || this.value === 'completed') {
                reasonField.style.display = 'none';
                noteField.style.display = 'block';
            } else {
                reasonField.style.display = 'none';
                noteField.style.display = 'none';
            }
        });
    }
}

/**
 * Submit withdrawal action
 */
function submitWithdrawalAction(action, formData) {
    const submitButton = document.querySelector(`#${action}WithdrawalForm button[type="submit"], #${action}Form button[type="submit"]`);
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    fetch(`api/${action}.php`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // Close modal
            const modalElement = submitButton.closest('.modal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            modal.hide();
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Action failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while processing the request', 'error');
    })
    .finally(() => {
        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
}

/**
 * Submit bulk action
 */
function submitBulkAction(formData) {
    if (selectedWithdrawals.length === 0) {
        showAlert('No withdrawals selected', 'warning');
        return;
    }
    
    // Add selected withdrawals to form data
    selectedWithdrawals.forEach(id => {
        formData.append('withdrawal_ids[]', id);
    });
    
    const submitButton = document.querySelector('#bulkActionsForm button[type="submit"]');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    fetch('api/bulk-action.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
            modal.hide();
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Bulk action failed', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while processing the bulk action', 'error');
    })
    .finally(() => {
        // Restore button state
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}