<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../classes/config/ConfigManager.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('login.php');
}

$config = ConfigManager::getInstance();
$themeColors = $config->get('theme_colors', [
    'primary' => '#007bff',
    'secondary' => '#6c757d',
    'success' => '#28a745',
    'danger' => '#dc3545',
    'warning' => '#ffc107',
    'info' => '#17a2b8',
    'dark' => '#343a40',
    'light' => '#f8f9fa'
]);

$siteName = $config->get('site_name', 'Coinage Trading');
$siteLogo = $config->get('site_logo', getBaseUrl() . 'assets/images/logo.png');
$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - Super Admin - ' . $siteName : 'Super Admin - ' . $siteName; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: <?php echo $themeColors['primary']; ?>;
            --secondary-color: <?php echo $themeColors['secondary']; ?>;
            --success-color: <?php echo $themeColors['success']; ?>;
            --danger-color: <?php echo $themeColors['danger']; ?>;
            --warning-color: <?php echo $themeColors['warning']; ?>;
            --info-color: <?php echo $themeColors['info']; ?>;
            --dark-color: <?php echo $themeColors['dark']; ?>;
            --light-color: <?php echo $themeColors['light']; ?>;
        }
        
        .sidebar {
            min-height: 100vh;
            background: #ffffff;
            box-shadow: 2px 0 15px rgba(0,0,0,0.08);
            border-right: 1px solid #e9ecef;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            margin: 2px 8px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }
        
        .sidebar .nav-link:hover {
            color: var(--danger-color);
            background: rgba(220, 53, 69, 0.08);
            border-color: rgba(220, 53, 69, 0.2);
            transform: translateX(3px);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--danger-color), #c82333);
            border-color: var(--danger-color);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }
        
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .navbar-brand img {
            height: 40px;
            width: auto;
        }
        
        .superadmin-badge {
            background: linear-gradient(45deg, var(--danger-color), #dc3545);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--danger-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .system-status {
            position: relative;
        }
        
        .status-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
            animation: blink 2s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        .nav-item {
            position: relative;
        }
        
        .danger-zone {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
            border: 1px solid rgba(220, 53, 69, 0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1050;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1040;
                display: none;
            }
            
            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>
    
    <?php if (isset($additionalCSS)): ?>
        <?php echo $additionalCSS; ?>
    <?php endif; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <!-- Logo -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="img-fluid" style="max-height: 60px;">
                        <h5 class="text-dark mt-2 mb-1"><?php echo $siteName; ?></h5>
                        <div class="superadmin-badge">SUPER ADMIN</div>
                    </div>
                    
                    <!-- User Info -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <div class="user-avatar mx-auto mb-2 system-status">
                            <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                            <div class="status-indicator"></div>
                        </div>
                        <small class="text-muted">Super Admin:</small>
                        <div class="text-dark fw-bold"><?php echo htmlspecialchars($user['first_name']); ?></div>
                    </div>
                    
                    <!-- Navigation -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'dashboard.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/dashboard/">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/settings/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/settings/">
                                <i class="fas fa-cogs me-2"></i>
                                System Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/appearance/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/appearance/">
                                <i class="fas fa-palette me-2"></i>
                                Appearance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/admins/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/admins/">
                                <i class="fas fa-user-shield me-2"></i>
                                Admin Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/email-templates/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/email-templates/">
                                <i class="fas fa-envelope-open-text me-2"></i>
                                Email Templates
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/audit/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/audit/">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Audit Logs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/backup/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/backup/">
                                <i class="fas fa-database me-2"></i>
                                Database Backup
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/maintenance/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/maintenance/">
                                <i class="fas fa-tools me-2"></i>
                                Maintenance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['PHP_SELF'], '/security/') !== false) ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/security/">
                                <i class="fas fa-shield-alt me-2"></i>
                                Security Center
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="<?php echo url('logout.php'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top navbar for mobile -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom d-md-none">
                    <button class="btn btn-outline-primary" type="button" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="h4 mb-0"><?php echo isset($pageTitle) ? $pageTitle : 'Super Admin'; ?></h1>
                    <div class="user-avatar system-status">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                        <div class="status-indicator"></div>
                    </div>
                </div>
                
                <!-- Page content -->
                <div class="content-wrapper">
                    <?php if (isset($content)): ?>
                        <?php echo $content; ?>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                });
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                });
            }
            
            // Load system status
            loadSystemStatus();
        });
        
        // Load system status
        function loadSystemStatus() {
            fetch('<?php echo getBaseUrl(); ?>superadmin/api/system-status.php')
                .then(response => response.json())
                .then(data => {
                    const indicators = document.querySelectorAll('.status-indicator');
                    indicators.forEach(indicator => {
                        if (data.status === 'healthy') {
                            indicator.style.background = 'var(--success-color)';
                        } else if (data.status === 'warning') {
                            indicator.style.background = 'var(--warning-color)';
                        } else {
                            indicator.style.background = 'var(--danger-color)';
                        }
                    });
                })
                .catch(error => {
                    console.error('Error loading system status:', error);
                    const indicators = document.querySelectorAll('.status-indicator');
                    indicators.forEach(indicator => {
                        indicator.style.background = 'var(--warning-color)';
                    });
                });
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Refresh system status every 60 seconds
        setInterval(loadSystemStatus, 60000);
        
        // Confirmation for dangerous actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-danger') || e.target.closest('.btn-danger')) {
                const action = e.target.textContent || e.target.closest('.btn-danger').textContent;
                if (!confirm(`Are you sure you want to ${action.toLowerCase()}? This action cannot be undone.`)) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>