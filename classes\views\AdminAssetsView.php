<?php
require_once __DIR__ . '/BaseView.php';

/**
 * Admin Assets View - Handles trading assets management interface
 */
class AdminAssetsView extends BaseView {
    
    protected function getTitle() {
        return 'Trading Assets Management - Admin Panel';
    }
    
    protected function getAdditionalHead() {
        return '
        <link href="../../assets/css/admin-assets.css" rel="stylesheet">
        ';
    }
    
    protected function getBodyContent($data) {
        $assets = $data['assets'] ?? [];
        $statistics = $data['statistics'] ?? [];
        $categoryBreakdown = $data['categoryBreakdown'] ?? [];
        $error = $data['error'] ?? null;
        
        ob_start();
        ?>
        
        <div class="admin-content">
            <!-- Header -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Trading Assets Management</h1>
                        <p class="text-muted">Manage available trading assets and instruments</p>
                    </div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAssetModal">
                        <i class="fas fa-plus"></i> Add Asset
                    </button>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <?php if (!empty($statistics)): ?>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['total_assets'] ?? 0) ?></h3>
                            <p>Total Assets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['active_assets'] ?? 0) ?></h3>
                            <p>Active Assets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['inactive_assets'] ?? 0) ?></h3>
                            <p>Inactive Assets</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['categories_count'] ?? 0) ?></h3>
                            <p>Categories</p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Category Breakdown -->
            <?php if (!empty($categoryBreakdown)): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Assets by Category</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($categoryBreakdown as $category): ?>
                                <div class="col-md-2 col-sm-4 col-6 mb-3">
                                    <div class="category-card">
                                        <div class="category-icon">
                                            <i class="fas fa-<?= $this->getCategoryIcon($category['category']) ?>"></i>
                                        </div>
                                        <h6><?= ucfirst($category['category']) ?></h6>
                                        <p class="mb-0">
                                            <span class="text-success"><?= $category['active_count'] ?></span> / 
                                            <?= $category['count'] ?> assets
                                        </p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Assets Table -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Trading Assets</h5>
                        <div class="d-flex gap-2">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="all">All Assets</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="active">Active Only</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="inactive">Inactive Only</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="crypto">Crypto</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="forex">Forex</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="stocks">Stocks</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="commodities">Commodities</a></li>
                                    <li><a class="dropdown-item filter-option" href="#" data-filter="indices">Indices</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($assets)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <h5>No Trading Assets Found</h5>
                        <p class="text-muted">Add trading assets to enable trading options.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAssetModal">
                            <i class="fas fa-plus"></i> Add Asset
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="assetsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Symbol</th>
                                    <th>Name</th>
                                    <th>Category</th>
                                    <th>Current Price</th>
                                    <th>24h Change</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assets as $asset): ?>
                                <tr data-status="<?= $asset['status'] ?>" data-category="<?= $asset['category'] ?>">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="asset-icon me-2">
                                                <i class="fas fa-<?= $this->getCategoryIcon($asset['category']) ?>"></i>
                                            </div>
                                            <span class="fw-bold"><?= htmlspecialchars($asset['symbol']) ?></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($asset['name']) ?></h6>
                                            <?php if (!empty($asset['description'])): ?>
                                            <small class="text-muted"><?= htmlspecialchars(substr($asset['description'], 0, 40)) ?>...</small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $this->getCategoryColor($asset['category']) ?>">
                                            <?= ucfirst($asset['category']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($asset['current_price']): ?>
                                        $<?= number_format($asset['current_price'], 4) ?>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($asset['price_change_24h'] !== null): ?>
                                        <span class="text-<?= $asset['price_change_24h'] >= 0 ? 'success' : 'danger' ?>">
                                            <?= $asset['price_change_24h'] >= 0 ? '+' : '' ?><?= number_format($asset['price_change_24h'], 2) ?>%
                                        </span>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $asset['status'] === 'active' ? 'success' : 'secondary' ?>">
                                            <?= ucfirst($asset['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?= date('M j, Y', strtotime($asset['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary edit-asset-btn" 
                                                    data-asset-id="<?= $asset['id'] ?>" title="Edit Asset">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-<?= $asset['status'] === 'active' ? 'warning' : 'success' ?> toggle-status-btn"
                                                    data-asset-id="<?= $asset['id'] ?>" 
                                                    data-current-status="<?= $asset['status'] ?>"
                                                    title="<?= $asset['status'] === 'active' ? 'Deactivate' : 'Activate' ?> Asset">
                                                <i class="fas fa-<?= $asset['status'] === 'active' ? 'pause' : 'play' ?>"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger delete-asset-btn" 
                                                    data-asset-id="<?= $asset['id'] ?>" 
                                                    data-asset-symbol="<?= htmlspecialchars($asset['symbol']) ?>"
                                                    title="Delete Asset">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Create/Edit Asset Modal -->
        <div class="modal fade" id="createAssetModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add New Asset</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="assetForm">
                        <div class="modal-body">
                            <input type="hidden" id="assetId" name="asset_id">
                            
                            <div class="mb-3">
                                <label for="symbol" class="form-label">Symbol *</label>
                                <input type="text" class="form-control" id="symbol" name="symbol" 
                                       placeholder="e.g., BTC/USD" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Name *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       placeholder="e.g., Bitcoin" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category *</label>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="">Select Category</option>
                                            <option value="crypto">Cryptocurrency</option>
                                            <option value="forex">Forex</option>
                                            <option value="stocks">Stocks</option>
                                            <option value="commodities">Commodities</option>
                                            <option value="indices">Indices</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currentPrice" class="form-label">Current Price ($)</label>
                                        <input type="number" class="form-control" id="currentPrice" name="current_price" 
                                               step="0.00000001" min="0">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="priceChange" class="form-label">24h Change (%)</label>
                                        <input type="number" class="form-control" id="priceChange" name="price_change_24h" 
                                               step="0.01">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                <span class="btn-text">Add Asset</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteAssetModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirm Delete</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to delete the asset "<strong id="deleteAssetSymbol"></strong>"?</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            This action cannot be undone.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                            Delete Asset
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <?php
        return ob_get_clean();
    }
    
    private function getCategoryIcon($category) {
        $icons = [
            'crypto' => 'bitcoin',
            'forex' => 'exchange-alt',
            'stocks' => 'chart-line',
            'commodities' => 'gem',
            'indices' => 'chart-bar'
        ];
        
        return $icons[$category] ?? 'chart-line';
    }
    
    private function getCategoryColor($category) {
        $colors = [
            'crypto' => 'warning',
            'forex' => 'info',
            'stocks' => 'success',
            'commodities' => 'primary',
            'indices' => 'secondary'
        ];
        
        return $colors[$category] ?? 'secondary';
    }
    
    protected function getAdditionalScripts() {
        return '
        <script src="../../assets/js/admin-assets.js"></script>
        ';
    }
    
    protected function getLayout() {
        return 'admin';
    }
}
?>