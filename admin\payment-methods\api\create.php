<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/PaymentMethod.php';
require_once '../../../classes/services/CSRFProtection.php';

header('Content-Type: application/json');

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Verify CSRF token
    if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    // Get form data
    $name = trim($_POST['name'] ?? '');
    $type = trim($_POST['type'] ?? '');
    $status = trim($_POST['status'] ?? 'active');
    $sortOrder = !empty($_POST['sort_order']) ? (int)$_POST['sort_order'] : null;
    $details = $_POST['details'] ?? [];
    
    // Create payment method instance
    $paymentMethod = new PaymentMethod();
    $paymentMethod->name = $name;
    $paymentMethod->type = $type;
    $paymentMethod->status = $status;
    $paymentMethod->details = $details;
    
    if ($sortOrder !== null) {
        $paymentMethod->sort_order = $sortOrder;
    }
    
    // Validate the payment method
    $errors = $paymentMethod->validate();
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Save the payment method
    if ($paymentMethod->save()) {
        echo json_encode([
            'success' => true,
            'message' => 'Payment method created successfully',
            'data' => [
                'id' => $paymentMethod->getId(),
                'name' => $paymentMethod->name,
                'type' => $paymentMethod->type,
                'status' => $paymentMethod->status
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create payment method'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Payment method creation error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while creating the payment method'
    ]);
}
?>