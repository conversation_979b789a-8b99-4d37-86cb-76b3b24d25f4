<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/Deposit.php';
require_once __DIR__ . '/../../classes/models/TradingPlan.php';
require_once __DIR__ . '/../../classes/models/PaymentMethod.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';
require_once __DIR__ . '/../../classes/validators/FinancialValidator.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to continue']);
    exit();
}

// Ensure user role is 'user'
if (!hasRole('user')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Verify CSRF token
    if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
        throw new Exception('Invalid security token. Please refresh the page and try again.');
    }
    
    // Get current user
    $user = getCurrentUser();
    if (!$user) {
        throw new Exception('User session expired. Please log in again.');
    }
    
    // Validate input data
    $validator = new FinancialValidator();
    $validationResult = $validator->validateDeposit($_POST, $_FILES);
    
    if (!$validationResult['valid']) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed: ' . implode(', ', $validationResult['errors'])
        ]);
        exit();
    }
    
    $data = $validationResult['data'];
    
    // Verify trading plan exists and is active
    $tradingPlan = TradingPlan::find($data['plan_id']);
    if (!$tradingPlan || !$tradingPlan->isActive()) {
        throw new Exception('Selected trading plan is not available');
    }
    
    // Verify amount is within plan limits
    if (!$tradingPlan->isAmountValid($data['amount'])) {
        throw new Exception('Deposit amount is outside the allowed range for this plan');
    }
    
    // Verify payment method exists and is active
    $paymentMethod = PaymentMethod::find($data['payment_method_id']);
    if (!$paymentMethod || !$paymentMethod->isActive()) {
        throw new Exception('Selected payment method is not available');
    }
    
    // Handle file upload if provided
    $proofOfPaymentPath = null;
    if (isset($_FILES['proof_of_payment']) && $_FILES['proof_of_payment']['error'] === UPLOAD_ERR_OK) {
        $proofOfPaymentPath = handleProofOfPaymentUpload($_FILES['proof_of_payment'], $user->getId());
        if (!$proofOfPaymentPath) {
            throw new Exception('Failed to upload proof of payment. Please try again.');
        }
    }
    
    // Create deposit record
    $deposit = new Deposit();
    $deposit->user_id = $user->getId();
    $deposit->plan_id = $data['plan_id'];
    $deposit->amount = $data['amount'];
    $deposit->payment_method_id = $data['payment_method_id'];
    $deposit->transaction_id = $data['transaction_id'] ?? null;
    $deposit->proof_of_payment = $proofOfPaymentPath;
    $deposit->status = Deposit::STATUS_PENDING;
    
    // Calculate bonus amount
    $deposit->bonus_amount = $deposit->calculateBonus();
    
    if (!$deposit->save()) {
        throw new Exception('Failed to create deposit record. Please try again.');
    }
    
    // Send notification email to user (optional)
    try {
        sendDepositSubmissionEmail($user, $deposit, $tradingPlan);
    } catch (Exception $e) {
        // Log email error but don't fail the deposit
        error_log("Deposit notification email failed: " . $e->getMessage());
    }
    
    // Send notification to admins (optional)
    try {
        notifyAdminsOfNewDeposit($deposit, $user, $tradingPlan);
    } catch (Exception $e) {
        // Log email error but don't fail the deposit
        error_log("Admin notification email failed: " . $e->getMessage());
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Your deposit has been submitted successfully! You will receive a confirmation email shortly. Your deposit will be processed within 24 hours.',
        'deposit_id' => $deposit->getId(),
        'redirect' => '/user/transactions/?tab=deposits'
    ]);
    
} catch (Exception $e) {
    error_log("Deposit processing error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle proof of payment file upload
 */
function handleProofOfPaymentUpload($file, $userId) {
    // Define upload directory
    $uploadDir = __DIR__ . '/../../assets/uploads/deposits/';
    
    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return false;
        }
    }
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Invalid file type. Please upload an image or PDF file.');
    }
    
    // Validate file size (5MB max)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File size too large. Maximum size is 5MB.');
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'deposit_' . $userId . '_' . time() . '_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        return false;
    }
    
    // Return relative path for database storage
    return 'assets/uploads/deposits/' . $filename;
}

/**
 * Send deposit submission email to user
 */
function sendDepositSubmissionEmail($user, $deposit, $tradingPlan) {
    require_once __DIR__ . '/../../includes/email_functions.php';
    
    $subject = 'Deposit Submission Confirmation - Coinage Trading';
    
    $message = "
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;'>
            <h1 style='margin: 0; font-size: 28px;'>Deposit Submission Confirmed</h1>
        </div>
        
        <div style='padding: 30px; background: #f8f9fa;'>
            <p style='font-size: 16px; color: #333;'>Dear {$user->first_name} {$user->last_name},</p>
            
            <p style='font-size: 16px; color: #333;'>We have received your deposit submission and it is currently being processed by our team.</p>
            
            <div style='background: white; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #667eea;'>
                <h3 style='color: #495057; margin-top: 0;'>Deposit Details</h3>
                <table style='width: 100%; border-collapse: collapse;'>
                    <tr>
                        <td style='padding: 8px 0; color: #6c757d; font-weight: 600;'>Amount:</td>
                        <td style='padding: 8px 0; color: #28a745; font-weight: bold; font-size: 18px;'>$" . number_format($deposit->amount, 2) . "</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6c757d; font-weight: 600;'>Trading Plan:</td>
                        <td style='padding: 8px 0; color: #495057; font-weight: 600;'>{$tradingPlan->name}</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6c757d; font-weight: 600;'>Expected Bonus:</td>
                        <td style='padding: 8px 0; color: #17a2b8; font-weight: bold;'>$" . number_format($deposit->bonus_amount, 2) . "</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6c757d; font-weight: 600;'>Submission Date:</td>
                        <td style='padding: 8px 0; color: #495057;'>" . date('F j, Y g:i A', strtotime($deposit->created_at)) . "</td>
                    </tr>
                    <tr>
                        <td style='padding: 8px 0; color: #6c757d; font-weight: 600;'>Reference ID:</td>
                        <td style='padding: 8px 0; color: #495057; font-family: monospace; font-weight: bold;'>DEP-" . str_pad($deposit->getId(), 6, '0', STR_PAD_LEFT) . "</td>
                    </tr>
                </table>
            </div>
            
            <div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0;'>
                <strong>⏱️ Processing Time:</strong> Your deposit will be processed within 24 hours. You will receive another email once your deposit has been approved and credited to your account.
            </div>
            
            <p style='font-size: 16px; color: #333;'>You can track the status of your deposit by logging into your account and visiting the Deposits section.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . getBaseUrl() . "/user/deposit/status.php' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; font-weight: 600; display: inline-block;'>Track Deposit Status</a>
            </div>
            
            <p style='font-size: 14px; color: #6c757d; text-align: center;'>If you have any questions, please contact our support team.</p>
            
            <p style='font-size: 16px; color: #333; text-align: center;'>Thank you for choosing <strong>Coinage Trading</strong>!</p>
        </div>
        
        <div style='background: #343a40; color: #adb5bd; padding: 20px; text-align: center; font-size: 12px;'>
            <p style='margin: 0;'>© 2024 Coinage Trading. All rights reserved.</p>
            <p style='margin: 5px 0 0 0;'>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
    ";
    
    return sendEmail($user->email, $subject, $message);
}

/**
 * Notify admins of new deposit
 */
function notifyAdminsOfNewDeposit($deposit, $user, $tradingPlan) {
    require_once __DIR__ . '/../../includes/email_functions.php';
    require_once __DIR__ . '/../../classes/models/User.php';
    
    // Get admin users
    $admins = User::where('role', 'admin');
    $superAdmins = User::where('role', 'super_admin');
    $allAdmins = array_merge($admins, $superAdmins);
    
    if (empty($allAdmins)) {
        return;
    }
    
    $subject = 'New Deposit Submission - Coinage Trading';
    
    $message = "
    <h2>New Deposit Submission</h2>
    <p>A new deposit has been submitted and requires review.</p>
    
    <h3>User Information:</h3>
    <ul>
        <li><strong>Name:</strong> {$user->first_name} {$user->last_name}</li>
        <li><strong>Email:</strong> {$user->email}</li>
        <li><strong>Username:</strong> {$user->username}</li>
    </ul>
    
    <h3>Deposit Details:</h3>
    <ul>
        <li><strong>Amount:</strong> $" . number_format($deposit->amount, 2) . "</li>
        <li><strong>Trading Plan:</strong> {$tradingPlan->name}</li>
        <li><strong>Expected Bonus:</strong> $" . number_format($deposit->bonus_amount, 2) . "</li>
        <li><strong>Transaction ID:</strong> " . ($deposit->transaction_id ?: 'Not provided') . "</li>
        <li><strong>Submission Date:</strong> " . date('F j, Y g:i A', strtotime($deposit->created_at)) . "</li>
        <li><strong>Reference ID:</strong> DEP-" . str_pad($deposit->getId(), 6, '0', STR_PAD_LEFT) . "</li>
    </ul>
    
    <p><a href='" . getBaseUrl() . "/admin/deposits/' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Review Deposit</a></p>
    ";
    
    // Send to all admins
    foreach ($allAdmins as $admin) {
        sendEmail($admin->email, $subject, $message);
    }
}

/**
 * Get base URL for email links
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    return $protocol . '://' . $host;
}
?>