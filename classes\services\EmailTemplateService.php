<?php

require_once __DIR__ . '/../models/EmailTemplate.php';
require_once __DIR__ . '/../models/SystemSetting.php';
require_once __DIR__ . '/../../includes/email_functions.php';

class EmailTemplateService {
    
    /**
     * Send email using template
     */
    public static function sendTemplateEmail($templateType, $recipientEmail, $recipientName, $data = []) {
        try {
            // Get the template
            $template = EmailTemplate::getByType($templateType);
            if (!$template) {
                throw new Exception("Email template not found: {$templateType}");
            }
            
            // Prepare template data with system settings
            $templateData = self::prepareTemplateData($data);
            
            // Replace placeholders in subject and body
            $subject = $template->replacePlaceholders($template['subject'], $templateData);
            $bodyHtml = $template->replacePlaceholders($template['body_html'], $templateData);
            $bodyText = $template->replacePlaceholders($template['body_text'], $templateData);
            
            // Send email
            return self::sendEmail($recipientEmail, $recipientName, $subject, $bodyHtml, $bodyText);
            
        } catch (Exception $e) {
            error_log("EmailTemplateService Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send welcome email
     */
    public static function sendWelcomeEmail($user) {
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{first_name}}' => $user['first_name'],
            '{{email}}' => $user['email'],
            '{{registration_bonus}}' => SystemSetting::get('registration_bonus', '0.00')
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_WELCOME,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send deposit confirmation email
     */
    public static function sendDepositConfirmation($user, $deposit) {
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{amount}}' => number_format($deposit['amount'], 2),
            '{{payment_method}}' => $deposit['payment_method'] ?? 'N/A',
            '{{transaction_id}}' => $deposit['transaction_id'] ?? $deposit['id'],
            '{{date}}' => date('F j, Y g:i A', strtotime($deposit['created_at']))
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_DEPOSIT_CONFIRMATION,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send withdrawal notification email
     */
    public static function sendWithdrawalNotification($user, $withdrawal) {
        $statusText = ucfirst(strtolower($withdrawal['status']));
        
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{amount}}' => number_format($withdrawal['amount'], 2),
            '{{status}}' => $statusText,
            '{{transaction_id}}' => $withdrawal['transaction_id'] ?? $withdrawal['id'],
            '{{date}}' => date('F j, Y g:i A', strtotime($withdrawal['created_at']))
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_WITHDRAWAL_NOTIFICATION,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send password reset email
     */
    public static function sendPasswordReset($user, $resetToken) {
        $baseUrl = SystemSetting::get('base_url', '');
        $resetLink = $baseUrl . '/reset-password.php?token=' . $resetToken;
        
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{reset_link}}' => $resetLink,
            '{{expiry_time}}' => '24 hours'
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_PASSWORD_RESET,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send email verification email
     */
    public static function sendEmailVerification($user, $verificationToken) {
        $baseUrl = SystemSetting::get('base_url', '');
        $verificationLink = $baseUrl . '/verify-email.php?token=' . $verificationToken;
        
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{verification_link}}' => $verificationLink
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_EMAIL_VERIFICATION,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send balance update notification
     */
    public static function sendBalanceUpdate($user, $amount, $newBalance, $updateType, $reason) {
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{amount}}' => number_format(abs($amount), 2),
            '{{new_balance}}' => number_format($newBalance, 2),
            '{{update_type}}' => $updateType,
            '{{reason}}' => $reason
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_BALANCE_UPDATE,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Send plan assignment notification
     */
    public static function sendPlanAssignment($user, $plan, $amount) {
        $data = [
            '{{user_name}}' => $user['first_name'] . ' ' . $user['last_name'],
            '{{plan_name}}' => $plan['name'],
            '{{plan_amount}}' => number_format($amount, 2),
            '{{expected_return}}' => $plan['return_percentage'] . '%',
            '{{duration}}' => $plan['duration_days'] . ' days'
        ];
        
        return self::sendTemplateEmail(
            EmailTemplate::TYPE_PLAN_ASSIGNMENT,
            $user['email'],
            $user['first_name'] . ' ' . $user['last_name'],
            $data
        );
    }
    
    /**
     * Prepare template data with system settings
     */
    private static function prepareTemplateData($data = []) {
        $systemData = [
            '{{site_name}}' => SystemSetting::get('site_name', 'Coinage Trading'),
            '{{currency_symbol}}' => SystemSetting::get('currency_symbol', '$'),
            '{{base_url}}' => SystemSetting::get('base_url', ''),
            '{{login_url}}' => SystemSetting::get('base_url', '') . '/login.php',
            '{{contact_email}}' => SystemSetting::get('contact_email', ''),
            '{{date}}' => date('F j, Y')
        ];
        
        return array_merge($systemData, $data);
    }
    
    /**
     * Send email using the email functions
     */
    private static function sendEmail($recipientEmail, $recipientName, $subject, $bodyHtml, $bodyText) {
        // Use existing email function
        if (function_exists('sendEmail')) {
            return sendEmail($recipientEmail, $subject, $bodyHtml, $recipientName);
        }
        
        // Fallback to basic mail function
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . SystemSetting::get('site_name', 'Coinage Trading') . ' <' . SystemSetting::get('contact_email', '<EMAIL>') . '>',
            'Reply-To: ' . SystemSetting::get('contact_email', '<EMAIL>'),
            'X-Mailer: PHP/' . phpversion()
        ];
        
        return mail($recipientEmail, $subject, $bodyHtml, implode("\r\n", $headers));
    }
    
    /**
     * Test email template
     */
    public static function testTemplate($templateType, $testEmail) {
        try {
            $template = EmailTemplate::getByType($templateType);
            if (!$template) {
                throw new Exception("Template not found: {$templateType}");
            }
            
            // Get sample data for testing
            $sampleData = self::getSampleDataForType($templateType);
            $templateData = self::prepareTemplateData($sampleData);
            
            // Replace placeholders
            $subject = $template->replacePlaceholders($template['subject'], $templateData);
            $bodyHtml = $template->replacePlaceholders($template['body_html'], $templateData);
            $bodyText = $template->replacePlaceholders($template['body_text'], $templateData);
            
            // Send test email
            return self::sendEmail($testEmail, 'Test User', '[TEST] ' . $subject, $bodyHtml, $bodyText);
            
        } catch (Exception $e) {
            error_log("EmailTemplateService Test Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get sample data for testing
     */
    private static function getSampleDataForType($templateType) {
        switch ($templateType) {
            case EmailTemplate::TYPE_WELCOME:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{first_name}}' => 'John',
                    '{{email}}' => '<EMAIL>',
                    '{{registration_bonus}}' => '50.00'
                ];
                
            case EmailTemplate::TYPE_DEPOSIT_CONFIRMATION:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{amount}}' => '500.00',
                    '{{payment_method}}' => 'Bitcoin',
                    '{{transaction_id}}' => 'TXN123456789'
                ];
                
            case EmailTemplate::TYPE_WITHDRAWAL_NOTIFICATION:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{amount}}' => '250.00',
                    '{{status}}' => 'Approved',
                    '{{transaction_id}}' => 'WTH987654321'
                ];
                
            case EmailTemplate::TYPE_PASSWORD_RESET:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{reset_link}}' => 'https://example.com/reset-password.php?token=abc123',
                    '{{expiry_time}}' => '24 hours'
                ];
                
            case EmailTemplate::TYPE_EMAIL_VERIFICATION:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{verification_link}}' => 'https://example.com/verify-email.php?token=xyz789'
                ];
                
            case EmailTemplate::TYPE_BALANCE_UPDATE:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{amount}}' => '100.00',
                    '{{new_balance}}' => '1,250.00',
                    '{{update_type}}' => 'Credit',
                    '{{reason}}' => 'Admin adjustment'
                ];
                
            case EmailTemplate::TYPE_PLAN_ASSIGNMENT:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{plan_name}}' => 'Premium Trading Plan',
                    '{{plan_amount}}' => '1,000.00',
                    '{{expected_return}}' => '15%',
                    '{{duration}}' => '30 days'
                ];
                
            default:
                return [
                    '{{user_name}}' => 'John Doe',
                    '{{first_name}}' => 'John',
                    '{{email}}' => '<EMAIL>'
                ];
        }
    }
}