<?php
require_once __DIR__ . '/BaseView.php';

/**
 * Deposit Status View - Displays user's deposit history and status tracking
 */
class DepositStatusView extends BaseView {
    private $deposits;
    private $stats;
    private $currentPage;
    private $totalPages;
    private $user;
    
    public function __construct($deposits, $stats, $currentPage, $totalPages, $user) {
        parent::__construct();
        $this->deposits = $deposits;
        $this->stats = $stats;
        $this->currentPage = $currentPage;
        $this->totalPages = $totalPages;
        $this->user = $user;
        $this->pageTitle = 'Deposit Status - Coinage Trading';
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/deposit-status.css">
        <?php
    }
    
    protected function renderBody() {
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row">
                <div class="col-12">
                    <div class="page-header mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-0">Deposit Status</h1>
                                <p class="text-muted">Track your deposit history and status</p>
                            </div>
                            <div>
                                <a href="/user/plans/" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> New Deposit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $this->stats['total_deposits']; ?></h3>
                            <p>Total Deposits</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $this->stats['pending_deposits']; ?></h3>
                            <p>Pending</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $this->stats['approved_deposits']; ?></h3>
                            <p>Approved</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="stat-icon bg-danger">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $this->stats['rejected_deposits']; ?></h3>
                            <p>Rejected</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Amount Summary -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="amount-summary-card">
                        <div class="amount-header">
                            <i class="fas fa-dollar-sign text-success"></i>
                            <h5>Total Approved Amount</h5>
                        </div>
                        <div class="amount-value text-success">
                            $<?php echo number_format($this->stats['total_amount'], 2); ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="amount-summary-card">
                        <div class="amount-header">
                            <i class="fas fa-hourglass-half text-warning"></i>
                            <h5>Pending Amount</h5>
                        </div>
                        <div class="amount-value text-warning">
                            $<?php echo number_format($this->stats['pending_amount'], 2); ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Deposits Table -->
            <div class="row">
                <div class="col-12">
                    <div class="deposits-table-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history"></i> Deposit History
                            </h5>
                        </div>
                        
                        <div class="card-body">
                            <?php if (empty($this->deposits)): ?>
                                <div class="empty-state">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h4>No Deposits Found</h4>
                                    <p class="text-muted">You haven't made any deposits yet.</p>
                                    <a href="/user/plans/" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Make Your First Deposit
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Reference</th>
                                                <th>Date</th>
                                                <th>Plan</th>
                                                <th>Amount</th>
                                                <th>Bonus</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($this->deposits as $deposit): ?>
                                                <tr>
                                                    <td>
                                                        <span class="reference-id">
                                                            DEP-<?php echo str_pad($deposit->getId(), 6, '0', STR_PAD_LEFT); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="deposit-date">
                                                            <?php echo date('M j, Y', strtotime($deposit->created_at)); ?>
                                                        </span>
                                                        <small class="text-muted d-block">
                                                            <?php echo date('g:i A', strtotime($deposit->created_at)); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <?php 
                                                        $plan = $deposit->getTradingPlan();
                                                        echo $plan ? htmlspecialchars($plan->name) : 'N/A';
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <span class="amount">
                                                            $<?php echo number_format($deposit->amount, 2); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="bonus-amount">
                                                            $<?php echo number_format($deposit->bonus_amount ?? 0, 2); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php echo $this->renderStatusBadge($deposit->status); ?>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                    onclick="viewDepositDetails(<?php echo $deposit->getId(); ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <?php if ($deposit->proof_of_payment): ?>
                                                                <a href="/<?php echo $deposit->proof_of_payment; ?>" 
                                                                   target="_blank" class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-file-image"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($this->totalPages > 1): ?>
                                    <div class="pagination-wrapper">
                                        <nav aria-label="Deposits pagination">
                                            <ul class="pagination justify-content-center">
                                                <?php if ($this->currentPage > 1): ?>
                                                    <li class="page-item">
                                                        <a class="page-link" href="?page=<?php echo $this->currentPage - 1; ?>">
                                                            <i class="fas fa-chevron-left"></i>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                
                                                <?php for ($i = 1; $i <= $this->totalPages; $i++): ?>
                                                    <li class="page-item <?php echo $i === $this->currentPage ? 'active' : ''; ?>">
                                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                                    </li>
                                                <?php endfor; ?>
                                                
                                                <?php if ($this->currentPage < $this->totalPages): ?>
                                                    <li class="page-item">
                                                        <a class="page-link" href="?page=<?php echo $this->currentPage + 1; ?>">
                                                            <i class="fas fa-chevron-right"></i>
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </nav>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deposit Details Modal -->
        <div class="modal fade" id="depositDetailsModal" tabindex="-1" aria-labelledby="depositDetailsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="depositDetailsModalLabel">
                            <i class="fas fa-info-circle"></i> Deposit Details
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" id="depositDetailsContent">
                        <!-- Content will be loaded via AJAX -->
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderStatusBadge($status) {
        $badges = [
            'pending' => '<span class="badge bg-warning"><i class="fas fa-clock"></i> Pending</span>',
            'approved' => '<span class="badge bg-success"><i class="fas fa-check"></i> Approved</span>',
            'rejected' => '<span class="badge bg-danger"><i class="fas fa-times"></i> Rejected</span>'
        ];
        
        return $badges[$status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/deposit-status.js"></script>
        <?php
    }
}
?>