<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/models/Deposit.php';
require_once '../../../classes/models/User.php';
require_once '../../../classes/models/Transaction.php';
require_once '../../../classes/services/TransactionManager.php';
require_once '../../../classes/services/NotificationService.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Verify CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit();
}

try {
    $depositId = (int)($_POST['deposit_id'] ?? 0);
    $reason = trim($_POST['reason'] ?? '');
    
    if ($depositId <= 0) {
        throw new Exception('Invalid deposit ID');
    }
    
    if (empty($reason)) {
        throw new Exception('Rejection reason is required');
    }
    
    // Get deposit details
    $deposit = Deposit::findById($depositId);
    if (!$deposit) {
        throw new Exception('Deposit not found');
    }
    
    if ($deposit->status !== 'pending') {
        throw new Exception('Deposit is not in pending status');
    }
    
    // Get user details
    $user = User::findById($deposit->user_id);
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Start database transaction
    $pdo = Database::getConnection();
    $pdo->beginTransaction();
    
    try {
        // Update deposit status
        $deposit->status = 'rejected';
        $deposit->rejected_at = date('Y-m-d H:i:s');
        $deposit->rejected_by = $_SESSION['user_id'];
        $deposit->rejection_reason = $reason;
        
        if (!$deposit->save()) {
            throw new Exception('Failed to update deposit status');
        }
        
        // Create transaction record for rejection
        $transactionManager = new TransactionManager();
        $rejectionTransaction = $transactionManager->createTransaction([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => $deposit->amount,
            'status' => 'rejected',
            'reference_id' => $deposit->id,
            'reference_type' => 'deposit',
            'description' => 'Deposit rejected - ' . $reason,
            'admin_id' => $_SESSION['user_id']
        ]);
        
        // Commit transaction
        $pdo->commit();
        
        // Send notification email
        try {
            $notificationService = new NotificationService();
            $notificationService->sendDepositRejectionNotification($user, $deposit, $reason);
        } catch (Exception $e) {
            error_log("Failed to send deposit rejection notification: " . $e->getMessage());
        }
        
        // Log admin action
        error_log("Admin {$_SESSION['user_id']} rejected deposit {$depositId} for user {$user->id}: {$reason}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit rejected successfully',
            'data' => [
                'deposit_id' => $deposit->id,
                'amount' => $deposit->amount,
                'reason' => $reason
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Deposit rejection error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>