<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../includes/db_connect.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    $diagnostics = [
        'Database' => [],
        'File System' => [],
        'PHP Configuration' => [],
        'Email System' => [],
        'Security' => []
    ];
    
    // Database diagnostics
    try {
        $pdo = getDBConnection();
        $diagnostics['Database']['Connection'] = [
            'status' => 'pass',
            'message' => 'Connected successfully'
        ];
        
        // Check critical tables
        $criticalTables = ['users', 'transactions', 'deposits', 'withdrawals', 'system_settings'];
        foreach ($criticalTables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                $diagnostics['Database'][$table . ' table'] = [
                    'status' => 'pass',
                    'message' => "$count records"
                ];
            } catch (Exception $e) {
                $diagnostics['Database'][$table . ' table'] = [
                    'status' => 'fail',
                    'message' => 'Table missing or inaccessible'
                ];
            }
        }
        
    } catch (Exception $e) {
        $diagnostics['Database']['Connection'] = [
            'status' => 'fail',
            'message' => 'Connection failed: ' . $e->getMessage()
        ];
    }
    
    // File system diagnostics
    $directories = [
        'assets/uploads' => __DIR__ . '/../../../assets/uploads/',
        'logs' => __DIR__ . '/../../../logs/',
        'database/backups' => __DIR__ . '/../../../database/backups/'
    ];
    
    foreach ($directories as $name => $path) {
        if (is_dir($path)) {
            if (is_writable($path)) {
                $diagnostics['File System'][$name . ' directory'] = [
                    'status' => 'pass',
                    'message' => 'Exists and writable'
                ];
            } else {
                $diagnostics['File System'][$name . ' directory'] = [
                    'status' => 'warning',
                    'message' => 'Exists but not writable'
                ];
            }
        } else {
            $diagnostics['File System'][$name . ' directory'] = [
                'status' => 'fail',
                'message' => 'Directory does not exist'
            ];
        }
    }
    
    // PHP configuration diagnostics
    $phpVersion = PHP_VERSION;
    $minVersion = '7.4.0';
    $diagnostics['PHP Configuration']['PHP Version'] = [
        'status' => version_compare($phpVersion, $minVersion, '>=') ? 'pass' : 'warning',
        'message' => $phpVersion . (version_compare($phpVersion, $minVersion, '>=') ? ' (OK)' : ' (Upgrade recommended)')
    ];
    
    $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json'];
    foreach ($requiredExtensions as $ext) {
        $diagnostics['PHP Configuration'][$ext . ' extension'] = [
            'status' => extension_loaded($ext) ? 'pass' : 'fail',
            'message' => extension_loaded($ext) ? 'Loaded' : 'Missing'
        ];
    }
    
    // Memory limit check
    $memoryLimit = ini_get('memory_limit');
    $memoryLimitBytes = return_bytes($memoryLimit);
    $recommendedBytes = 128 * 1024 * 1024; // 128MB
    
    $diagnostics['PHP Configuration']['Memory Limit'] = [
        'status' => $memoryLimitBytes >= $recommendedBytes ? 'pass' : 'warning',
        'message' => $memoryLimit . ($memoryLimitBytes >= $recommendedBytes ? ' (OK)' : ' (Increase recommended)')
    ];
    
    // Email system diagnostics
    if (defined('SMTP_HOST') && !empty(SMTP_HOST)) {
        $diagnostics['Email System']['SMTP Configuration'] = [
            'status' => 'pass',
            'message' => 'SMTP configured'
        ];
        
        // Test SMTP connection (basic check)
        try {
            $socket = @fsockopen(SMTP_HOST, SMTP_PORT, $errno, $errstr, 10);
            if ($socket) {
                fclose($socket);
                $diagnostics['Email System']['SMTP Connectivity'] = [
                    'status' => 'pass',
                    'message' => 'SMTP server reachable'
                ];
            } else {
                $diagnostics['Email System']['SMTP Connectivity'] = [
                    'status' => 'warning',
                    'message' => 'SMTP server unreachable'
                ];
            }
        } catch (Exception $e) {
            $diagnostics['Email System']['SMTP Connectivity'] = [
                'status' => 'warning',
                'message' => 'Could not test connectivity'
            ];
        }
    } else {
        $diagnostics['Email System']['SMTP Configuration'] = [
            'status' => 'warning',
            'message' => 'SMTP not configured'
        ];
    }
    
    // Security diagnostics
    $diagnostics['Security']['HTTPS'] = [
        'status' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'pass' : 'warning',
        'message' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'Enabled' : 'Not enabled'
    ];
    
    $diagnostics['Security']['Session Security'] = [
        'status' => (ini_get('session.cookie_secure') && ini_get('session.cookie_httponly')) ? 'pass' : 'warning',
        'message' => (ini_get('session.cookie_secure') && ini_get('session.cookie_httponly')) ? 'Secure' : 'Could be improved'
    ];
    
    $diagnostics['Security']['Error Display'] = [
        'status' => ini_get('display_errors') ? 'warning' : 'pass',
        'message' => ini_get('display_errors') ? 'Enabled (disable in production)' : 'Disabled'
    ];
    
    echo json_encode([
        'success' => true,
        'results' => $diagnostics
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Diagnostics failed: ' . $e->getMessage()
    ]);
}

/**
 * Convert PHP memory limit to bytes
 */
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}
?>