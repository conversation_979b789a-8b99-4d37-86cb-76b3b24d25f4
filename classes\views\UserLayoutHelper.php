<?php
require_once __DIR__ . '/../config/ConfigManager.php';
require_once __DIR__ . '/../../includes/functions.php';

/**
 * User Layout Helper
 * Handles user layout configuration and data preparation
 */
class UserLayoutHelper {
    private $config;
    private $user;
    private $themeColors;
    
    public function __construct() {
        $this->config = ConfigManager::getInstance();
        $this->loadThemeColors();
        $this->loadUserData();
    }
    
    /**
     * Load theme colors from configuration
     */
    private function loadThemeColors() {
        $this->themeColors = $this->config->get('theme_colors', [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'success' => '#28a745',
            'danger' => '#dc3545',
            'warning' => '#ffc107',
            'info' => '#17a2b8',
            'dark' => '#343a40',
            'light' => '#f8f9fa'
        ]);
    }
    
    /**
     * Load current user data
     */
    private function loadUserData() {
        if (!function_exists('getCurrentUser')) {
            throw new Exception('getCurrentUser function not available');
        }
        
        $this->user = getCurrentUser();
        if (!$this->user) {
            throw new Exception('User data not available');
        }
    }
    
    /**
     * Get site configuration
     */
    public function getSiteConfig() {
        return [
            'name' => $this->config->get('site_name', 'Coinage Trading'),
            'logo' => $this->config->get('site_logo', getBaseUrl() . 'assets/images/logo.png'),
            'favicon' => $this->config->get('site_favicon', getBaseUrl() . 'assets/images/favicon.ico')
        ];
    }
    
    /**
     * Get theme colors
     */
    public function getThemeColors() {
        return $this->themeColors;
    }
    
    /**
     * Get user data
     */
    public function getUser() {
        return $this->user;
    }
    
    /**
     * Get user avatar initials
     */
    public function getUserInitials() {
        $firstName = $this->user['first_name'] ?? '';
        $lastName = $this->user['last_name'] ?? '';
        
        return strtoupper(substr($firstName, 0, 1) . substr($lastName, 0, 1));
    }
    
    /**
     * Get navigation items
     */
    public function getNavigationItems() {
        $baseUrl = getBaseUrl();
        return [
            [
                'url' => $baseUrl . 'user/dashboard/',
                'icon' => 'fas fa-tachometer-alt',
                'label' => 'Dashboard',
                'active' => $this->isCurrentPage('dashboard')
            ],
            [
                'url' => $baseUrl . 'user/plans/',
                'icon' => 'fas fa-chart-line',
                'label' => 'Trading Plans',
                'active' => $this->isCurrentPage('plans')
            ],
            [
                'url' => $baseUrl . 'user/deposit/status.php',
                'icon' => 'fas fa-plus-circle',
                'label' => 'Deposits',
                'active' => $this->isCurrentPage('deposit')
            ],
            [
                'url' => $baseUrl . 'user/withdraw/',
                'icon' => 'fas fa-minus-circle',
                'label' => 'Withdraw',
                'active' => $this->isCurrentPage('withdraw')
            ],
            [
                'url' => $baseUrl . 'user/transactions/',
                'icon' => 'fas fa-history',
                'label' => 'Transactions',
                'active' => $this->isCurrentPage('transactions')
            ],
            [
                'url' => $baseUrl . 'user/profile/',
                'icon' => 'fas fa-user',
                'label' => 'Profile',
                'active' => $this->isCurrentPage('profile')
            ],
            [
                'url' => $baseUrl . 'user/settings/',
                'icon' => 'fas fa-cog',
                'label' => 'Settings',
                'active' => $this->isCurrentPage('settings')
            ],
            [
                'url' => $baseUrl . 'user/support/',
                'icon' => 'fas fa-headset',
                'label' => 'Support',
                'active' => $this->isCurrentPage('support')
            ]
        ];
    }
    
    /**
     * Check if current page matches given path
     */
    private function isCurrentPage($path) {
        $currentPath = $_SERVER['PHP_SELF'] ?? '';
        return strpos($currentPath, "/$path/") !== false || 
               basename($currentPath) === "$path.php";
    }
    
    /**
     * Generate CSS variables for theme colors
     */
    public function generateCSSVariables() {
        $css = '';
        foreach ($this->themeColors as $key => $value) {
            $cssKey = str_replace('_', '-', $key);
            $css .= "--{$cssKey}-color: {$value};\n            ";
        }
        return trim($css);
    }
    
    /**
     * Get page title with site name
     */
    public function getPageTitle($pageTitle = null) {
        $siteConfig = $this->getSiteConfig();
        return $pageTitle ? $pageTitle . ' - ' . $siteConfig['name'] : $siteConfig['name'];
    }
    
    /**
     * Validate user access
     */
    public function validateAccess() {
        if (!function_exists('isLoggedIn') || !isLoggedIn()) {
            throw new Exception('User not authenticated');
        }
        
        if (!function_exists('hasRole') || !hasRole('user')) {
            throw new Exception('Insufficient permissions');
        }
    }
    
    /**
     * Get user balance information
     */
    public function getUserBalance() {
        return [
            'balance' => $this->user['balance'] ?? 0,
            'bonus' => $this->user['bonus'] ?? 0,
            'total_deposit' => $this->user['total_deposit'] ?? 0,
            'total_withdrawal' => $this->user['total_withdrawal'] ?? 0
        ];
    }
}