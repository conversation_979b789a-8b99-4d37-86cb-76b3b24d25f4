<?php
require_once __DIR__ . '/../models/SupportTicket.php';
require_once __DIR__ . '/EmailNotificationService.php';
require_once __DIR__ . '/SecurityAuditService.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * Support Ticket Service
 * Comprehensive support ticket management with email notifications
 */
class SupportTicketService {
    private static $instance = null;
    private $emailService;
    private $auditService;
    
    private function __construct() {
        $this->emailService = EmailNotificationService::getInstance();
        $this->auditService = SecurityAuditService::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Create a new support ticket
     */
    public function createTicket($userId, $subject, $message, $category = 'general', $priority = 'medium') {
        try {
            // Validate input
            $errors = $this->validateTicketData($subject, $message, $category, $priority);
            if (!empty($errors)) {
                return ['success' => false, 'errors' => $errors];
            }
            
            // Check rate limiting (max 5 tickets per hour per user)
            if (!$this->checkRateLimit($userId)) {
                return ['success' => false, 'errors' => ['rate_limit' => 'Too many tickets created. Please wait before creating another ticket.']];
            }
            
            // Create ticket
            $ticketData = [
                'user_id' => $userId,
                'subject' => ValidationHelper::sanitize($subject),
                'message' => ValidationHelper::sanitize($message),
                'category' => $category,
                'priority' => $priority,
                'status' => SupportTicket::STATUS_PENDING
            ];
            
            $ticket = new SupportTicket();
            $ticket->fill($ticketData);
            $ticketId = $ticket->save();
            
            if ($ticketId) {
                // Log ticket creation
                $this->auditService->logSystemEvent('support_ticket_created', [
                    'ticket_id' => $ticketId,
                    'user_id' => $userId,
                    'category' => $category,
                    'priority' => $priority
                ]);
                
                // Send email notifications
                $this->emailService->sendSupportTicketNotification($ticketId, 'created');
                $this->emailService->sendAdminSupportNotification($ticketId);
                
                return [
                    'success' => true,
                    'ticket_id' => $ticketId,
                    'message' => 'Support ticket created successfully. You will receive email updates on its progress.'
                ];
            }
            
            return ['success' => false, 'errors' => ['general' => 'Failed to create support ticket']];
            
        } catch (Exception $e) {
            error_log("Support ticket creation error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'System error occurred']];
        }
    }
    
    /**
     * Reply to a support ticket (admin function)
     */
    public function replyToTicket($ticketId, $adminId, $reply) {
        try {
            // Validate input
            if (empty($reply) || strlen(trim($reply)) < 10) {
                return ['success' => false, 'errors' => ['reply' => 'Reply must be at least 10 characters long']];
            }
            
            // Get ticket
            $ticket = new SupportTicket();
            $ticketData = $ticket->find($ticketId);
            
            if (!$ticketData) {
                return ['success' => false, 'errors' => ['ticket' => 'Ticket not found']];
            }
            
            // Update ticket with reply
            $updateData = [
                'admin_reply' => ValidationHelper::sanitize($reply),
                'replied_by' => $adminId,
                'replied_at' => date('Y-m-d H:i:s'),
                'status' => SupportTicket::STATUS_ANSWERED
            ];
            
            if ($ticket->update($ticketId, $updateData)) {
                // Log ticket reply
                $this->auditService->logAdminEvent('support_ticket_replied', $adminId, $ticketData['user_id'], [
                    'ticket_id' => $ticketId,
                    'reply_length' => strlen($reply)
                ]);
                
                // Send email notification to user
                $this->emailService->sendSupportTicketNotification($ticketId, 'replied');
                
                return [
                    'success' => true,
                    'message' => 'Reply sent successfully. User has been notified via email.'
                ];
            }
            
            return ['success' => false, 'errors' => ['general' => 'Failed to send reply']];
            
        } catch (Exception $e) {
            error_log("Support ticket reply error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'System error occurred']];
        }
    }
    
    /**
     * Update ticket status
     */
    public function updateTicketStatus($ticketId, $adminId, $status, $reason = '') {
        try {
            // Validate status
            $validStatuses = [SupportTicket::STATUS_PENDING, SupportTicket::STATUS_ANSWERED, SupportTicket::STATUS_CLOSED];
            if (!in_array($status, $validStatuses)) {
                return ['success' => false, 'errors' => ['status' => 'Invalid status']];
            }
            
            // Get ticket
            $ticket = new SupportTicket();
            $ticketData = $ticket->find($ticketId);
            
            if (!$ticketData) {
                return ['success' => false, 'errors' => ['ticket' => 'Ticket not found']];
            }
            
            // Update status
            $updateData = ['status' => $status];
            
            // Add reason if provided
            if (!empty($reason) && $status === SupportTicket::STATUS_CLOSED) {
                $updateData['admin_reply'] = ValidationHelper::sanitize($reason);
                $updateData['replied_by'] = $adminId;
                $updateData['replied_at'] = date('Y-m-d H:i:s');
            }
            
            if ($ticket->update($ticketId, $updateData)) {
                // Log status change
                $this->auditService->logAdminEvent('support_ticket_status_changed', $adminId, $ticketData['user_id'], [
                    'ticket_id' => $ticketId,
                    'old_status' => $ticketData['status'],
                    'new_status' => $status,
                    'reason' => $reason
                ]);
                
                // Send notification if ticket was closed with a reason
                if ($status === SupportTicket::STATUS_CLOSED && !empty($reason)) {
                    $this->emailService->sendSupportTicketNotification($ticketId, 'replied');
                }
                
                return [
                    'success' => true,
                    'message' => 'Ticket status updated successfully.'
                ];
            }
            
            return ['success' => false, 'errors' => ['general' => 'Failed to update ticket status']];
            
        } catch (Exception $e) {
            error_log("Support ticket status update error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'System error occurred']];
        }
    }
    
    /**
     * Get user's tickets with pagination
     */
    public function getUserTickets($userId, $page = 1, $limit = 10, $status = null) {
        try {
            $offset = ($page - 1) * $limit;
            
            $db = Database::getInstance()->getConnection();
            
            // Build query
            $whereClause = "WHERE user_id = ?";
            $params = [$userId];
            
            if ($status) {
                $whereClause .= " AND status = ?";
                $params[] = $status;
            }
            
            // Get total count
            $countQuery = "SELECT COUNT(*) FROM support_tickets $whereClause";
            $stmt = $db->prepare($countQuery);
            $stmt->execute($params);
            $totalCount = $stmt->fetchColumn();
            
            // Get tickets
            $query = "
                SELECT id, subject, category, priority, status, created_at, replied_at
                FROM support_tickets 
                $whereClause 
                ORDER BY created_at DESC 
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'tickets' => $tickets,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalCount / $limit),
                    'total_count' => $totalCount,
                    'per_page' => $limit
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Get user tickets error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Failed to retrieve tickets']];
        }
    }
    
    /**
     * Get all tickets for admin with filtering
     */
    public function getAllTickets($page = 1, $limit = 20, $filters = []) {
        try {
            $offset = ($page - 1) * $limit;
            
            $db = Database::getInstance()->getConnection();
            
            // Build where clause
            $whereConditions = [];
            $params = [];
            
            if (!empty($filters['status'])) {
                $whereConditions[] = "st.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['priority'])) {
                $whereConditions[] = "st.priority = ?";
                $params[] = $filters['priority'];
            }
            
            if (!empty($filters['category'])) {
                $whereConditions[] = "st.category = ?";
                $params[] = $filters['category'];
            }
            
            if (!empty($filters['search'])) {
                $whereConditions[] = "(st.subject LIKE ? OR st.message LIKE ? OR u.email LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }
            
            $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
            
            // Get total count
            $countQuery = "
                SELECT COUNT(*) 
                FROM support_tickets st 
                JOIN users u ON st.user_id = u.id 
                $whereClause
            ";
            $stmt = $db->prepare($countQuery);
            $stmt->execute($params);
            $totalCount = $stmt->fetchColumn();
            
            // Get tickets
            $query = "
                SELECT 
                    st.id, st.subject, st.category, st.priority, st.status, 
                    st.created_at, st.replied_at,
                    u.first_name, u.last_name, u.email,
                    admin.first_name as admin_first_name, admin.last_name as admin_last_name
                FROM support_tickets st 
                JOIN users u ON st.user_id = u.id 
                LEFT JOIN users admin ON st.replied_by = admin.id
                $whereClause 
                ORDER BY 
                    CASE st.priority 
                        WHEN 'urgent' THEN 1 
                        WHEN 'high' THEN 2 
                        WHEN 'medium' THEN 3 
                        WHEN 'low' THEN 4 
                    END,
                    st.created_at DESC 
                LIMIT ? OFFSET ?
            ";
            
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'tickets' => $tickets,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => ceil($totalCount / $limit),
                    'total_count' => $totalCount,
                    'per_page' => $limit
                ]
            ];
            
        } catch (Exception $e) {
            error_log("Get all tickets error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Failed to retrieve tickets']];
        }
    }
    
    /**
     * Get ticket details
     */
    public function getTicketDetails($ticketId, $userId = null) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $query = "
                SELECT 
                    st.*,
                    u.first_name, u.last_name, u.email,
                    admin.first_name as admin_first_name, admin.last_name as admin_last_name
                FROM support_tickets st 
                JOIN users u ON st.user_id = u.id 
                LEFT JOIN users admin ON st.replied_by = admin.id
                WHERE st.id = ?
            ";
            
            $params = [$ticketId];
            
            // If userId is provided, ensure user can only see their own tickets
            if ($userId) {
                $query .= " AND st.user_id = ?";
                $params[] = $userId;
            }
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$ticket) {
                return ['success' => false, 'errors' => ['ticket' => 'Ticket not found']];
            }
            
            return [
                'success' => true,
                'ticket' => $ticket
            ];
            
        } catch (Exception $e) {
            error_log("Get ticket details error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Failed to retrieve ticket details']];
        }
    }
    
    /**
     * Get ticket statistics for dashboard
     */
    public function getTicketStatistics($userId = null) {
        try {
            $db = Database::getInstance()->getConnection();
            
            if ($userId) {
                // User statistics
                $query = "
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                        SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed
                    FROM support_tickets 
                    WHERE user_id = ?
                ";
                $stmt = $db->prepare($query);
                $stmt->execute([$userId]);
            } else {
                // Admin statistics
                $query = "
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                        SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed,
                        SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent,
                        SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high
                    FROM support_tickets
                ";
                $stmt = $db->prepare($query);
                $stmt->execute();
            }
            
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'success' => true,
                'statistics' => $stats
            ];
            
        } catch (Exception $e) {
            error_log("Get ticket statistics error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Failed to retrieve statistics']];
        }
    }
    
    /**
     * Validate ticket data
     */
    private function validateTicketData($subject, $message, $category, $priority) {
        $errors = [];
        
        // Subject validation
        if (empty($subject)) {
            $errors['subject'] = 'Subject is required';
        } elseif (strlen($subject) < 5) {
            $errors['subject'] = 'Subject must be at least 5 characters long';
        } elseif (strlen($subject) > 200) {
            $errors['subject'] = 'Subject must not exceed 200 characters';
        }
        
        // Message validation
        if (empty($message)) {
            $errors['message'] = 'Message is required';
        } elseif (strlen($message) < 20) {
            $errors['message'] = 'Message must be at least 20 characters long';
        } elseif (strlen($message) > 5000) {
            $errors['message'] = 'Message must not exceed 5000 characters';
        }
        
        // Category validation
        $validCategories = [
            SupportTicket::CATEGORY_GENERAL,
            SupportTicket::CATEGORY_DEPOSIT,
            SupportTicket::CATEGORY_WITHDRAWAL,
            SupportTicket::CATEGORY_TECHNICAL,
            SupportTicket::CATEGORY_ACCOUNT
        ];
        if (!in_array($category, $validCategories)) {
            $errors['category'] = 'Invalid category';
        }
        
        // Priority validation
        $validPriorities = [
            SupportTicket::PRIORITY_LOW,
            SupportTicket::PRIORITY_MEDIUM,
            SupportTicket::PRIORITY_HIGH,
            SupportTicket::PRIORITY_URGENT
        ];
        if (!in_array($priority, $validPriorities)) {
            $errors['priority'] = 'Invalid priority';
        }
        
        return $errors;
    }
    
    /**
     * Check rate limiting for ticket creation
     */
    private function checkRateLimit($userId) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Check tickets created in last hour
            $stmt = $db->prepare("
                SELECT COUNT(*) 
                FROM support_tickets 
                WHERE user_id = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute([$userId]);
            $count = $stmt->fetchColumn();
            
            return $count < 5; // Max 5 tickets per hour
            
        } catch (Exception $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            return true; // Allow on error
        }
    }
    
    /**
     * Close ticket automatically after inactivity
     */
    public function closeInactiveTickets($days = 7) {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Close tickets that have been answered but no response for X days
            $stmt = $db->prepare("
                UPDATE support_tickets 
                SET status = ?, updated_at = NOW()
                WHERE status = ? 
                AND replied_at IS NOT NULL 
                AND replied_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            $stmt->execute([
                SupportTicket::STATUS_CLOSED,
                SupportTicket::STATUS_ANSWERED,
                $days
            ]);
            
            $closedCount = $stmt->rowCount();
            
            if ($closedCount > 0) {
                $this->auditService->logSystemEvent('tickets_auto_closed', [
                    'count' => $closedCount,
                    'inactivity_days' => $days
                ]);
            }
            
            return $closedCount;
            
        } catch (Exception $e) {
            error_log("Auto close tickets error: " . $e->getMessage());
            return 0;
        }
    }
}
?>