<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';
require_once '../../../classes/services/TransactionManager.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $tradeId = intval($_POST['trade_id'] ?? 0);
    $exitPrice = floatval($_POST['exit_price'] ?? 0);
    $closedAt = $_POST['closed_at'] ?? '';
    $notes = trim($_POST['notes'] ?? '');
    
    // Validation
    $errors = [];
    
    if (!$tradeId) {
        $errors['trade_id'] = 'Trade ID is required';
    }
    
    if ($exitPrice <= 0) {
        $errors['exit_price'] = 'Exit price must be greater than zero';
    }
    
    if (empty($closedAt)) {
        $errors['closed_at'] = 'Closed date is required';
    } elseif (strtotime($closedAt) > time()) {
        $errors['closed_at'] = 'Closed date cannot be in the future';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Get the trade
    $stmt = $pdo->prepare("SELECT * FROM trades WHERE id = ? AND status = 'open'");
    $stmt->execute([$tradeId]);
    $trade = $stmt->fetch();
    
    if (!$trade) {
        echo json_encode([
            'success' => false,
            'message' => 'Trade not found or already closed'
        ]);
        exit;
    }
    
    // Validate closed date is after opened date
    if (strtotime($closedAt) < strtotime($trade['opened_at'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Closed date must be after opened date',
            'errors' => ['closed_at' => 'Closed date must be after opened date']
        ]);
        exit;
    }
    
    // Calculate profit/loss
    $entryPrice = floatval($trade['entry_price']);
    $amount = floatval($trade['amount']);
    
    if ($trade['trade_type'] === 'buy') {
        // For buy trades: profit = (exit_price - entry_price) / entry_price * amount
        $profitLoss = (($exitPrice - $entryPrice) / $entryPrice) * $amount;
    } else {
        // For sell trades: profit = (entry_price - exit_price) / entry_price * amount
        $profitLoss = (($entryPrice - $exitPrice) / $entryPrice) * $amount;
    }
    
    $pdo->beginTransaction();
    
    try {
        // Update the trade
        $sql = "UPDATE trades SET 
                    exit_price = ?, 
                    profit_loss = ?, 
                    status = 'closed', 
                    closed_at = ?, 
                    notes = CONCAT(COALESCE(notes, ''), CASE WHEN notes IS NOT NULL AND notes != '' THEN '\n\n' ELSE '' END, 'Closed: ', ?),
                    updated_at = NOW()
                WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $exitPrice,
            $profitLoss,
            $closedAt,
            $notes ?: 'Trade closed by admin',
            $tradeId
        ]);
        
        // Record transaction for profit/loss
        if ($profitLoss != 0) {
            $transactionType = $profitLoss > 0 ? 'trade_profit' : 'trade_loss';
            $description = sprintf(
                'Trade %s: %s %s at $%s (Entry: $%s, Exit: $%s)',
                $profitLoss > 0 ? 'profit' : 'loss',
                strtoupper($trade['trade_type']),
                $trade['asset'],
                number_format($exitPrice, 8),
                number_format($entryPrice, 8),
                number_format($exitPrice, 8)
            );
            
            TransactionManager::recordTransaction(
                $trade['user_id'],
                $transactionType,
                abs($profitLoss),
                $description,
                $tradeId,
                'trade'
            );
        }
        
        $pdo->commit();
        
        // Log the action
        AuditTrailService::log(
            'trade_closed',
            'trade',
            $tradeId,
            [
                'user_id' => $trade['user_id'],
                'asset' => $trade['asset'],
                'trade_type' => $trade['trade_type'],
                'entry_price' => $entryPrice,
                'exit_price' => $exitPrice,
                'profit_loss' => $profitLoss,
                'closed_at' => $closedAt
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Trade closed successfully',
            'profit_loss' => $profitLoss
        ]);
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Close trade error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while closing the trade'
    ]);
}
?>