<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

try {
    $db = getDB();
    $password = hashPassword('admin123');
    
    $stmt = $db->prepare('INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    $result = $stmt->execute(['admin', '<EMAIL>', $password, 'Admin', 'User', 'admin', 'active', 1]);
    
    if ($result) {
        echo "Admin user created successfully!\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
    } else {
        echo "Failed to create admin user or user already exists.\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>