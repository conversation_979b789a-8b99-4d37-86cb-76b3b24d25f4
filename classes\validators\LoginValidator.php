<?php
require_once __DIR__ . '/ValidationHelper.php';

/**
 * Login Form Validator
 * Handles validation of login form inputs
 */
class LoginValidator {
    private $errors = [];
    
    /**
     * Validate login form data
     */
    public function validate($data) {
        $this->errors = [];
        
        $this->validateIdentifier($data['identifier'] ?? '');
        $this->validatePassword($data['password'] ?? '');
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : '';
    }
    
    /**
     * Validate identifier (username or email)
     */
    private function validateIdentifier($identifier) {
        $identifier = trim($identifier);
        
        if (empty($identifier)) {
            $this->errors['identifier'] = 'Username or email is required.';
            return;
        }
        
        if (strlen($identifier) > 100) {
            $this->errors['identifier'] = 'Username or email is too long.';
            return;
        }
        
        // Check if it's an email or username format
        if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
            // It's an email - validate email format
            if (!ValidationHelper::validateEmail($identifier)) {
                $this->errors['identifier'] = 'Invalid email format.';
            }
        } else {
            // It's a username - validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $identifier)) {
                $this->errors['identifier'] = 'Username can only contain letters, numbers, and underscores.';
            }
        }
    }
    
    /**
     * Validate password
     */
    private function validatePassword($password) {
        if (empty($password)) {
            $this->errors['password'] = 'Password is required.';
            return;
        }
        
        if (strlen($password) > 255) {
            $this->errors['password'] = 'Password is too long.';
            return;
        }
        
        // Don't validate password strength on login - only on registration/change
    }
    
    /**
     * Validate CSRF token
     */
    private function validateCSRFToken($token) {
        if (!CSRFProtection::validateToken($token)) {
            $this->errors['csrf_token'] = 'Invalid security token. Please refresh the page and try again.';
        }
    }
    
    /**
     * Sanitize input data
     */
    public static function sanitize($data) {
        return [
            'identifier' => trim(filter_var($data['identifier'] ?? '', FILTER_SANITIZE_STRING)),
            'password' => $data['password'] ?? '', // Don't sanitize password
            'remember' => isset($data['remember']),
            'csrf_token' => $data['csrf_token'] ?? ''
        ];
    }
}
?>