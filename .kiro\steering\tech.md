# Technology Stack

## Backend
- **Language**: PHP 7.4+
- **Database**: MySQL with PDO
- **Architecture**: Custom MVC pattern
- **Email**: PHPMailer for SMTP

## Frontend
- **Framework**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.0.0
- **JavaScript**: Vanilla JS with Bootstrap components

## Dependencies
- **Composer**: Package management
- **PHPMailer**: Email functionality (`phpmailer/phpmailer: ^6.10`)

## Database
- **Engine**: MySQL with InnoDB tables
- **Migrations**: Custom migration system in `database/migrate.php`
- **Schema**: Complete schema in `database/schema.sql`
- **Seeding**: Sample data in `database/seed.sql`

## Security Features
- CSRF protection with token validation
- Password hashing with <PERSON><PERSON>'s `password_hash()`
- Session management with timeout
- SQL injection prevention via PDO prepared statements
- Input sanitization and validation
- Rate limiting for login attempts

## Common Commands

### Database Setup
```bash
# Run migrations
php database/migrate.php

# Seed database with sample data
mysql -u root -p coinage < database/seed.sql

# Create database backup
php database/backup.php
```

### Development
```bash
# Install dependencies
composer install

# Start local server
php -S localhost:8000

# Run tests
php test_folder/complete_test.php
```

## Configuration
- Main config in `config.php` with database, SMTP, and feature flags
- Environment-specific settings via PHP constants
- Dynamic theme colors and system features