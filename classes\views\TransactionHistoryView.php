<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * TransactionHistoryView - Displays user transaction history with filtering
 */
class TransactionHistoryView extends BaseView {
    use StatusColorTrait;
    
    protected function getTitle() {
        return 'Transaction History - Coinage Trading';
    }
    
    protected function getAdditionalCSS() {
        return [
            '/assets/css/transaction-history.css'
        ];
    }
    
    protected function getAdditionalJS() {
        return [
            '/assets/js/transaction-history.js'
        ];
    }
    
    protected function renderBody() {
        $user = $this->data['user'];
        $transactions = $this->data['transactions'];
        $statistics = $this->data['statistics'];
        $summary = $this->data['summary'];
        $filters = $this->data['filters'];
        $pagination = $this->data['pagination'];
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0">Transaction History</h1>
                            <p class="text-muted">View all your financial transactions and activity</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Transaction Summary Cards -->
            <div class="row mb-4">
                <?php foreach ($summary as $stat): ?>
                <div class="col-md-3 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="card-title text-muted mb-1">
                                        <?= $this->getTransactionTypeIcon($stat['type']) ?>
                                        <?= $this->getTransactionTypeDisplayName($stat['type']) ?>
                                    </h6>
                                    <div class="d-flex justify-content-between">
                                        <span class="h5 mb-0"><?= $stat['count'] ?></span>
                                        <span class="text-success">
                                            $<?= number_format($stat['total_credit'], 2) ?>
                                        </span>
                                    </div>
                                    <?php if ($stat['total_debit'] > 0): ?>
                                    <small class="text-danger">
                                        -$<?= number_format($stat['total_debit'], 2) ?>
                                    </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Transaction Type</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">All Types</option>
                                <option value="deposit" <?= $filters['type'] === 'deposit' ? 'selected' : '' ?>>Deposits</option>
                                <option value="withdrawal" <?= $filters['type'] === 'withdrawal' ? 'selected' : '' ?>>Withdrawals</option>
                                <option value="bonus" <?= $filters['type'] === 'bonus' ? 'selected' : '' ?>>Bonuses</option>
                                <option value="trade_profit" <?= $filters['type'] === 'trade_profit' ? 'selected' : '' ?>>Trade Profits</option>
                                <option value="trade_loss" <?= $filters['type'] === 'trade_loss' ? 'selected' : '' ?>>Trade Losses</option>
                                <option value="admin_credit" <?= $filters['type'] === 'admin_credit' ? 'selected' : '' ?>>Admin Credits</option>
                                <option value="admin_debit" <?= $filters['type'] === 'admin_debit' ? 'selected' : '' ?>>Admin Debits</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" 
                                   value="<?= htmlspecialchars($filters['date_from']) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" 
                                   value="<?= htmlspecialchars($filters['date_to']) ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="?" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Transaction Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        Transactions 
                        <span class="badge bg-secondary"><?= number_format($pagination['total_records']) ?></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($transactions)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h5>No Transactions Found</h5>
                        <p class="text-muted">No transactions match your current filters.</p>
                        <a href="?" class="btn btn-primary">Clear Filters</a>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Balance</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                <tr>
                                    <td>
                                        <div class="fw-medium">
                                            <?= date('M j, Y', strtotime($transaction->created_at)) ?>
                                        </div>
                                        <small class="text-muted">
                                            <?= date('g:i A', strtotime($transaction->created_at)) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $this->getTransactionTypeColor($transaction->type) ?>">
                                            <?= $this->getTransactionTypeIcon($transaction->type) ?>
                                            <?= $transaction->getTypeDisplayName() ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="transaction-description">
                                            <?= htmlspecialchars($transaction->description) ?>
                                        </div>
                                        <?php if ($transaction->reference_id): ?>
                                        <small class="text-muted">
                                            Ref: <?= $transaction->reference_type ?>-<?= $transaction->reference_id ?>
                                        </small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-bold <?= $transaction->isCredit() ? 'text-success' : 'text-danger' ?>">
                                            <?= $transaction->getFormattedAmount() ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="balance-info">
                                            <div class="fw-medium">$<?= number_format($transaction->balance_after, 2) ?></div>
                                            <small class="text-muted">
                                                From: $<?= number_format($transaction->balance_before, 2) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $transaction->getStatusClass() ?>">
                                            <?= $transaction->getStatusDisplayName() ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" 
                                                    onclick="viewTransactionDetails(<?= $transaction->getId() ?>)"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($transaction->reference_id && $transaction->reference_type === 'deposit'): ?>
                                            <a href="../deposit/details.php?id=<?= $transaction->reference_id ?>" 
                                               class="btn btn-outline-info" title="View Deposit">
                                                <i class="fas fa-plus-circle"></i>
                                            </a>
                                            <?php elseif ($transaction->reference_id && $transaction->reference_type === 'withdrawal'): ?>
                                            <a href="../withdraw/details.php?id=<?= $transaction->reference_id ?>" 
                                               class="btn btn-outline-warning" title="View Withdrawal">
                                                <i class="fas fa-minus-circle"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="card-footer">
                        <nav aria-label="Transaction pagination">
                            <ul class="pagination justify-content-center mb-0">
                                <?php if ($pagination['current_page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] - 1])) ?>">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $pagination['current_page'] + 1])) ?>">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing <?= (($pagination['current_page'] - 1) * $pagination['limit']) + 1 ?> to 
                                <?= min($pagination['current_page'] * $pagination['limit'], $pagination['total_records']) ?> 
                                of <?= number_format($pagination['total_records']) ?> transactions
                            </small>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Transaction Details Modal -->
        <div class="modal fade" id="transactionDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Transaction Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="transactionDetailsContent">
                        <div class="text-center py-3">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Export Modal -->
        <div class="modal fade" id="exportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Export Transactions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="exportForm">
                            <div class="mb-3">
                                <label for="export_format" class="form-label">Format</label>
                                <select name="format" id="export_format" class="form-select">
                                    <option value="csv">CSV</option>
                                    <option value="pdf">PDF</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="export_date_from" class="form-label">From Date</label>
                                <input type="date" name="date_from" id="export_date_from" class="form-control">
                            </div>
                            
                            <div class="mb-3">
                                <label for="export_date_to" class="form-label">To Date</label>
                                <input type="date" name="date_to" id="export_date_to" class="form-control">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="exportTransactions()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function getTransactionTypeIcon($type) {
        $icons = [
            'deposit' => '<i class="fas fa-plus-circle"></i>',
            'withdrawal' => '<i class="fas fa-minus-circle"></i>',
            'bonus' => '<i class="fas fa-gift"></i>',
            'trade_profit' => '<i class="fas fa-arrow-up"></i>',
            'trade_loss' => '<i class="fas fa-arrow-down"></i>',
            'transfer_in' => '<i class="fas fa-arrow-right"></i>',
            'transfer_out' => '<i class="fas fa-arrow-left"></i>',
            'admin_credit' => '<i class="fas fa-user-shield"></i>',
            'admin_debit' => '<i class="fas fa-user-minus"></i>'
        ];
        
        return $icons[$type] ?? '<i class="fas fa-exchange-alt"></i>';
    }
    
    private function getTransactionTypeColor($type) {
        $colors = [
            'deposit' => 'success',
            'withdrawal' => 'warning',
            'bonus' => 'info',
            'trade_profit' => 'success',
            'trade_loss' => 'danger',
            'transfer_in' => 'primary',
            'transfer_out' => 'secondary',
            'admin_credit' => 'success',
            'admin_debit' => 'danger'
        ];
        
        return $colors[$type] ?? 'secondary';
    }
    
    private function getTransactionTypeDisplayName($type) {
        $names = [
            'deposit' => 'Deposit',
            'withdrawal' => 'Withdrawal',
            'bonus' => 'Bonus',
            'trade_profit' => 'Trade Profit',
            'trade_loss' => 'Trade Loss',
            'transfer_in' => 'Transfer In',
            'transfer_out' => 'Transfer Out',
            'admin_credit' => 'Admin Credit',
            'admin_debit' => 'Admin Debit'
        ];
        
        return $names[$type] ?? ucfirst(str_replace('_', ' ', $type));
    }
}
?>