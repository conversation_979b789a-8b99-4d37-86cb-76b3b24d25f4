<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * Transaction Model - Handles transaction data and operations
 */
class Transaction extends BaseModel {
    protected $table = 'transactions';
    protected $fillable = [
        'user_id', 'type', 'amount', 'balance_before', 'balance_after',
        'description', 'reference_id', 'reference_type', 'status', 'processed_by'
    ];
    
    // Transaction types
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_BONUS = 'bonus';
    const TYPE_TRADE_PROFIT = 'trade_profit';
    const TYPE_TRADE_LOSS = 'trade_loss';
    const TYPE_TRANSFER_IN = 'transfer_in';
    const TYPE_TRANSFER_OUT = 'transfer_out';
    const TYPE_ADMIN_CREDIT = 'admin_credit';
    const TYPE_ADMIN_DEBIT = 'admin_debit';
    
    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    
    // Reference types
    const REF_TYPE_DEPOSIT = 'deposit';
    const REF_TYPE_WITHDRAWAL = 'withdrawal';
    const REF_TYPE_TRADE = 'trade';
    const REF_TYPE_MANUAL = 'manual';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // User ID validation
        if (empty($this->user_id)) {
            $errors['user_id'] = 'User ID is required';
        } elseif (!is_numeric($this->user_id)) {
            $errors['user_id'] = 'User ID must be numeric';
        }
        
        // Type validation
        if (empty($this->type)) {
            $errors['type'] = 'Transaction type is required';
        } elseif (!in_array($this->type, [
            self::TYPE_DEPOSIT, self::TYPE_WITHDRAWAL, self::TYPE_BONUS,
            self::TYPE_TRADE_PROFIT, self::TYPE_TRADE_LOSS, self::TYPE_TRANSFER_IN,
            self::TYPE_TRANSFER_OUT, self::TYPE_ADMIN_CREDIT, self::TYPE_ADMIN_DEBIT
        ])) {
            $errors['type'] = 'Invalid transaction type';
        }
        
        // Amount validation
        if (!isset($this->amount)) {
            $errors['amount'] = 'Amount is required';
        } elseif (!is_numeric($this->amount)) {
            $errors['amount'] = 'Amount must be numeric';
        } elseif ($this->amount == 0) {
            $errors['amount'] = 'Amount cannot be zero';
        } elseif (abs($this->amount) > 999999999.99) {
            $errors['amount'] = 'Amount is too large';
        }
        
        // Balance validation
        if (!isset($this->balance_before)) {
            $errors['balance_before'] = 'Balance before is required';
        } elseif (!is_numeric($this->balance_before)) {
            $errors['balance_before'] = 'Balance before must be numeric';
        } elseif ($this->balance_before < 0) {
            $errors['balance_before'] = 'Balance before cannot be negative';
        }
        
        if (!isset($this->balance_after)) {
            $errors['balance_after'] = 'Balance after is required';
        } elseif (!is_numeric($this->balance_after)) {
            $errors['balance_after'] = 'Balance after must be numeric';
        } elseif ($this->balance_after < 0) {
            $errors['balance_after'] = 'Balance after cannot be negative';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [
            self::STATUS_PENDING, self::STATUS_COMPLETED, self::STATUS_FAILED, self::STATUS_CANCELLED
        ])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Reference type validation
        if (!empty($this->reference_type) && !in_array($this->reference_type, [
            self::REF_TYPE_DEPOSIT, self::REF_TYPE_WITHDRAWAL, self::REF_TYPE_TRADE, self::REF_TYPE_MANUAL
        ])) {
            $errors['reference_type'] = 'Invalid reference type';
        }
        
        // Reference ID validation
        if (!empty($this->reference_id) && strlen($this->reference_id) > 100) {
            $errors['reference_id'] = 'Reference ID must not exceed 100 characters';
        }
        
        return $errors;
    }
    
    /**
     * Get the user for this transaction
     */
    public function getUser() {
        require_once __DIR__ . '/User.php';
        return User::find($this->user_id);
    }
    
    /**
     * Get the admin who processed this transaction
     */
    public function getProcessedBy() {
        if (!$this->processed_by) {
            return null;
        }
        
        require_once __DIR__ . '/User.php';
        return User::find($this->processed_by);
    }
    
    /**
     * Get the related record based on reference type and ID
     */
    public function getRelatedRecord() {
        if (!$this->reference_id || !$this->reference_type) {
            return null;
        }
        
        switch ($this->reference_type) {
            case self::REF_TYPE_DEPOSIT:
                require_once __DIR__ . '/Deposit.php';
                return Deposit::find($this->reference_id);
                
            case self::REF_TYPE_WITHDRAWAL:
                require_once __DIR__ . '/Withdrawal.php';
                return Withdrawal::find($this->reference_id);
                
            case self::REF_TYPE_TRADE:
                // Trade model would be implemented later
                return null;
                
            default:
                return null;
        }
    }
    
    /**
     * Check if transaction is credit (increases balance)
     */
    public function isCredit() {
        return in_array($this->type, [
            self::TYPE_DEPOSIT, self::TYPE_BONUS, self::TYPE_TRADE_PROFIT,
            self::TYPE_TRANSFER_IN, self::TYPE_ADMIN_CREDIT
        ]);
    }
    
    /**
     * Check if transaction is debit (decreases balance)
     */
    public function isDebit() {
        return in_array($this->type, [
            self::TYPE_WITHDRAWAL, self::TYPE_TRADE_LOSS,
            self::TYPE_TRANSFER_OUT, self::TYPE_ADMIN_DEBIT
        ]);
    }
    
    /**
     * Get formatted amount with sign
     */
    public function getFormattedAmount() {
        $sign = $this->isCredit() ? '+' : '-';
        return $sign . '$' . number_format(abs($this->amount), 2);
    }
    
    /**
     * Get transaction type display name
     */
    public function getTypeDisplayName() {
        $types = [
            self::TYPE_DEPOSIT => 'Deposit',
            self::TYPE_WITHDRAWAL => 'Withdrawal',
            self::TYPE_BONUS => 'Bonus',
            self::TYPE_TRADE_PROFIT => 'Trade Profit',
            self::TYPE_TRADE_LOSS => 'Trade Loss',
            self::TYPE_TRANSFER_IN => 'Transfer In',
            self::TYPE_TRANSFER_OUT => 'Transfer Out',
            self::TYPE_ADMIN_CREDIT => 'Admin Credit',
            self::TYPE_ADMIN_DEBIT => 'Admin Debit'
        ];
        
        return $types[$this->type] ?? $this->type;
    }
    
    /**
     * Get status display name
     */
    public function getStatusDisplayName() {
        $statuses = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled'
        ];
        
        return $statuses[$this->status] ?? $this->status;
    }
    
    /**
     * Get status CSS class for styling
     */
    public function getStatusClass() {
        $classes = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary'
        ];
        
        return $classes[$this->status] ?? 'secondary';
    }
    
    /**
     * Create a new transaction record
     */
    public static function createTransaction($data) {
        $transaction = new static();
        $transaction->fill($data);
        
        // Set default values
        if (!isset($transaction->status)) {
            $transaction->status = self::STATUS_COMPLETED;
        }
        
        if (!isset($transaction->reference_type)) {
            $transaction->reference_type = self::REF_TYPE_MANUAL;
        }
        
        if ($transaction->save()) {
            return $transaction;
        }
        
        return null;
    }
    
    /**
     * Get transactions by user
     */
    public static function getByUser($userId, $limit = null, $offset = 0, $type = null) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE user_id = :user_id";
        
        if ($type) {
            $sql .= " AND type = :type";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $params = ['user_id' => $userId];
        if ($type) {
            $params['type'] = $type;
        }
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        $transactions = [];
        foreach ($results as $data) {
            $transactions[] = new static($data);
        }
        
        return $transactions;
    }
    
    /**
     * Get transactions by type
     */
    public static function getByType($type, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE type = :type ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['type' => $type]);
        $results = $stmt->fetchAll();
        
        $transactions = [];
        foreach ($results as $data) {
            $transactions[] = new static($data);
        }
        
        return $transactions;
    }
    
    /**
     * Get transactions by status
     */
    public static function getByStatus($status, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE status = :status ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $results = $stmt->fetchAll();
        
        $transactions = [];
        foreach ($results as $data) {
            $transactions[] = new static($data);
        }
        
        return $transactions;
    }
    
    /**
     * Get transactions with user information
     */
    public static function getWithUserDetails($limit = null, $offset = 0, $filters = []) {
        $instance = new static();
        $sql = "SELECT t.*, u.username, u.first_name, u.last_name, u.email
                FROM {$instance->table} t
                LEFT JOIN users u ON t.user_id = u.id";
        
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "t.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['type'])) {
            $whereConditions[] = "t.type = :type";
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['status'])) {
            $whereConditions[] = "t.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "t.created_at >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "t.created_at <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $sql .= " ORDER BY t.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get transaction statistics
     */
    public static function getStatistics($userId = null, $dateFrom = null, $dateTo = null) {
        $instance = new static();
        $sql = "SELECT 
                    type,
                    COUNT(*) as count,
                    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_credit,
                    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_debit
                FROM {$instance->table}";
        
        $whereConditions = [];
        $params = [];
        
        if ($userId) {
            $whereConditions[] = "user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        if ($dateFrom) {
            $whereConditions[] = "created_at >= :date_from";
            $params['date_from'] = $dateFrom;
        }
        
        if ($dateTo) {
            $whereConditions[] = "created_at <= :date_to";
            $params['date_to'] = $dateTo;
        }
        
        $whereConditions[] = "status = 'completed'";
        
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $sql .= " GROUP BY type";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get total amount by type and status
     */
    public static function getTotalAmount($type = null, $status = 'completed', $userId = null) {
        $instance = new static();
        $sql = "SELECT COALESCE(SUM(ABS(amount)), 0) as total FROM {$instance->table} WHERE status = :status";
        
        $params = ['status' => $status];
        
        if ($type) {
            $sql .= " AND type = :type";
            $params['type'] = $type;
        }
        
        if ($userId) {
            $sql .= " AND user_id = :user_id";
            $params['user_id'] = $userId;
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
}
?>