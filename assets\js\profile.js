// Profile JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize forms
    initializeProfileForm();
    initializePasswordForm();
    initializeProfilePictureForm();
    
    // Initialize password strength indicator
    initializePasswordStrength();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function initializeProfileForm() {
    const form = document.getElementById('profileForm');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Clear previous validation
        clearValidation(form);
        
        const formData = new FormData(form);
        formData.append('action', 'update_profile');
        
        fetch('/user/api/profile.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Profile updated successfully!', 'success');
            } else {
                if (data.errors) {
                    displayValidationErrors(form, data.errors);
                } else {
                    showToast(data.error || 'Failed to update profile', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Remove loading state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
}

function initializePasswordForm() {
    const form = document.getElementById('passwordForm');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Validate passwords match
        const newPassword = form.querySelector('#new_password').value;
        const confirmPassword = form.querySelector('#confirm_password').value;
        
        if (newPassword !== confirmPassword) {
            const confirmField = form.querySelector('#confirm_password');
            confirmField.classList.add('is-invalid');
            confirmField.nextElementSibling.textContent = 'Passwords do not match';
            return;
        }
        
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Clear previous validation
        clearValidation(form);
        
        const formData = new FormData(form);
        formData.append('action', 'change_password');
        
        fetch('/user/api/profile.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Password changed successfully!', 'success');
                form.reset();
            } else {
                if (data.errors) {
                    displayValidationErrors(form, data.errors);
                } else {
                    showToast(data.error || 'Failed to change password', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Remove loading state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
    
    // Real-time password confirmation validation
    const confirmPasswordField = form.querySelector('#confirm_password');
    const newPasswordField = form.querySelector('#new_password');
    
    if (confirmPasswordField && newPasswordField) {
        confirmPasswordField.addEventListener('input', function() {
            if (this.value && this.value !== newPasswordField.value) {
                this.classList.add('is-invalid');
                this.nextElementSibling.textContent = 'Passwords do not match';
            } else {
                this.classList.remove('is-invalid');
                this.nextElementSibling.textContent = '';
            }
        });
    }
}

function initializeProfilePictureForm() {
    const form = document.getElementById('profilePictureForm');
    if (!form) return;
    
    const fileInput = form.querySelector('#profilePictureInput');
    
    // Preview image before upload
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                showToast('Invalid file type. Only JPEG, PNG, and GIF are allowed.', 'error');
                this.value = '';
                return;
            }
            
            // Validate file size (2MB)
            if (file.size > 2 * 1024 * 1024) {
                showToast('File too large. Maximum size is 2MB.', 'error');
                this.value = '';
                return;
            }
            
            // Preview image
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.querySelector('.profile-picture') || 
                               document.querySelector('.profile-picture-placeholder');
                
                if (preview.tagName === 'IMG') {
                    preview.src = e.target.result;
                } else {
                    // Replace placeholder with image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'profile-picture';
                    img.alt = 'Profile Picture Preview';
                    preview.parentNode.replaceChild(img, preview);
                }
            };
            reader.readAsDataURL(file);
        }
    });
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        if (!fileInput.files[0]) {
            showToast('Please select a file to upload.', 'error');
            return;
        }
        
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        const formData = new FormData(form);
        formData.append('action', 'upload_picture');
        
        fetch('/user/api/profile.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Profile picture updated successfully!', 'success');
                // Update the profile picture display
                const profileImg = document.querySelector('.profile-picture');
                if (profileImg && data.profile_picture) {
                    profileImg.src = '/' + data.profile_picture + '?t=' + Date.now();
                }
            } else {
                showToast(data.error || 'Failed to upload profile picture', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Remove loading state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
}

function initializePasswordStrength() {
    const passwordField = document.getElementById('new_password');
    if (!passwordField) return;
    
    // Create password strength indicator
    const strengthIndicator = document.createElement('div');
    strengthIndicator.className = 'password-strength';
    passwordField.parentNode.appendChild(strengthIndicator);
    
    passwordField.addEventListener('input', function() {
        const password = this.value;
        const strength = calculatePasswordStrength(password);
        
        strengthIndicator.className = `password-strength ${strength.level}`;
        
        // Update form text
        const formText = this.parentNode.querySelector('.form-text');
        if (formText) {
            formText.textContent = `Password strength: ${strength.text}`;
        }
    });
}

function calculatePasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (password.match(/[a-z]/)) score++;
    if (password.match(/[A-Z]/)) score++;
    if (password.match(/[0-9]/)) score++;
    if (password.match(/[^a-zA-Z0-9]/)) score++;
    
    if (score < 3) {
        return { level: 'weak', text: 'Weak' };
    } else if (score < 5) {
        return { level: 'medium', text: 'Medium' };
    } else {
        return { level: 'strong', text: 'Strong' };
    }
}

function clearValidation(form) {
    const invalidFields = form.querySelectorAll('.is-invalid');
    invalidFields.forEach(field => {
        field.classList.remove('is-invalid');
        const feedback = field.nextElementSibling;
        if (feedback && feedback.classList.contains('invalid-feedback')) {
            feedback.textContent = '';
        }
    });
}

function displayValidationErrors(form, errors) {
    Object.keys(errors).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('is-invalid');
            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.textContent = errors[fieldName];
            }
        }
    });
}

function showToast(message, type) {
    const toastId = type === 'success' ? 'successToast' : 'errorToast';
    const toast = document.getElementById(toastId);
    
    if (toast) {
        const toastBody = toast.querySelector('.toast-body');
        toastBody.textContent = message;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}