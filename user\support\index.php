<?php
require_once '../../includes/security_init.php';
require_once '../../classes/controllers/AuthController.php';
require_once '../../classes/services/SupportTicketService.php';
require_once '../../classes/views/BaseView.php';

// Require authentication
AuthController::requireAuth();

$supportService = SupportTicketService::getInstance();
$userId = $_SESSION['user_id'];

// Get pagination parameters
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$status = isset($_GET['status']) ? $_GET['status'] : null;

// Get user's tickets
$ticketsResult = $supportService->getUserTickets($userId, $page, 10, $status);
$tickets = $ticketsResult['success'] ? $ticketsResult['tickets'] : [];
$pagination = $ticketsResult['success'] ? $ticketsResult['pagination'] : null;

// Get statistics
$statsResult = $supportService->getTicketStatistics($userId);
$statistics = $statsResult['success'] ? $statsResult['statistics'] : [];

/**
 * Support Tickets View
 */
class SupportTicketsView extends BaseView {
    private $tickets;
    private $pagination;
    private $statistics;
    private $currentStatus;
    
    public function __construct($tickets, $pagination, $statistics, $currentStatus = null) {
        parent::__construct();
        $this->tickets = $tickets;
        $this->pagination = $pagination;
        $this->statistics = $statistics;
        $this->currentStatus = $currentStatus;
        $this->setTitle('Support Tickets - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/support-tickets.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">Support Tickets</h1>
                        <a href="/user/support/create-ticket.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Create New Ticket
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $this->statistics['total'] ?? 0 ?></h4>
                                    <p class="mb-0">Total Tickets</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-ticket-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $this->statistics['pending'] ?? 0 ?></h4>
                                    <p class="mb-0">Pending</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $this->statistics['answered'] ?? 0 ?></h4>
                                    <p class="mb-0">Answered</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-reply fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0"><?= $this->statistics['closed'] ?? 0 ?></h4>
                                    <p class="mb-0">Closed</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="btn-group" role="group">
                                        <a href="?status=" class="btn <?= empty($this->currentStatus) ? 'btn-primary' : 'btn-outline-primary' ?>">
                                            All Tickets
                                        </a>
                                        <a href="?status=pending" class="btn <?= $this->currentStatus === 'pending' ? 'btn-warning' : 'btn-outline-warning' ?>">
                                            Pending
                                        </a>
                                        <a href="?status=answered" class="btn <?= $this->currentStatus === 'answered' ? 'btn-info' : 'btn-outline-info' ?>">
                                            Answered
                                        </a>
                                        <a href="?status=closed" class="btn <?= $this->currentStatus === 'closed' ? 'btn-success' : 'btn-outline-success' ?>">
                                            Closed
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <small class="text-muted">
                                        Showing <?= count($this->tickets) ?> of <?= $this->pagination['total_count'] ?? 0 ?> tickets
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tickets List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <?php if (empty($this->tickets)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">No Support Tickets</h4>
                                    <p class="text-muted">You haven't created any support tickets yet.</p>
                                    <a href="/user/support/create-ticket.php" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create Your First Ticket
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Subject</th>
                                                <th>Category</th>
                                                <th>Priority</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Last Reply</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($this->tickets as $ticket): ?>
                                                <tr>
                                                    <td>
                                                        <strong>#<?= htmlspecialchars($ticket['id']) ?></strong>
                                                    </td>
                                                    <td>
                                                        <a href="/user/support/ticket.php?id=<?= $ticket['id'] ?>" class="text-decoration-none">
                                                            <?= htmlspecialchars($ticket['subject']) ?>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">
                                                            <?= ucfirst(htmlspecialchars($ticket['category'])) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $priorityColors = [
                                                            'low' => 'success',
                                                            'medium' => 'warning',
                                                            'high' => 'danger',
                                                            'urgent' => 'dark'
                                                        ];
                                                        $priorityColor = $priorityColors[$ticket['priority']] ?? 'secondary';
                                                        ?>
                                                        <span class="badge bg-<?= $priorityColor ?>">
                                                            <?= ucfirst(htmlspecialchars($ticket['priority'])) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        $statusColors = [
                                                            'pending' => 'warning',
                                                            'answered' => 'info',
                                                            'closed' => 'success'
                                                        ];
                                                        $statusColor = $statusColors[$ticket['status']] ?? 'secondary';
                                                        ?>
                                                        <span class="badge bg-<?= $statusColor ?>">
                                                            <?= ucfirst(htmlspecialchars($ticket['status'])) ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">
                                                            <?= date('M j, Y g:i A', strtotime($ticket['created_at'])) ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <?php if ($ticket['replied_at']): ?>
                                                            <small class="text-muted">
                                                                <?= date('M j, Y g:i A', strtotime($ticket['replied_at'])) ?>
                                                            </small>
                                                        <?php else: ?>
                                                            <small class="text-muted">-</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <a href="/user/support/ticket.php?id=<?= $ticket['id'] ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye me-1"></i>View
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($this->pagination && $this->pagination['total_pages'] > 1): ?>
                                    <nav aria-label="Tickets pagination" class="mt-4">
                                        <ul class="pagination justify-content-center">
                                            <?php if ($this->pagination['current_page'] > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?= $this->pagination['current_page'] - 1 ?><?= $this->currentStatus ? '&status=' . $this->currentStatus : '' ?>">
                                                        Previous
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = 1; $i <= $this->pagination['total_pages']; $i++): ?>
                                                <li class="page-item <?= $i === $this->pagination['current_page'] ? 'active' : '' ?>">
                                                    <a class="page-link" href="?page=<?= $i ?><?= $this->currentStatus ? '&status=' . $this->currentStatus : '' ?>">
                                                        <?= $i ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($this->pagination['current_page'] < $this->pagination['total_pages']): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?page=<?= $this->pagination['current_page'] + 1 ?><?= $this->currentStatus ? '&status=' . $this->currentStatus : '' ?>">
                                                        Next
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script>
            // Auto-refresh every 30 seconds for pending tickets
            <?php if ($this->currentStatus === 'pending' || empty($this->currentStatus)): ?>
            setInterval(function() {
                // Only refresh if user is still on the page
                if (!document.hidden) {
                    location.reload();
                }
            }, 30000);
            <?php endif; ?>
        </script>
        <?php
    }
}

// Render the page
$view = new SupportTicketsView($tickets, $pagination, $statistics, $status);
$view->render();
?>