<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/services/TwoFactorAuthService.php';
require_once __DIR__ . '/../classes/services/SecurityAuditService.php';
require_once __DIR__ . '/../classes/validators/SecurityValidator.php';
require_once __DIR__ . '/../classes/services/SecurityMiddleware.php';

/**
 * Comprehensive Security and 2FA Testing Suite
 */
class SecurityAndTwoFATest {
    private $db;
    private $testUserId;
    private $results = [];
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->setupTestUser();
    }
    
    public function runAllTests() {
        echo "<h1>Security and 2FA System Test Suite</h1>\n";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
        
        $this->test2FAService();
        $this->testSecurityValidator();
        $this->testSecurityAudit();
        $this->testSecurityMiddleware();
        $this->testDatabaseMigrations();
        
        $this->displayResults();
        echo "</div>\n";
    }
    
    private function setupTestUser() {
        try {
            // Create test user
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
            ");
            
            $hashedPassword = password_hash('TestPassword123!', PASSWORD_DEFAULT);
            $stmt->execute([
                'test_security_user',
                '<EMAIL>',
                $hashedPassword,
                'Security',
                'Test',
                'user',
                'active',
                1
            ]);
            
            $this->testUserId = $this->db->lastInsertId();
            if (!$this->testUserId) {
                // Get existing user ID
                $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute(['<EMAIL>']);
                $this->testUserId = $stmt->fetchColumn();
            }
            
            $this->addResult('Setup', 'Test user created/found', true);
        } catch (Exception $e) {
            $this->addResult('Setup', 'Failed to create test user: ' . $e->getMessage(), false);
        }
    }
    
    private function test2FAService() {
        echo "<h2>Testing Two-Factor Authentication Service</h2>\n";
        
        $twoFA = TwoFactorAuthService::getInstance();
        
        // Test secret generation
        $secret = $twoFA->generateSecret();
        $this->addResult('2FA', 'Secret generation', !empty($secret) && strlen($secret) === 32);
        
        // Test QR code URL generation
        $qrUrl = $twoFA->getQRCodeUrl($secret, '<EMAIL>');
        $this->addResult('2FA', 'QR code URL generation', strpos($qrUrl, 'chart.googleapis.com') !== false);
        
        // Test code verification (this will fail as we don't have a real authenticator)
        $testCode = '123456';
        $verifyResult = $twoFA->verifyCode($secret, $testCode);
        $this->addResult('2FA', 'Code verification (expected to fail)', $verifyResult === false);
        
        // Test 2FA status
        $status = $twoFA->get2FAStatus($this->testUserId);
        $this->addResult('2FA', '2FA status check', isset($status['enabled']) && isset($status['backup_codes']));
        
        // Test enabling 2FA (will fail without valid code)
        $enableResult = $twoFA->enable2FA($this->testUserId, $secret, '000000');
        $this->addResult('2FA', 'Enable 2FA (expected to fail)', $enableResult['success'] === false);
    }
    
    private function testSecurityValidator() {
        echo "<h2>Testing Security Validator</h2>\n";
        
        $validator = new SecurityValidator();
        
        // Test password strength validation
        $weakPassword = '123456';
        $strongPassword = 'StrongPass123!@#';
        
        $weakResult = $validator->validatePasswordStrength($weakPassword);
        $strongResult = $validator->validatePasswordStrength($strongPassword);
        
        $this->addResult('Security Validator', 'Weak password rejection', !$weakResult);
        $this->addResult('Security Validator', 'Strong password acceptance', $strongResult);
        
        // Test login attempt validation
        $loginResult = $validator->validateLoginAttempt('<EMAIL>', 'password', '127.0.0.1', 'Test Agent');
        $this->addResult('Security Validator', 'Login attempt validation', $loginResult);
        
        // Test rate limiting
        for ($i = 0; $i < 6; $i++) {
            $rateLimitResult = $validator->validateRateLimit('test_operation', '127.0.0.1', 5, 300);
        }
        $this->addResult('Security Validator', 'Rate limiting', !$rateLimitResult);
        
        // Test session validation
        session_start();
        $_SESSION['last_activity'] = time();
        $sessionResult = $validator->validateSession();
        $this->addResult('Security Validator', 'Session validation', $sessionResult);
    }
    
    private function testSecurityAudit() {
        echo "<h2>Testing Security Audit Service</h2>\n";
        
        $audit = SecurityAuditService::getInstance();
        
        // Test logging different event types
        $audit->logAuthEvent('test_login', $this->testUserId, ['ip' => '127.0.0.1']);
        $audit->logFinancialEvent('test_deposit', $this->testUserId, ['amount' => 100.00]);
        $audit->logAdminEvent('test_admin_action', $this->testUserId, null, ['action' => 'test']);
        $audit->logSystemEvent('test_system_event', ['component' => 'test']);
        
        $this->addResult('Security Audit', 'Event logging', true);
        
        // Test failed login monitoring
        $audit->monitorFailedLogins('<EMAIL>', '127.0.0.1');
        $this->addResult('Security Audit', 'Failed login monitoring', true);
        
        // Test financial activity monitoring
        $audit->monitorFinancialActivity($this->testUserId, 'deposit_request', 500.00);
        $this->addResult('Security Audit', 'Financial activity monitoring', true);
        
        // Test security report generation
        $report = $audit->generateSecurityReport(date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
        $this->addResult('Security Audit', 'Security report generation', is_array($report));
        
        // Test recent events retrieval
        $events = $audit->getRecentEvents(10);
        $this->addResult('Security Audit', 'Recent events retrieval', is_array($events));
        
        // Test security statistics
        $stats = $audit->getSecurityStats(7);
        $this->addResult('Security Audit', 'Security statistics', is_array($stats));
    }
    
    private function testSecurityMiddleware() {
        echo "<h2>Testing Security Middleware</h2>\n";
        
        $middleware = SecurityMiddleware::getInstance();
        
        // Test configuration
        $config = $middleware->getConfig();
        $this->addResult('Security Middleware', 'Configuration retrieval', is_array($config));
        
        // Test configuration update
        $updateResult = $middleware->updateConfig('max_request_size', 5242880);
        $this->addResult('Security Middleware', 'Configuration update', $updateResult);
        
        // Test CSRF validation
        $csrfResult = $middleware->validateCSRF('invalid_token');
        $this->addResult('Security Middleware', 'CSRF validation (expected to fail)', !$csrfResult);
        
        // Test IP validation
        $ipResult = $middleware->validateIP('127.0.0.1');
        $this->addResult('Security Middleware', 'IP validation', $ipResult);
    }
    
    private function testDatabaseMigrations() {
        echo "<h2>Testing Database Migrations</h2>\n";
        
        try {
            // Test backup codes table
            $stmt = $this->db->query("DESCRIBE user_backup_codes");
            $backupCodesTable = $stmt->fetchAll();
            $this->addResult('Database', 'Backup codes table exists', !empty($backupCodesTable));
            
            // Test security audit logs table
            $stmt = $this->db->query("DESCRIBE security_audit_logs");
            $auditLogsTable = $stmt->fetchAll();
            $this->addResult('Database', 'Security audit logs table exists', !empty($auditLogsTable));
            
            // Test 2FA fields in users table
            $stmt = $this->db->query("DESCRIBE users");
            $userFields = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
            $has2FAFields = in_array('two_fa_enabled', $userFields) && in_array('two_fa_secret', $userFields);
            $this->addResult('Database', '2FA fields in users table', $has2FAFields);
            
        } catch (Exception $e) {
            $this->addResult('Database', 'Migration test failed: ' . $e->getMessage(), false);
        }
    }
    
    private function addResult($category, $test, $passed) {
        $this->results[] = [
            'category' => $category,
            'test' => $test,
            'passed' => $passed
        ];
        
        $status = $passed ? '✅ PASS' : '❌ FAIL';
        echo "<div style='margin: 5px 0;'><strong>[$category]</strong> $test: $status</div>\n";
    }
    
    private function displayResults() {
        echo "<h2>Test Summary</h2>\n";
        
        $total = count($this->results);
        $passed = array_sum(array_column($this->results, 'passed'));
        $failed = $total - $passed;
        
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<strong>Total Tests:</strong> $total<br>\n";
        echo "<strong style='color: green;'>Passed:</strong> $passed<br>\n";
        echo "<strong style='color: red;'>Failed:</strong> $failed<br>\n";
        echo "<strong>Success Rate:</strong> " . round(($passed / $total) * 100, 2) . "%\n";
        echo "</div>\n";
        
        if ($failed > 0) {
            echo "<h3>Failed Tests:</h3>\n";
            foreach ($this->results as $result) {
                if (!$result['passed']) {
                    echo "<div style='color: red;'>❌ [{$result['category']}] {$result['test']}</div>\n";
                }
            }
        }
        
        echo "<h3>Recommendations:</h3>\n";
        echo "<ul>\n";
        echo "<li>Run database migrations to ensure all tables exist</li>\n";
        echo "<li>Test 2FA with actual authenticator app for full validation</li>\n";
        echo "<li>Configure rate limiting thresholds based on your needs</li>\n";
        echo "<li>Set up log rotation for security audit logs</li>\n";
        echo "<li>Implement threat intelligence integration for IP validation</li>\n";
        echo "</ul>\n";
    }
    
    public function __destruct() {
        // Clean up test user
        try {
            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$this->testUserId]);
            
            $stmt = $this->db->prepare("DELETE FROM user_backup_codes WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
            
            $stmt = $this->db->prepare("DELETE FROM security_audit_logs WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
    }
}

// Run the tests
$tester = new SecurityAndTwoFATest();
$tester->runAllTests();
?>