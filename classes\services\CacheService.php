<?php
/**
 * Cache Service - Provides caching mechanisms for improved performance
 */
class CacheService {
    private static $instance = null;
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour default
    
    private function __construct() {
        $this->cacheDir = __DIR__ . '/../../cache';
        $this->ensureCacheDirectory();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Ensure cache directory exists
     */
    private function ensureCacheDirectory() {
        if (!file_exists($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create subdirectories for different cache types
        $subdirs = ['data', 'views', 'queries', 'sessions', 'system'];
        foreach ($subdirs as $subdir) {
            $path = $this->cacheDir . '/' . $subdir;
            if (!file_exists($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    /**
     * Generate cache key
     */
    private function generateKey($key, $namespace = 'default') {
        return $namespace . '_' . md5($key);
    }
    
    /**
     * Get cache file path
     */
    private function getCacheFilePath($key, $namespace = 'default') {
        $hashedKey = $this->generateKey($key, $namespace);
        return $this->cacheDir . '/' . $namespace . '/' . $hashedKey . '.cache';
    }
    
    /**
     * Store data in cache
     */
    public function set($key, $data, $ttl = null, $namespace = 'data') {
        $ttl = $ttl ?? $this->defaultTTL;
        $expiry = time() + $ttl;
        
        $cacheData = [
            'data' => $data,
            'expiry' => $expiry,
            'created' => time(),
            'key' => $key
        ];
        
        $filePath = $this->getCacheFilePath($key, $namespace);
        $serialized = serialize($cacheData);
        
        return file_put_contents($filePath, $serialized, LOCK_EX) !== false;
    }
    
    /**
     * Retrieve data from cache
     */
    public function get($key, $namespace = 'data') {
        $filePath = $this->getCacheFilePath($key, $namespace);
        
        if (!file_exists($filePath)) {
            return null;
        }
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            return null;
        }
        
        $cacheData = unserialize($content);
        if ($cacheData === false) {
            // Invalid cache file, remove it
            unlink($filePath);
            return null;
        }
        
        // Check if cache has expired
        if (time() > $cacheData['expiry']) {
            unlink($filePath);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Check if cache key exists and is valid
     */
    public function has($key, $namespace = 'data') {
        return $this->get($key, $namespace) !== null;
    }
    
    /**
     * Delete cache entry
     */
    public function delete($key, $namespace = 'data') {
        $filePath = $this->getCacheFilePath($key, $namespace);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }
    
    /**
     * Clear all cache in a namespace
     */
    public function clearNamespace($namespace = 'data') {
        $namespacePath = $this->cacheDir . '/' . $namespace;
        
        if (!is_dir($namespacePath)) {
            return true;
        }
        
        $files = glob($namespacePath . '/*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
        }
        
        return $cleared;
    }
    
    /**
     * Clear all cache
     */
    public function clearAll() {
        $namespaces = ['data', 'views', 'queries', 'sessions', 'system'];
        $totalCleared = 0;
        
        foreach ($namespaces as $namespace) {
            $totalCleared += $this->clearNamespace($namespace);
        }
        
        return $totalCleared;
    }
    
    /**
     * Clean expired cache entries
     */
    public function cleanExpired() {
        $namespaces = ['data', 'views', 'queries', 'sessions', 'system'];
        $cleaned = 0;
        
        foreach ($namespaces as $namespace) {
            $namespacePath = $this->cacheDir . '/' . $namespace;
            
            if (!is_dir($namespacePath)) {
                continue;
            }
            
            $files = glob($namespacePath . '/*.cache');
            
            foreach ($files as $file) {
                $content = file_get_contents($file);
                if ($content === false) {
                    continue;
                }
                
                $cacheData = unserialize($content);
                if ($cacheData === false || time() > $cacheData['expiry']) {
                    if (unlink($file)) {
                        $cleaned++;
                    }
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats() {
        $namespaces = ['data', 'views', 'queries', 'sessions', 'system'];
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0,
            'namespaces' => []
        ];
        
        foreach ($namespaces as $namespace) {
            $namespacePath = $this->cacheDir . '/' . $namespace;
            $namespaceStats = [
                'files' => 0,
                'size' => 0,
                'expired' => 0
            ];
            
            if (is_dir($namespacePath)) {
                $files = glob($namespacePath . '/*.cache');
                
                foreach ($files as $file) {
                    $size = filesize($file);
                    $namespaceStats['files']++;
                    $namespaceStats['size'] += $size;
                    
                    // Check if expired
                    $content = file_get_contents($file);
                    if ($content !== false) {
                        $cacheData = unserialize($content);
                        if ($cacheData !== false && time() > $cacheData['expiry']) {
                            $namespaceStats['expired']++;
                        }
                    }
                }
            }
            
            $stats['namespaces'][$namespace] = $namespaceStats;
            $stats['total_files'] += $namespaceStats['files'];
            $stats['total_size'] += $namespaceStats['size'];
            $stats['expired_files'] += $namespaceStats['expired'];
        }
        
        return $stats;
    }
    
    /**
     * Cache database query results
     */
    public function cacheQuery($sql, $params, $result, $ttl = 300) {
        $key = md5($sql . serialize($params));
        return $this->set($key, $result, $ttl, 'queries');
    }
    
    /**
     * Get cached query result
     */
    public function getCachedQuery($sql, $params) {
        $key = md5($sql . serialize($params));
        return $this->get($key, 'queries');
    }
    
    /**
     * Cache system settings
     */
    public function cacheSystemSettings($settings, $ttl = 1800) {
        return $this->set('system_settings', $settings, $ttl, 'system');
    }
    
    /**
     * Get cached system settings
     */
    public function getCachedSystemSettings() {
        return $this->get('system_settings', 'system');
    }
    
    /**
     * Cache user session data
     */
    public function cacheUserSession($userId, $sessionData, $ttl = 3600) {
        $key = 'user_session_' . $userId;
        return $this->set($key, $sessionData, $ttl, 'sessions');
    }
    
    /**
     * Get cached user session
     */
    public function getCachedUserSession($userId) {
        $key = 'user_session_' . $userId;
        return $this->get($key, 'sessions');
    }
    
    /**
     * Cache view/template output
     */
    public function cacheView($viewKey, $content, $ttl = 600) {
        return $this->set($viewKey, $content, $ttl, 'views');
    }
    
    /**
     * Get cached view content
     */
    public function getCachedView($viewKey) {
        return $this->get($viewKey, 'views');
    }
    
    /**
     * Remember pattern - get from cache or execute callback
     */
    public function remember($key, $callback, $ttl = null, $namespace = 'data') {
        $cached = $this->get($key, $namespace);
        
        if ($cached !== null) {
            return $cached;
        }
        
        $result = call_user_func($callback);
        $this->set($key, $result, $ttl, $namespace);
        
        return $result;
    }
    
    /**
     * Increment cache value (for counters)
     */
    public function increment($key, $value = 1, $namespace = 'data') {
        $current = $this->get($key, $namespace) ?? 0;
        $new = $current + $value;
        $this->set($key, $new, null, $namespace);
        return $new;
    }
    
    /**
     * Decrement cache value
     */
    public function decrement($key, $value = 1, $namespace = 'data') {
        return $this->increment($key, -$value, $namespace);
    }
    
    /**
     * Set default TTL
     */
    public function setDefaultTTL($ttl) {
        $this->defaultTTL = $ttl;
    }
    
    /**
     * Get cache directory size
     */
    public function getCacheSize() {
        $size = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->cacheDir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
        
        return $size;
    }
    
    /**
     * Format bytes to human readable
     */
    public function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
?>