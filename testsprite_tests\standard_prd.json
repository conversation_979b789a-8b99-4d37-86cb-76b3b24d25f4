{"meta": {"project": "Crypto Trading & Investment Platform", "version": "1.0.0", "date": "2025-07-01", "prepared_by": "Generated by TestSprite"}, "product_overview": "A comprehensive PHP-based cryptocurrency trading and investment platform featuring User, Admin, and Super Admin roles designed for security, modularity, and full control over trading operations without external API dependencies.", "core_goals": ["Provide secure user authentication and role-based access control.", "Enable users to manage profiles and track trading investments.", "Offer admin management of users, finances, trading plans, and communication.", "Allow Super Admin configuration of system, security, and appearance.", "Ensure high security with 2FA, CSRF protection, and secure password handling.", "Implement a responsive, themed user interface with dynamic layouts.", "Maintain data integrity with a robust database schema and audit logs."], "key_features": ["Complete user authentication system with registration, login, password reset, email verification, and session management.", "User dashboard displaying balance, bonus, and trading activity with profile management.", "Trading plans with deposit workflow including admin approval and automatic bonus crediting.", "Transaction management with detailed history and withdrawal requests.", "Admin panel for user management, financial approvals, trading plan and asset control, notification, and support ticket handling.", "Super Admin panel for system feature toggles, appearance customization, SMTP/email template management, and audit logs.", "Two-factor authentication (2FA) integration with QR code setup and enforcement controls.", "Comprehensive security framework including input validation, CSRF protection, and session security.", "Email notification system via PHPMailer with customizable templates.", "Responsive Bootstrap-based layouts with dynamic theming and navigation.", "Modular, maintainable codebase adhering to best practices and code size limits."], "user_flow_summary": ["User registers and verifies email (if enabled), then logs in to access a role-specific dashboard.", "User selects a trading plan, makes a deposit, then awaits admin approval for account credit and bonus.", "User manages profile, updates password, and optionally sets up two-factor authentication.", "Admin reviews and processes deposit and withdrawal requests, manages users and trading plans.", "Super Admin configures system-wide settings, SMTP credentials, manages admin accounts, and reviews audit logs.", "System sends automated emails for registration, deposit approval, withdrawal processing, and password resets.", "Users and admins can access support tickets and receive notifications within their dashboards."], "validation_criteria": ["All user authentication flows function correctly including 2FA and email verification.", "Trading plans and deposit workflows accurately process deposits, bonuses, and status changes.", "User and admin interfaces enforce role-based access control preventing unauthorized access.", "Admin panel can manage users, financials, and trading settings as specified.", "Super Admin panel correctly toggles features, updates themes, manages email templates, and logs audits.", "Security measures including CSRF tokens, input sanitization, prepared statements, and password hashing are in place and verified.", "Email notifications are sent on triggered events and can be toggled by Super Admin.", "Responsive layouts render correctly on desktop and mobile devices with dynamic theming applied.", "Database schema supports all entities with foreign keys, constraints, and indexing for performance.", "Comprehensive testing ensures business logic correctness, security, and integration workflows."], "code_summary": {"tech_stack": ["PHP", "MySQL", "Bootstrap", "JavaScript", "PDO", "P<PERSON><PERSON><PERSON><PERSON>", "Font Awesome"], "features": [{"name": "Authentication System", "description": "Complete user authentication with login, registration, password reset, email verification, and session management", "files": ["classes/controllers/AuthController.php", "classes/services/AuthenticationManager.php", "classes/services/SessionManager.php", "classes/services/CSRFProtection.php", "classes/views/LoginView.php", "login.php", "register.php", "forgot-password.php", "reset-password.php", "verify-email.php"]}, {"name": "User Management", "description": "User model with profile management, balance tracking, and role-based access control", "files": ["classes/models/User.php", "classes/services/UserService.php", "classes/validators/RegistrationValidator.php", "classes/validators/LoginValidator.php"]}, {"name": "Financial System", "description": "Deposit, withdrawal, and transaction management with balance tracking and bonus calculations", "files": ["classes/models/Deposit.php", "classes/models/Withdrawal.php", "classes/models/Transaction.php", "classes/services/FinancialService.php", "classes/validators/FinancialValidator.php"]}, {"name": "Trading Plans", "description": "Investment plans management with different returns and durations", "files": ["classes/models/TradingPlan.php"]}, {"name": "Support System", "description": "Ticket-based customer support system", "files": ["classes/models/SupportTicket.php"]}, {"name": "Payment Methods", "description": "Payment method configuration and management", "files": ["classes/models/PaymentMethod.php"]}, {"name": "System Settings", "description": "Dynamic system configuration and feature toggles", "files": ["classes/models/SystemSetting.php", "classes/config/ConfigManager.php"]}, {"name": "Layout System", "description": "Responsive layout templates for different user roles with theming support", "files": ["layouts/user_layout.php", "layouts/admin_layout.php", "layouts/superadmin_layout.php", "layouts/guest_layout.php", "classes/views/BaseView.php", "classes/views/LayoutHelper.php", "classes/views/UserLayoutHelper.php", "classes/services/ThemeManager.php", "assets/css/user-layout.css", "assets/css/responsive.css", "assets/js/user-layout.js"]}, {"name": "Security Framework", "description": "Comprehensive security with input validation, CSRF protection, and sanitization", "files": ["classes/services/SecurityMiddleware.php", "classes/services/InputSanitizer.php", "classes/validators/SecurityValidator.php", "classes/validators/BaseValidator.php", "includes/security_init.php"]}, {"name": "Database Layer", "description": "Database schema, migrations, and base model with Active Record pattern", "files": ["classes/models/BaseModel.php", "database/schema.sql", "database/seed.sql", "database/migrate.php", "database/MigrationLogger.php", "includes/db_connect.php"]}, {"name": "Email System", "description": "Email functionality with SMTP configuration and template support", "files": ["includes/email_functions.php"]}, {"name": "UI Components", "description": "Reusable UI components and navigation elements", "files": ["includes/ui_components.php", "classes/views/components/NavigationComponent.php"]}, {"name": "Testing Framework", "description": "Comprehensive testing suite for authentication, database, models, and security", "files": ["test_folder/test_authentication.php", "test_folder/test_database.php", "test_folder/test_models.php", "test_folder/test_validation_security.php", "test_folder/complete_test.php", "test_folder/verify_setup.php"]}]}}