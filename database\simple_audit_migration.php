<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = getDB();
    
    // Check if audit_logs table exists
    $result = $db->query("SHOW TABLES LIKE 'audit_logs'");
    if ($result->rowCount() == 0) {
        echo "audit_logs table does not exist, creating it...\n";
        
        $createTable = "
        CREATE TABLE audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT,
            action VARCHAR(100) NOT NULL,
            entity_type VARCHAR(50) NOT NULL,
            entity_id VARCHAR(50) NOT NULL,
            changes JSO<PERSON>,
            ip_address VARCHAR(45),
            user_agent TEXT,
            additional_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            
            INDEX idx_user_id (user_id),
            INDEX idx_action (action),
            INDEX idx_entity_type (entity_type),
            INDEX idx_entity_id (entity_id),
            INDEX idx_created_at (created_at),
            INDEX idx_ip_address (ip_address)
        )";
        
        $db->exec($createTable);
        echo "audit_logs table created successfully\n";
    } else {
        echo "audit_logs table exists, checking structure...\n";
        
        // Check if we need to update structure
        $columns = $db->query("DESCRIBE audit_logs")->fetchAll(PDO::FETCH_COLUMN);
        
        if (in_array('table_name', $columns)) {
            echo "Old structure detected, updating...\n";
            
            // Drop old columns and add new ones
            $db->exec("ALTER TABLE audit_logs DROP COLUMN table_name");
            $db->exec("ALTER TABLE audit_logs DROP COLUMN record_id");
            $db->exec("ALTER TABLE audit_logs DROP COLUMN old_values");
            $db->exec("ALTER TABLE audit_logs DROP COLUMN new_values");
            $db->exec("ALTER TABLE audit_logs DROP COLUMN session_id");
            
            $db->exec("ALTER TABLE audit_logs ADD COLUMN entity_type VARCHAR(50) NOT NULL AFTER action");
            $db->exec("ALTER TABLE audit_logs ADD COLUMN entity_id VARCHAR(50) NOT NULL AFTER entity_type");
            $db->exec("ALTER TABLE audit_logs ADD COLUMN changes JSON AFTER entity_id");
            $db->exec("ALTER TABLE audit_logs ADD COLUMN additional_data JSON AFTER user_agent");
            
            // Update indexes
            $db->exec("DROP INDEX idx_table_name ON audit_logs");
            $db->exec("DROP INDEX idx_record_id ON audit_logs");
            $db->exec("CREATE INDEX idx_entity_type ON audit_logs (entity_type)");
            $db->exec("CREATE INDEX idx_entity_id ON audit_logs (entity_id)");
            
            echo "Structure updated successfully\n";
        } else {
            echo "Structure is already up to date\n";
        }
    }
    
    echo "Migration completed successfully\n";
    
} catch (Exception $e) {
    echo "Migration error: " . $e->getMessage() . "\n";
    exit(1);
}
?>