<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../classes/config/ConfigManager.php';

// Ensure user is authenticated and has admin role
if (!isLoggedIn() || !hasRole('admin')) {
    redirectTo('login.php');
}

$config = ConfigManager::getInstance();
$themeColors = $config->get('theme_colors', [
    'primary' => '#007bff',
    'secondary' => '#6c757d',
    'success' => '#28a745',
    'danger' => '#dc3545',
    'warning' => '#ffc107',
    'info' => '#17a2b8',
    'dark' => '#343a40',
    'light' => '#f8f9fa'
]);

$siteName = $config->get('site_name', 'Coinage Trading');
$siteLogo = $config->get('site_logo', getBaseUrl() . 'assets/images/logo.png');
$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - Admin Panel - ' . $siteName : 'Admin Panel - ' . $siteName; ?></title>
    
    <!-- CSRF Token -->
    <?php if (class_exists('CSRFProtection')): ?>
        <meta name="csrf-token" content="<?php echo CSRFProtection::generateToken(); ?>">
    <?php endif; ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: <?php echo $themeColors['primary']; ?>;
            --secondary-color: <?php echo $themeColors['secondary']; ?>;
            --success-color: <?php echo $themeColors['success']; ?>;
            --danger-color: <?php echo $themeColors['danger']; ?>;
            --warning-color: <?php echo $themeColors['warning']; ?>;
            --info-color: <?php echo $themeColors['info']; ?>;
            --dark-color: <?php echo $themeColors['dark']; ?>;
            --light-color: <?php echo $themeColors['light']; ?>;
        }
        
        .sidebar {
            min-height: 100vh;
            background: #ffffff;
            box-shadow: 2px 0 15px rgba(0,0,0,0.08);
            border-right: 1px solid #e9ecef;
        }
        
        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            margin: 2px 8px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid transparent;
        }
        
        .sidebar .nav-link:hover {
            color: var(--primary-color);
            background: rgba(0, 123, 255, 0.08);
            border-color: rgba(0, 123, 255, 0.2);
            transform: translateX(3px);
        }
        
        .sidebar .nav-link.active {
            color: white;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .navbar-brand img {
            height: 40px;
            width: auto;
        }
        
        .admin-badge {
            background: linear-gradient(45deg, var(--warning-color), #fd7e14);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--warning-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .stats-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .pending-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .nav-item {
            position: relative;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -250px;
                width: 250px;
                z-index: 1050;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 1040;
                display: none;
            }
            
            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>
    
    <?php if (isset($additionalCSS)): ?>
        <?php echo $additionalCSS; ?>
    <?php endif; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <!-- Logo -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="img-fluid" style="max-height: 60px;">
                        <h5 class="text-dark mt-2 mb-1"><?php echo $siteName; ?></h5>
                        <div class="admin-badge">ADMIN PANEL</div>
                    </div>
                    
                    <!-- User Info -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <div class="user-avatar mx-auto mb-2">
                            <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                        </div>
                        <small class="text-muted">Admin:</small>
                        <div class="text-dark fw-bold"><?php echo htmlspecialchars($user['first_name']); ?></div>
                    </div>
                    
                    <!-- Navigation -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/dashboard') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/dashboard/'); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/users') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/users/'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/deposits') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/deposits/'); ?>">
                                <i class="fas fa-plus-circle me-2"></i>
                                Deposits
                                <span class="pending-badge" id="pendingDeposits" style="display: none;"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/withdrawals') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/withdrawals/'); ?>">
                                <i class="fas fa-minus-circle me-2"></i>
                                Withdrawals
                                <span class="pending-badge" id="pendingWithdrawals" style="display: none;"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/payment-methods') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/payment-methods/'); ?>">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Methods
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/plans') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/plans/'); ?>">
                                <i class="fas fa-chart-line me-2"></i>
                                Trading Plans
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/transactions') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/transactions/'); ?>">
                                <i class="fas fa-history me-2"></i>
                                Transactions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/support') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/support/'); ?>">
                                <i class="fas fa-headset me-2"></i>
                                Support
                                <span class="pending-badge" id="pendingTickets" style="display: none;"></span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/reports') !== false) ? 'active' : ''; ?>" href="<?php echo url('admin/reports/'); ?>">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="<?php echo url('logout.php'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top navbar for mobile -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom d-md-none">
                    <button class="btn btn-outline-primary" type="button" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="h4 mb-0"><?php echo isset($pageTitle) ? $pageTitle : 'Admin Dashboard'; ?></h1>
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                    </div>
                </div>
                
                <!-- Page content -->
                <div class="content-wrapper">
                    <?php if (isset($content)): ?>
                        <?php echo $content; ?>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                });
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                });
            }
            
            // Load pending counts
            loadPendingCounts();
        });
        
        // Load pending items count
        function loadPendingCounts() {
            fetch('<?php echo getBaseUrl(); ?>admin/api/pending-counts.php')
                .then(response => response.json())
                .then(data => {
                    if (data.deposits > 0) {
                        document.getElementById('pendingDeposits').textContent = data.deposits;
                        document.getElementById('pendingDeposits').style.display = 'flex';
                    }
                    if (data.withdrawals > 0) {
                        document.getElementById('pendingWithdrawals').textContent = data.withdrawals;
                        document.getElementById('pendingWithdrawals').style.display = 'flex';
                    }
                    if (data.tickets > 0) {
                        document.getElementById('pendingTickets').textContent = data.tickets;
                        document.getElementById('pendingTickets').style.display = 'flex';
                    }
                })
                .catch(error => console.error('Error loading pending counts:', error));
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Refresh pending counts every 30 seconds
        setInterval(loadPendingCounts, 30000);
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>