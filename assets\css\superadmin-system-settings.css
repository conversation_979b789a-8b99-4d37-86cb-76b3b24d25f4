/* Super Admin System Settings Styles */

.system-settings-container {
    max-width: 100%;
}

/* Tab Navigation */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
    font-weight: 600;
}

/* Card Styling */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

/* Form Elements */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Switch Styling */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

.form-switch .form-check-input {
    width: 2em;
    height: 1em;
}

.form-switch .form-check-label {
    padding-left: 0.5rem;
}

/* Color Picker Styling */
.form-control-color {
    width: 3rem;
    height: calc(2.25rem + 2px);
    border-radius: 0.375rem 0 0 0.375rem;
}

.color-preview-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.color-preview {
    text-align: center;
}

.preview-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

#primary-preview {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

#secondary-preview {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Logo Preview */
.current-logo {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.logo-preview {
    text-align: center;
}

.logo-preview img {
    max-width: 200px;
    height: auto;
}

/* System Info Cards */
.info-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.info-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.info-value {
    font-size: 1rem;
    color: #495057;
    font-weight: 600;
}

/* Button Styling */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Alert Styling */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    .nav-tabs .nav-link i {
        display: none;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .preview-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
    }
    
    .d-flex.gap-2 .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Tab Content Animation */
.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Validation States */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Email Templates Section */
.email-templates-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1.5rem;
}

/* System Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.online {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-indicator.offline {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-indicator.maintenance {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Hover Effects */
.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.15s ease-in-out;
}

/* Focus States */
.form-control:focus,
.form-select:focus,
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Custom Scrollbar */
.tab-content {
    max-height: 80vh;
    overflow-y: auto;
}

.tab-content::-webkit-scrollbar {
    width: 6px;
}

.tab-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}