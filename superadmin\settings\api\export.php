<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../classes/models/SystemSetting.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

try {
    // Get all system settings
    $settings = SystemSetting::getAll();
    
    // Prepare export data
    $exportData = [
        'export_info' => [
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'exported_by' => getCurrentUser()['email']
        ],
        'settings' => []
    ];
    
    // Group settings by category
    foreach ($settings as $setting) {
        $category = $setting->category ?? 'general';
        if (!isset($exportData['settings'][$category])) {
            $exportData['settings'][$category] = [];
        }
        
        $exportData['settings'][$category][$setting->setting_key] = [
            'value' => $setting->setting_value,
            'type' => $setting->value_type,
            'description' => $setting->description
        ];
    }
    
    // Set headers for file download
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="system-settings-' . date('Y-m-d') . '.json"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    echo json_encode($exportData, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Export failed: ' . $e->getMessage()
    ]);
}
?>