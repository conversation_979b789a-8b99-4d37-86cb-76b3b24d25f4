<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $assetId = intval($_GET['id'] ?? 0);
    
    if (!$assetId) {
        echo json_encode(['success' => false, 'message' => 'Asset ID is required']);
        exit;
    }
    
    // Load the asset
    $stmt = $pdo->prepare("SELECT * FROM trading_assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();
    
    if (!$asset) {
        echo json_encode(['success' => false, 'message' => 'Asset not found']);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'asset' => $asset
    ]);
    
} catch (Exception $e) {
    error_log("Get asset details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading asset details'
    ]);
}
?>