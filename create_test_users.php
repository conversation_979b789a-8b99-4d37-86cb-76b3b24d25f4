<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

try {
    $db = getDB();
    
    // Create a regular user (password: user123)
    $userPassword = hashPassword('user123');
    $stmt = $db->prepare('INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, status, email_verified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)');
    $result1 = $stmt->execute(['testuser', '<EMAIL>', $userPassword, 'Test', 'User', 'user', 'active', 1]);
    
    if ($result1) {
        echo "✅ Regular user created successfully!\n";
        echo "Username: testuser\n";
        echo "Password: user123\n";
        echo "Role: user\n\n";
    }
    
    // Verify superadmin exists
    $stmt2 = $db->prepare('SELECT * FROM users WHERE username = ?');
    $stmt2->execute(['superadmin']);
    $superadmin = $stmt2->fetch();
    
    if ($superadmin) {
        echo "✅ Super admin exists!\n";
        echo "Username: superadmin\n";
        echo "Password: admin123\n";
        echo "Role: " . $superadmin['role'] . "\n\n";
    }
    
    // Verify admin exists
    $stmt3 = $db->prepare('SELECT * FROM users WHERE username = ?');
    $stmt3->execute(['admin']);
    $admin = $stmt3->fetch();
    
    if ($admin) {
        echo "✅ Admin exists!\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
        echo "Role: " . $admin['role'] . "\n\n";
    }
    
    // List all users
    echo "=== ALL USERS IN DATABASE ===\n";
    $stmt4 = $db->query("SELECT id, username, email, role, status FROM users ORDER BY id");
    $users = $stmt4->fetchAll();
    
    foreach ($users as $user) {
        echo "ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Role: {$user['role']}, Status: {$user['status']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>