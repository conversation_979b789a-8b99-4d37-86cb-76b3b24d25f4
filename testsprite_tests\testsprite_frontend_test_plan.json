[{"id": "TC001", "title": "User Registration with Valid Data", "description": "Verify that a new user can successfully register with valid inputs and receives an email verification if enabled.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the registration page"}, {"type": "action", "description": "Enter valid username, email, password, and other required fields"}, {"type": "action", "description": "Submit the registration form"}, {"type": "assertion", "description": "Confirm registration is successful and confirmation email is sent if email verification is enabled"}]}, {"id": "TC002", "title": "User Registration with Invalid Email", "description": "Verify registration fails when an invalid email format is provided.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Navigate to the registration page"}, {"type": "action", "description": "Fill the registration form with invalid email format"}, {"type": "action", "description": "Submit the registration form"}, {"type": "assertion", "description": "Verify validation error message for invalid email is displayed and registration is blocked"}]}, {"id": "TC003", "title": "Email Verification Enforcement", "description": "Ensure a user cannot log in before verifying the email when email verification is enabled.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Register a new user with valid information"}, {"type": "action", "description": "Attempt to log in before email verification"}, {"type": "assertion", "description": "Login attempt should fail with a message prompting email verification"}, {"type": "action", "description": "Complete email verification via the emailed link"}, {"type": "action", "description": "Attempt to log in again"}, {"type": "assertion", "description": "Login should be successful after email is verified"}]}, {"id": "TC004", "title": "Login with Correct Credentials", "description": "Verify that users can log in using valid username/email and correct password.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Navigate to login page"}, {"type": "action", "description": "Enter valid username/email and correct password"}, {"type": "action", "description": "Click login button"}, {"type": "assertion", "description": "Check login is successful and user is redirected to the appropriate dashboard based on role"}]}, {"id": "TC005", "title": "Login with Incorrect Credentials", "description": "Verify login failure with incorrect password or username/email.", "category": "error handling", "priority": "High", "steps": [{"type": "action", "description": "Navigate to login page"}, {"type": "action", "description": "Enter valid username/email and incorrect password"}, {"type": "action", "description": "Click login button"}, {"type": "assertion", "description": "Verify that login fails with a proper error message"}]}, {"id": "TC006", "title": "Two-Factor Authentication (2FA) Setup and Enforcement", "description": "Ensure users can enable 2FA with QR code setup, and login requires 2FA code when enabled.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Log in as user and navigate to 2FA setup section"}, {"type": "action", "description": "Enable 2FA and scan the QR code with authenticator app"}, {"type": "action", "description": "Enter the generated 2FA code to confirm setup"}, {"type": "assertion", "description": "Verify 2FA is enabled and saved for the user"}, {"type": "action", "description": "Log out and attempt to log in again"}, {"type": "action", "description": "After valid credentials, enter incorrect 2FA code"}, {"type": "assertion", "description": "Login should be blocked with 2FA error"}, {"type": "action", "description": "Retry login and enter correct 2FA code"}, {"type": "assertion", "description": "<PERSON><PERSON> is successful"}]}, {"id": "TC007", "title": "User Profile Update", "description": "Verify users can successfully update their profile details including password.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Login as a user and navigate to profile management"}, {"type": "action", "description": "Update profile fields such as display name and email"}, {"type": "action", "description": "Change password with current and new password inputs"}, {"type": "action", "description": "Submit profile update"}, {"type": "assertion", "description": "Confirm profile updates are saved and password is changed"}, {"type": "action", "description": "Logout and login again using the new password"}, {"type": "assertion", "description": "Verify login is successful with updated password"}]}, {"id": "TC008", "title": "Deposit Workflow with Admin Approval and Bonus Crediting", "description": "Verify deposit requests by users are recorded, admins can approve deposits, and bonuses are credited automatically.", "category": "integration", "priority": "High", "steps": [{"type": "action", "description": "Login as user and select a trading plan"}, {"type": "action", "description": "Make a deposit request associated with the chosen trading plan"}, {"type": "assertion", "description": "Verify deposit request is created with pending status"}, {"type": "action", "description": "Login as admin and navigate to deposit approval section"}, {"type": "action", "description": "Approve the user's deposit request"}, {"type": "assertion", "description": "Check user's balance is credited with deposit amount and bonus as per trading plan"}, {"type": "assertion", "description": "Deposit request status changes to approved"}, {"type": "assertion", "description": "Verify a notification email is sent to the user about deposit approval"}]}, {"id": "TC009", "title": "Withdrawal Request Processing and Status Update", "description": "Verify users can create withdrawal requests and admins can process and update their status.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Login as user and request withdrawal of valid amount"}, {"type": "assertion", "description": "Verify withdrawal request is created with pending status"}, {"type": "action", "description": "<PERSON>gin as admin and review the withdrawal request"}, {"type": "action", "description": "Approve or reject the withdrawal request"}, {"type": "assertion", "description": "User balance is updated accordingly if approved"}, {"type": "assertion", "description": "Withdrawal request status changes correctly to approved or rejected"}, {"type": "assertion", "description": "User receives appropriate email notification about withdrawal status"}]}, {"id": "TC010", "title": "Role-Based Access Control Verification", "description": "Ensure that users, admins, and super admins can access only the features permitted to their roles, with unauthorized access blocked.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Login as a regular user and attempt to access admin or super admin pages"}, {"type": "assertion", "description": "Access should be denied with proper message or redirect"}, {"type": "action", "description": "Login as admin and verify access to user management, finances, trading plans but not to super admin settings"}, {"type": "assertion", "description": "Admin can access allowed pages and restricted from super admin features"}, {"type": "action", "description": "Login as super admin and verify access to all system configuration and audit log pages"}, {"type": "assertion", "description": "Super admin has full unrestricted access"}]}, {"id": "TC011", "title": "Super Admin Feature Toggles and Theme Customization", "description": "Verify that Super Admin can toggle system features on/off, update UI themes, and their changes affect user interfaces accordingly.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Login as Super Admin and navigate to system settings panel"}, {"type": "action", "description": "Toggle a configurable feature (e.g., enable/disable deposits)"}, {"type": "assertion", "description": "Confirm that the toggle is saved and enforced in the system (e.g., deposits disabled for users)"}, {"type": "action", "description": "Modify appearance settings such as theme colors or layout options"}, {"type": "assertion", "description": "Verify that UI for users, admins, and super admins reflect updated theme dynamically"}]}, {"id": "TC012", "title": "CSRF Protection Enforcement on Form Submissions", "description": "Verify all forms include CSRF tokens and submissions with missing/invalid tokens are rejected.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Navigate to forms such as login, registration, deposit, and profile update"}, {"type": "assertion", "description": "Confirm that each form includes a valid CSRF token"}, {"type": "action", "description": "Attempt to submit form with missing or tampered CSRF token"}, {"type": "assertion", "description": "Verify submission is blocked and error message is shown"}]}, {"id": "TC013", "title": "Input Validation and Sanitization Checks", "description": "Verify input fields across the platform validate inputs correctly and reject or sanitize malicious inputs.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Attempt to inject SQL, XSS payloads, and invalid formats into input fields in registration, login, profile update, trading plans, and admin panels"}, {"type": "assertion", "description": "Verify system rejects invalid inputs or sanitizes them preventing injection and scripting attacks"}]}, {"id": "TC014", "title": "Email Notifications Send on Triggered Events", "description": "Verify that email notifications are sent for registration, deposit approval, withdrawal processing, password resets, and that Super Admin toggle controls email sending.", "category": "integration", "priority": "Medium", "steps": [{"type": "action", "description": "Register new user and verify registration confirmation email is received"}, {"type": "action", "description": "Submit deposit request, approve as admin, and verify deposit approval email is sent"}, {"type": "action", "description": "Submit withdrawal request, process as admin, and verify withdrawal status email is sent"}, {"type": "action", "description": "Perform password reset and verify password reset email is sent"}, {"type": "action", "description": "Login as super admin and disable email notifications via settings"}, {"type": "action", "description": "Trigger an email event again and verify that no email is sent"}]}, {"id": "TC015", "title": "Responsive Layout and Theming on Various Devices", "description": "Verify that UI layouts for users, admins, super admins, and guests render correctly on desktop, tablet, and mobile devices with dynamic theme applied.", "category": "ui", "priority": "Medium", "steps": [{"type": "action", "description": "Access the platform as each role on desktop, tablet, and mobile screen sizes"}, {"type": "assertion", "description": "Verify that navigation, content, and controls adjust responsively and remain usable"}, {"type": "action", "description": "Change theme settings as super admin"}, {"type": "assertion", "description": "Verify that the new theme colors and styles are applied properly across all devices"}]}, {"id": "TC016", "title": "Database Integrity Checks for Foreign Keys and Constraints", "description": "Verify that database schema enforces foreign key relationships, unique constraints, and indexes for all entities and that data manipulations respect those constraints.", "category": "functional", "priority": "High", "steps": [{"type": "action", "description": "Attempt to insert, update, or delete records violating foreign key constraints for users, deposits, transactions, and trading plans"}, {"type": "assertion", "description": "Verify database rejects invalid operations and returns appropriate errors"}, {"type": "action", "description": "Insert valid related records and verify data integrity and performance with indexing"}]}, {"id": "TC017", "title": "Audit Log Recording and Access by Super Admin", "description": "Verify that critical system actions are logged in audit logs and super admin can review logs via the panel.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Perform critical actions such as user creation, deposit approval, system settings changes"}, {"type": "action", "description": "Login as super admin and navigate to audit logs section"}, {"type": "assertion", "description": "Verify recent critical actions are recorded with correct details and timestamps"}]}, {"id": "TC018", "title": "Support Ticket Submission and Management", "description": "Verify users can create support tickets and admins can view, respond, and resolve tickets within the admin panel.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "<PERSON><PERSON> as user and submit a new support ticket with subject and message"}, {"type": "assertion", "description": "Verify ticket is created and visible in user's dashboard"}, {"type": "action", "description": "<PERSON>gin as admin and access support ticket management panel"}, {"type": "action", "description": "View user's ticket, post a response, and mark it resolved"}, {"type": "assertion", "description": "Verify ticket status updates and user sees admin responses in their dashboard"}]}, {"id": "TC019", "title": "Session and Cookie Security Validation", "description": "Verify that user sessions are managed securely with proper expiration, regeneration on login, and cookies are set with secure flags.", "category": "security", "priority": "High", "steps": [{"type": "action", "description": "Log in as user and inspect session behavior"}, {"type": "assertion", "description": "Verify session ID is regenerated upon login and logout destroys session"}, {"type": "assertion", "description": "Check cookies have HttpOnly and Secure flags when appropriate"}, {"type": "action", "description": "Attempt session fixation or hijacking attempts"}, {"type": "assertion", "description": "Verify session security measures prevent attack"}]}, {"id": "TC020", "title": "Logout Functionality and Session Termination", "description": "Verify that users can logout properly and their sessions are terminated immediately.", "category": "functional", "priority": "Medium", "steps": [{"type": "action", "description": "Login as user and navigate to logout"}, {"type": "action", "description": "Perform logout"}, {"type": "assertion", "description": "Verify user is redirected to guest view and session is destroyed"}, {"type": "action", "description": "Navigate back to a protected page"}, {"type": "assertion", "description": "Verify access is denied or redirected to login"}]}]