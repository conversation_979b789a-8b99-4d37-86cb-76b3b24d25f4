<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Deposit.php';
require_once __DIR__ . '/../models/Withdrawal.php';
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../../includes/email_functions.php';

/**
 * Notification Service
 * Handles all email notifications for the platform
 */
class NotificationService {
    
    /**
     * Send deposit approval notification
     */
    public static function sendDepositApprovalNotification($user, $deposit) {
        $subject = "Deposit Approved - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #28a745;'>Deposit Approved!</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>Great news! Your deposit has been approved and your account has been credited.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Deposit Details</h3>
                <p><strong>Amount:</strong> $" . number_format($deposit->amount, 2) . "</p>
                " . ($deposit->bonus_amount > 0 ? "<p><strong>Bonus:</strong> $" . number_format($deposit->bonus_amount, 2) . "</p>" : "") . "
                <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($deposit->approved_at)) . "</p>
                <p><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>Approved</span></p>
            </div>
            
            <p>You can now start trading with your deposited funds. Visit your dashboard to view your updated balance and explore available trading plans.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/dashboard/' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   View Dashboard
                </a>
            </div>
            
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
            
            <p>Happy Trading!<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send deposit rejection notification
     */
    public static function sendDepositRejectionNotification($user, $deposit, $reason) {
        $subject = "Deposit Update - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #dc3545;'>Deposit Update</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>We regret to inform you that your recent deposit could not be processed.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Deposit Details</h3>
                <p><strong>Amount:</strong> $" . number_format($deposit->amount, 2) . "</p>
                <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($deposit->created_at)) . "</p>
                <p><strong>Status:</strong> <span style='color: #dc3545; font-weight: bold;'>Not Processed</span></p>
            </div>
            
            <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h4 style='margin-top: 0; color: #856404;'>Reason:</h4>
                <p style='margin-bottom: 0; color: #856404;'>{$reason}</p>
            </div>
            
            <p>Please review the reason above and feel free to submit a new deposit request with the correct information. If you need assistance or have questions about this decision, our support team is here to help.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/deposit/process.php' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>
                   Make New Deposit
                </a>
                <a href='" . self::getBaseUrl() . "/user/support/' 
                   style='background: #6c757d; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   Contact Support
                </a>
            </div>
            
            <p>Thank you for your understanding.</p>
            
            <p>Best regards,<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send withdrawal approval notification
     */
    public static function sendWithdrawalApprovalNotification($user, $withdrawal) {
        $subject = "Withdrawal Approved - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #28a745;'>Withdrawal Approved!</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>Great news! Your withdrawal request has been approved.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Withdrawal Details</h3>
                <p><strong>Amount:</strong> $" . number_format($withdrawal->amount, 2) . "</p>
                <p><strong>Method:</strong> " . htmlspecialchars($withdrawal->withdrawal_method) . "</p>
                <p><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>Approved</span></p>
            </div>
            
            <p>Your withdrawal will be processed shortly and funds will be transferred to your specified account.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/withdraw/status.php' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   Track Status
                </a>
            </div>
            
            <p>Thank you for using our platform!</p>
            
            <p>Best regards,<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send withdrawal rejection notification
     */
    public static function sendWithdrawalRejectionNotification($user, $withdrawal, $reason) {
        $subject = "Withdrawal Update - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #dc3545;'>Withdrawal Update</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>We regret to inform you that your withdrawal request could not be processed.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Withdrawal Details</h3>
                <p><strong>Amount:</strong> $" . number_format($withdrawal->amount, 2) . "</p>
                <p><strong>Method:</strong> " . htmlspecialchars($withdrawal->withdrawal_method) . "</p>
                <p><strong>Status:</strong> <span style='color: #dc3545; font-weight: bold;'>Rejected</span></p>
            </div>
            
            <div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                <h4 style='margin-top: 0; color: #856404;'>Reason:</h4>
                <p style='margin-bottom: 0; color: #856404;'>{$reason}</p>
            </div>
            
            <p>Your account balance has been restored. Please review the reason above and feel free to submit a new withdrawal request with the correct information.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/withdraw/request.php' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>
                   New Withdrawal
                </a>
                <a href='" . self::getBaseUrl() . "/user/support/' 
                   style='background: #6c757d; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   Contact Support
                </a>
            </div>
            
            <p>If you have any questions, please don't hesitate to contact our support team.</p>
            
            <p>Best regards,<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send withdrawal processing notification
     */
    public static function sendWithdrawalProcessingNotification($user, $withdrawal) {
        $subject = "Withdrawal Processing - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #007bff;'>Withdrawal Being Processed</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>Your withdrawal request is now being processed by our team.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Withdrawal Details</h3>
                <p><strong>Amount:</strong> $" . number_format($withdrawal->amount, 2) . "</p>
                <p><strong>Method:</strong> " . htmlspecialchars($withdrawal->withdrawal_method) . "</p>
                <p><strong>Status:</strong> <span style='color: #007bff; font-weight: bold;'>Processing</span></p>
            </div>
            
            <p>Your funds will be transferred to your specified account within 1-3 business days. You will receive another notification once the transfer is completed.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/withdraw/status.php' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   Track Status
                </a>
            </div>
            
            <p>If you have any questions, please contact our support team.</p>
            
            <p>Best regards,<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send withdrawal completion notification
     */
    public static function sendWithdrawalCompletionNotification($user, $withdrawal) {
        $subject = "Withdrawal Completed - Coinage Trading";
        
        $message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #28a745;'>Withdrawal Completed!</h2>
            
            <p>Dear {$user->first_name},</p>
            
            <p>Your withdrawal has been successfully completed and the funds have been transferred to your account.</p>
            
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h3 style='margin-top: 0; color: #333;'>Withdrawal Details</h3>
                <p><strong>Amount:</strong> $" . number_format($withdrawal->amount, 2) . "</p>
                <p><strong>Method:</strong> " . htmlspecialchars($withdrawal->withdrawal_method) . "</p>
                <p><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>Completed</span></p>
                <p><strong>Completed:</strong> " . date('F j, Y g:i A', strtotime($withdrawal->completed_at)) . "</p>
            </div>
            
            <p>Please allow 1-3 business days for the funds to appear in your account, depending on your bank's processing time.</p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='" . self::getBaseUrl() . "/user/transactions/' 
                   style='background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;'>
                   View Transactions
                </a>
            </div>
            
            <p>Thank you for using our platform!</p>
            
            <p>Best regards,<br>
            The Coinage Trading Team</p>
        </div>";
        
        return self::sendEmail($user->email, $subject, $message);
    }
    
    /**
     * Send email using the platform's email system
     */
    private static function sendEmail($to, $subject, $message) {
        try {
            return sendEmail($to, $subject, $message);
        } catch (Exception $e) {
            error_log("Notification email error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get base URL for email links
     */
    private static function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host;
    }
}
?>