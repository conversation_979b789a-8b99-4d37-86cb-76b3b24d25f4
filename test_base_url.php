<?php
require_once 'includes/functions.php';

echo "<h2>Base URL Test</h2>";

echo "<h3>Current Environment:</h3>";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "<br>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "<br>";

echo "<h3>Base URL Results:</h3>";
echo "getBaseUrl(): " . getBaseUrl() . "<br>";

echo "<h3>Test URLs:</h3>";
echo "url('admin/dashboard/'): " . url('admin/dashboard/') . "<br>";
echo "url('superadmin/dashboard/'): " . url('superadmin/dashboard/') . "<br>";
echo "url('user/dashboard/'): " . url('user/dashboard/') . "<br>";

echo "<h3>Test from different directories:</h3>";
// Simulate being in admin directory
$_SERVER['SCRIPT_NAME'] = '/Coinage/admin/login.php';
echo "From /admin/login.php - getBaseUrl(): " . getBaseUrl() . "<br>";

// Simulate being in superadmin directory  
$_SERVER['SCRIPT_NAME'] = '/Coinage/superadmin/login.php';
echo "From /superadmin/login.php - getBaseUrl(): " . getBaseUrl() . "<br>";

// Simulate being in user directory
$_SERVER['SCRIPT_NAME'] = '/Coinage/user/login.php';
echo "From /user/login.php - getBaseUrl(): " . getBaseUrl() . "<br>";

// Simulate being in root
$_SERVER['SCRIPT_NAME'] = '/Coinage/login.php';
echo "From /login.php - getBaseUrl(): " . getBaseUrl() . "<br>";
?>