<!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Test Results - 2025-07-27 19:48:54</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
            <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
            <style>
                .test-pass { color: #28a745; }
                .test-fail { color: #dc3545; }
                .test-skip { color: #ffc107; }
                .test-error { color: #fd7e14; }
                .suite-header { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .test-item { padding: 10px; border-left: 4px solid #e9ecef; margin: 5px 0; }
                .test-item.pass { border-left-color: #28a745; }
                .test-item.fail { border-left-color: #dc3545; }
                .test-item.skip { border-left-color: #ffc107; }
                .test-item.error { border-left-color: #fd7e14; }
            </style>
        </head>
        <body class='bg-light'>
            <div class='container mt-4'>
                <h1><i class='fas fa-vial me-2'></i>Test Results</h1>
                
                <div class='row mb-4'>
                    <div class='col-md-3'>
                        <div class='card bg-success text-white'>
                            <div class='card-body text-center'>
                                <h3>10</h3>
                                <p class='mb-0'>Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-danger text-white'>
                            <div class='card-body text-center'>
                                <h3>0</h3>
                                <p class='mb-0'>Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-warning text-white'>
                            <div class='card-body text-center'>
                                <h3>0</h3>
                                <p class='mb-0'>Skipped</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-info text-white'>
                            <div class='card-body text-center'>
                                <h3>0</h3>
                                <p class='mb-0'>Errors</p>
                            </div>
                        </div>
                    </div>
                </div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Error Handling Basic Tests (system)</h4>
                    <small class='text-muted'>
                        Passed: 6 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Error Handler Initialization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.14ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Application Error Logging</strong>
                    <span class='float-end'>
                        <small class='text-muted'>37.54ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Database Error Handling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>29.87ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Authentication Error Handling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>32.45ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>File Upload Error Handling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>221.15ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Validation Error Handling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>19.66ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>System Health Basic Tests (system)</h4>
                    <small class='text-muted'>
                        Passed: 2 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Health Service Initialization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.07ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Basic Health Check</strong>
                    <span class='float-end'>
                        <small class='text-muted'>744.38ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Testing Framework Tests (unit)</h4>
                    <small class='text-muted'>
                        Passed: 2 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Test Context Assertions</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.21ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Test Exception Handling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.08ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div></div>
        <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
        </body>
        </html>