<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/models/Deposit.php';
require_once '../../../classes/models/User.php';
require_once '../../../classes/models/Transaction.php';
require_once '../../../classes/services/TransactionManager.php';
require_once '../../../classes/services/NotificationService.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Verify CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit();
}

try {
    $depositId = (int)($_POST['deposit_id'] ?? 0);
    $bonusAmount = isset($_POST['bonus_amount']) && $_POST['bonus_amount'] !== '' ? 
                   (float)$_POST['bonus_amount'] : null;
    $adminNote = trim($_POST['admin_note'] ?? '');
    
    if ($depositId <= 0) {
        throw new Exception('Invalid deposit ID');
    }
    
    // Get deposit details
    $deposit = Deposit::findById($depositId);
    if (!$deposit) {
        throw new Exception('Deposit not found');
    }
    
    if ($deposit->status !== 'pending') {
        throw new Exception('Deposit is not in pending status');
    }
    
    // Get user details
    $user = User::findById($deposit->user_id);
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Calculate bonus if not provided
    if ($bonusAmount === null) {
        $bonusAmount = $deposit->calculateBonus();
    }
    
    // Start database transaction
    $pdo = Database::getConnection();
    $pdo->beginTransaction();
    
    try {
        // Update deposit status
        $deposit->status = 'approved';
        $deposit->approved_at = date('Y-m-d H:i:s');
        $deposit->approved_by = $_SESSION['user_id'];
        $deposit->bonus_amount = $bonusAmount;
        $deposit->admin_note = $adminNote;
        
        if (!$deposit->save()) {
            throw new Exception('Failed to update deposit status');
        }
        
        // Update user balance
        $user->balance += $deposit->amount;
        if ($bonusAmount > 0) {
            $user->bonus += $bonusAmount;
        }
        
        if (!$user->save()) {
            throw new Exception('Failed to update user balance');
        }
        
        // Create transaction records
        $transactionManager = new TransactionManager();
        
        // Main deposit transaction
        $depositTransaction = $transactionManager->createTransaction([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => $deposit->amount,
            'status' => 'completed',
            'reference_id' => $deposit->id,
            'reference_type' => 'deposit',
            'description' => 'Deposit approved - ' . ($deposit->payment_method_name ?? 'Payment'),
            'admin_id' => $_SESSION['user_id']
        ]);
        
        // Bonus transaction if applicable
        if ($bonusAmount > 0) {
            $bonusTransaction = $transactionManager->createTransaction([
                'user_id' => $user->id,
                'type' => 'bonus',
                'amount' => $bonusAmount,
                'status' => 'completed',
                'reference_id' => $deposit->id,
                'reference_type' => 'deposit_bonus',
                'description' => 'Deposit bonus - ' . number_format($bonusAmount, 2),
                'admin_id' => $_SESSION['user_id']
            ]);
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Send notification email
        try {
            $notificationService = new NotificationService();
            $notificationService->sendDepositApprovalNotification($user, $deposit);
        } catch (Exception $e) {
            error_log("Failed to send deposit approval notification: " . $e->getMessage());
        }
        
        // Log admin action
        error_log("Admin {$_SESSION['user_id']} approved deposit {$depositId} for user {$user->id}");
        
        echo json_encode([
            'success' => true,
            'message' => 'Deposit approved successfully',
            'data' => [
                'deposit_id' => $deposit->id,
                'amount' => $deposit->amount,
                'bonus_amount' => $bonusAmount,
                'new_balance' => $user->balance,
                'new_bonus' => $user->bonus
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Deposit approval error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>