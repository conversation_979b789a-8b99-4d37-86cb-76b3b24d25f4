<?php
require_once __DIR__ . '/ValidationHelper.php';

/**
 * Financial Operations Validator
 * Handles validation for deposits, withdrawals, and trading operations
 */
class FinancialValidator {
    private $errors = [];
    
    /**
     * Validate deposit form data
     */
    public function validateDeposit($data) {
        $this->errors = [];
        
        $this->validateAmount($data['amount'] ?? '', 'deposit');
        $this->validatePaymentMethod($data['payment_method'] ?? '');
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate withdrawal form data
     */
    public function validateWithdrawal($data, $userBalance = 0) {
        $this->errors = [];
        
        $this->validateAmount($data['amount'] ?? '', 'withdrawal');
        $this->validateWithdrawalMethod($data['withdrawal_method'] ?? '');
        $this->validateAccountDetails($data);
        $this->validateSufficientBalance($data['amount'] ?? 0, $userBalance);
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate trading plan investment
     */
    public function validateInvestment($data, $userBalance = 0, $planMinAmount = 0, $planMaxAmount = null) {
        $this->errors = [];
        
        $this->validateInvestmentAmount($data['amount'] ?? '', $planMinAmount, $planMaxAmount);
        $this->validateTradingPlan($data['plan_id'] ?? '');
        $this->validateSufficientBalance($data['amount'] ?? 0, $userBalance);
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : '';
    }
    
    /**
     * Validate amount for financial operations
     */
    private function validateAmount($amount, $operation = 'transaction') {
        $minAmount = $operation === 'deposit' ? 10.00 : 5.00;
        $maxAmount = $operation === 'deposit' ? 100000.00 : 50000.00;
        
        $error = ValidationHelper::validateAmount($amount, $minAmount, $maxAmount);
        if ($error) {
            $this->errors['amount'] = $error;
            return;
        }
        
        // Additional validation for decimal places
        if (strpos($amount, '.') !== false) {
            $decimalPlaces = strlen(substr(strrchr($amount, '.'), 1));
            if ($decimalPlaces > 2) {
                $this->errors['amount'] = 'Amount cannot have more than 2 decimal places.';
            }
        }
    }
    
    /**
     * Validate investment amount with plan limits
     */
    private function validateInvestmentAmount($amount, $minAmount, $maxAmount = null) {
        $error = ValidationHelper::validateAmount($amount, $minAmount, $maxAmount);
        if ($error) {
            $this->errors['amount'] = $error;
            return;
        }
        
        // Check plan-specific limits
        if ($amount < $minAmount) {
            $this->errors['amount'] = "Minimum investment amount is $" . number_format($minAmount, 2);
        }
        
        if ($maxAmount && $amount > $maxAmount) {
            $this->errors['amount'] = "Maximum investment amount is $" . number_format($maxAmount, 2);
        }
    }
    
    /**
     * Validate payment method
     */
    private function validatePaymentMethod($paymentMethod) {
        $allowedMethods = ['bank_transfer', 'credit_card', 'paypal', 'cryptocurrency'];
        
        $error = ValidationHelper::validateEnum($paymentMethod, $allowedMethods, 'Payment method');
        if ($error) {
            $this->errors['payment_method'] = $error;
        }
    }
    
    /**
     * Validate withdrawal method
     */
    private function validateWithdrawalMethod($withdrawalMethod) {
        $allowedMethods = ['bank_transfer', 'paypal', 'cryptocurrency'];
        
        $error = ValidationHelper::validateEnum($withdrawalMethod, $allowedMethods, 'Withdrawal method');
        if ($error) {
            $this->errors['withdrawal_method'] = $error;
        }
    }
    
    /**
     * Validate account details for withdrawal
     */
    private function validateAccountDetails($data) {
        $method = $data['withdrawal_method'] ?? '';
        
        switch ($method) {
            case 'bank_transfer':
                $this->validateBankDetails($data);
                break;
            case 'paypal':
                $this->validatePayPalDetails($data);
                break;
            case 'cryptocurrency':
                $this->validateCryptoDetails($data);
                break;
        }
    }
    
    /**
     * Validate bank account details
     */
    private function validateBankDetails($data) {
        if (empty($data['bank_name'])) {
            $this->errors['bank_name'] = 'Bank name is required.';
        }
        
        if (empty($data['account_number'])) {
            $this->errors['account_number'] = 'Account number is required.';
        } elseif (!preg_match('/^[0-9]{8,20}$/', $data['account_number'])) {
            $this->errors['account_number'] = 'Invalid account number format.';
        }
        
        if (empty($data['routing_number'])) {
            $this->errors['routing_number'] = 'Routing number is required.';
        } elseif (!preg_match('/^[0-9]{9}$/', $data['routing_number'])) {
            $this->errors['routing_number'] = 'Routing number must be 9 digits.';
        }
        
        if (empty($data['account_holder_name'])) {
            $this->errors['account_holder_name'] = 'Account holder name is required.';
        }
    }
    
    /**
     * Validate PayPal details
     */
    private function validatePayPalDetails($data) {
        if (empty($data['paypal_email'])) {
            $this->errors['paypal_email'] = 'PayPal email is required.';
        } else {
            $error = ValidationHelper::validateEmail($data['paypal_email']);
            if ($error) {
                $this->errors['paypal_email'] = 'Invalid PayPal email format.';
            }
        }
    }
    
    /**
     * Validate cryptocurrency details
     */
    private function validateCryptoDetails($data) {
        if (empty($data['crypto_address'])) {
            $this->errors['crypto_address'] = 'Cryptocurrency address is required.';
        } elseif (strlen($data['crypto_address']) < 26 || strlen($data['crypto_address']) > 62) {
            $this->errors['crypto_address'] = 'Invalid cryptocurrency address format.';
        }
        
        if (empty($data['crypto_type'])) {
            $this->errors['crypto_type'] = 'Cryptocurrency type is required.';
        } else {
            $allowedCrypto = ['bitcoin', 'ethereum', 'litecoin', 'usdt'];
            $error = ValidationHelper::validateEnum($data['crypto_type'], $allowedCrypto, 'Cryptocurrency type');
            if ($error) {
                $this->errors['crypto_type'] = $error;
            }
        }
    }
    
    /**
     * Validate sufficient balance
     */
    private function validateSufficientBalance($amount, $userBalance) {
        if (!is_numeric($amount)) {
            return; // Amount validation will catch this
        }
        
        $amount = (float) $amount;
        if ($amount > $userBalance) {
            $this->errors['amount'] = 'Insufficient balance. Available: $' . number_format($userBalance, 2);
        }
    }
    
    /**
     * Validate trading plan ID
     */
    private function validateTradingPlan($planId) {
        if (empty($planId)) {
            $this->errors['plan_id'] = 'Trading plan is required.';
            return;
        }
        
        if (!is_numeric($planId)) {
            $this->errors['plan_id'] = 'Invalid trading plan ID.';
        }
    }
    
    /**
     * Validate CSRF token
     */
    private function validateCSRFToken($token) {
        $error = ValidationHelper::validateCSRFToken($token);
        if ($error) {
            $this->errors['csrf_token'] = $error;
        }
    }
    
    /**
     * Sanitize financial form data
     */
    public static function sanitizeFinancialData($data) {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            switch ($key) {
                case 'amount':
                    $sanitized[$key] = filter_var($value, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                    break;
                case 'account_number':
                case 'routing_number':
                    $sanitized[$key] = preg_replace('/[^0-9]/', '', $value);
                    break;
                case 'paypal_email':
                    $sanitized[$key] = filter_var($value, FILTER_SANITIZE_EMAIL);
                    break;
                case 'crypto_address':
                    $sanitized[$key] = preg_replace('/[^a-zA-Z0-9]/', '', $value);
                    break;
                case 'password':
                case 'csrf_token':
                    $sanitized[$key] = $value; // Don't sanitize these
                    break;
                default:
                    $sanitized[$key] = ValidationHelper::sanitize($value);
                    break;
            }
        }
        
        return $sanitized;
    }
}
?>