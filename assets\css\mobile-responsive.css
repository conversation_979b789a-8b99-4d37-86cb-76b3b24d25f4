/* Mobile-First Responsive Design System */

/* Base mobile styles (320px and up) */
* {
    box-sizing: border-box;
}

body {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

/* Touch-friendly interactive elements */
.btn, .form-control, .form-select, .nav-link, .dropdown-item {
    min-height: 44px;
    min-width: 44px;
}

/* Mobile navigation */
.mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: white;
    border-bottom: 1px solid #dee2e6;
    z-index: 1030;
    padding: 10px 15px;
    display: none;
}

.mobile-nav.show {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-menu-toggle {
    background: none;
    border: none;
    font-size: 1.5rem;
    padding: 8px;
    color: #495057;
    cursor: pointer;
}

.mobile-user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Responsive sidebar */
.sidebar-mobile {
    position: fixed;
    top: 0;
    left: -100%;
    width: 280px;
    height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    z-index: 1050;
    transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.sidebar-mobile.open {
    left: 0;
}

.sidebar-mobile .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-mobile .sidebar-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    padding: 5px;
    cursor: pointer;
}

/* Mobile overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Responsive grid system */
.grid-responsive {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

/* Mobile-specific components */
.mobile-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
}

.mobile-card-header {
    background: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.mobile-card-body {
    padding: 15px;
}

/* Mobile form styles */
.form-mobile {
    padding: 0 15px;
}

.form-mobile .form-group {
    margin-bottom: 1.5rem;
}

.form-mobile .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.form-mobile .form-control,
.form-mobile .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    font-size: 16px;
    transition: border-color 0.15s ease-in-out;
}

.form-mobile .form-control:focus,
.form-mobile .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Mobile button styles */
.btn-mobile {
    width: 100%;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.btn-mobile:active {
    transform: translateY(1px);
}

/* Mobile table styles */
.table-mobile {
    font-size: 14px;
}

.table-mobile th,
.table-mobile td {
    padding: 8px 4px;
    vertical-align: middle;
}

.table-mobile .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* Mobile modal styles */
.modal-mobile .modal-dialog {
    margin: 10px;
    max-width: calc(100% - 20px);
}

.modal-mobile .modal-content {
    border-radius: 12px;
}

.modal-mobile .modal-header {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.modal-mobile .modal-body {
    padding: 15px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-mobile .modal-footer {
    padding: 15px;
    border-top: 1px solid #dee2e6;
}

/* Responsive breakpoints */

/* Small devices (576px and up) */
@media (min-width: 576px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .btn-mobile {
        width: auto;
        display: inline-block;
        margin-right: 10px;
    }
    
    .form-mobile {
        padding: 0 20px;
    }
    
    .mobile-card {
        margin-bottom: 1.5rem;
    }
}

/* Medium devices (768px and up) */
@media (min-width: 768px) {
    .mobile-nav {
        display: none !important;
    }
    
    .sidebar-mobile {
        position: relative;
        left: 0;
        width: 250px;
        height: auto;
        transition: none;
    }
    
    .mobile-overlay {
        display: none !important;
    }
    
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .main-content {
        margin-left: 250px;
        padding: 20px;
    }
    
    .form-mobile {
        padding: 0;
    }
    
    .mobile-card {
        margin-bottom: 2rem;
    }
}

/* Large devices (992px and up) */
@media (min-width: 992px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .main-content {
        padding: 25px;
    }
    
    .sidebar-mobile {
        width: 260px;
    }
    
    .main-content {
        margin-left: 260px;
    }
}

/* Extra large devices (1200px and up) */
@media (min-width: 1200px) {
    .main-content {
        padding: 30px;
    }
    
    .grid-responsive {
        gap: 1.5rem;
    }
}

/* Mobile-specific utilities */
@media (max-width: 767.98px) {
    .hide-mobile {
        display: none !important;
    }
    
    .show-mobile {
        display: block !important;
    }
    
    .show-mobile-flex {
        display: flex !important;
    }
    
    .show-mobile-inline {
        display: inline !important;
    }
    
    .show-mobile-inline-block {
        display: inline-block !important;
    }
    
    /* Mobile spacing utilities */
    .p-mobile-1 { padding: 0.5rem !important; }
    .p-mobile-2 { padding: 1rem !important; }
    .p-mobile-3 { padding: 1.5rem !important; }
    
    .m-mobile-1 { margin: 0.5rem !important; }
    .m-mobile-2 { margin: 1rem !important; }
    .m-mobile-3 { margin: 1.5rem !important; }
    
    /* Mobile text utilities */
    .text-mobile-sm { font-size: 0.875rem !important; }
    .text-mobile-md { font-size: 1rem !important; }
    .text-mobile-lg { font-size: 1.125rem !important; }
    
    /* Mobile width utilities */
    .w-mobile-100 { width: 100% !important; }
    .w-mobile-75 { width: 75% !important; }
    .w-mobile-50 { width: 50% !important; }
    .w-mobile-25 { width: 25% !important; }
}

/* Desktop-specific utilities */
@media (min-width: 768px) {
    .hide-desktop {
        display: none !important;
    }
    
    .show-desktop {
        display: block !important;
    }
    
    .show-desktop-flex {
        display: flex !important;
    }
    
    .show-desktop-inline {
        display: inline !important;
    }
    
    .show-desktop-inline-block {
        display: inline-block !important;
    }
}

/* Touch and hover states */
@media (hover: hover) {
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
}

@media (hover: none) {
    .btn:active {
        transform: translateY(1px);
    }
    
    .card:active {
        transform: scale(0.98);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .form-control,
    .form-select {
        border: 2px solid currentColor;
    }
    
    .card {
        border: 2px solid currentColor;
    }
}

/* Print styles */
@media print {
    .mobile-nav,
    .sidebar-mobile,
    .mobile-overlay,
    .btn,
    .form-control,
    .form-select {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
    }
    
    .table {
        border-collapse: collapse !important;
    }
    
    .table th,
    .table td {
        border: 1px solid #000 !important;
    }
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Swipe gestures support */
.swipe-container {
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
}

/* Safe area support for notched devices */
@supports (padding: max(0px)) {
    .safe-area-top {
        padding-top: max(20px, env(safe-area-inset-top));
    }
    
    .safe-area-bottom {
        padding-bottom: max(20px, env(safe-area-inset-bottom));
    }
    
    .safe-area-left {
        padding-left: max(15px, env(safe-area-inset-left));
    }
    
    .safe-area-right {
        padding-right: max(15px, env(safe-area-inset-right));
    }
}