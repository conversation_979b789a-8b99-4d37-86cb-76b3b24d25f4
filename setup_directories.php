<?php
/**
 * Directory Structure Setup Script
 * Creates the complete directory structure for the crypto trading platform
 */

echo "<h1>Crypto Trading Platform - Directory Setup</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>";

// Define directory structure
$directories = [
    // Core application directories
    'admin',
    'admin/dashboard',
    'admin/users',
    'admin/deposits',
    'admin/withdrawals',
    'admin/plans',
    'admin/support',
    'admin/reports',
    
    // Super admin directories
    'superadmin',
    'superadmin/settings',
    'superadmin/admins',
    'superadmin/email-templates',
    'superadmin/audit',
    'superadmin/system',
    
    // User panel directories
    'user',
    'user/dashboard',
    'user/profile',
    'user/deposit',
    'user/withdraw',
    'user/transactions',
    'user/support',
    'user/settings',
    
    // Layout and template directories
    'layouts',
    'layouts/admin',
    'layouts/user',
    'layouts/superadmin',
    'layouts/public',
    
    // PHP classes directory
    'classes',
    'classes/models',
    'classes/controllers',
    'classes/services',
    'classes/validators',
    
    // Assets directories
    'assets',
    'assets/css',
    'assets/js',
    'assets/images',
    'assets/uploads',
    'assets/uploads/profiles',
    'assets/uploads/documents',
    'assets/uploads/proofs',
    
    // API directory (for future expansion)
    'api',
    'api/v1',
    
    // Public pages
    'public',
    'public/auth',
    
    // Backup directories
    'database/backups',
    
    // Logs directory
    'logs'
];

echo "<h2>📁 Creating Directory Structure</h2>";

$created = 0;
$existed = 0;

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p style='color: green;'>✅ Created: <strong>$dir</strong></p>";
            $created++;
        } else {
            echo "<p style='color: red;'>❌ Failed to create: <strong>$dir</strong></p>";
        }
    } else {
        echo "<p style='color: blue;'>📁 Already exists: <strong>$dir</strong></p>";
        $existed++;
    }
}

// Create .htaccess files for security
$htaccessFiles = [
    'database/.htaccess' => "Deny from all\n",
    'includes/.htaccess' => "Deny from all\n",
    'classes/.htaccess' => "Deny from all\n",
    'logs/.htaccess' => "Deny from all\n",
    'assets/uploads/.htaccess' => "# Allow image files only\n<FilesMatch \"\\.(jpg|jpeg|png|gif|pdf|doc|docx)$\">\n    Allow from all\n</FilesMatch>\nDeny from all\n"
];

echo "<h2>🔒 Creating Security Files</h2>";

foreach ($htaccessFiles as $file => $content) {
    if (!file_exists($file)) {
        if (file_put_contents($file, $content)) {
            echo "<p style='color: green;'>✅ Created security file: <strong>$file</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create: <strong>$file</strong></p>";
        }
    } else {
        echo "<p style='color: blue;'>🔒 Security file exists: <strong>$file</strong></p>";
    }
}

// Create index.php files to prevent directory browsing
$indexFiles = [
    'admin/index.php',
    'superadmin/index.php',
    'user/index.php',
    'classes/index.php',
    'includes/index.php',
    'database/index.php',
    'logs/index.php'
];

$indexContent = "<?php\n// Prevent directory browsing\nheader('Location: ../index.php');\nexit();\n?>";

echo "<h2>🚫 Creating Index Protection Files</h2>";

foreach ($indexFiles as $file) {
    if (!file_exists($file)) {
        if (file_put_contents($file, $indexContent)) {
            echo "<p style='color: green;'>✅ Created protection: <strong>$file</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create: <strong>$file</strong></p>";
        }
    } else {
        echo "<p style='color: blue;'>🚫 Protection exists: <strong>$file</strong></p>";
    }
}

// Create a basic robots.txt file
$robotsContent = "User-agent: *\nDisallow: /admin/\nDisallow: /superadmin/\nDisallow: /user/\nDisallow: /database/\nDisallow: /includes/\nDisallow: /classes/\nDisallow: /logs/\nDisallow: /test_folder/\n\nAllow: /assets/\nAllow: /public/\n";

if (!file_exists('robots.txt')) {
    if (file_put_contents('robots.txt', $robotsContent)) {
        echo "<p style='color: green;'>✅ Created: <strong>robots.txt</strong></p>";
    }
} else {
    echo "<p style='color: blue;'>🤖 robots.txt already exists</p>";
}

// Summary
echo "<h2>📊 Setup Summary</h2>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<p style='color: #155724;'><strong>✅ Directory Setup Complete!</strong></p>";
echo "<p style='color: #155724;'>• Created: <strong>$created</strong> new directories</p>";
echo "<p style='color: #155724;'>• Existed: <strong>$existed</strong> directories</p>";
echo "<p style='color: #155724;'>• Security files and protections added</p>";
echo "</div>";

// Next steps
echo "<h2>🚀 Next Steps</h2>";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<ol style='color: #383d41;'>";
echo "<li>Database schema and structure ✅ <em>(Completed)</em></li>";
echo "<li>Directory structure ✅ <em>(Completed)</em></li>";
echo "<li>Create layout system and authentication (Task 2)</li>";
echo "<li>Implement user dashboard and profile management (Task 5)</li>";
echo "<li>Build admin and super admin panels</li>";
echo "</ol>";
echo "</div>";

// File permissions note
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<p style='color: #856404;'><strong>📝 Important Notes:</strong></p>";
echo "<ul style='color: #856404;'>";
echo "<li>Ensure proper file permissions (755 for directories, 644 for files)</li>";
echo "<li>The uploads directory needs write permissions for file uploads</li>";
echo "<li>Database backups directory should be writable</li>";
echo "<li>Logs directory needs write permissions</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
?>