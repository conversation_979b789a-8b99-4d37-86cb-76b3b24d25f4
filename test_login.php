<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'classes/models/User.php';

try {
    echo "<h2>Testing Login Credentials</h2>";
    
    // Test finding user by username
    $user = User::findByUsername('admin');
    if ($user) {
        echo "<p>✓ User 'admin' found in database</p>";
        echo "<p>User ID: " . $user->id . "</p>";
        echo "<p>Email: " . $user->email . "</p>";
        echo "<p>Role: " . $user->role . "</p>";
        echo "<p>Status: " . $user->status . "</p>";
        echo "<p>Email Verified: " . ($user->email_verified ? 'Yes' : 'No') . "</p>";
        
        // Test password verification
        $password = 'admin123';
        if ($user->verifyPassword($password)) {
            echo "<p>✓ Password verification successful</p>";
        } else {
            echo "<p>✗ Password verification failed</p>";
            echo "<p>Stored password hash: " . $user->password . "</p>";
            echo "<p>Test password: " . $password . "</p>";
            echo "<p>Generated hash: " . hashPassword($password) . "</p>";
        }
    } else {
        echo "<p>✗ User 'admin' not found in database</p>";
        
        // List all users
        $db = getDB();
        $stmt = $db->query("SELECT id, username, email, role, status FROM users");
        $users = $stmt->fetchAll();
        
        echo "<h3>All users in database:</h3>";
        foreach ($users as $u) {
            echo "<p>ID: {$u['id']}, Username: {$u['username']}, Email: {$u['email']}, Role: {$u['role']}, Status: {$u['status']}</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>