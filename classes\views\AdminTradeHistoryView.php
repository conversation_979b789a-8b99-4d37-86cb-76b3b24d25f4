<?php
require_once __DIR__ . '/BaseView.php';

/**
 * Admin Trade History View - Handles trade history management interface
 */
class AdminTradeHistoryView extends BaseView {
    
    protected function getTitle() {
        return 'Trade History Management - Admin Panel';
    }
    
    protected function getAdditionalHead() {
        return '
        <link href="../../assets/css/admin-trade-history.css" rel="stylesheet">
        ';
    }
    
    protected function getBodyContent($data) {
        $trades = $data['trades'] ?? [];
        $users = $data['users'] ?? [];
        $plans = $data['plans'] ?? [];
        $statistics = $data['statistics'] ?? [];
        $filters = $data['filters'] ?? [];
        $pagination = $data['pagination'] ?? [];
        $error = $data['error'] ?? null;
        
        ob_start();
        ?>
        
        <div class="admin-content">
            <!-- Header -->
            <div class="content-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0">Trade History Management</h1>
                        <p class="text-muted">Manage and create trade records for users</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createTradeModal">
                            <i class="fas fa-plus"></i> Create Trade
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#bulkTradeModal">
                            <i class="fas fa-layer-group"></i> Bulk Create
                        </button>
                    </div>
                </div>
            </div>
            
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>
            
            <!-- Statistics Cards -->
            <?php if (!empty($statistics)): ?>
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-primary">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['total_trades'] ?? 0) ?></h3>
                            <p>Total Trades</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['open_trades'] ?? 0) ?></h3>
                            <p>Open Trades</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['closed_trades'] ?? 0) ?></h3>
                            <p>Closed Trades</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?= number_format($statistics['profitable_trades'] ?? 0) ?></h3>
                            <p>Profitable</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-<?= ($statistics['total_profit_loss'] ?? 0) >= 0 ? 'success' : 'danger' ?>">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>$<?= number_format($statistics['total_profit_loss'] ?? 0, 2) ?></h3>
                            <p>Total P&L</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-icon bg-secondary">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="stat-content">
                            <h3>$<?= number_format($statistics['avg_profit_loss'] ?? 0, 2) ?></h3>
                            <p>Avg P&L</p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Filters</h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">User</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                <option value="<?= $user->getId() ?>" <?= $filters['user_id'] == $user->getId() ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($user->first_name . ' ' . $user->last_name) ?> (<?= htmlspecialchars($user->username) ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="plan_id" class="form-label">Trading Plan</label>
                            <select class="form-select" id="plan_id" name="plan_id">
                                <option value="">All Plans</option>
                                <?php foreach ($plans as $plan): ?>
                                <option value="<?= $plan->getId() ?>" <?= $filters['plan_id'] == $plan->getId() ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($plan->name) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="open" <?= $filters['status'] === 'open' ? 'selected' : '' ?>>Open</option>
                                <option value="closed" <?= $filters['status'] === 'closed' ? 'selected' : '' ?>>Closed</option>
                                <option value="cancelled" <?= $filters['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="<?= htmlspecialchars($filters['date_from']) ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="<?= htmlspecialchars($filters['date_to']) ?>">
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Apply Filters
                            </button>
                            <a href="trades.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Trades Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Trade Records</h5>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($trades)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                        <h5>No Trade Records Found</h5>
                        <p class="text-muted">Create trade records to track user investments.</p>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createTradeModal">
                            <i class="fas fa-plus"></i> Create Trade
                        </button>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>User</th>
                                    <th>Plan</th>
                                    <th>Asset</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Entry Price</th>
                                    <th>Exit Price</th>
                                    <th>P&L</th>
                                    <th>Status</th>
                                    <th>Opened</th>
                                    <th>Closed</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($trades as $trade): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0"><?= htmlspecialchars($trade['first_name'] . ' ' . $trade['last_name']) ?></h6>
                                                <small class="text-muted">@<?= htmlspecialchars($trade['username']) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($trade['plan_name']): ?>
                                        <span class="badge bg-info"><?= htmlspecialchars($trade['plan_name']) ?></span>
                                        <?php else: ?>
                                        <span class="text-muted">No Plan</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-medium"><?= htmlspecialchars($trade['asset']) ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $trade['trade_type'] === 'buy' ? 'success' : 'danger' ?>">
                                            <?= strtoupper($trade['trade_type']) ?>
                                        </span>
                                    </td>
                                    <td>$<?= number_format($trade['amount'], 2) ?></td>
                                    <td>$<?= number_format($trade['entry_price'], 4) ?></td>
                                    <td>
                                        <?php if ($trade['exit_price']): ?>
                                        $<?= number_format($trade['exit_price'], 4) ?>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="fw-medium text-<?= $trade['profit_loss'] >= 0 ? 'success' : 'danger' ?>">
                                            $<?= number_format($trade['profit_loss'], 2) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= 
                                            $trade['status'] === 'open' ? 'warning' : 
                                            ($trade['status'] === 'closed' ? 'success' : 'secondary') 
                                        ?>">
                                            <?= ucfirst($trade['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small><?= date('M j, Y H:i', strtotime($trade['opened_at'])) ?></small>
                                    </td>
                                    <td>
                                        <?php if ($trade['closed_at']): ?>
                                        <small><?= date('M j, Y H:i', strtotime($trade['closed_at'])) ?></small>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary edit-trade-btn" 
                                                    data-trade-id="<?= $trade['id'] ?>" title="Edit Trade">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if ($trade['status'] === 'open'): ?>
                                            <button type="button" class="btn btn-outline-success close-trade-btn" 
                                                    data-trade-id="<?= $trade['id'] ?>" title="Close Trade">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-outline-danger delete-trade-btn" 
                                                    data-trade-id="<?= $trade['id'] ?>" title="Delete Trade">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination['total_pages'] > 1): ?>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                Showing <?= (($pagination['current_page'] - 1) * $pagination['limit']) + 1 ?> to 
                                <?= min($pagination['current_page'] * $pagination['limit'], $pagination['total_records']) ?> 
                                of <?= number_format($pagination['total_records']) ?> trades
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <?php if ($pagination['current_page'] > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['current_page'] - 1])) ?>">Previous</a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>"><?= $i ?></a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($filters, ['page' => $pagination['current_page'] + 1])) ?>">Next</a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Create/Edit Trade Modal -->
        <div class="modal fade" id="createTradeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New Trade</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="tradeForm">
                        <div class="modal-body">
                            <input type="hidden" id="tradeId" name="trade_id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tradeUserId" class="form-label">User *</label>
                                        <select class="form-select" id="tradeUserId" name="user_id" required>
                                            <option value="">Select User</option>
                                            <?php foreach ($users as $user): ?>
                                            <option value="<?= $user->getId() ?>">
                                                <?= htmlspecialchars($user->first_name . ' ' . $user->last_name) ?> (<?= htmlspecialchars($user->username) ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tradePlanId" class="form-label">Trading Plan</label>
                                        <select class="form-select" id="tradePlanId" name="plan_id">
                                            <option value="">No Plan</option>
                                            <?php foreach ($plans as $plan): ?>
                                            <option value="<?= $plan->getId() ?>">
                                                <?= htmlspecialchars($plan->name) ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="asset" class="form-label">Asset *</label>
                                        <input type="text" class="form-control" id="asset" name="asset" 
                                               placeholder="e.g., BTC/USD" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="tradeType" class="form-label">Trade Type *</label>
                                        <select class="form-select" id="tradeType" name="trade_type" required>
                                            <option value="">Select Type</option>
                                            <option value="buy">Buy</option>
                                            <option value="sell">Sell</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">Amount ($) *</label>
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               step="0.01" min="0.01" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="entryPrice" class="form-label">Entry Price ($) *</label>
                                        <input type="number" class="form-control" id="entryPrice" name="entry_price" 
                                               step="0.00000001" min="0.00000001" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="openedAt" class="form-label">Opened At *</label>
                                        <input type="datetime-local" class="form-control" id="openedAt" name="opened_at" required>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                <span class="btn-text">Create Trade</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Close Trade Modal -->
        <div class="modal fade" id="closeTradeModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Close Trade</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="closeTradeForm">
                        <div class="modal-body">
                            <input type="hidden" id="closeTradeId" name="trade_id">
                            
                            <div class="mb-3">
                                <label for="exitPrice" class="form-label">Exit Price ($) *</label>
                                <input type="number" class="form-control" id="exitPrice" name="exit_price" 
                                       step="0.00000001" min="0.00000001" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="closedAt" class="form-label">Closed At *</label>
                                <input type="datetime-local" class="form-control" id="closedAt" name="closed_at" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="closeNotes" class="form-label">Closing Notes</label>
                                <textarea class="form-control" id="closeNotes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                Close Trade
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Bulk Create Modal -->
        <div class="modal fade" id="bulkTradeModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bulk Create Trades</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="bulkTradeForm">
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Create multiple trades for selected users with the same parameters.
                            </div>
                            
                            <div class="mb-3">
                                <label for="bulkUsers" class="form-label">Select Users *</label>
                                <select class="form-select" id="bulkUsers" name="user_ids[]" multiple required>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?= $user->getId() ?>">
                                        <?= htmlspecialchars($user->first_name . ' ' . $user->last_name) ?> (<?= htmlspecialchars($user->username) ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Hold Ctrl/Cmd to select multiple users</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bulkAsset" class="form-label">Asset *</label>
                                        <input type="text" class="form-control" id="bulkAsset" name="asset" 
                                               placeholder="e.g., BTC/USD" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bulkTradeType" class="form-label">Trade Type *</label>
                                        <select class="form-select" id="bulkTradeType" name="trade_type" required>
                                            <option value="">Select Type</option>
                                            <option value="buy">Buy</option>
                                            <option value="sell">Sell</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="bulkAmount" class="form-label">Amount ($) *</label>
                                        <input type="number" class="form-control" id="bulkAmount" name="amount" 
                                               step="0.01" min="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="bulkEntryPrice" class="form-label">Entry Price ($) *</label>
                                        <input type="number" class="form-control" id="bulkEntryPrice" name="entry_price" 
                                               step="0.00000001" min="0.00000001" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="bulkOpenedAt" class="form-label">Opened At *</label>
                                        <input type="datetime-local" class="form-control" id="bulkOpenedAt" name="opened_at" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-info">
                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                Create Trades
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <?php
        return ob_get_clean();
    }
    
    protected function getAdditionalScripts() {
        return '
        <script src="../../assets/js/admin-trade-history.js"></script>
        ';
    }
    
    protected function getLayout() {
        return 'admin';
    }
}
?>