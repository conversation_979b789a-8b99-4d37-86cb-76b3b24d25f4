<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Dashboard';
$user = getCurrentUser();

// Get system statistics
$db = getDB();

// Get total users
$stmt = $db->query("SELECT COUNT(*) as total_users FROM users");
$totalUsers = $stmt->fetch()['total_users'] ?? 0;

// Get total admins
$stmt = $db->query("SELECT COUNT(*) as total_admins FROM users WHERE role IN ('admin', 'superadmin')");
$totalAdmins = $stmt->fetch()['total_admins'] ?? 0;

// Get recent activity count
$stmt = $db->query("SELECT COUNT(*) as recent_activity FROM audit_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)");
$recentActivity = $stmt->fetch()['recent_activity'] ?? 0;

// Get site settings
$siteName = SystemSetting::getValue('site_name', 'Coinage Trading');
$siteLogo = SystemSetting::getValue('logo_url', getBaseUrl() . 'assets/images/logo.png');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $siteName; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #6366f1;
            --secondary-color: #8b5cf6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-muted: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--light-color);
            color: var(--dark-color);
            line-height: 1.6;
        }

        .dashboard-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card h6 {
            color: var(--text-muted);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .stat-card h3 {
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0;
        }

        .crown-icon {
            color: #ffd700;
        }

        .quick-action-btn {
            display: block;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: var(--shadow-sm);
            margin-bottom: 1rem;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
            color: var(--primary-color);
            text-decoration: none;
        }

        .quick-action-btn i {
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .quick-action-btn h6 {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .quick-action-btn small {
            color: var(--text-muted);
        }

        .system-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .system-info-item:last-child {
            border-bottom: none;
        }

        .system-info-label {
            font-weight: 500;
            color: var(--text-muted);
        }

        .system-info-value {
            font-weight: 600;
            color: var(--dark-color);
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-online {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-warning {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-error {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        @media (max-width: 768px) {
            .stat-card {
                padding: 1.5rem;
            }

            .quick-action-btn {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- Include Sidebar -->
        <?php include __DIR__ . '/../includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-crown crown-icon me-2"></i>Super Admin Dashboard
                </h1>
                <div class="text-muted">
                    <small>Welcome back, <?php echo htmlspecialchars($user['first_name']); ?>!</small>
                </div>
            </div>

            <!-- System Overview -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">Total Users</h6>
                                <h3 class="mb-0"><?php echo number_format($totalUsers); ?></h3>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-users fa-2x" style="color: var(--primary-color);"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">Admin Users</h6>
                                <h3 class="mb-0"><?php echo number_format($totalAdmins); ?></h3>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-user-shield fa-2x" style="color: var(--success-color);"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">Recent Activity</h6>
                                <h3 class="mb-0"><?php echo number_format($recentActivity); ?></h3>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-chart-line fa-2x" style="color: var(--info-color);"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h6 class="mb-0">System Status</h6>
                                <h3 class="mb-0"><span class="status-badge status-online">Online</span></h3>
                            </div>
                            <div class="ms-3">
                                <i class="fas fa-server fa-2x" style="color: var(--success-color);"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions and System Information -->
            <div class="row">
                <div class="col-md-6">
                    <div class="dashboard-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="../settings/" class="quick-action-btn">
                                        <i class="fas fa-cogs fa-2x d-block"></i>
                                        <h6>System Settings</h6>
                                        <small>Configure platform settings</small>
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="../admins/" class="quick-action-btn">
                                        <i class="fas fa-users-cog fa-2x d-block"></i>
                                        <h6>Manage Admins</h6>
                                        <small>Add and manage admin users</small>
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="../appearance/" class="quick-action-btn">
                                        <i class="fas fa-palette fa-2x d-block"></i>
                                        <h6>Appearance</h6>
                                        <small>Customize platform appearance</small>
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="../audit/" class="quick-action-btn">
                                        <i class="fas fa-clipboard-list fa-2x d-block"></i>
                                        <h6>Audit Logs</h6>
                                        <small>View system activity logs</small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="dashboard-card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>System Information
                            </h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="system-info-item">
                                <span class="system-info-label">Platform Version</span>
                                <span class="system-info-value">v2.1.0</span>
                            </div>
                            <div class="system-info-item">
                                <span class="system-info-label">PHP Version</span>
                                <span class="system-info-value"><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="system-info-item">
                                <span class="system-info-label">Database Status</span>
                                <span class="system-info-value">
                                    <span class="status-badge status-online">Connected</span>
                                </span>
                            </div>
                            <div class="system-info-item">
                                <span class="system-info-label">Last Login</span>
                                <span class="system-info-value"><?php echo date('M j, Y g:i A'); ?></span>
                            </div>
                            <div class="system-info-item">
                                <span class="system-info-label">Server Time</span>
                                <span class="system-info-value"><?php echo date('Y-m-d H:i:s T'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>