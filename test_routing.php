<?php
/**
 * Test Routing and Dynamic Link Generation
 */

// Include necessary files
require_once 'config.php';
require_once 'includes/functions.php';
require_once 'classes/config/Routes.php';

echo "<h1>Routing and Dynamic Link Generation Test</h1>";

// Test 1: Basic URL generation
echo "<h2>Test 1: Basic URL Generation</h2>";
try {
    echo "BASE_URL: " . BASE_URL . "<br>";
    echo "AUTO_BASE_URL: " . AUTO_BASE_URL . "<br>";
    echo "getBaseUrl(): " . getBaseUrl() . "<br>";
    echo "url('test'): " . url('test') . "<br>";
    echo "asset('css/style.css'): " . asset('css/style.css') . "<br>";
    echo "<span style='color: green;'>✓ Basic URL generation working</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Basic URL generation failed: " . $e->getMessage() . "</span><br>";
}

// Test 2: Routes class functionality
echo "<h2>Test 2: Routes Class Functionality</h2>";
try {
    echo "Login route: " . Routes::get('login') . "<br>";
    echo "Register route: " . Routes::get('register') . "<br>";
    echo "User dashboard route: " . Routes::get('user_dashboard') . "<br>";
    echo "Admin dashboard route: " . Routes::get('admin_dashboard') . "<br>";
    echo "Superadmin dashboard route: " . Routes::get('superadmin_dashboard') . "<br>";
    echo "<span style='color: green;'>✓ Routes class working</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Routes class failed: " . $e->getMessage() . "</span><br>";
}

// Test 3: Dashboard route based on role
echo "<h2>Test 3: Dashboard Route by Role</h2>";
try {
    echo "User role dashboard: " . Routes::getDashboardRoute('user') . "<br>";
    echo "Admin role dashboard: " . Routes::getDashboardRoute('admin') . "<br>";
    echo "Superadmin role dashboard: " . Routes::getDashboardRoute('superadmin') . "<br>";
    echo "<span style='color: green;'>✓ Role-based dashboard routing working</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Role-based dashboard routing failed: " . $e->getMessage() . "</span><br>";
}

// Test 4: Invalid route handling
echo "<h2>Test 4: Invalid Route Handling</h2>";
try {
    $invalidRoute = Routes::get('nonexistent_route');
    echo "<span style='color: red;'>✗ Invalid route should have thrown exception</span><br>";
} catch (InvalidArgumentException $e) {
    echo "<span style='color: green;'>✓ Invalid route properly handled: " . $e->getMessage() . "</span><br>";
} catch (Exception $e) {
    echo "<span style='color: orange;'>? Unexpected exception: " . $e->getMessage() . "</span><br>";
}

// Test 5: Route with parameters (if supported)
echo "<h2>Test 5: Route Parameters</h2>";
try {
    // Add a test route with parameters
    $testRoute = Routes::get('user_dashboard', ['id' => 123]);
    echo "User dashboard with params: " . $testRoute . "<br>";
    echo "<span style='color: green;'>✓ Route parameters working</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Route parameters failed: " . $e->getMessage() . "</span><br>";
}

echo "<h2>Summary</h2>";
echo "Routing system test completed. Check above for any issues.";
?>
