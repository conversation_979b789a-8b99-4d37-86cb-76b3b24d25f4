<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../classes/services/EmailTemplateService.php';

// Set JSON header
header('Content-Type: application/json');

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        throw new Exception('Invalid security token');
    }
    
    $templateType = $_POST['template_type'] ?? '';
    $testEmail = $_POST['test_email'] ?? '';
    
    if (!$templateType) {
        throw new Exception('Template type is required');
    }
    
    if (!$testEmail) {
        throw new Exception('Test email address is required');
    }
    
    if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email address');
    }
    
    // Send test email
    $result = EmailTemplateService::testTemplate($templateType, $testEmail);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => "Test email sent successfully to {$testEmail}"
        ]);
    } else {
        throw new Exception('Failed to send test email. Please check your SMTP configuration.');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}