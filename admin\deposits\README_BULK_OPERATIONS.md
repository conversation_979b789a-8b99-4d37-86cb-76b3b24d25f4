# Bulk Operations for Deposit and Withdrawal Management

## Overview

The bulk operations feature allows administrators to process multiple deposits and withdrawals simultaneously, significantly improving efficiency when handling large volumes of transactions.

## Features Implemented

### Deposit Bulk Operations
- **Bulk Approval**: Approve multiple pending deposits at once with automatic bonus calculation
- **Bulk Rejection**: Reject multiple deposits with a common reason
- **Transaction Safety**: Each operation is wrapped in database transactions with rollback on failure
- **User Balance Updates**: Automatic balance and bonus updates for approved deposits
- **Email Notifications**: Automatic email notifications sent to users for each processed deposit

### Withdrawal Bulk Operations
- **Bulk Approval**: Approve multiple pending withdrawals with balance validation
- **Bulk Rejection**: Reject multiple withdrawals with reason tracking and balance restoration
- **Bulk Status Updates**: Mark multiple withdrawals as processing or completed
- **Multi-stage Processing**: Support for the complete withdrawal workflow (pending → approved → processing → completed)
- **Email Notifications**: Automatic notifications for each status change

## Technical Implementation

### Backend API Endpoints

#### Deposit Bulk Actions (`admin/deposits/api/bulk-action.php`)
```php
POST /admin/deposits/api/bulk-action.php
Content-Type: application/x-www-form-urlencoded

Parameters:
- action: 'approve' | 'reject'
- deposit_ids[]: Array of deposit IDs
- reason: Required for rejection
- csrf_token: CSRF protection token
```

#### Withdrawal Bulk Actions (`admin/withdrawals/api/bulk-action.php`)
```php
POST /admin/withdrawals/api/bulk-action.php
Content-Type: application/x-www-form-urlencoded

Parameters:
- action: 'approve' | 'reject' | 'processing' | 'completed'
- withdrawal_ids[]: Array of withdrawal IDs
- reason: Required for rejection
- admin_note: Optional note for other actions
- csrf_token: CSRF protection token
```

### Frontend Integration

#### JavaScript Functions
- `showBulkActions()`: Display bulk actions modal
- `updateSelectedDeposits()` / `updateSelectedWithdrawals()`: Track selected items
- `submitBulkActionsForm()`: Handle form submission with AJAX
- `toggleSelectAll()`: Select/deselect all items

#### UI Components
- Checkboxes for individual item selection
- "Select All" checkbox for bulk selection
- Bulk Actions button with modal interface
- Dynamic form fields based on selected action
- Real-time validation and feedback

### Security Features

1. **CSRF Protection**: All bulk operations require valid CSRF tokens
2. **Authentication**: Admin/superadmin role verification
3. **Input Validation**: Sanitization of all input parameters
4. **Transaction Safety**: Database transactions with rollback on errors
5. **Audit Logging**: All bulk operations are logged for audit trails

### Database Operations

#### Deposit Approval Process
1. Validate deposit exists and is pending
2. Calculate bonus amount based on trading plan
3. Update deposit status and metadata
4. Credit user balance and bonus
5. Create transaction records
6. Send email notification

#### Withdrawal Processing Workflow
1. **Approval**: Validate balance, deduct amount, update status
2. **Processing**: Mark as being processed by payment team
3. **Completion**: Mark as completed with timestamp
4. **Rejection**: Restore balance, record reason, notify user

## Usage Instructions

### For Administrators

#### Processing Deposits
1. Navigate to Admin Panel → Deposit Management
2. Use filters to view pending deposits
3. Select deposits using checkboxes
4. Click "Bulk Actions" button
5. Choose action (Approve/Reject)
6. Add reason if rejecting
7. Click "Execute Action"

#### Processing Withdrawals
1. Navigate to Admin Panel → Withdrawal Management
2. Filter by status (pending, approved, processing)
3. Select withdrawals using checkboxes
4. Click "Bulk Actions" button
5. Choose appropriate action
6. Add notes or reasons as needed
7. Execute the action

### Error Handling

The system provides comprehensive error handling:
- Individual transaction failures don't affect other operations
- Detailed error messages for each failed operation
- Automatic rollback for database consistency
- User-friendly error reporting in the interface

## Performance Considerations

- Operations are processed individually to maintain data integrity
- Large bulk operations may take time - UI shows progress
- Database transactions prevent partial updates
- Email notifications are queued to prevent blocking

## Testing

A comprehensive test suite (`test_folder/test_bulk_operations.php`) validates:
- Bulk approval and rejection workflows
- Balance calculations and updates
- Transaction integrity
- Email notification sending
- API endpoint security
- Frontend integration

## Monitoring and Logging

All bulk operations are logged with:
- Admin user performing the action
- Number of items processed
- Success/failure counts
- Timestamp and IP address
- Individual item results

## Future Enhancements

Potential improvements for future versions:
- Batch processing for very large operations
- Scheduled bulk operations
- Advanced filtering and selection criteria
- Export functionality for processed items
- Real-time progress indicators
- Undo functionality for recent bulk operations

## Support

For technical support or questions about bulk operations:
1. Check the audit logs for operation history
2. Review error logs for detailed failure information
3. Test with small batches before large operations
4. Contact system administrator for database issues