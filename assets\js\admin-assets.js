/**
 * Admin Assets Management JavaScript
 */

class AdminAssets {
    constructor() {
        this.currentFilter = 'all';
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        // Form submission
        document.getElementById('assetForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });
        
        // Edit asset buttons
        document.querySelectorAll('.edit-asset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const assetId = e.currentTarget.dataset.assetId;
                this.editAsset(assetId);
            });
        });
        
        // Toggle status buttons
        document.querySelectorAll('.toggle-status-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const assetId = e.currentTarget.dataset.assetId;
                const currentStatus = e.currentTarget.dataset.currentStatus;
                this.toggleAssetStatus(assetId, currentStatus);
            });
        });
        
        // Delete asset buttons
        document.querySelectorAll('.delete-asset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const assetId = e.currentTarget.dataset.assetId;
                const assetSymbol = e.currentTarget.dataset.assetSymbol;
                this.showDeleteConfirmation(assetId, assetSymbol);
            });
        });
        
        // Delete confirmation
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.deleteAsset();
        });
        
        // Filter options
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const filter = e.currentTarget.dataset.filter;
                this.applyFilter(filter);
            });
        });
        
        // Modal events
        document.getElementById('createAssetModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm();
        });
        
        // Form validation
        document.querySelectorAll('#assetForm input, #assetForm textarea, #assetForm select').forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
            
            field.addEventListener('input', () => {
                this.clearFieldError(field);
            });
        });
    }
    
    handleFormSubmit() {
        const form = document.getElementById('assetForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        const btnText = submitBtn.querySelector('.btn-text');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        // Determine if this is create or update
        const assetId = document.getElementById('assetId').value;
        const url = assetId ? 'api/update-asset.php' : 'api/create-asset.php';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(
                    assetId ? 'Asset updated successfully' : 'Asset created successfully', 
                    'success'
                );
                
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('createAssetModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.handleFormErrors(data.errors || {});
                this.showNotification(data.message || 'Please correct the errors below', 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting form:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }
    
    editAsset(assetId) {
        fetch(`api/asset-details.php?id=${assetId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.populateForm(data.asset);
                
                // Update modal title
                document.querySelector('#createAssetModal .modal-title').textContent = 'Edit Asset';
                document.querySelector('#assetForm .btn-text').textContent = 'Update Asset';
                
                // Show modal
                new bootstrap.Modal(document.getElementById('createAssetModal')).show();
            } else {
                this.showNotification(data.message || 'Failed to load asset details', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading asset:', error);
            this.showNotification('Failed to load asset details', 'error');
        });
    }
    
    populateForm(asset) {
        document.getElementById('assetId').value = asset.id;
        document.getElementById('symbol').value = asset.symbol;
        document.getElementById('name').value = asset.name;
        document.getElementById('category').value = asset.category;
        document.getElementById('status').value = asset.status;
        document.getElementById('currentPrice').value = asset.current_price || '';
        document.getElementById('priceChange').value = asset.price_change_24h || '';
        document.getElementById('description').value = asset.description || '';
    }
    
    toggleAssetStatus(assetId, currentStatus) {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        
        fetch('api/toggle-asset-status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ 
                asset_id: assetId, 
                status: newStatus 
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(`Asset ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`, 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification(data.message || 'Failed to update asset status', 'error');
            }
        })
        .catch(error => {
            console.error('Error toggling status:', error);
            this.showNotification('Failed to update asset status', 'error');
        });
    }
    
    showDeleteConfirmation(assetId, assetSymbol) {
        document.getElementById('deleteAssetSymbol').textContent = assetSymbol;
        document.getElementById('confirmDeleteBtn').dataset.assetId = assetId;
        
        new bootstrap.Modal(document.getElementById('deleteAssetModal')).show();
    }
    
    deleteAsset() {
        const assetId = document.getElementById('confirmDeleteBtn').dataset.assetId;
        const btn = document.getElementById('confirmDeleteBtn');
        const spinner = btn.querySelector('.spinner-border');
        
        // Show loading state
        spinner.classList.remove('d-none');
        btn.disabled = true;
        
        fetch('api/delete-asset.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ asset_id: assetId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Asset deleted successfully', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deleteAssetModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification(data.message || 'Failed to delete asset', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting asset:', error);
            this.showNotification('Failed to delete asset', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            btn.disabled = false;
        });
    }
    
    applyFilter(filter) {
        this.currentFilter = filter;
        const rows = document.querySelectorAll('#assetsTable tbody tr');
        
        // Update active filter button
        document.querySelectorAll('.filter-option').forEach(option => {
            option.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        // Filter rows
        rows.forEach(row => {
            const status = row.dataset.status;
            const category = row.dataset.category;
            let shouldShow = false;
            
            switch (filter) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'active':
                case 'inactive':
                    shouldShow = status === filter;
                    break;
                default:
                    shouldShow = category === filter;
                    break;
            }
            
            row.style.display = shouldShow ? '' : 'none';
        });
        
        // Update visible count
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
        this.updateFilterInfo(visibleRows.length, rows.length);
    }
    
    updateFilterInfo(visible, total) {
        // You can add a filter info display here if needed
        console.log(`Showing ${visible} of ${total} assets`);
    }
    
    validateField(field) {
        const value = field.value.trim();
        const name = field.name;
        let error = '';
        
        switch (name) {
            case 'symbol':
                if (!value) error = 'Symbol is required';
                else if (value.length > 20) error = 'Symbol must not exceed 20 characters';
                break;
                
            case 'name':
                if (!value) error = 'Name is required';
                else if (value.length > 100) error = 'Name must not exceed 100 characters';
                break;
                
            case 'category':
                if (!value) error = 'Category is required';
                else if (!['crypto', 'forex', 'stocks', 'commodities', 'indices'].includes(value)) {
                    error = 'Invalid category';
                }
                break;
                
            case 'current_price':
                if (value && (isNaN(value) || parseFloat(value) < 0)) {
                    error = 'Current price must be a positive number';
                }
                break;
                
            case 'price_change_24h':
                if (value && isNaN(value)) {
                    error = 'Price change must be a number';
                }
                break;
        }
        
        this.setFieldError(field, error);
        return !error;
    }
    
    setFieldError(field, error) {
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        
        if (error) {
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = error;
        } else {
            field.classList.remove('is-invalid');
            if (feedback) feedback.textContent = '';
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';
    }
    
    handleFormErrors(errors) {
        // Clear all previous errors
        document.querySelectorAll('#assetForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Set new errors
        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.setFieldError(field, errors[fieldName]);
            }
        });
    }
    
    resetForm() {
        const form = document.getElementById('assetForm');
        form.reset();
        
        // Clear errors
        document.querySelectorAll('#assetForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Reset modal title and button text
        document.querySelector('#createAssetModal .modal-title').textContent = 'Add New Asset';
        document.querySelector('#assetForm .btn-text').textContent = 'Add Asset';
        
        // Clear asset ID
        document.getElementById('assetId').value = '';
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminAssets();
});

// Additional utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 8
    }).format(amount);
}

function formatPercentage(value) {
    return new Intl.NumberFormat('en-US', {
        style: 'percent',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(value / 100);
}