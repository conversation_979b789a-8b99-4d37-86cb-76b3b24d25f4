<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/BaseModel.php';
require_once __DIR__ . '/../classes/testing/TestFramework.php';
require_once __DIR__ . '/../classes/services/ErrorHandlingService.php';
require_once __DIR__ . '/../classes/services/SystemHealthService.php';
require_once __DIR__ . '/../classes/services/SecurityAuditService.php';
require_once __DIR__ . '/../classes/services/TwoFactorAuthService.php';
require_once __DIR__ . '/../classes/services/EmailNotificationService.php';
require_once __DIR__ . '/../classes/services/SupportTicketService.php';
require_once __DIR__ . '/../classes/services/SecurityMiddleware.php';

/**
 * Comprehensive System Test Suite
 * Tests all major system components including security, communication, and error handling
 */

// Initialize test framework
$framework = new TestFramework();

// Test Error Handling System
$framework->suite('Error Handling System', TestFramework::TYPE_SYSTEM)
    ->setup(function() {
        // Setup test environment
        $_SESSION['user_id'] = 1;
        $_SERVER['REMOTE_ADDR'] = '127.0.0.1';
        $_SERVER['HTTP_USER_AGENT'] = 'Test Agent';
    })
    ->test('Error Handler Initialization', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $test->assertNotNull($errorHandler, 'Error handler should be initialized');
    })
    ->test('Application Error Logging', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $errorId = $errorHandler->logApplicationError(
            'Test application error',
            ErrorHandlingService::CATEGORY_SYSTEM,
            ErrorHandlingService::SEVERITY_MEDIUM
        );
        $test->assertNotNull($errorId, 'Error ID should be generated');
        $test->assertStringContains('ERR_', $errorId, 'Error ID should have correct format');
    })
    ->test('Database Error Handling', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $exception = new PDOException('Test database error');
        $result = $errorHandler->handleDatabaseError($exception, 'SELECT * FROM test', []);
        
        $test->assertFalse($result['success'], 'Database error should return failure');
        $test->assertNotNull($result['error_id'], 'Error ID should be provided');
        $test->assertEqual('Database operation failed. Please try again.', $result['error'], 'User-friendly error message should be provided');
    })
    ->test('Authentication Error Handling', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $result = $errorHandler->handleAuthenticationError('Invalid credentials', null, '<EMAIL>', '127.0.0.1');
        
        $test->assertFalse($result['success'], 'Authentication error should return failure');
        $test->assertNotNull($result['error_id'], 'Error ID should be provided');
        $test->assertEqual('Authentication failed. Please check your credentials.', $result['error'], 'User-friendly error message should be provided');
    })
    ->test('File Upload Error Handling', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $result = $errorHandler->handleFileUploadError(UPLOAD_ERR_INI_SIZE, 'test.jpg', 5000000);
        
        $test->assertFalse($result['success'], 'File upload error should return failure');
        $test->assertNotNull($result['error_id'], 'Error ID should be provided');
        $test->assertStringContains('exceeds', $result['error'], 'Error message should describe the issue');
    })
    ->test('Financial Error Handling', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $result = $errorHandler->handleFinancialError('Transaction failed', ['amount' => 100.00], ErrorHandlingService::SEVERITY_HIGH);
        
        $test->assertFalse($result['success'], 'Financial error should return failure');
        $test->assertNotNull($result['error_id'], 'Error ID should be provided');
        $test->assertStringContains('contact support', $result['error'], 'Error message should suggest contacting support');
    })
    ->test('Validation Error Handling', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        $errors = ['email' => 'Invalid email format', 'password' => 'Password too short'];
        $result = $errorHandler->handleValidationError($errors, ['email' => 'invalid-email']);
        
        $test->assertFalse($result['success'], 'Validation error should return failure');
        $test->assertNotNull($result['error_id'], 'Error ID should be provided');
        $test->assertEqual($errors, $result['errors'], 'Validation errors should be returned');
    })
    ->teardown(function() {
        // Clean up test data
        unset($_SESSION['user_id']);
    })
    ->end();

// Test System Health Monitoring
$framework->suite('System Health Monitoring', TestFramework::TYPE_SYSTEM)
    ->test('Health Service Initialization', function($test) {
        $healthService = SystemHealthService::getInstance();
        $test->assertNotNull($healthService, 'Health service should be initialized');
    })
    ->test('Database Health Check', function($test) {
        $healthService = SystemHealthService::getInstance();
        $result = $healthService->runHealthCheck();
        
        $test->assertTrue(isset($result[SystemHealthService::CHECK_DATABASE]), 'Database check should be included');
        $test->assertTrue(in_array($result[SystemHealthService::CHECK_DATABASE]['status'], [
            SystemHealthService::STATUS_HEALTHY,
            SystemHealthService::STATUS_WARNING,
            SystemHealthService::STATUS_CRITICAL
        ]), 'Database status should be valid');
    })
    ->test('Memory Health Check', function($test) {
        $healthService = SystemHealthService::getInstance();
        $result = $healthService->runHealthCheck();
        
        $test->assertTrue(isset($result[SystemHealthService::CHECK_MEMORY]), 'Memory check should be included');
        $test->assertTrue(isset($result[SystemHealthService::CHECK_MEMORY]['metrics']['current_usage_mb']), 'Memory usage should be reported');
        $test->assertTrue($result[SystemHealthService::CHECK_MEMORY]['metrics']['current_usage_mb'] > 0, 'Memory usage should be positive');
    })
    ->test('File System Health Check', function($test) {
        $healthService = SystemHealthService::getInstance();
        $result = $healthService->runHealthCheck();
        
        $test->assertTrue(isset($result[SystemHealthService::CHECK_FILE_SYSTEM]), 'File system check should be included');
        $test->assertTrue(isset($result[SystemHealthService::CHECK_FILE_SYSTEM]['metrics']['directories']), 'Directory status should be reported');
    })
    ->test('System Status Summary', function($test) {
        $healthService = SystemHealthService::getInstance();
        $summary = $healthService->getSystemStatusSummary();
        
        $test->assertTrue(isset($summary['overall_status']), 'Overall status should be provided');
        $test->assertTrue(isset($summary['checks']), 'Individual checks should be provided');
        $test->assertTrue(is_array($summary['checks']), 'Checks should be an array');
    })
    ->end();

// Test Security System Integration
$framework->suite('Security System Integration', TestFramework::TYPE_SECURITY)
    ->setup(function() {
        // Create test user for security tests
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
            ");
            $stmt->execute([
                'test_security_user',
                '<EMAIL>',
                password_hash('TestPassword123!', PASSWORD_DEFAULT),
                'Security',
                'Test',
                'user',
                'active',
                1
            ]);
        } catch (Exception $e) {
            // Ignore setup errors
        }
    })
    ->test('Security Audit Service', function($test) {
        $auditService = SecurityAuditService::getInstance();
        $test->assertNotNull($auditService, 'Security audit service should be initialized');
        
        // Test logging
        $auditService->logAuthEvent('test_login', 1, ['ip' => '127.0.0.1']);
        $auditService->logSystemEvent('test_system_event', ['component' => 'test']);
        
        $test->assertTrue(true, 'Security events should be logged without errors');
    })
    ->test('Two-Factor Authentication Service', function($test) {
        $twoFAService = TwoFactorAuthService::getInstance();
        $test->assertNotNull($twoFAService, '2FA service should be initialized');
        
        // Test secret generation
        $secret = $twoFAService->generateSecret();
        $test->assertNotNull($secret, 'Secret should be generated');
        $test->assertEqual(32, strlen($secret), 'Secret should be 32 characters long');
        
        // Test QR code URL generation
        $qrUrl = $twoFAService->getQRCodeUrl($secret, '<EMAIL>');
        $test->assertStringContains('chart.googleapis.com', $qrUrl, 'QR code URL should be valid');
        
        // Test code verification (should fail with invalid code)
        $verifyResult = $twoFAService->verifyCode($secret, '000000');
        $test->assertFalse($verifyResult, 'Invalid code should fail verification');
    })
    ->test('Security Middleware Integration', function($test) {
        $middleware = SecurityMiddleware::getInstance();
        $test->assertNotNull($middleware, 'Security middleware should be initialized');
        
        // Test configuration
        $config = $middleware->getConfig();
        $test->assertTrue(is_array($config), 'Configuration should be an array');
        $test->assertTrue(isset($config['csrf_protection']), 'CSRF protection should be configured');
        $test->assertTrue(isset($config['xss_protection']), 'XSS protection should be configured');
    })
    ->teardown(function() {
        // Clean up test user
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("DELETE FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
    })
    ->end();

// Test Communication System Integration
$framework->suite('Communication System Integration', TestFramework::TYPE_INTEGRATION)
    ->setup(function() {
        // Create test user for communication tests
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
            ");
            $stmt->execute([
                'test_comm_user',
                '<EMAIL>',
                password_hash('TestPassword123!', PASSWORD_DEFAULT),
                'Communication',
                'Test',
                'user',
                'active',
                1
            ]);
        } catch (Exception $e) {
            // Ignore setup errors
        }
    })
    ->test('Email Notification Service', function($test) {
        $emailService = EmailNotificationService::getInstance();
        $test->assertNotNull($emailService, 'Email notification service should be initialized');
        
        // Test notification preferences
        $notificationsEnabled = $emailService->areNotificationsEnabled('general');
        $test->assertTrue(is_bool($notificationsEnabled), 'Notification preferences should return boolean');
    })
    ->test('Support Ticket Service', function($test) {
        $supportService = SupportTicketService::getInstance();
        $test->assertNotNull($supportService, 'Support ticket service should be initialized');
        
        // Test ticket statistics
        $statsResult = $supportService->getTicketStatistics(1);
        $test->assertTrue($statsResult['success'], 'Ticket statistics should be retrievable');
        $test->assertTrue(isset($statsResult['statistics']), 'Statistics should be provided');
    })
    ->test('Email-Support Integration', function($test) {
        $supportService = SupportTicketService::getInstance();
        
        // Create a test ticket (this should trigger email notifications)
        $result = $supportService->createTicket(
            1,
            'Integration Test Ticket',
            'This is a test ticket to verify email-support integration works correctly.',
            'technical',
            'medium'
        );
        
        $test->assertTrue($result['success'], 'Ticket creation should succeed');
        $test->assertNotNull($result['ticket_id'], 'Ticket ID should be provided');
        
        // Clean up test ticket
        if ($result['success']) {
            try {
                $db = Database::getInstance()->getConnection();
                $stmt = $db->prepare("DELETE FROM support_tickets WHERE id = ?");
                $stmt->execute([$result['ticket_id']]);
            } catch (Exception $e) {
                // Ignore cleanup errors
            }
        }
    })
    ->teardown(function() {
        // Clean up test user
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("DELETE FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
    })
    ->end();

// Test Performance and Load
$framework->suite('Performance Tests', TestFramework::TYPE_PERFORMANCE)
    ->test('Database Query Performance', function($test) {
        $startTime = microtime(true);
        
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->query("SELECT COUNT(*) FROM users");
            $count = $stmt->fetchColumn();
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            $test->assertTrue($duration < 1000, 'Database query should complete within 1 second');
            $test->assertTrue($count >= 0, 'Query should return valid result');
        } catch (Exception $e) {
            $test->assertTrue(false, 'Database query should not throw exception: ' . $e->getMessage());
        }
    })
    ->test('Memory Usage', function($test) {
        $startMemory = memory_get_usage(true);
        
        // Perform some operations
        $errorHandler = ErrorHandlingService::getInstance();
        $healthService = SystemHealthService::getInstance();
        $auditService = SecurityAuditService::getInstance();
        
        $endMemory = memory_get_usage(true);
        $memoryIncrease = $endMemory - $startMemory;
        
        // Memory increase should be reasonable (less than 10MB)
        $test->assertTrue($memoryIncrease < 10485760, 'Memory usage should be reasonable');
    })
    ->test('Error Handling Performance', function($test) {
        $errorHandler = ErrorHandlingService::getInstance();
        
        $startTime = microtime(true);
        
        // Log multiple errors
        for ($i = 0; $i < 10; $i++) {
            $errorHandler->logApplicationError(
                "Performance test error $i",
                ErrorHandlingService::CATEGORY_SYSTEM,
                ErrorHandlingService::SEVERITY_LOW
            );
        }
        
        $duration = (microtime(true) - $startTime) * 1000;
        
        $test->assertTrue($duration < 5000, 'Error logging should be fast (< 5 seconds for 10 errors)');
    })
    ->end();

// Run all tests and output results
echo $framework->run('html');

// Save results to file
$timestamp = date('Y-m-d_H-i-s');
$framework->saveResults("comprehensive_test_results_$timestamp.html", 'html');
$framework->saveResults("comprehensive_test_results_$timestamp.json", 'json');
$framework->saveResults("comprehensive_test_results_$timestamp.xml", 'xml');

echo "<div class='alert alert-info mt-4'>
    <i class='fas fa-info-circle me-2'></i>
    Test results have been saved to the test_results directory.
</div>";
?>