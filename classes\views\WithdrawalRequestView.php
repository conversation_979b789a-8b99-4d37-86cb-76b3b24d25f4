<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * WithdrawalRequestView - Displays withdrawal request form
 */
class WithdrawalRequestView extends BaseView {
    use StatusColorTrait;
    
    protected function getTitle() {
        return 'Request Withdrawal - Coinage Trading';
    }
    
    protected function getAdditionalCSS() {
        return [
            '/assets/css/withdrawal-request.css'
        ];
    }
    
    protected function getAdditionalJS() {
        return [
            '/assets/js/withdrawal-request.js'
        ];
    }
    
    protected function renderBody() {
        $user = $this->data['user'];
        $errors = $this->data['errors'];
        $withdrawalData = $this->data['withdrawal_data'];
        $canWithdraw = $this->data['can_withdraw'];
        $withdrawalMessage = $this->data['withdrawal_message'];
        $minimumAmount = $this->data['minimum_amount'];
        $withdrawalMethods = $this->data['withdrawal_methods'];
        $recentWithdrawals = $this->data['recent_withdrawals'];
        $csrfToken = $this->data['csrf_token'];
        ?>
        <div class="container-fluid">
            <!-- Page Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0">Request Withdrawal</h1>
                            <p class="text-muted">Withdraw funds from your account</p>
                        </div>
                        <div>
                            <a href="status.php" class="btn btn-outline-primary">
                                <i class="fas fa-list"></i> View Status
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Account Balance Card -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">Available Balance</h6>
                                    <h3 class="mb-0">$<?= number_format($user->balance, 2) ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-wallet fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">Minimum Withdrawal</h6>
                                    <h3 class="mb-0">$<?= number_format($minimumAmount, 2) ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-info-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="card-title mb-1">Total Withdrawn</h6>
                                    <h3 class="mb-0">$<?= number_format($user->total_withdrawal, 2) ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-arrow-down fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- Withdrawal Form -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-minus-circle"></i> Withdrawal Request
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!$canWithdraw): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Withdrawal Not Available:</strong> <?= htmlspecialchars($withdrawalMessage) ?>
                            </div>
                            <?php else: ?>
                            
                            <?php if (!empty($errors['general'])): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i>
                                <?= htmlspecialchars($errors['general']) ?>
                            </div>
                            <?php endif; ?>
                            
                            <form method="POST" id="withdrawalForm">
                                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrfToken) ?>">
                                
                                <!-- Amount -->
                                <div class="mb-3">
                                    <label for="amount" class="form-label">
                                        Withdrawal Amount <span class="text-danger">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" 
                                               name="amount" 
                                               id="amount" 
                                               class="form-control <?= !empty($errors['amount']) ? 'is-invalid' : '' ?>"
                                               value="<?= htmlspecialchars($withdrawalData['amount'] ?? '') ?>"
                                               min="<?= $minimumAmount ?>"
                                               max="<?= $user->balance ?>"
                                               step="0.01"
                                               required>
                                    </div>
                                    <?php if (!empty($errors['amount'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?= htmlspecialchars($errors['amount']) ?>
                                    </div>
                                    <?php endif; ?>
                                    <div class="form-text">
                                        Minimum: $<?= number_format($minimumAmount, 2) ?> | 
                                        Maximum: $<?= number_format($user->balance, 2) ?>
                                    </div>
                                </div>
                                
                                <!-- Withdrawal Method -->
                                <div class="mb-3">
                                    <label for="withdrawal_method" class="form-label">
                                        Withdrawal Method <span class="text-danger">*</span>
                                    </label>
                                    <select name="withdrawal_method" 
                                            id="withdrawal_method" 
                                            class="form-select <?= !empty($errors['withdrawal_method']) ? 'is-invalid' : '' ?>"
                                            required>
                                        <option value="">Select withdrawal method</option>
                                        <?php foreach ($withdrawalMethods as $method): ?>
                                        <option value="<?= htmlspecialchars($method->name) ?>"
                                                <?= ($withdrawalData['withdrawal_method'] ?? '') === $method->name ? 'selected' : '' ?>
                                                data-type="<?= htmlspecialchars($method->type) ?>"
                                                data-fields="<?= htmlspecialchars(json_encode($method->getRequiredFields())) ?>">
                                            <?= htmlspecialchars($method->name) ?>
                                            <?php if ($method->processing_time): ?>
                                            (<?= htmlspecialchars($method->processing_time) ?>)
                                            <?php endif; ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if (!empty($errors['withdrawal_method'])): ?>
                                    <div class="invalid-feedback">
                                        <?= htmlspecialchars($errors['withdrawal_method']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Dynamic Account Details -->
                                <div id="accountDetailsContainer" class="mb-3" style="display: none;">
                                    <label class="form-label">
                                        Account Details <span class="text-danger">*</span>
                                    </label>
                                    <div id="accountDetailsFields">
                                        <!-- Dynamic fields will be inserted here -->
                                    </div>
                                    <?php if (!empty($errors['account_details'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?= htmlspecialchars($errors['account_details']) ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Terms and Conditions -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               name="agree_terms" 
                                               id="agree_terms" 
                                               class="form-check-input"
                                               required>
                                        <label for="agree_terms" class="form-check-label">
                                            I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">withdrawal terms and conditions</a>
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Submit Button -->
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-paper-plane"></i> Submit Withdrawal Request
                                    </button>
                                </div>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Withdrawals -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-history"></i> Recent Withdrawals
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (empty($recentWithdrawals)): ?>
                            <div class="text-center py-3">
                                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                <p class="text-muted mb-0">No recent withdrawals</p>
                            </div>
                            <?php else: ?>
                            <div class="withdrawal-list">
                                <?php foreach ($recentWithdrawals as $withdrawal): ?>
                                <div class="withdrawal-item mb-3 pb-3 border-bottom">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-medium">$<?= number_format($withdrawal->amount, 2) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($withdrawal->withdrawal_method) ?></small>
                                        </div>
                                        <span class="badge bg-<?= $this->getStatusColor($withdrawal->status) ?>">
                                            <?= ucfirst($withdrawal->status) ?>
                                        </span>
                                    </div>
                                    <div class="mt-1">
                                        <small class="text-muted">
                                            <?= date('M j, Y g:i A', strtotime($withdrawal->created_at)) ?>
                                        </small>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="text-center">
                                <a href="status.php" class="btn btn-outline-primary btn-sm">
                                    View All Withdrawals
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Withdrawal Information -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Important Information
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-clock text-warning me-2"></i>
                                    Processing time: 1-3 business days
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-shield-alt text-success me-2"></i>
                                    All withdrawals are manually reviewed
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-dollar-sign text-info me-2"></i>
                                    Minimum withdrawal: $<?= number_format($minimumAmount, 2) ?>
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    Email notifications for status updates
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Terms Modal -->
        <div class="modal fade" id="termsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Withdrawal Terms and Conditions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>Processing Time</h6>
                        <p>Withdrawal requests are processed within 1-3 business days. Processing times may vary depending on the withdrawal method and verification requirements.</p>
                        
                        <h6>Verification Requirements</h6>
                        <p>All withdrawal requests are subject to identity verification and may require additional documentation. Large withdrawals may require enhanced verification.</p>
                        
                        <h6>Fees and Limits</h6>
                        <p>Withdrawal fees may apply depending on the selected method. Daily and monthly withdrawal limits may be enforced based on your account status.</p>
                        
                        <h6>Security</h6>
                        <p>For security purposes, withdrawals can only be made to verified accounts in your name. We reserve the right to request additional verification for any withdrawal request.</p>
                        
                        <h6>Cancellation</h6>
                        <p>Withdrawal requests can be cancelled only while they are in pending status. Once processing begins, cancellation may not be possible.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
?>