<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $tradeId = intval($input['trade_id'] ?? 0);
    
    if (!$tradeId) {
        echo json_encode(['success' => false, 'message' => 'Trade ID is required']);
        exit;
    }
    
    // Get the trade details for audit log
    $stmt = $pdo->prepare("SELECT * FROM trades WHERE id = ?");
    $stmt->execute([$tradeId]);
    $trade = $stmt->fetch();
    
    if (!$trade) {
        echo json_encode(['success' => false, 'message' => 'Trade not found']);
        exit;
    }
    
    $pdo->beginTransaction();
    
    try {
        // Delete related transactions first (if any)
        $stmt = $pdo->prepare("DELETE FROM transactions WHERE reference_id = ? AND reference_type = 'trade'");
        $stmt->execute([$tradeId]);
        
        // Delete the trade
        $stmt = $pdo->prepare("DELETE FROM trades WHERE id = ?");
        $result = $stmt->execute([$tradeId]);
        
        if ($result) {
            $pdo->commit();
            
            // Log the action
            AuditTrailService::log(
                'trade_deleted',
                'trade',
                $tradeId,
                [
                    'deleted_trade' => [
                        'user_id' => $trade['user_id'],
                        'asset' => $trade['asset'],
                        'trade_type' => $trade['trade_type'],
                        'amount' => $trade['amount'],
                        'entry_price' => $trade['entry_price'],
                        'exit_price' => $trade['exit_price'],
                        'profit_loss' => $trade['profit_loss'],
                        'status' => $trade['status'],
                        'opened_at' => $trade['opened_at'],
                        'closed_at' => $trade['closed_at']
                    ],
                    'reason' => 'Admin deletion'
                ]
            );
            
            echo json_encode([
                'success' => true,
                'message' => 'Trade deleted successfully'
            ]);
        } else {
            $pdo->rollback();
            echo json_encode([
                'success' => false,
                'message' => 'Failed to delete trade'
            ]);
        }
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Delete trade error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while deleting the trade'
    ]);
}
?>