<?php
require_once '../../includes/security_init.php';
require_once '../../classes/controllers/AuthController.php';
require_once '../../classes/services/SupportTicketService.php';
require_once '../../classes/views/BaseView.php';

// Require authentication
AuthController::requireAuth();

$errors = [];
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRF($_POST['csrf_token'] ?? '')) {
        $errors['csrf'] = 'Invalid security token';
    } else {
        $supportService = SupportTicketService::getInstance();
        
        $result = $supportService->createTicket(
            $_SESSION['user_id'],
            $_POST['subject'] ?? '',
            $_POST['message'] ?? '',
            $_POST['category'] ?? 'general',
            $_POST['priority'] ?? 'medium'
        );
        
        if ($result['success']) {
            $success = true;
            $message = $result['message'];
            $ticketId = $result['ticket_id'];
        } else {
            $errors = $result['errors'];
        }
    }
}

// Create view
class SupportTicketCreateView extends BaseView {
    private $errors;
    private $message;
    private $success;
    private $ticketId;
    
    public function __construct($errors = [], $message = '', $success = false, $ticketId = null) {
        parent::__construct();
        $this->errors = $errors;
        $this->message = $message;
        $this->success = $success;
        $this->ticketId = $ticketId;
        $this->setTitle('Create Support Ticket - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/support.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container mt-4">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-life-ring me-2"></i>Create Support Ticket</h4>
                        </div>
                        
                        <div class="card-body">
                            <?php if (!empty($this->errors)): ?>
                                <div class="alert alert-danger">
                                    <?php foreach ($this->errors as $error): ?>
                                        <div><?= htmlspecialchars($error) ?></div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($this->success): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?= htmlspecialchars($this->message) ?>
                                </div>
                                
                                <div class="text-center">
                                    <a href="/user/support/ticket.php?id=<?= $this->ticketId ?>" class="btn btn-primary">
                                        <i class="fas fa-eye me-2"></i>View Ticket
                                    </a>
                                    <a href="/user/support/" class="btn btn-secondary">
                                        <i class="fas fa-list me-2"></i>All Tickets
                                    </a>
                                </div>
                            <?php else: ?>
                                <form method="POST" id="supportTicketForm">
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars(CSRFProtection::generateToken()) ?>">
                                    
                                    <div class="row">
                                        <div class="col-md-8 mb-3">
                                            <label for="subject" class="form-label">Subject *</label>
                                            <input type="text" 
                                                   class="form-control" 
                                                   id="subject" 
                                                   name="subject" 
                                                   value="<?= htmlspecialchars($_POST['subject'] ?? '') ?>"
                                                   maxlength="200"
                                                   required>
                                            <div class="form-text">Brief description of your issue (5-200 characters)</div>
                                        </div>
                                        
                                        <div class="col-md-4 mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select" id="priority" name="priority">
                                                <option value="low" <?= ($_POST['priority'] ?? 'medium') === 'low' ? 'selected' : '' ?>>Low</option>
                                                <option value="medium" <?= ($_POST['priority'] ?? 'medium') === 'medium' ? 'selected' : '' ?>>Medium</option>
                                                <option value="high" <?= ($_POST['priority'] ?? 'medium') === 'high' ? 'selected' : '' ?>>High</option>
                                                <option value="urgent" <?= ($_POST['priority'] ?? 'medium') === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Category</label>
                                        <select class="form-select" id="category" name="category">
                                            <option value="general" <?= ($_POST['category'] ?? 'general') === 'general' ? 'selected' : '' ?>>General Inquiry</option>
                                            <option value="account" <?= ($_POST['category'] ?? 'general') === 'account' ? 'selected' : '' ?>>Account Issues</option>
                                            <option value="deposit" <?= ($_POST['category'] ?? 'general') === 'deposit' ? 'selected' : '' ?>>Deposit Problems</option>
                                            <option value="withdrawal" <?= ($_POST['category'] ?? 'general') === 'withdrawal' ? 'selected' : '' ?>>Withdrawal Issues</option>
                                            <option value="technical" <?= ($_POST['category'] ?? 'general') === 'technical' ? 'selected' : '' ?>>Technical Support</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="message" class="form-label">Message *</label>
                                        <textarea class="form-control" 
                                                  id="message" 
                                                  name="message" 
                                                  rows="8" 
                                                  maxlength="5000"
                                                  required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                                        <div class="form-text">
                                            Detailed description of your issue (20-5000 characters)
                                            <span id="charCount" class="float-end">0/5000</span>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Before submitting:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Check our FAQ section for common solutions</li>
                                            <li>Provide as much detail as possible about your issue</li>
                                            <li>Include any error messages you received</li>
                                            <li>You will receive email updates on your ticket progress</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <a href="/user/support/" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Support
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i>Create Ticket
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script>
            // Character counter
            const messageTextarea = document.getElementById('message');
            const charCount = document.getElementById('charCount');
            
            function updateCharCount() {
                const count = messageTextarea.value.length;
                charCount.textContent = count + '/5000';
                
                if (count > 4500) {
                    charCount.style.color = '#dc3545';
                } else if (count > 4000) {
                    charCount.style.color = '#ffc107';
                } else {
                    charCount.style.color = '#6c757d';
                }
            }
            
            messageTextarea.addEventListener('input', updateCharCount);
            updateCharCount();
            
            // Form validation
            document.getElementById('supportTicketForm').addEventListener('submit', function(e) {
                const subject = document.getElementById('subject').value.trim();
                const message = document.getElementById('message').value.trim();
                
                if (subject.length < 5) {
                    e.preventDefault();
                    alert('Subject must be at least 5 characters long.');
                    return false;
                }
                
                if (message.length < 20) {
                    e.preventDefault();
                    alert('Message must be at least 20 characters long.');
                    return false;
                }
            });
            
            // Priority color coding
            const prioritySelect = document.getElementById('priority');
            function updatePriorityColor() {
                const priority = prioritySelect.value;
                prioritySelect.className = 'form-select';
                
                switch(priority) {
                    case 'urgent':
                        prioritySelect.classList.add('border-danger');
                        break;
                    case 'high':
                        prioritySelect.classList.add('border-warning');
                        break;
                    case 'medium':
                        prioritySelect.classList.add('border-info');
                        break;
                    default:
                        prioritySelect.classList.add('border-secondary');
                }
            }
            
            prioritySelect.addEventListener('change', updatePriorityColor);
            updatePriorityColor();
        </script>
        <?php
    }
}

$view = new SupportTicketCreateView($errors, $message, $success, $ticketId ?? null);
$view->render();
?>