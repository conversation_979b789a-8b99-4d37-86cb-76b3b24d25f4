<?php
require_once 'config.php';
require_once 'classes/views/BaseView.php';

class SimpleTestView extends BaseView {
    public function __construct() {
        parent::__construct('Test View');
    }
    
    protected function renderContent() {
        echo "<h1>Hello from Simple Test View!</h1>";
        echo "<p>This is a test of the BaseView rendering system.</p>";
    }
}

echo "<h2>Testing Simple View</h2>";

try {
    $view = new SimpleTestView();
    echo "<p>View created successfully</p>";
    
    $output = $view->render();
    echo "<p>View rendered, output length: " . strlen($output) . "</p>";
    
    if (strlen($output) > 0) {
        echo "<hr><h3>Rendered Output:</h3>";
        echo htmlspecialchars($output);
    } else {
        echo "<p>No output generated!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>