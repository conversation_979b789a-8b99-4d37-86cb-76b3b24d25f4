<?php

/**
 * Reusable UI Components for Coinage Trading Platform
 */

class UIComponents {
    
    /**
     * Generate alert component
     */
    public static function alert($message, $type = 'info', $dismissible = true, $permanent = false) {
        $alertClass = "alert alert-{$type}";
        if ($dismissible) {
            $alertClass .= " alert-dismissible fade show";
        }
        if ($permanent) {
            $alertClass .= " alert-permanent";
        }
        
        $html = "<div class=\"{$alertClass}\" role=\"alert\">";
        $html .= htmlspecialchars($message);
        
        if ($dismissible) {
            $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * Generate modal component
     */
    public static function modal($id, $title, $body, $footer = '', $size = '') {
        $modalClass = "modal fade";
        $dialogClass = "modal-dialog";
        
        if ($size) {
            $dialogClass .= " modal-{$size}";
        }
        
        $html = "
        <div class=\"{$modalClass}\" id=\"{$id}\" tabindex=\"-1\" aria-labelledby=\"{$id}Label\" aria-hidden=\"true\">
            <div class=\"{$dialogClass}\">
                <div class=\"modal-content\">
                    <div class=\"modal-header\">
                        <h5 class=\"modal-title\" id=\"{$id}Label\">" . htmlspecialchars($title) . "</h5>
                        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>
                    </div>
                    <div class=\"modal-body\">
                        {$body}
                    </div>";
        
        if ($footer) {
            $html .= "<div class=\"modal-footer\">{$footer}</div>";
        }
        
        $html .= "
                </div>
            </div>
        </div>";
        
        return $html;
    }
    
    /**
     * Generate form input component
     */
    public static function formInput($name, $label, $type = 'text', $value = '', $required = false, $placeholder = '', $help = '') {
        $requiredAttr = $required ? 'required' : '';
        $requiredLabel = $required ? '<span class="text-danger">*</span>' : '';
        
        $html = "<div class=\"mb-3\">";
        $html .= "<label for=\"{$name}\" class=\"form-label\">{$label} {$requiredLabel}</label>";
        
        if ($type === 'textarea') {
            $html .= "<textarea class=\"form-control\" id=\"{$name}\" name=\"{$name}\" placeholder=\"{$placeholder}\" {$requiredAttr}>" . htmlspecialchars($value) . "</textarea>";
        } elseif ($type === 'select') {
            $html .= "<select class=\"form-select\" id=\"{$name}\" name=\"{$name}\" {$requiredAttr}>";
            if (is_array($value)) {
                foreach ($value as $optValue => $optLabel) {
                    $html .= "<option value=\"{$optValue}\">{$optLabel}</option>";
                }
            }
            $html .= "</select>";
        } else {
            $html .= "<input type=\"{$type}\" class=\"form-control\" id=\"{$name}\" name=\"{$name}\" value=\"" . htmlspecialchars($value) . "\" placeholder=\"{$placeholder}\" {$requiredAttr}>";
        }
        
        if ($help) {
            $html .= "<div class=\"form-text\">{$help}</div>";
        }
        
        $html .= "</div>";
        return $html;
    }
    
    /**
     * Generate stats card component
     */
    public static function statsCard($title, $value, $icon, $color = 'primary', $change = null) {
        $html = "
        <div class=\"col-md-6 col-lg-3 mb-4\">
            <div class=\"card stats-card h-100\">
                <div class=\"card-body\">
                    <div class=\"d-flex align-items-center\">
                        <div class=\"flex-shrink-0\">
                            <div class=\"bg-{$color} text-white rounded-circle p-3\">
                                <i class=\"{$icon} fa-lg\"></i>
                            </div>
                        </div>
                        <div class=\"flex-grow-1 ms-3\">
                            <h6 class=\"card-title text-muted mb-1\">{$title}</h6>
                            <h4 class=\"card-text mb-0\">{$value}</h4>";
        
        if ($change !== null) {
            $changeClass = $change >= 0 ? 'text-success' : 'text-danger';
            $changeIcon = $change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
            $html .= "<small class=\"{$changeClass}\"><i class=\"fas {$changeIcon}\"></i> " . abs($change) . "%</small>";
        }
        
        $html .= "
                        </div>
                    </div>
                </div>
            </div>
        </div>";
        
        return $html;
    }
    
    /**
     * Generate data table component
     */
    public static function dataTable($id, $headers, $data, $actions = []) {
        $html = "
        <div class=\"table-responsive\">
            <table class=\"table table-striped table-hover\" id=\"{$id}\">
                <thead class=\"table-dark\">
                    <tr>";
        
        foreach ($headers as $header) {
            $html .= "<th>" . htmlspecialchars($header) . "</th>";
        }
        
        if (!empty($actions)) {
            $html .= "<th>Actions</th>";
        }
        
        $html .= "
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($data as $row) {
            $html .= "<tr>";
            foreach ($row as $cell) {
                $html .= "<td>" . htmlspecialchars($cell) . "</td>";
            }
            
            if (!empty($actions)) {
                $html .= "<td>";
                foreach ($actions as $action) {
                    $html .= "<button class=\"btn btn-sm btn-{$action['color']} me-1\" onclick=\"{$action['onclick']}\">";
                    $html .= "<i class=\"{$action['icon']}\"></i> {$action['label']}";
                    $html .= "</button>";
                }
                $html .= "</td>";
            }
            
            $html .= "</tr>";
        }
        
        $html .= "
                </tbody>
            </table>
        </div>";
        
        return $html;
    }
    
    /**
     * Generate breadcrumb component
     */
    public static function breadcrumb($items) {
        $html = "<nav aria-label=\"breadcrumb\">";
        $html .= "<ol class=\"breadcrumb\">";
        
        $lastIndex = count($items) - 1;
        foreach ($items as $index => $item) {
            if ($index === $lastIndex) {
                $html .= "<li class=\"breadcrumb-item active\" aria-current=\"page\">{$item['label']}</li>";
            } else {
                $html .= "<li class=\"breadcrumb-item\"><a href=\"{$item['url']}\">{$item['label']}</a></li>";
            }
        }
        
        $html .= "</ol>";
        $html .= "</nav>";
        
        return $html;
    }
    
    /**
     * Generate pagination component
     */
    public static function pagination($currentPage, $totalPages, $baseUrl, $params = []) {
        if ($totalPages <= 1) {
            return '';
        }
        
        $html = "<nav aria-label=\"Page navigation\">";
        $html .= "<ul class=\"pagination justify-content-center\">";
        
        // Previous button
        $prevDisabled = $currentPage <= 1 ? 'disabled' : '';
        $prevPage = max(1, $currentPage - 1);
        $prevUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $prevPage]));
        
        $html .= "<li class=\"page-item {$prevDisabled}\">";
        $html .= "<a class=\"page-link\" href=\"{$prevUrl}\">Previous</a>";
        $html .= "</li>";
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        if ($start > 1) {
            $url = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => 1]));
            $html .= "<li class=\"page-item\"><a class=\"page-link\" href=\"{$url}\">1</a></li>";
            if ($start > 2) {
                $html .= "<li class=\"page-item disabled\"><span class=\"page-link\">...</span></li>";
            }
        }
        
        for ($i = $start; $i <= $end; $i++) {
            $active = $i === $currentPage ? 'active' : '';
            $url = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $i]));
            $html .= "<li class=\"page-item {$active}\"><a class=\"page-link\" href=\"{$url}\">{$i}</a></li>";
        }
        
        if ($end < $totalPages) {
            if ($end < $totalPages - 1) {
                $html .= "<li class=\"page-item disabled\"><span class=\"page-link\">...</span></li>";
            }
            $url = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $totalPages]));
            $html .= "<li class=\"page-item\"><a class=\"page-link\" href=\"{$url}\">{$totalPages}</a></li>";
        }
        
        // Next button
        $nextDisabled = $currentPage >= $totalPages ? 'disabled' : '';
        $nextPage = min($totalPages, $currentPage + 1);
        $nextUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $nextPage]));
        
        $html .= "<li class=\"page-item {$nextDisabled}\">";
        $html .= "<a class=\"page-link\" href=\"{$nextUrl}\">Next</a>";
        $html .= "</li>";
        
        $html .= "</ul>";
        $html .= "</nav>";
        
        return $html;
    }
    
    /**
     * Generate loading spinner component
     */
    public static function loadingSpinner($size = 'md', $text = 'Loading...') {
        $spinnerClass = "spinner-border";
        if ($size === 'sm') {
            $spinnerClass .= " spinner-border-sm";
        } elseif ($size === 'lg') {
            $spinnerClass .= " spinner-border-lg";
        }
        
        $html = "
        <div class=\"d-flex justify-content-center align-items-center p-4\">
            <div class=\"{$spinnerClass} text-primary me-2\" role=\"status\">
                <span class=\"visually-hidden\">Loading...</span>
            </div>
            <span>{$text}</span>
        </div>";
        
        return $html;
    }
    
    /**
     * Generate status badge component
     */
    public static function statusBadge($status, $customColors = []) {
        $colors = array_merge([
            'active' => 'success',
            'inactive' => 'secondary',
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'completed' => 'success',
            'processing' => 'info',
            'cancelled' => 'danger',
            'suspended' => 'warning'
        ], $customColors);
        
        $color = $colors[$status] ?? 'secondary';
        $label = ucfirst(str_replace('_', ' ', $status));
        
        return "<span class=\"badge bg-{$color}\">{$label}</span>";
    }
    
    /**
     * Generate confirmation button with modal
     */
    public static function confirmButton($text, $action, $message, $color = 'danger') {
        $modalId = 'confirm_' . md5($action);
        
        $button = "<button type=\"button\" class=\"btn btn-{$color}\" data-bs-toggle=\"modal\" data-bs-target=\"#{$modalId}\">{$text}</button>";
        
        $modalFooter = "
            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>
            <button type=\"button\" class=\"btn btn-{$color}\" onclick=\"{$action}\" data-bs-dismiss=\"modal\">Confirm</button>
        ";
        
        $modal = self::modal($modalId, 'Confirm Action', $message, $modalFooter);
        
        return $button . $modal;
    }
}