<?php
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * Input Sanitizer Service
 * Centralized input sanitization with context-aware cleaning
 */
class InputSanitizer {
    
    /**
     * Context-specific sanitization rules
     */
    private static $contextRules = [
        'financial' => [
            'preserve_decimal' => true,
            'allow_negative' => false,
            'max_decimal_places' => 2
        ],
        'user_profile' => [
            'allow_html' => false,
            'trim_whitespace' => true,
            'normalize_case' => true
        ],
        'search' => [
            'escape_wildcards' => true,
            'min_length' => 2,
            'max_length' => 100
        ]
    ];
    
    /**
     * Sanitize form data based on context with improved rules
     */
    public static function sanitizeForm($data, $context = 'general') {
        if (!is_array($data)) {
            throw new InvalidArgumentException('Data must be an array');
        }
        
        $sanitized = [];
        $rules = self::$contextRules[$context] ?? [];
        
        foreach ($data as $key => $value) {
            $sanitized[$key] = self::sanitizeField($value, $key, $context, $rules);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize individual field based on type and context
     */
    public static function sanitizeField($value, $fieldName, $context = 'general') {
        // Don't sanitize passwords and tokens
        if (in_array($fieldName, ['password', 'current_password', 'new_password', 'password_confirmation', 'csrf_token'])) {
            return $value;
        }
        
        // Handle arrays recursively
        if (is_array($value)) {
            return array_map(function($item) use ($fieldName, $context) {
                return self::sanitizeField($item, $fieldName, $context);
            }, $value);
        }
        
        // Convert to string if not already
        $value = (string) $value;
        
        // Apply context-specific sanitization
        switch ($context) {
            case 'financial':
                return self::sanitizeFinancialField($value, $fieldName);
            case 'user_profile':
                return self::sanitizeProfileField($value, $fieldName);
            case 'search':
                return self::sanitizeSearchField($value);
            case 'admin':
                return self::sanitizeAdminField($value, $fieldName);
            default:
                return self::sanitizeGeneralField($value, $fieldName);
        }
    }
    
    /**
     * Sanitize financial-related fields
     */
    private static function sanitizeFinancialField($value, $fieldName) {
        switch ($fieldName) {
            case 'amount':
                // Keep only numbers and decimal point
                return preg_replace('/[^0-9.]/', '', $value);
                
            case 'account_number':
            case 'routing_number':
                // Keep only numbers
                return preg_replace('/[^0-9]/', '', $value);
                
            case 'crypto_address':
                // Keep only alphanumeric characters
                return preg_replace('/[^a-zA-Z0-9]/', '', $value);
                
            case 'paypal_email':
                // Basic email sanitization
                return filter_var($value, FILTER_SANITIZE_EMAIL);
                
            default:
                return ValidationHelper::sanitize($value);
        }
    }
    
    /**
     * Sanitize user profile fields
     */
    private static function sanitizeProfileField($value, $fieldName) {
        switch ($fieldName) {
            case 'email':
                return strtolower(trim(filter_var($value, FILTER_SANITIZE_EMAIL)));
                
            case 'username':
                // Keep only alphanumeric and underscore
                return preg_replace('/[^a-zA-Z0-9_]/', '', $value);
                
            case 'phone':
                // Keep only numbers, spaces, hyphens, parentheses, and plus
                return preg_replace('/[^0-9\s\-\(\)\+]/', '', $value);
                
            case 'first_name':
            case 'last_name':
            case 'full_name':
                // Keep only letters, spaces, hyphens, apostrophes, and periods
                $value = preg_replace('/[^a-zA-Z\s\-\'\.]/', '', $value);
                return ucwords(strtolower(trim($value)));
                
            case 'date_of_birth':
                // Keep only numbers and hyphens
                return preg_replace('/[^0-9\-]/', '', $value);
                
            default:
                return ValidationHelper::sanitize($value);
        }
    }
    
    /**
     * Sanitize search fields
     */
    private static function sanitizeSearchField($value) {
        // Remove potentially dangerous characters but keep search-friendly ones
        $value = preg_replace('/[<>"\']/', '', $value);
        $value = ValidationHelper::preventXSS($value);
        return trim($value);
    }
    
    /**
     * Sanitize admin fields (more permissive for content)
     */
    private static function sanitizeAdminField($value, $fieldName) {
        switch ($fieldName) {
            case 'content':
            case 'description':
            case 'message':
                // Allow some HTML but remove dangerous tags
                $allowedTags = '<p><br><strong><em><u><ul><ol><li><a><h1><h2><h3><h4><h5><h6>';
                $value = strip_tags($value, $allowedTags);
                return ValidationHelper::preventXSS($value);
                
            default:
                return ValidationHelper::sanitize($value);
        }
    }
    
    /**
     * Sanitize general fields
     */
    private static function sanitizeGeneralField($value, $fieldName) {
        return ValidationHelper::sanitize($value);
    }
    
    /**
     * Deep sanitize array data
     */
    public static function deepSanitize($data, $context = 'general') {
        if (is_array($data)) {
            $sanitized = [];
            foreach ($data as $key => $value) {
                $sanitizedKey = ValidationHelper::sanitize($key);
                $sanitized[$sanitizedKey] = self::deepSanitize($value, $context);
            }
            return $sanitized;
        }
        
        return self::sanitizeField($data, 'unknown', $context);
    }
    
    /**
     * Sanitize file upload data
     */
    public static function sanitizeFileUpload($fileData) {
        if (!is_array($fileData)) {
            return $fileData;
        }
        
        $sanitized = [];
        foreach ($fileData as $key => $value) {
            switch ($key) {
                case 'name':
                    // Sanitize filename
                    $sanitized[$key] = self::sanitizeFilename($value);
                    break;
                case 'type':
                    // Keep MIME type as is but validate
                    $sanitized[$key] = self::sanitizeMimeType($value);
                    break;
                case 'tmp_name':
                case 'error':
                case 'size':
                    // Keep these as is
                    $sanitized[$key] = $value;
                    break;
                default:
                    $sanitized[$key] = ValidationHelper::sanitize($value);
                    break;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize filename
     */
    private static function sanitizeFilename($filename) {
        // Remove directory traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '_', $filename);
        
        // Prevent double extensions
        $filename = preg_replace('/\.+/', '.', $filename);
        
        // Limit length
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }
        
        return $filename;
    }
    
    /**
     * Sanitize MIME type
     */
    private static function sanitizeMimeType($mimeType) {
        // Keep only valid MIME type characters
        return preg_replace('/[^a-zA-Z0-9\/\-\+\.]/', '', $mimeType);
    }
    
    /**
     * Sanitize URL parameters
     */
    public static function sanitizeUrlParams($params) {
        $sanitized = [];
        
        foreach ($params as $key => $value) {
            $sanitizedKey = preg_replace('/[^a-zA-Z0-9_\-]/', '', $key);
            
            if (is_array($value)) {
                $sanitized[$sanitizedKey] = self::sanitizeUrlParams($value);
            } else {
                // URL decode first, then sanitize
                $value = urldecode($value);
                $sanitized[$sanitizedKey] = ValidationHelper::preventXSS($value);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize JSON data
     */
    public static function sanitizeJsonData($jsonString) {
        // First validate JSON
        $data = json_decode($jsonString, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        
        // Recursively sanitize the data
        $sanitized = self::deepSanitize($data);
        
        return json_encode($sanitized);
    }
    
    /**
     * Remove invisible characters
     */
    public static function removeInvisibleChars($str) {
        // Remove null bytes
        $str = str_replace(chr(0), '', $str);
        
        // Remove other control characters except tab, newline, and carriage return
        $str = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $str);
        
        return $str;
    }
    
    /**
     * Normalize whitespace
     */
    public static function normalizeWhitespace($str) {
        // Replace multiple whitespace with single space
        $str = preg_replace('/\s+/', ' ', $str);
        
        // Trim
        return trim($str);
    }
    
    /**
     * Sanitize for database storage
     */
    public static function sanitizeForDatabase($data, $fieldType = 'text') {
        if (is_array($data)) {
            return array_map(function($item) use ($fieldType) {
                return self::sanitizeForDatabase($item, $fieldType);
            }, $data);
        }
        
        // Remove invisible characters
        $data = self::removeInvisibleChars($data);
        
        switch ($fieldType) {
            case 'email':
                return strtolower(trim(filter_var($data, FILTER_SANITIZE_EMAIL)));
                
            case 'url':
                return filter_var($data, FILTER_SANITIZE_URL);
                
            case 'int':
                return (int) filter_var($data, FILTER_SANITIZE_NUMBER_INT);
                
            case 'float':
                return (float) filter_var($data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                
            case 'boolean':
                return filter_var($data, FILTER_VALIDATE_BOOLEAN);
                
            case 'text':
            default:
                return ValidationHelper::sanitize($data);
        }
    }
}
?>