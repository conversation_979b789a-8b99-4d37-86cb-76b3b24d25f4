<?php
session_start();

require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/controllers/AdminController.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: /login.php');
    exit();
}

try {
    // Get filters from request
    $filters = [
        'search' => $_GET['search'] ?? '',
        'status' => $_GET['status'] ?? '',
        'sort' => $_GET['sort'] ?? 'created_at'
    ];
    
    // Get all users (no pagination for export)
    $result = AdminController::getAllUsers($filters, 1, 10000);
    if ($result === false) {
        throw new Exception('Failed to load users for export');
    }
    
    $users = $result['users'];
    
    // Set headers for CSV download
    $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // Open output stream
    $output = fopen('php://output', 'w');
    
    // Write CSV headers
    fputcsv($output, [
        'ID',
        'Username',
        'Email',
        'First Name',
        'Last Name',
        'Balance',
        'Bonus',
        'Status',
        'Email Verified',
        'Registration Date',
        'Last Login',
        'Current Plan',
        'Total Deposits',
        'Total Withdrawals'
    ]);
    
    // Write user data
    foreach ($users as $user) {
        // Get additional user data
        $userDetails = AdminController::getUserDetails($user['id']);
        
        fputcsv($output, [
            $user['id'],
            $user['username'],
            $user['email'],
            $user['first_name'],
            $user['last_name'],
            number_format($user['balance'], 2),
            number_format($user['bonus'] ?? 0, 2),
            ucfirst($user['status']),
            $user['email_verified'] ? 'Yes' : 'No',
            date('Y-m-d H:i:s', strtotime($user['created_at'])),
            $user['last_login'] ? date('Y-m-d H:i:s', strtotime($user['last_login'])) : 'Never',
            $user['current_plan_name'] ?? 'None',
            number_format($userDetails['total_deposits'] ?? 0, 2),
            number_format($userDetails['total_withdrawals'] ?? 0, 2)
        ]);
    }
    
    fclose($output);
    
} catch (Exception $e) {
    error_log("Error exporting users: " . $e->getMessage());
    
    // Redirect back with error
    header('Location: /admin/users/?error=' . urlencode('Failed to export users'));
    exit();
}
?>