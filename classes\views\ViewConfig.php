<?php
/**
 * ViewConfig - Centralized view configuration
 */
class ViewConfig {
    
    /**
     * Get dashboard card configurations
     */
    public static function getDashboardBalanceCards($user) {
        return [
            [
                'title' => 'Current Balance',
                'value' => $user->balance,
                'icon' => 'fas fa-wallet',
                'color' => 'primary',
                'format' => 'currency'
            ],
            [
                'title' => 'Bonus Balance', 
                'value' => $user->bonus,
                'icon' => 'fas fa-gift',
                'color' => 'success',
                'format' => 'currency'
            ],
            [
                'title' => 'Total Deposits',
                'value' => $user->total_deposit,
                'icon' => 'fas fa-chart-line', 
                'color' => 'info',
                'format' => 'currency'
            ]
        ];
    }
    
    /**
     * Get dashboard statistics configuration
     */
    public static function getDashboardStatistics($stats) {
        return [
            [
                'title' => 'Total Deposits',
                'value' => '$' . number_format($stats['total_deposits'], 2),
                'icon' => 'fas fa-arrow-down',
                'color' => 'success'
            ],
            [
                'title' => 'Total Withdrawals', 
                'value' => '$' . number_format($stats['total_withdrawals'], 2),
                'icon' => 'fas fa-arrow-up',
                'color' => 'warning'
            ],
            [
                'title' => 'Pending Deposits',
                'value' => $stats['pending_deposits'],
                'icon' => 'fas fa-clock',
                'color' => 'info'
            ],
            [
                'title' => 'Days Active',
                'value' => $stats['account_age_days'],
                'icon' => 'fas fa-calendar',
                'color' => 'primary'
            ]
        ];
    }
    
    /**
     * Get quick action buttons configuration
     */
    public static function getQuickActions() {
        return [
            [
                'title' => 'Make Deposit',
                'url' => '/user/deposit/',
                'icon' => 'fas fa-plus',
                'color' => 'success'
            ],
            [
                'title' => 'Request Withdrawal',
                'url' => '/user/withdraw/',
                'icon' => 'fas fa-minus',
                'color' => 'warning'
            ],
            [
                'title' => 'View Transactions',
                'url' => '/user/transactions/',
                'icon' => 'fas fa-history',
                'color' => 'info'
            ],
            [
                'title' => 'Contact Support',
                'url' => '/user/support/',
                'icon' => 'fas fa-headset',
                'color' => 'secondary'
            ]
        ];
    }
}
?>