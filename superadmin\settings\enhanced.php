<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Enhanced System Settings';
$user = getCurrentUser();

// Handle file uploads
function handleFileUpload($file, $uploadDir = 'assets/uploads/') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Invalid file type. Only images are allowed.');
    }
    
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        throw new Exception('File size too large. Maximum 5MB allowed.');
    }
    
    $uploadPath = __DIR__ . '/../../' . $uploadDir;
    if (!is_dir($uploadPath)) {
        mkdir($uploadPath, 0755, true);
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $fullPath = $uploadPath . $filename;
    
    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return $uploadDir . $filename;
    }
    
    return false;
}

// Test SMTP function
function testSMTPConnection($settings, $testEmail) {
    // This is a simplified test - in production, use PHPMailer
    $subject = 'SMTP Test Email';
    $message = 'This is a test email to verify SMTP configuration.';
    $headers = "From: {$settings['from_name']} <{$settings['from_email']}>\r\n";
    $headers .= "Reply-To: {$settings['from_email']}\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    return mail($testEmail, $subject, $message, $headers);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        // Validate CSRF token
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_general':
                $settings = [
                    'site_name' => $_POST['site_name'] ?? '',
                    'company_name' => $_POST['company_name'] ?? '',
                    'footer_text' => $_POST['footer_text'] ?? '',
                    'contact_email' => $_POST['contact_email'] ?? '',
                    'contact_phone' => $_POST['contact_phone'] ?? '',
                    'company_address' => $_POST['company_address'] ?? ''
                ];
                
                foreach ($settings as $key => $value) {
                    SystemSetting::setValue($key, $value, 'string', $user['id']);
                }
                
                $response = ['success' => true, 'message' => 'General settings updated successfully'];
                break;
                
            case 'update_branding':
                $settings = [];
                
                // Handle logo uploads
                if (isset($_FILES['main_logo']) && $_FILES['main_logo']['error'] === UPLOAD_ERR_OK) {
                    $logoPath = handleFileUpload($_FILES['main_logo']);
                    if ($logoPath) {
                        $settings['main_logo'] = $logoPath;
                    }
                }
                
                if (isset($_FILES['favicon']) && $_FILES['favicon']['error'] === UPLOAD_ERR_OK) {
                    $faviconPath = handleFileUpload($_FILES['favicon']);
                    if ($faviconPath) {
                        $settings['favicon'] = $faviconPath;
                    }
                }
                
                if (isset($_FILES['light_logo']) && $_FILES['light_logo']['error'] === UPLOAD_ERR_OK) {
                    $lightLogoPath = handleFileUpload($_FILES['light_logo']);
                    if ($lightLogoPath) {
                        $settings['light_logo'] = $lightLogoPath;
                    }
                }
                
                if (isset($_FILES['dark_logo']) && $_FILES['dark_logo']['error'] === UPLOAD_ERR_OK) {
                    $darkLogoPath = handleFileUpload($_FILES['dark_logo']);
                    if ($darkLogoPath) {
                        $settings['dark_logo'] = $darkLogoPath;
                    }
                }
                
                // Logo size settings
                $settings['logo_width'] = intval($_POST['logo_width'] ?? 150);
                $settings['logo_height'] = intval($_POST['logo_height'] ?? 50);
                
                foreach ($settings as $key => $value) {
                    SystemSetting::setValue($key, $value, is_int($value) ? 'number' : 'string', $user['id']);
                }
                
                $response = ['success' => true, 'message' => 'Branding settings updated successfully'];
                break;
                
            case 'update_appearance':
                $settings = [
                    'primary_color' => $_POST['primary_color'] ?? '#007bff',
                    'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
                    'sidebar_bg_color' => $_POST['sidebar_bg_color'] ?? '#343a40',
                    'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff'
                ];
                
                foreach ($settings as $key => $value) {
                    SystemSetting::setValue($key, $value, 'string', $user['id']);
                }
                
                $response = ['success' => true, 'message' => 'Appearance settings updated successfully'];
                break;
                
            case 'update_smtp':
                $settings = [
                    'smtp_host' => $_POST['smtp_host'] ?? '',
                    'smtp_port' => intval($_POST['smtp_port'] ?? 587),
                    'smtp_username' => $_POST['smtp_username'] ?? '',
                    'smtp_password' => $_POST['smtp_password'] ?? '',
                    'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                    'smtp_from_email' => $_POST['smtp_from_email'] ?? '',
                    'smtp_from_name' => $_POST['smtp_from_name'] ?? ''
                ];
                
                foreach ($settings as $key => $value) {
                    $type = is_int($value) ? 'number' : 'string';
                    SystemSetting::setValue($key, $value, $type, $user['id']);
                }
                
                $response = ['success' => true, 'message' => 'SMTP settings updated successfully'];
                break;
                
            case 'test_smtp':
                $testEmail = $_POST['test_email'] ?? '';
                if (empty($testEmail)) {
                    throw new Exception('Test email address is required');
                }
                
                // Test SMTP configuration
                $smtpSettings = [
                    'host' => SystemSetting::getValue('smtp_host'),
                    'port' => SystemSetting::getValue('smtp_port', 587),
                    'username' => SystemSetting::getValue('smtp_username'),
                    'password' => SystemSetting::getValue('smtp_password'),
                    'encryption' => SystemSetting::getValue('smtp_encryption', 'tls'),
                    'from_email' => SystemSetting::getValue('smtp_from_email'),
                    'from_name' => SystemSetting::getValue('smtp_from_name')
                ];
                
                // Simple test (in real implementation, you'd use PHPMailer or similar)
                $testResult = testSMTPConnection($smtpSettings, $testEmail);
                
                if ($testResult) {
                    $response = ['success' => true, 'message' => 'Test email sent successfully'];
                } else {
                    throw new Exception('Failed to send test email. Please check your SMTP settings.');
                }
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for page reload
    $_SESSION['settings_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get response from session if available
$response = $_SESSION['settings_response'] ?? null;
unset($_SESSION['settings_response']);

// Get current settings
$currentSettings = [
    'site_name' => SystemSetting::getValue('site_name', 'Coinage Trading'),
    'company_name' => SystemSetting::getValue('company_name', ''),
    'footer_text' => SystemSetting::getValue('footer_text', ''),
    'contact_email' => SystemSetting::getValue('contact_email', ''),
    'contact_phone' => SystemSetting::getValue('contact_phone', ''),
    'company_address' => SystemSetting::getValue('company_address', ''),
    'main_logo' => SystemSetting::getValue('main_logo', ''),
    'favicon' => SystemSetting::getValue('favicon', ''),
    'light_logo' => SystemSetting::getValue('light_logo', ''),
    'dark_logo' => SystemSetting::getValue('dark_logo', ''),
    'logo_width' => SystemSetting::getValue('logo_width', 150),
    'logo_height' => SystemSetting::getValue('logo_height', 50),
    'primary_color' => SystemSetting::getValue('primary_color', '#007bff'),
    'secondary_color' => SystemSetting::getValue('secondary_color', '#6c757d'),
    'sidebar_bg_color' => SystemSetting::getValue('sidebar_bg_color', '#343a40'),
    'sidebar_text_color' => SystemSetting::getValue('sidebar_text_color', '#ffffff'),
    'smtp_host' => SystemSetting::getValue('smtp_host', ''),
    'smtp_port' => SystemSetting::getValue('smtp_port', 587),
    'smtp_username' => SystemSetting::getValue('smtp_username', ''),
    'smtp_password' => SystemSetting::getValue('smtp_password', ''),
    'smtp_encryption' => SystemSetting::getValue('smtp_encryption', 'tls'),
    'smtp_from_email' => SystemSetting::getValue('smtp_from_email', ''),
    'smtp_from_name' => SystemSetting::getValue('smtp_from_name', '')
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $currentSettings['site_name']; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: <?php echo $currentSettings['primary_color']; ?>;
            --secondary-color: <?php echo $currentSettings['secondary_color']; ?>;
            --sidebar-bg: <?php echo $currentSettings['sidebar_bg_color']; ?>;
            --sidebar-text: <?php echo $currentSettings['sidebar_text_color']; ?>;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }

        .settings-card {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border-radius: 16px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        .settings-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: 16px 16px 0 0;
            padding: 20px 24px;
        }

        .settings-body {
            padding: 24px;
        }

        .nav-tabs {
            border: none;
            margin-bottom: 24px;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 12px;
            margin-right: 8px;
            padding: 12px 20px;
            color: #6c757d;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .nav-tabs .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 16px rgba(0,123,255,0.3);
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.15);
        }

        .btn {
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .color-preview {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            border: 3px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-preview:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        }

        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(0,123,255,0.05);
        }

        .upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(0,123,255,0.1);
        }

        .logo-preview {
            max-width: 150px;
            max-height: 100px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .sidebar {
            background: var(--sidebar-bg);
            color: var(--sidebar-text);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: var(--sidebar-text);
            border-radius: 8px;
            margin: 4px 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .sidebar .nav-link.active {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include __DIR__ . '/../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2">
                        <i class="fas fa-crown me-2" style="color: var(--primary-color);"></i><?php echo $pageTitle; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>Reset to Defaults
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                            <i class="fas fa-save me-2"></i>Save All Changes
                        </button>
                    </div>
                </div>

                <?php if ($response): ?>
                    <div class="alert alert-<?php echo $response['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show">
                        <i class="fas fa-<?php echo $response['success'] ? 'check-circle' : 'exclamation-triangle'; ?> me-2"></i>
                        <?php echo htmlspecialchars($response['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                            <i class="fas fa-cog me-2"></i>General Settings
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="branding-tab" data-bs-toggle="tab" data-bs-target="#branding" type="button" role="tab">
                            <i class="fas fa-image me-2"></i>Logo & Branding
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                            <i class="fas fa-palette me-2"></i>Appearance
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="admins-tab" data-bs-toggle="tab" data-bs-target="#admins" type="button" role="tab">
                            <i class="fas fa-users-cog me-2"></i>Admin Users
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="smtp-tab" data-bs-toggle="tab" data-bs-target="#smtp" type="button" role="tab">
                            <i class="fas fa-envelope me-2"></i>SMTP Settings
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="settingsTabContent">
                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel">
                        <div class="settings-card">
                            <div class="settings-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-cog me-2"></i>General Settings
                                </h5>
                            </div>
                            <div class="settings-body">
                                <form id="generalForm" method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                    <input type="hidden" name="action" value="update_general">

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Site Name</label>
                                            <input type="text" class="form-control" name="site_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['site_name']); ?>" required>
                                            <div class="form-text">The name of your trading platform</div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Company Name</label>
                                            <input type="text" class="form-control" name="company_name"
                                                   value="<?php echo htmlspecialchars($currentSettings['company_name']); ?>">
                                            <div class="form-text">Your company's legal name</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Footer Text</label>
                                        <textarea class="form-control" name="footer_text" rows="3"><?php echo htmlspecialchars($currentSettings['footer_text']); ?></textarea>
                                        <div class="form-text">Text displayed in the website footer</div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Contact Email</label>
                                            <input type="email" class="form-control" name="contact_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_email']); ?>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Contact Phone</label>
                                            <input type="text" class="form-control" name="contact_phone"
                                                   value="<?php echo htmlspecialchars($currentSettings['contact_phone']); ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Company Address</label>
                                        <textarea class="form-control" name="company_address" rows="3"><?php echo htmlspecialchars($currentSettings['company_address']); ?></textarea>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save General Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Branding Tab -->
                    <div class="tab-pane fade" id="branding" role="tabpanel">
                        <div class="settings-card">
                            <div class="settings-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-image me-2"></i>Logo & Branding Management
                                </h5>
                            </div>
                            <div class="settings-body">
                                <form id="brandingForm" method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                    <input type="hidden" name="action" value="update_branding">

                                    <div class="row">
                                        <!-- Main Logo -->
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Main Logo</label>
                                            <div class="upload-area" onclick="document.getElementById('main_logo').click()">
                                                <?php if ($currentSettings['main_logo']): ?>
                                                    <img src="<?php echo htmlspecialchars($currentSettings['main_logo']); ?>"
                                                         class="logo-preview mb-2" alt="Main Logo">
                                                    <br>
                                                <?php endif; ?>
                                                <i class="fas fa-cloud-upload-alt fa-2x mb-2"></i>
                                                <p class="mb-0">Click to upload main logo</p>
                                                <small class="text-muted">PNG, JPG, SVG up to 5MB</small>
                                            </div>
                                            <input type="file" id="main_logo" name="main_logo" class="d-none" accept="image/*">
                                        </div>

                                        <!-- Favicon -->
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Favicon</label>
                                            <div class="upload-area" onclick="document.getElementById('favicon').click()">
                                                <?php if ($currentSettings['favicon']): ?>
                                                    <img src="<?php echo htmlspecialchars($currentSettings['favicon']); ?>"
                                                         class="logo-preview mb-2" alt="Favicon" style="max-width: 32px; max-height: 32px;">
                                                    <br>
                                                <?php endif; ?>
                                                <i class="fas fa-star fa-2x mb-2"></i>
                                                <p class="mb-0">Click to upload favicon</p>
                                                <small class="text-muted">ICO, PNG 16x16 or 32x32</small>
                                            </div>
                                            <input type="file" id="favicon" name="favicon" class="d-none" accept="image/*">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <!-- Light Theme Logo -->
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Light Theme Logo</label>
                                            <div class="upload-area" onclick="document.getElementById('light_logo').click()">
                                                <?php if ($currentSettings['light_logo']): ?>
                                                    <img src="<?php echo htmlspecialchars($currentSettings['light_logo']); ?>"
                                                         class="logo-preview mb-2" alt="Light Logo">
                                                    <br>
                                                <?php endif; ?>
                                                <i class="fas fa-sun fa-2x mb-2"></i>
                                                <p class="mb-0">Click to upload light theme logo</p>
                                                <small class="text-muted">For light backgrounds</small>
                                            </div>
                                            <input type="file" id="light_logo" name="light_logo" class="d-none" accept="image/*">
                                        </div>

                                        <!-- Dark Theme Logo -->
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Dark Theme Logo</label>
                                            <div class="upload-area" onclick="document.getElementById('dark_logo').click()">
                                                <?php if ($currentSettings['dark_logo']): ?>
                                                    <img src="<?php echo htmlspecialchars($currentSettings['dark_logo']); ?>"
                                                         class="logo-preview mb-2" alt="Dark Logo">
                                                    <br>
                                                <?php endif; ?>
                                                <i class="fas fa-moon fa-2x mb-2"></i>
                                                <p class="mb-0">Click to upload dark theme logo</p>
                                                <small class="text-muted">For dark backgrounds</small>
                                            </div>
                                            <input type="file" id="dark_logo" name="dark_logo" class="d-none" accept="image/*">
                                        </div>
                                    </div>

                                    <!-- Logo Size Controls -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Logo Width (px)</label>
                                            <input type="number" class="form-control" name="logo_width"
                                                   value="<?php echo $currentSettings['logo_width']; ?>" min="50" max="500">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Logo Height (px)</label>
                                            <input type="number" class="form-control" name="logo_height"
                                                   value="<?php echo $currentSettings['logo_height']; ?>" min="20" max="200">
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Branding Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Appearance Tab -->
                    <div class="tab-pane fade" id="appearance" role="tabpanel">
                        <div class="settings-card">
                            <div class="settings-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-palette me-2"></i>Appearance Settings
                                </h5>
                            </div>
                            <div class="settings-body">
                                <form id="appearanceForm" method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                    <input type="hidden" name="action" value="update_appearance">

                                    <div class="row">
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Primary Color</label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-3"
                                                       name="primary_color" value="<?php echo $currentSettings['primary_color']; ?>"
                                                       style="width: 60px; height: 50px;">
                                                <input type="text" class="form-control"
                                                       value="<?php echo $currentSettings['primary_color']; ?>" readonly>
                                            </div>
                                            <div class="form-text">Main brand color used throughout the platform</div>
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Secondary Color</label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-3"
                                                       name="secondary_color" value="<?php echo $currentSettings['secondary_color']; ?>"
                                                       style="width: 60px; height: 50px;">
                                                <input type="text" class="form-control"
                                                       value="<?php echo $currentSettings['secondary_color']; ?>" readonly>
                                            </div>
                                            <div class="form-text">Secondary accent color</div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Sidebar Background Color</label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-3"
                                                       name="sidebar_bg_color" value="<?php echo $currentSettings['sidebar_bg_color']; ?>"
                                                       style="width: 60px; height: 50px;">
                                                <input type="text" class="form-control"
                                                       value="<?php echo $currentSettings['sidebar_bg_color']; ?>" readonly>
                                            </div>
                                            <div class="form-text">Background color for the sidebar navigation</div>
                                        </div>

                                        <div class="col-md-6 mb-4">
                                            <label class="form-label">Sidebar Text Color</label>
                                            <div class="d-flex align-items-center">
                                                <input type="color" class="form-control form-control-color me-3"
                                                       name="sidebar_text_color" value="<?php echo $currentSettings['sidebar_text_color']; ?>"
                                                       style="width: 60px; height: 50px;">
                                                <input type="text" class="form-control"
                                                       value="<?php echo $currentSettings['sidebar_text_color']; ?>" readonly>
                                            </div>
                                            <div class="form-text">Text color for sidebar navigation items</div>
                                        </div>
                                    </div>

                                    <!-- Live Preview -->
                                    <div class="mb-4">
                                        <label class="form-label">Live Preview</label>
                                        <div id="colorPreview" class="border rounded p-3" style="height: 200px; background: #f8f9fa;">
                                            <div class="d-flex h-100">
                                                <div class="sidebar-preview" style="width: 80px; padding: 10px; background: var(--sidebar-bg); color: var(--sidebar-text);">
                                                    <div class="text-center mb-3">
                                                        <div style="width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; margin: 0 auto;"></div>
                                                    </div>
                                                    <div style="height: 8px; background: var(--primary-color); border-radius: 4px; margin: 5px 0;"></div>
                                                    <div style="height: 8px; background: rgba(255,255,255,0.3); border-radius: 4px; margin: 5px 0;"></div>
                                                    <div style="height: 8px; background: rgba(255,255,255,0.3); border-radius: 4px; margin: 5px 0;"></div>
                                                </div>
                                                <div class="flex-fill p-3 bg-white">
                                                    <div style="height: 20px; background: var(--primary-color); border-radius: 4px; width: 60%; margin-bottom: 15px;"></div>
                                                    <div style="height: 12px; background: var(--secondary-color); border-radius: 4px; width: 80%; margin-bottom: 8px;"></div>
                                                    <div style="height: 12px; background: #dee2e6; border-radius: 4px; width: 60%; margin-bottom: 8px;"></div>
                                                    <div style="height: 12px; background: #dee2e6; border-radius: 4px; width: 40%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save Appearance Settings
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Users Tab -->
                    <div class="tab-pane fade" id="admins" role="tabpanel">
                        <div class="settings-card">
                            <div class="settings-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-users-cog me-2"></i>Admin User Management
                                </h5>
                            </div>
                            <div class="settings-body">
                                <div class="text-center py-5">
                                    <i class="fas fa-tools fa-3x text-muted mb-3"></i>
                                    <h5>Admin Management Coming Soon</h5>
                                    <p class="text-muted">This feature will be available in the dedicated admin management page.</p>
                                    <a href="../admins/" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt me-2"></i>Go to Admin Management
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SMTP Settings Tab -->
                    <div class="tab-pane fade" id="smtp" role="tabpanel">
                        <div class="settings-card">
                            <div class="settings-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-envelope me-2"></i>SMTP Email Settings
                                </h5>
                            </div>
                            <div class="settings-body">
                                <form id="smtpForm" method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                    <input type="hidden" name="action" value="update_smtp">

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">SMTP Host</label>
                                            <input type="text" class="form-control" name="smtp_host"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_host']); ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">SMTP Port</label>
                                            <input type="number" class="form-control" name="smtp_port"
                                                   value="<?php echo $currentSettings['smtp_port']; ?>"
                                                   placeholder="587">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Username</label>
                                            <input type="text" class="form-control" name="smtp_username"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_username']); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Password</label>
                                            <input type="password" class="form-control" name="smtp_password"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_password']); ?>"
                                                   placeholder="Your app password">
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Encryption</label>
                                            <select class="form-select" name="smtp_encryption">
                                                <option value="tls" <?php echo $currentSettings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo $currentSettings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                <option value="none" <?php echo $currentSettings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                                            </select>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">From Email</label>
                                            <input type="email" class="form-control" name="smtp_from_email"
                                                   value="<?php echo htmlspecialchars($currentSettings['smtp_from_email']); ?>"
                                                   placeholder="<EMAIL>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">From Name</label>
                                        <input type="text" class="form-control" name="smtp_from_name"
                                               value="<?php echo htmlspecialchars($currentSettings['smtp_from_name']); ?>"
                                               placeholder="Your Site Name">
                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="button" class="btn btn-outline-info" onclick="testSMTP()">
                                                <i class="fas fa-paper-plane me-2"></i>Test SMTP
                                            </button>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i>Save SMTP Settings
                                        </button>
                                    </div>
                                </form>

                                <!-- Test SMTP Modal -->
                                <div class="modal fade" id="testSMTPModal" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Test SMTP Configuration</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="testSMTPForm">
                                                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                                    <input type="hidden" name="action" value="test_smtp">

                                                    <div class="mb-3">
                                                        <label class="form-label">Test Email Address</label>
                                                        <input type="email" class="form-control" name="test_email" required
                                                               placeholder="Enter email to send test message">
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-primary" onclick="sendTestEmail()">
                                                    <i class="fas fa-paper-plane me-2"></i>Send Test Email
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Update color input text when color picker changes
        document.querySelectorAll('input[type="color"]').forEach(colorInput => {
            colorInput.addEventListener('change', function() {
                const textInput = this.parentElement.querySelector('input[type="text"]');
                if (textInput) {
                    textInput.value = this.value;
                }
                updateColorPreview();
            });
        });

        // Update live color preview
        function updateColorPreview() {
            const primaryColor = document.querySelector('input[name="primary_color"]')?.value || '#007bff';
            const secondaryColor = document.querySelector('input[name="secondary_color"]')?.value || '#6c757d';
            const sidebarBg = document.querySelector('input[name="sidebar_bg_color"]')?.value || '#343a40';
            const sidebarText = document.querySelector('input[name="sidebar_text_color"]')?.value || '#ffffff';

            document.documentElement.style.setProperty('--primary-color', primaryColor);
            document.documentElement.style.setProperty('--secondary-color', secondaryColor);
            document.documentElement.style.setProperty('--sidebar-bg', sidebarBg);
            document.documentElement.style.setProperty('--sidebar-text', sidebarText);
        }

        // File upload preview
        function setupFileUpload(inputId, previewContainer) {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const img = previewContainer.querySelector('img') || document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'logo-preview mb-2';
                            img.alt = 'Preview';

                            if (!previewContainer.querySelector('img')) {
                                previewContainer.insertBefore(img, previewContainer.firstChild);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        }

        // Setup file uploads
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload('main_logo', document.querySelector('[onclick="document.getElementById(\'main_logo\').click()"]'));
            setupFileUpload('favicon', document.querySelector('[onclick="document.getElementById(\'favicon\').click()"]'));
            setupFileUpload('light_logo', document.querySelector('[onclick="document.getElementById(\'light_logo\').click()"]'));
            setupFileUpload('dark_logo', document.querySelector('[onclick="document.getElementById(\'dark_logo\').click()"]'));

            updateColorPreview();
        });

        // Test SMTP
        function testSMTP() {
            const modal = new bootstrap.Modal(document.getElementById('testSMTPModal'));
            modal.show();
        }

        function sendTestEmail() {
            const form = document.getElementById('testSMTPForm');
            const formData = new FormData(form);

            fetch(window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Test email sent successfully!');
                } else {
                    alert('Error: ' + data.message);
                }
                bootstrap.Modal.getInstance(document.getElementById('testSMTPModal')).hide();
            })
            .catch(error => {
                alert('Error sending test email: ' + error.message);
            });
        }

        // Reset to defaults
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
                // Implementation would go here
                alert('Reset functionality would be implemented here');
            }
        }

        // Save all settings
        function saveAllSettings() {
            alert('Save all functionality would be implemented here');
        }

        // Drag and drop for file uploads
        document.querySelectorAll('.upload-area').forEach(area => {
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const input = this.parentElement.querySelector('input[type="file"]');
                    if (input) {
                        input.files = files;
                        input.dispatchEvent(new Event('change'));
                    }
                }
            });
        });
    </script>
</body>
</html>
