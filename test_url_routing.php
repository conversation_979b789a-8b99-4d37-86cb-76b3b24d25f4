<?php
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h2>URL Routing Test</h2>";

echo "<h3>Configuration:</h3>";
echo "BASE_URL: " . BASE_URL . "<br>";
echo "AUTO_BASE_URL: " . (defined('AUTO_BASE_URL') ? AUTO_BASE_URL : 'Not defined') . "<br>";
echo "getBaseUrl(): " . getBaseUrl() . "<br>";

echo "<h3>Server Variables:</h3>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "<br>";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "<br>";

echo "<h3>Expected URLs:</h3>";
echo "Super Admin Login: " . getBaseUrl() . "superadmin/login.php<br>";
echo "Super Admin Dashboard: " . getBaseUrl() . "superadmin/dashboard/<br>";
echo "Super Admin Appearance: " . getBaseUrl() . "superadmin/appearance/<br>";

echo "<h3>Test Links:</h3>";
echo '<a href="' . getBaseUrl() . 'superadmin/login.php">Super Admin Login</a><br>';
echo '<a href="' . getBaseUrl() . 'superadmin/dashboard/">Super Admin Dashboard</a><br>';
echo '<a href="' . getBaseUrl() . 'superadmin/appearance/">Super Admin Appearance</a><br>';
?>