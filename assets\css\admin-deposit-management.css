/**
 * <PERSON><PERSON> Deposit Management Styles
 */

/* Statistics Cards */
.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    font-size: 0.9rem;
    opacity: 0.9;
}

.stats-card i {
    opacity: 0.8;
}

/* Filter Section */
.filter-section {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.filter-section .btn-group .btn {
    border-radius: 0.375rem;
    margin-right: 0.5rem;
}

.filter-section .btn-group .btn.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Search Input */
.search-container {
    position: relative;
}

.search-container input {
    padding-right: 2.5rem;
}

.search-container .btn {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* Deposits Table */
.deposits-table {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.deposits-table .table {
    margin-bottom: 0;
}

.deposits-table .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.deposits-table .table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.deposits-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* User Avatar */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    margin-right: 0.75rem;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    color: #333;
}

.user-details small {
    color: #6c757d;
}

/* Amount Display */
.amount-display {
    font-weight: 600;
    font-size: 1.1rem;
}

.bonus-amount {
    font-size: 0.85rem;
    color: #28a745;
    font-weight: 500;
}

/* Status Badges */
.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 1rem;
    font-weight: 500;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #b8daff;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
}

.action-buttons .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
}

.action-buttons .btn i {
    font-size: 0.8rem;
}

/* Bulk Actions */
.bulk-actions-bar {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    display: none;
}

.bulk-actions-bar.show {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.bulk-actions-bar .selected-count {
    font-weight: 600;
    color: #1976d2;
}

/* Checkboxes */
.form-check-input {
    width: 1.2rem;
    height: 1.2rem;
    margin-top: 0;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
}

/* Pagination */
.pagination-container {
    background: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.pagination .page-link {
    color: #0d6efd;
    border: 1px solid #dee2e6;
    padding: 0.5rem 0.75rem;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Modals */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #f3f3f3;
    border-top: 0.3rem solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-card h4 {
        font-size: 1.5rem;
    }
    
    .filter-section {
        padding: 0.75rem;
    }
    
    .filter-section .btn-group {
        flex-wrap: wrap;
    }
    
    .filter-section .btn-group .btn {
        margin-bottom: 0.5rem;
    }
    
    .deposits-table .table th,
    .deposits-table .table td {
        padding: 0.5rem;
        font-size: 0.875rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        margin-right: 0.5rem;
    }
    
    .action-buttons .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .deposits-table {
        font-size: 0.8rem;
    }
    
    .deposits-table .table th:nth-child(4),
    .deposits-table .table td:nth-child(4),
    .deposits-table .table th:nth-child(5),
    .deposits-table .table td:nth-child(5) {
        display: none;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-card h4 {
        font-size: 1.25rem;
    }
}

/* Print Styles */
@media print {
    .filter-section,
    .action-buttons,
    .pagination-container,
    .bulk-actions-bar {
        display: none !important;
    }
    
    .deposits-table {
        box-shadow: none;
    }
    
    .table th,
    .table td {
        border: 1px solid #dee2e6 !important;
    }
}