{"tech_stack": ["PHP", "MySQL", "Bootstrap", "JavaScript", "PDO", "P<PERSON><PERSON><PERSON><PERSON>", "Composer"], "features": [{"name": "Authentication System", "description": "Complete user authentication with login, registration, password reset, email verification", "files": ["classes/controllers/AuthController.php", "classes/services/AuthenticationManager.php", "classes/services/SessionManager.php", "login.php", "register.php", "forgot-password.php", "reset-password.php", "verify-email.php"]}, {"name": "User Management", "description": "User profile management, dashboard, settings, and account operations", "files": ["classes/models/User.php", "classes/controllers/UserController.php", "classes/services/UserService.php", "user/profile/index.php", "classes/views/ProfileView.php", "classes/views/DashboardView.php"]}, {"name": "Trading Plans System", "description": "Investment plans management and user plan selection", "files": ["classes/models/TradingPlan.php", "classes/views/TradingPlansView.php", "assets/js/trading-plans.js", "assets/css/trading-plans.css"]}, {"name": "Deposit Management", "description": "User deposit processing, admin approval system, payment methods", "files": ["classes/models/Deposit.php", "user/deposit/process.php", "user/deposit/status.php", "classes/views/DepositStatusView.php", "admin/deposits/index.php", "classes/views/AdminDepositManagementView.php"]}, {"name": "Withdrawal System", "description": "User withdrawal requests and admin processing", "files": ["classes/models/Withdrawal.php", "user/withdraw/request.php", "user/withdraw/status.php", "classes/views/WithdrawalRequestView.php", "classes/views/WithdrawalStatusView.php"]}, {"name": "Transaction Management", "description": "Financial transaction tracking, history, and audit trails", "files": ["classes/models/Transaction.php", "classes/services/TransactionManager.php", "user/transactions/index.php", "classes/views/TransactionHistoryView.php", "classes/services/FinancialService.php"]}, {"name": "Admin Panel", "description": "Administrative interface for user and financial management", "files": ["classes/controllers/AdminController.php", "admin/users/index.php", "classes/views/AdminUserManagementView.php", "classes/views/AdminDashboardView.php", "admin/deposits/index.php"]}, {"name": "Security Framework", "description": "CSRF protection, input validation, session security", "files": ["classes/services/CSRFProtection.php", "classes/services/SecurityMiddleware.php", "classes/services/InputSanitizer.php", "classes/validators/SecurityValidator.php", "includes/security_init.php"]}, {"name": "Database Layer", "description": "Database models, migrations, and data access layer", "files": ["classes/models/BaseModel.php", "database/schema.sql", "database/migrate.php", "database/seed.sql", "includes/db_connect.php"]}, {"name": "Layout System", "description": "Responsive UI framework with role-based layouts", "files": ["classes/views/BaseView.php", "layouts/user_layout.php", "layouts/admin_layout.php", "layouts/superadmin_layout.php", "classes/services/ThemeManager.php", "includes/ui_components.php"]}, {"name": "Validation System", "description": "Input validation and data sanitization framework", "files": ["classes/validators/BaseValidator.php", "classes/validators/RegistrationValidator.php", "classes/validators/LoginValidator.php", "classes/validators/FinancialValidator.php", "classes/validators/ValidationHelper.php"]}, {"name": "Email System", "description": "Email sending functionality and templates", "files": ["includes/email_functions.php", "classes/services/NotificationService.php"]}]}