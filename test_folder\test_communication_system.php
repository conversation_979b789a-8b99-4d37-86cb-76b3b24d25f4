<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/BaseModel.php';
require_once __DIR__ . '/../classes/services/EmailNotificationService.php';
require_once __DIR__ . '/../classes/services/SupportTicketService.php';
require_once __DIR__ . '/../classes/services/EmailTemplateService.php';

/**
 * Comprehensive Communication System Test Suite
 */
class CommunicationSystemTest {
    private $db;
    private $testUserId;
    private $results = [];
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->setupTestUser();
    }
    
    public function runAllTests() {
        echo "<h1>Communication System Test Suite</h1>\n";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
        
        $this->testEmailTemplateService();
        $this->testEmailNotificationService();
        $this->testSupportTicketService();
        $this->testIntegration();
        
        $this->displayResults();
        echo "</div>\n";
    }
    
    private function setupTestUser() {
        try {
            // Create test user
            $stmt = $this->db->prepare("
                INSERT INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)
            ");
            
            $hashedPassword = password_hash('TestPassword123!', PASSWORD_DEFAULT);
            $stmt->execute([
                'test_comm_user',
                '<EMAIL>',
                $hashedPassword,
                'Communication',
                'Test',
                'user',
                'active',
                1
            ]);
            
            $this->testUserId = $this->db->lastInsertId();
            if (!$this->testUserId) {
                // Get existing user ID
                $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute(['<EMAIL>']);
                $this->testUserId = $stmt->fetchColumn();
            }
            
            $this->addResult('Setup', 'Test user created/found', true);
        } catch (Exception $e) {
            $this->addResult('Setup', 'Failed to create test user: ' . $e->getMessage(), false);
        }
    }
    
    private function testEmailTemplateService() {
        echo "<h2>Testing Email Template Service</h2>\n";
        
        try {
            // Test getting template
            $template = EmailTemplateService::getTemplate('welcome');
            $this->addResult('Email Templates', 'Get welcome template', !empty($template));
            
            // Test template rendering
            $data = [
                'first_name' => 'Test',
                'site_name' => 'Test Site',
                'email' => '<EMAIL>'
            ];
            
            $rendered = EmailTemplateService::renderTemplate('welcome', $data);
            $this->addResult('Email Templates', 'Template rendering', !empty($rendered));
            
            // Test sending template email
            $result = EmailTemplateService::sendTemplateEmail('welcome', '<EMAIL>', 'Test User', $data);
            $this->addResult('Email Templates', 'Send template email', is_bool($result));
            
        } catch (Exception $e) {
            $this->addResult('Email Templates', 'Service error: ' . $e->getMessage(), false);
        }
    }
    
    private function testEmailNotificationService() {
        echo "<h2>Testing Email Notification Service</h2>\n";
        
        $emailService = EmailNotificationService::getInstance();
        
        // Test welcome email
        $result = $emailService->sendWelcomeEmail($this->testUserId, '<EMAIL>', 'Communication');
        $this->addResult('Email Notifications', 'Send welcome email', is_bool($result));
        
        // Test deposit confirmation
        $depositData = [
            'id' => 123,
            'amount' => 100.00,
            'status' => 'completed',
            'payment_method' => 'Test Method',
            'created_at' => date('Y-m-d H:i:s')
        ];
        $result = $emailService->sendDepositConfirmation($this->testUserId, $depositData);
        $this->addResult('Email Notifications', 'Send deposit confirmation', is_bool($result));
        
        // Test withdrawal notification
        $withdrawalData = [
            'id' => 456,
            'amount' => 50.00,
            'status' => 'pending',
            'payment_method' => 'Test Method',
            'created_at' => date('Y-m-d H:i:s')
        ];
        $result = $emailService->sendWithdrawalNotification($this->testUserId, $withdrawalData);
        $this->addResult('Email Notifications', 'Send withdrawal notification', is_bool($result));
        
        // Test 2FA notifications
        $result = $emailService->send2FAEnabledNotification($this->testUserId);
        $this->addResult('Email Notifications', 'Send 2FA enabled notification', is_bool($result));
        
        $result = $emailService->send2FADisabledNotification($this->testUserId);
        $this->addResult('Email Notifications', 'Send 2FA disabled notification', is_bool($result));
        
        // Test suspicious login alert
        $loginDetails = [
            'time' => date('Y-m-d H:i:s'),
            'ip' => '*************',
            'location' => 'Test Location',
            'user_agent' => 'Test Browser'
        ];
        $result = $emailService->sendSuspiciousLoginAlert($this->testUserId, $loginDetails);
        $this->addResult('Email Notifications', 'Send suspicious login alert', is_bool($result));
    }
    
    private function testSupportTicketService() {
        echo "<h2>Testing Support Ticket Service</h2>\n";
        
        $supportService = SupportTicketService::getInstance();
        
        // Test ticket creation
        $result = $supportService->createTicket(
            $this->testUserId,
            'Test Support Ticket',
            'This is a test message for the support ticket system. It needs to be at least 20 characters long.',
            'technical',
            'medium'
        );
        
        $this->addResult('Support Tickets', 'Create ticket', $result['success']);
        
        if ($result['success']) {
            $ticketId = $result['ticket_id'];
            
            // Test getting ticket details
            $detailsResult = $supportService->getTicketDetails($ticketId, $this->testUserId);
            $this->addResult('Support Tickets', 'Get ticket details', $detailsResult['success']);
            
            // Test admin reply (simulate admin user)
            $replyResult = $supportService->replyToTicket($ticketId, 1, 'This is a test reply from admin support team.');
            $this->addResult('Support Tickets', 'Admin reply to ticket', $replyResult['success']);
            
            // Test status update
            $statusResult = $supportService->updateTicketStatus($ticketId, 1, 'closed', 'Issue resolved');
            $this->addResult('Support Tickets', 'Update ticket status', $statusResult['success']);
        }
        
        // Test getting user tickets
        $ticketsResult = $supportService->getUserTickets($this->testUserId);
        $this->addResult('Support Tickets', 'Get user tickets', $ticketsResult['success']);
        
        // Test getting ticket statistics
        $statsResult = $supportService->getTicketStatistics($this->testUserId);
        $this->addResult('Support Tickets', 'Get ticket statistics', $statsResult['success']);
        
        // Test rate limiting
        $rateLimitTest = true;
        for ($i = 0; $i < 6; $i++) {
            $result = $supportService->createTicket(
                $this->testUserId,
                "Rate limit test ticket $i",
                'This is a rate limit test message that is long enough to pass validation requirements.',
                'general',
                'low'
            );
            if ($i >= 5 && $result['success']) {
                $rateLimitTest = false;
                break;
            }
        }
        $this->addResult('Support Tickets', 'Rate limiting protection', $rateLimitTest);
    }
    
    private function testIntegration() {
        echo "<h2>Testing System Integration</h2>\n";
        
        try {
            // Test email notification triggered by support ticket
            $supportService = SupportTicketService::getInstance();
            $result = $supportService->createTicket(
                $this->testUserId,
                'Integration Test Ticket',
                'This ticket tests the integration between support system and email notifications.',
                'general',
                'high'
            );
            
            $this->addResult('Integration', 'Support ticket with email notification', $result['success']);
            
            // Test security audit integration
            $emailService = EmailNotificationService::getInstance();
            $result = $emailService->sendWelcomeEmail($this->testUserId, '<EMAIL>', 'Integration');
            
            // Check if security audit was logged
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count 
                FROM security_audit_logs 
                WHERE category = 'system' AND event = 'email_sent' 
                AND user_id = ? 
                AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            ");
            $stmt->execute([$this->testUserId]);
            $auditCount = $stmt->fetchColumn();
            
            $this->addResult('Integration', 'Email with security audit logging', $auditCount > 0);
            
            // Test notification preferences
            $notificationsEnabled = $emailService->areNotificationsEnabled('general');
            $this->addResult('Integration', 'Notification preferences check', is_bool($notificationsEnabled));
            
        } catch (Exception $e) {
            $this->addResult('Integration', 'Integration test error: ' . $e->getMessage(), false);
        }
    }
    
    private function addResult($category, $test, $passed) {
        $this->results[] = [
            'category' => $category,
            'test' => $test,
            'passed' => $passed
        ];
        
        $status = $passed ? '✅ PASS' : '❌ FAIL';
        echo "<div style='margin: 5px 0;'><strong>[$category]</strong> $test: $status</div>\n";
    }
    
    private function displayResults() {
        echo "<h2>Test Summary</h2>\n";
        
        $total = count($this->results);
        $passed = array_sum(array_column($this->results, 'passed'));
        $failed = $total - $passed;
        
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<strong>Total Tests:</strong> $total<br>\n";
        echo "<strong style='color: green;'>Passed:</strong> $passed<br>\n";
        echo "<strong style='color: red;'>Failed:</strong> $failed<br>\n";
        echo "<strong>Success Rate:</strong> " . round(($passed / $total) * 100, 2) . "%\n";
        echo "</div>\n";
        
        if ($failed > 0) {
            echo "<h3>Failed Tests:</h3>\n";
            foreach ($this->results as $result) {
                if (!$result['passed']) {
                    echo "<div style='color: red;'>❌ [{$result['category']}] {$result['test']}</div>\n";
                }
            }
        }
        
        echo "<h3>Communication System Features:</h3>\n";
        echo "<ul>\n";
        echo "<li>✅ Email Template Management</li>\n";
        echo "<li>✅ Comprehensive Email Notifications</li>\n";
        echo "<li>✅ Support Ticket System</li>\n";
        echo "<li>✅ Security Integration</li>\n";
        echo "<li>✅ Rate Limiting Protection</li>\n";
        echo "<li>✅ Admin Management Interface</li>\n";
        echo "<li>✅ User-Friendly Support Interface</li>\n";
        echo "</ul>\n";
        
        echo "<h3>Next Steps:</h3>\n";
        echo "<ul>\n";
        echo "<li>Configure SMTP settings for email delivery</li>\n";
        echo "<li>Customize email templates for branding</li>\n";
        echo "<li>Set up admin notification preferences</li>\n";
        echo "<li>Test email delivery in production environment</li>\n";
        echo "<li>Configure support ticket categories and priorities</li>\n";
        echo "</ul>\n";
    }
    
    public function __destruct() {
        // Clean up test data
        try {
            $stmt = $this->db->prepare("DELETE FROM users WHERE id = ?");
            $stmt->execute([$this->testUserId]);
            
            $stmt = $this->db->prepare("DELETE FROM support_tickets WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
            
            $stmt = $this->db->prepare("DELETE FROM security_audit_logs WHERE user_id = ?");
            $stmt->execute([$this->testUserId]);
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
    }
}

// Run the tests
$tester = new CommunicationSystemTest();
$tester->runAllTests();
?>