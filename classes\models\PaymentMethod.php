<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * PaymentMethod Model - Handles payment method data and operations
 */
class PaymentMethod extends BaseModel {
    protected $table = 'payment_methods';
    protected $fillable = [
        'name', 'type', 'details', 'status', 'sort_order'
    ];
    
    // Payment method types
    const TYPE_CRYPTO = 'crypto';
    const TYPE_BANK = 'bank';
    const TYPE_PAYPAL = 'paypal';
    const TYPE_OTHER = 'other';
    
    // Payment method statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // Name validation
        if (empty($this->name)) {
            $errors['name'] = 'Payment method name is required';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'Payment method name must not exceed 100 characters';
        } elseif ($this->isNameTaken()) {
            $errors['name'] = 'Payment method name is already taken';
        }
        
        // Type validation
        if (empty($this->type)) {
            $errors['type'] = 'Payment method type is required';
        } elseif (!in_array($this->type, [self::TYPE_CRYPTO, self::TYPE_BANK, self::TYPE_PAYPAL, self::TYPE_OTHER])) {
            $errors['type'] = 'Invalid payment method type';
        }
        
        // Details validation
        if (empty($this->details)) {
            $errors['details'] = 'Payment method details are required';
        } elseif (!is_string($this->details) && !is_array($this->details)) {
            $errors['details'] = 'Payment method details must be valid';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_INACTIVE])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Sort order validation
        if (isset($this->sort_order) && !is_numeric($this->sort_order)) {
            $errors['sort_order'] = 'Sort order must be numeric';
        }
        
        return $errors;
    }
    
    /**
     * Check if name is already taken
     */
    private function isNameTaken() {
        $sql = "SELECT id FROM {$this->table} WHERE name = :name";
        if ($this->exists) {
            $sql .= " AND id != :id";
        }
        
        $stmt = $this->db->prepare($sql);
        $params = ['name' => $this->name];
        if ($this->exists) {
            $params['id'] = $this->getId();
        }
        
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Override save to handle JSON encoding and defaults
     */
    public function save() {
        if (is_array($this->details)) {
            $this->details = json_encode($this->details);
        }
        
        // Set default sort order if not provided
        if (!isset($this->sort_order)) {
            $this->sort_order = $this->getNextSortOrder();
        }
        
        // Set default status if not provided
        if (!isset($this->status)) {
            $this->status = self::STATUS_ACTIVE;
        }
        
        return parent::save();
    }
    
    /**
     * Get details as array
     */
    public function getDetailsArray() {
        if (is_string($this->details)) {
            return json_decode($this->details, true) ?? [];
        }
        
        return $this->details ?? [];
    }
    
    /**
     * Get next sort order
     */
    private function getNextSortOrder() {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        return ($result['max_order'] ?? 0) + 1;
    }
    
    /**
     * Check if payment method is active
     */
    public function isActive() {
        return $this->status === self::STATUS_ACTIVE;
    }
    
    /**
     * Check if payment method is inactive
     */
    public function isInactive() {
        return $this->status === self::STATUS_INACTIVE;
    }
    
    /**
     * Activate the payment method
     */
    public function activate() {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }
    
    /**
     * Deactivate the payment method
     */
    public function deactivate() {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }
    
    /**
     * Get type display name
     */
    public function getTypeDisplayName() {
        $types = [
            self::TYPE_CRYPTO => 'Cryptocurrency',
            self::TYPE_BANK => 'Bank Transfer',
            self::TYPE_PAYPAL => 'PayPal',
            self::TYPE_OTHER => 'Other'
        ];
        
        return $types[$this->type] ?? $this->type;
    }
    
    /**
     * Get type icon class
     */
    public function getTypeIconClass() {
        $icons = [
            self::TYPE_CRYPTO => 'fab fa-bitcoin',
            self::TYPE_BANK => 'fas fa-university',
            self::TYPE_PAYPAL => 'fab fa-paypal',
            self::TYPE_OTHER => 'fas fa-credit-card'
        ];
        
        return $icons[$this->type] ?? 'fas fa-credit-card';
    }
    
    /**
     * Get specific detail by key
     */
    public function getDetail($key, $default = null) {
        $details = $this->getDetailsArray();
        return $details[$key] ?? $default;
    }
    
    /**
     * Set specific detail
     */
    public function setDetail($key, $value) {
        $details = $this->getDetailsArray();
        $details[$key] = $value;
        $this->details = $details;
        return $this;
    }
    
    /**
     * Get formatted details for display
     */
    public function getFormattedDetails() {
        $details = $this->getDetailsArray();
        $formatted = [];
        
        switch ($this->type) {
            case self::TYPE_CRYPTO:
                if (isset($details['address'])) {
                    $formatted['Address'] = $details['address'];
                }
                if (isset($details['network'])) {
                    $formatted['Network'] = $details['network'];
                }
                if (isset($details['confirmations'])) {
                    $formatted['Confirmations'] = $details['confirmations'];
                }
                if (isset($details['fee'])) {
                    $formatted['Fee'] = $details['fee'];
                }
                break;
                
            case self::TYPE_BANK:
                if (isset($details['account_name'])) {
                    $formatted['Account Name'] = $details['account_name'];
                }
                if (isset($details['account_number'])) {
                    $formatted['Account Number'] = $details['account_number'];
                }
                if (isset($details['bank_name'])) {
                    $formatted['Bank Name'] = $details['bank_name'];
                }
                if (isset($details['routing_number'])) {
                    $formatted['Routing Number'] = $details['routing_number'];
                }
                if (isset($details['swift_code'])) {
                    $formatted['SWIFT Code'] = $details['swift_code'];
                }
                break;
                
            case self::TYPE_PAYPAL:
                if (isset($details['email'])) {
                    $formatted['PayPal Email'] = $details['email'];
                }
                if (isset($details['fee_percent'])) {
                    $formatted['Fee'] = $details['fee_percent'] . '%';
                }
                break;
                
            default:
                $formatted = $details;
                break;
        }
        
        return $formatted;
    }
    
    /**
     * Get deposits using this payment method
     */
    public function getDeposits($limit = null) {
        require_once __DIR__ . '/Deposit.php';
        
        $sql = "SELECT * FROM deposits WHERE payment_method_id = :payment_method_id ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['payment_method_id' => $this->getId()]);
        $results = $stmt->fetchAll();
        
        $deposits = [];
        foreach ($results as $data) {
            $deposits[] = new Deposit($data);
        }
        
        return $deposits;
    }
    
    /**
     * Get total deposits amount for this payment method
     */
    public function getTotalDepositsAmount() {
        $sql = "SELECT COALESCE(SUM(amount), 0) as total FROM deposits 
                WHERE payment_method_id = :payment_method_id AND status = 'approved'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['payment_method_id' => $this->getId()]);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
    
    /**
     * Get deposits count for this payment method
     */
    public function getDepositsCount() {
        $sql = "SELECT COUNT(*) as count FROM deposits 
                WHERE payment_method_id = :payment_method_id AND status = 'approved'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['payment_method_id' => $this->getId()]);
        $result = $stmt->fetch();
        
        return (int) $result['count'];
    }
    
    /**
     * Get active payment methods
     */
    public static function getActiveMethods() {
        return static::where('status', self::STATUS_ACTIVE);
    }
    
    /**
     * Get active payment methods (alias)
     */
    public static function getActive() {
        return static::getActiveMethods();
    }
    
    /**
     * Get payment methods ordered by sort order
     */
    public static function getOrderedMethods($status = null) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table}";
        
        if ($status) {
            $sql .= " WHERE status = :status";
        }
        
        $sql .= " ORDER BY sort_order ASC, created_at ASC";
        
        $stmt = $instance->db->prepare($sql);
        $params = [];
        if ($status) {
            $params['status'] = $status;
        }
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        $methods = [];
        foreach ($results as $data) {
            $methods[] = new static($data);
        }
        
        return $methods;
    }
    
    /**
     * Get payment methods by type
     */
    public static function getByType($type, $status = null) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE type = :type";
        
        if ($status) {
            $sql .= " AND status = :status";
        }
        
        $sql .= " ORDER BY sort_order ASC, created_at ASC";
        
        $stmt = $instance->db->prepare($sql);
        $params = ['type' => $type];
        if ($status) {
            $params['status'] = $status;
        }
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        $methods = [];
        foreach ($results as $data) {
            $methods[] = new static($data);
        }
        
        return $methods;
    }
    
    /**
     * Update sort orders
     */
    public static function updateSortOrders($methodIds) {
        $instance = new static();
        
        $instance->beginTransaction();
        
        try {
            foreach ($methodIds as $index => $methodId) {
                $sql = "UPDATE {$instance->table} SET sort_order = :sort_order WHERE id = :id";
                $stmt = $instance->db->prepare($sql);
                $stmt->execute([
                    'sort_order' => $index + 1,
                    'id' => $methodId
                ]);
            }
            
            $instance->commit();
            return true;
            
        } catch (Exception $e) {
            $instance->rollback();
            error_log("Sort order update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get payment method statistics
     */
    public static function getStatistics() {
        $instance = new static();
        
        $sql = "SELECT 
                    COUNT(*) as total_methods,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_methods,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_methods,
                    COUNT(DISTINCT type) as types_count
                FROM {$instance->table}";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    /**
     * Get payment methods with deposit statistics
     */
    public static function getWithDepositStats() {
        $instance = new static();
        
        $sql = "SELECT 
                    pm.*,
                    COUNT(d.id) as deposits_count,
                    COALESCE(SUM(CASE WHEN d.status = 'approved' THEN d.amount ELSE 0 END), 0) as total_deposits,
                    COALESCE(SUM(CASE WHEN d.status = 'pending' THEN 1 ELSE 0 END), 0) as pending_deposits
                FROM {$instance->table} pm
                LEFT JOIN deposits d ON pm.id = d.payment_method_id
                GROUP BY pm.id
                ORDER BY pm.sort_order ASC, pm.created_at ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    /**
     * Find payment method by name
     */
    public static function findByName($name) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE name = :name LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['name' => $name]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Get most used payment method (by deposits count)
     */
    public static function getMostUsed() {
        $instance = new static();
        
        $sql = "SELECT pm.*, COUNT(d.id) as deposits_count
                FROM {$instance->table} pm
                LEFT JOIN deposits d ON pm.id = d.payment_method_id AND d.status = 'approved'
                WHERE pm.status = 'active'
                GROUP BY pm.id
                ORDER BY deposits_count DESC
                LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
}
?>