/* Transaction History Styles */

.transaction-description {
    max-width: 300px;
    word-wrap: break-word;
}

.balance-info {
    min-width: 120px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Transaction Type Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.badge i {
    margin-right: 0.25rem;
}

/* Status Colors */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Summary Cards */
.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-body {
    padding: 1.25rem;
}

.card-title {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Filter Form */
.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-secondary {
    color: #858796;
    border-color: #858796;
}

.btn-outline-info {
    color: #36b9cc;
    border-color: #36b9cc;
}

.btn-outline-warning {
    color: #f6c23e;
    border-color: #f6c23e;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #4e73df;
    border-color: #dddfeb;
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    color: #224abe;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Modal Styles */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #5a5c69;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

/* Empty State */
.text-center i.fa-3x {
    opacity: 0.3;
}

/* Loading Spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
    color: #4e73df;
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .transaction-description {
        max-width: 200px;
    }
    
    .balance-info {
        min-width: 100px;
    }
    
    .btn-group-sm > .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.6875rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8125rem;
    }
    
    .badge {
        font-size: 0.6875rem;
        padding: 0.25rem 0.5rem;
    }
    
    .btn {
        font-size: 0.8125rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Print Styles */
@media print {
    .btn, .pagination, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        font-size: 0.75rem;
    }
    
    .badge {
        border: 1px solid #000;
        background-color: transparent !important;
        color: #000 !important;
    }
}

/* Animation for loading states */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

/* Transaction amount highlighting */
.fw-bold.text-success {
    font-weight: 600 !important;
}

.fw-bold.text-danger {
    font-weight: 600 !important;
}

/* Custom scrollbar for table */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}