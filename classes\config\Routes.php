<?php
/**
 * Application Routes Configuration
 */
class Routes {
    private static $routes = [
        'login' => '/login.php',
        'register' => '/register.php',
        'forgot_password' => '/forgot-password.php',
        'user_dashboard' => '/user/dashboard/',
        'admin_dashboard' => '/admin/dashboard/',
        'superadmin_dashboard' => '/superadmin/dashboard/',
    ];
    
    /**
     * Get route URL
     */
    public static function get($routeName, $params = []) {
        if (!isset(self::$routes[$routeName])) {
            throw new InvalidArgumentException("Route '{$routeName}' not found");
        }
        
        $baseUrl = rtrim(BASE_URL ?? '', '/');
        $route = self::$routes[$routeName];
        
        // Replace parameters in route
        foreach ($params as $key => $value) {
            $route = str_replace('{' . $key . '}', $value, $route);
        }
        
        return $baseUrl . $route;
    }
    
    /**
     * Get dashboard route based on user role
     */
    public static function getDashboardRoute($userRole) {
        switch ($userRole) {
            case 'superadmin':
                return self::get('superadmin_dashboard');
            case 'admin':
                return self::get('admin_dashboard');
            default:
                return self::get('user_dashboard');
        }
    }
}
?>