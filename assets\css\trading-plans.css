/* Trading Plans Styles */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.user-balance-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

.balance-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.balance-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.balance-amount {
    font-size: 1.5rem;
    font-weight: bold;
    margin-top: 0.25rem;
}

/* Trading Plan Cards */
.trading-plan-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.trading-plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.plan-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.plan-return {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.return-rate {
    font-size: 2rem;
    font-weight: bold;
    line-height: 1;
}

.return-period {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.25rem;
}

.plan-body {
    padding: 1.5rem;
    flex-grow: 1;
}

.plan-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-item i {
    color: #667eea;
    width: 20px;
    margin-right: 1rem;
}

.detail-item div {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: #495057;
}

.plan-description {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.plan-description p {
    margin-bottom: 0;
    color: #6c757d;
}

.plan-features {
    margin-bottom: 1.5rem;
}

.plan-features h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.plan-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.plan-features li i {
    color: #28a745;
    margin-right: 0.5rem;
    width: 16px;
}

.plan-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    margin-top: auto;
}

.btn-select-plan {
    width: 100%;
    padding: 0.75rem;
    font-weight: 600;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-select-plan:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    filter: invert(1);
}

.selected-plan-info .alert {
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-left: 4px solid #667eea;
}

.input-group-text {
    background: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    font-weight: 600;
}

.form-control:focus,
.form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.bonus-info .alert {
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-left: 4px solid #28a745;
}

#paymentDetails .alert {
    border-radius: 10px;
    border: none;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-left: 4px solid #ffc107;
}

/* Amount Validation */
.amount-valid {
    color: #28a745;
    font-weight: 600;
}

.amount-invalid {
    color: #dc3545;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .user-balance-card {
        margin-top: 1rem;
    }
    
    .trading-plan-card {
        margin-bottom: 1.5rem;
    }
    
    .plan-header {
        padding: 1rem;
    }
    
    .plan-name {
        font-size: 1.25rem;
    }
    
    .return-rate {
        font-size: 1.5rem;
    }
    
    .plan-body {
        padding: 1rem;
    }
    
    .plan-footer {
        padding: 1rem;
    }
    
    .modal-dialog {
        margin: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .balance-amount {
        font-size: 1.25rem;
    }
    
    .detail-item {
        padding: 0.5rem;
    }
    
    .detail-item i {
        margin-right: 0.75rem;
    }
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error States */
.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border: none;
    border-left: 4px solid #28a745;
    border-radius: 10px;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    border: none;
    border-left: 4px solid #dc3545;
    border-radius: 10px;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    border: none;
    border-left: 4px solid #17a2b8;
    border-radius: 10px;
}