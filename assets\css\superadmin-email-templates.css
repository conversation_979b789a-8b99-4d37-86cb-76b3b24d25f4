/**
 * Super Admin Email Templates Styles
 */

/* Template Management */
.template-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.template-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.template-status {
    font-size: 0.875rem;
    font-weight: 500;
}

.template-actions .btn {
    margin: 0 2px;
}

/* Modal Styles */
.modal-xl {
    max-width: 1200px;
}

.modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Placeholders Help */
.placeholder-code {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-block;
    margin-bottom: 4px;
}

.placeholder-code:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.placeholders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.placeholder-item {
    padding: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #f8f9fa;
}

/* Content Tabs */
.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-bottom-color: #dee2e6;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    padding: 20px;
    background: #fff;
}

/* Template Editor */
.template-editor textarea {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.template-editor .form-control {
    border-radius: 0;
}

/* Preview Styles */
.preview-container {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    margin-top: 15px;
}

.preview-subject {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    font-weight: 600;
    margin-bottom: 15px;
}

.preview-html {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    background: #fff;
    min-height: 400px;
    overflow-y: auto;
}

.preview-text {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
    background: #f8f9fa;
    font-family: monospace;
    white-space: pre-wrap;
    min-height: 400px;
    overflow-y: auto;
}

/* Table Styles */
.table th {
    background: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.table .btn-group {
    white-space: nowrap;
}

.table .text-truncate {
    max-width: 200px;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

/* Action Buttons */
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 6px;
    font-weight: 500;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modal-xl {
        max-width: 95%;
        margin: 10px auto;
    }
    
    .modal-body {
        padding: 15px;
    }
    
    .btn-group-sm > .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .placeholders-grid {
        grid-template-columns: 1fr;
    }
    
    .text-truncate {
        max-width: 150px !important;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group > .btn {
        margin-bottom: 2px;
        border-radius: 4px !important;
    }
    
    .modal-body {
        max-height: 60vh;
    }
    
    .tab-content {
        padding: 15px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .template-card {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .placeholder-code {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .placeholder-code:hover {
        background: #3182ce;
        border-color: #3182ce;
    }
    
    .preview-container {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .preview-subject {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Utility Classes */
.cursor-pointer {
    cursor: pointer;
}

.text-monospace {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.border-dashed {
    border-style: dashed !important;
}

.bg-light-blue {
    background-color: #e3f2fd;
}

.text-primary-dark {
    color: #0056b3;
}

/* Custom Scrollbar */
.modal-body::-webkit-scrollbar,
.preview-html::-webkit-scrollbar,
.preview-text::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track,
.preview-html::-webkit-scrollbar-track,
.preview-text::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb,
.preview-html::-webkit-scrollbar-thumb,
.preview-text::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.preview-html::-webkit-scrollbar-thumb:hover,
.preview-text::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}