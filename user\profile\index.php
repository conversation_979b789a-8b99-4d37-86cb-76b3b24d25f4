<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/controllers/UserController.php';
require_once __DIR__ . '/../../classes/views/ProfileView.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: /login.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'update_profile':
                $result = UserController::updateProfile();
                echo json_encode($result);
                exit();
                
            case 'change_password':
                $result = UserController::changePassword();
                echo json_encode($result);
                exit();
                
            case 'upload_picture':
                $result = UserController::uploadProfilePicture();
                echo json_encode($result);
                exit();
                
            default:
                echo json_encode(['success' => false, 'error' => 'Invalid action']);
                exit();
        }
    } catch (Exception $e) {
        error_log("Profile action error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'System error occurred']);
        exit();
    }
}

try {
    // Get profile data
    $profileData = UserController::profile();
    
    // Create and render the profile view
    $view = new ProfileView($profileData['user']);
    $view->render();
    
} catch (Exception $e) {
    error_log("Profile error: " . $e->getMessage());
    header('Location: /user/dashboard/?error=system_error');
    exit();
}
?>