<?php
/**
 * Authentication System Testing Script
 * Tests all authentication and session management components
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/services/AuthenticationManager.php';
require_once __DIR__ . '/../classes/services/SessionManager.php';
require_once __DIR__ . '/../classes/services/CSRFProtection.php';
require_once __DIR__ . '/../classes/controllers/AuthController.php';
require_once __DIR__ . '/../classes/validators/ValidationHelper.php';

echo "<h1>🔐 Authentication and Session Management System Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto;'>";

$errors = [];
$success = [];
$warnings = [];

// Test 1: SessionManager functionality
echo "<h2>1️⃣ SessionManager Test</h2>";

try {
    // Test session start
    $sessionStarted = SessionManager::startSession();
    if ($sessionStarted) {
        $success[] = "✅ SessionManager::startSession() working";
    } else {
        $errors[] = "❌ SessionManager::startSession() failed";
    }
    
    // Test CSRF token generation
    $csrfToken = SessionManager::getCSRFToken();
    if (!empty($csrfToken) && strlen($csrfToken) === 64) {
        $success[] = "✅ CSRF token generation working (length: " . strlen($csrfToken) . ")";
    } else {
        $errors[] = "❌ CSRF token generation failed";
    }
    
    // Test CSRF token validation
    if (SessionManager::validateCSRFToken($csrfToken)) {
        $success[] = "✅ CSRF token validation working";
    } else {
        $errors[] = "❌ CSRF token validation failed";
    }
    
    // Test session message functionality
    SessionManager::setMessage('Test message', 'info');
    $message = SessionManager::getMessage();
    if ($message && $message['message'] === 'Test message' && $message['type'] === 'info') {
        $success[] = "✅ Session message functionality working";
    } else {
        $errors[] = "❌ Session message functionality failed";
    }
    
    // Test session timeout
    $timeout = SessionManager::getSessionTimeout();
    if (is_numeric($timeout) && $timeout > 0) {
        $success[] = "✅ Session timeout configuration working ($timeout seconds)";
    } else {
        $warnings[] = "⚠️ Session timeout configuration may have issues";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ SessionManager test failed: " . $e->getMessage();
}

// Test 2: CSRFProtection functionality
echo "<h2>2️⃣ CSRFProtection Test</h2>";

try {
    // Test token generation
    $token = CSRFProtection::generateToken();
    if (!empty($token)) {
        $success[] = "✅ CSRFProtection token generation working";
    } else {
        $errors[] = "❌ CSRFProtection token generation failed";
    }
    
    // Test token validation
    if (CSRFProtection::validateToken($token)) {
        $success[] = "✅ CSRFProtection token validation working";
    } else {
        $errors[] = "❌ CSRFProtection token validation failed";
    }
    
    // Test HTML field generation
    $tokenField = CSRFProtection::getTokenField();
    if (strpos($tokenField, 'csrf_token') !== false && strpos($tokenField, 'hidden') !== false) {
        $success[] = "✅ CSRF token HTML field generation working";
    } else {
        $errors[] = "❌ CSRF token HTML field generation failed";
    }
    
    // Test meta tag generation
    $tokenMeta = CSRFProtection::getTokenMeta();
    if (strpos($tokenMeta, 'csrf-token') !== false && strpos($tokenMeta, 'meta') !== false) {
        $success[] = "✅ CSRF token meta tag generation working";
    } else {
        $errors[] = "❌ CSRF token meta tag generation failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ CSRFProtection test failed: " . $e->getMessage();
}

// Test 3: AuthenticationManager functionality
echo "<h2>3️⃣ AuthenticationManager Test</h2>";

try {
    // Test authentication status (should be false initially)
    $isAuth = AuthenticationManager::isAuthenticated();
    if (!$isAuth) {
        $success[] = "✅ AuthenticationManager::isAuthenticated() working (not authenticated)";
    } else {
        $warnings[] = "⚠️ User appears to be authenticated in test environment";
    }
    
    // Test user retrieval (should be null when not authenticated)
    $currentUser = AuthenticationManager::getCurrentUser();
    if ($currentUser === null) {
        $success[] = "✅ AuthenticationManager::getCurrentUser() working (returns null when not authenticated)";
    } else {
        $warnings[] = "⚠️ getCurrentUser() returned user when not authenticated";
    }
    
    // Test login with invalid credentials
    $loginResult = AuthenticationManager::login('<EMAIL>', 'wrongpassword');
    if (!$loginResult['success'] && isset($loginResult['error'])) {
        $success[] = "✅ AuthenticationManager login rejection working";
    } else {
        $errors[] = "❌ AuthenticationManager should reject invalid credentials";
    }
    
    // Test rate limiting check
    $ipAddress = '127.0.0.1';
    $isRateLimited = AuthenticationManager::isIPRateLimited($ipAddress);
    if (is_bool($isRateLimited)) {
        $success[] = "✅ IP rate limiting check working";
    } else {
        $errors[] = "❌ IP rate limiting check failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ AuthenticationManager test failed: " . $e->getMessage();
}

// Test 4: User registration validation
echo "<h2>4️⃣ User Registration Validation Test</h2>";

try {
    // Test with invalid data
    $invalidUserData = [
        'username' => 'ab', // too short
        'email' => 'invalid-email',
        'password' => '123', // too short
        'first_name' => '',
        'last_name' => '',
        'csrf_token' => CSRFProtection::generateToken()
    ];
    
    // Simulate POST data for validation
    $_POST = $invalidUserData;
    
    // This would normally be called by the controller, but we'll test the validation logic
    $rules = [
        'username' => ['required' => true, 'length' => ['min' => 3, 'max' => 50]],
        'email' => ['required' => true, 'email' => true],
        'password' => ['required' => true, 'length' => ['min' => 6, 'max' => 255]],
        'first_name' => ['required' => true, 'length' => ['max' => 50]],
        'last_name' => ['required' => true, 'length' => ['max' => 50]]
    ];
    
    $validationErrors = ValidationHelper::validateFields($invalidUserData, $rules);
    
    if (!empty($validationErrors)) {
        $success[] = "✅ User registration validation working (found " . count($validationErrors) . " errors)";
    } else {
        $errors[] = "❌ User registration validation should catch invalid data";
    }
    
    // Test with valid data structure
    $validUserData = [
        'username' => 'testuser123',
        'email' => '<EMAIL>',
        'password' => 'password123',
        'first_name' => 'Test',
        'last_name' => 'User',
        'csrf_token' => CSRFProtection::generateToken()
    ];
    
    $validationErrors = ValidationHelper::validateFields($validUserData, $rules);
    
    if (empty($validationErrors)) {
        $success[] = "✅ User registration validation passes with valid data";
    } else {
        $warnings[] = "⚠️ User registration validation may be too strict: " . implode(', ', $validationErrors);
    }
    
} catch (Exception $e) {
    $errors[] = "❌ User registration validation test failed: " . $e->getMessage();
}

// Test 5: Password validation
echo "<h2>5️⃣ Password Validation Test</h2>";

try {
    // Test weak passwords
    $weakPasswords = ['123', 'password', 'abc123', ''];
    $weakPasswordErrors = 0;
    
    foreach ($weakPasswords as $password) {
        $error = ValidationHelper::validatePassword($password);
        if ($error) {
            $weakPasswordErrors++;
        }
    }
    
    if ($weakPasswordErrors === count($weakPasswords)) {
        $success[] = "✅ Password validation rejects weak passwords";
    } else {
        $errors[] = "❌ Password validation should reject all weak passwords";
    }
    
    // Test strong passwords
    $strongPasswords = ['Password123', 'MySecure123', 'Test123456'];
    $strongPasswordErrors = 0;
    
    foreach ($strongPasswords as $password) {
        $error = ValidationHelper::validatePassword($password);
        if ($error) {
            $strongPasswordErrors++;
        }
    }
    
    if ($strongPasswordErrors === 0) {
        $success[] = "✅ Password validation accepts strong passwords";
    } else {
        $warnings[] = "⚠️ Password validation may be rejecting valid passwords";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Password validation test failed: " . $e->getMessage();
}

// Test 6: Email validation
echo "<h2>6️⃣ Email Validation Test</h2>";

try {
    // Test invalid emails
    $invalidEmails = ['invalid', 'test@', '@example.com', 'test.example.com'];
    $invalidEmailErrors = 0;
    
    foreach ($invalidEmails as $email) {
        $error = ValidationHelper::validateEmail($email);
        if ($error) {
            $invalidEmailErrors++;
        }
    }
    
    if ($invalidEmailErrors === count($invalidEmails)) {
        $success[] = "✅ Email validation rejects invalid emails";
    } else {
        $errors[] = "❌ Email validation should reject all invalid emails";
    }
    
    // Test valid emails
    $validEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    $validEmailErrors = 0;
    
    foreach ($validEmails as $email) {
        $error = ValidationHelper::validateEmail($email);
        if ($error) {
            $validEmailErrors++;
        }
    }
    
    if ($validEmailErrors === 0) {
        $success[] = "✅ Email validation accepts valid emails";
    } else {
        $warnings[] = "⚠️ Email validation may be rejecting valid emails";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Email validation test failed: " . $e->getMessage();
}

// Test 7: Database integration
echo "<h2>7️⃣ Database Integration Test</h2>";

try {
    // Test user lookup functions
    $nonExistentUser = User::findByEmail('<EMAIL>');
    if ($nonExistentUser === null) {
        $success[] = "✅ User::findByEmail() returns null for non-existent user";
    } else {
        $errors[] = "❌ User::findByEmail() should return null for non-existent user";
    }
    
    // Test existing user lookup (should find the demo user)
    $existingUser = User::findByUsername('demo');
    if ($existingUser && $existingUser->username === 'demo') {
        $success[] = "✅ User::findByUsername() finds existing user";
    } else {
        $warnings[] = "⚠️ Demo user not found - this may be expected if not seeded";
    }
    
    // Test session cleanup
    $cleanedSessions = SessionManager::cleanupExpiredSessions();
    if (is_numeric($cleanedSessions)) {
        $success[] = "✅ Session cleanup working (cleaned $cleanedSessions sessions)";
    } else {
        $errors[] = "❌ Session cleanup failed";
    }
    
    // Test active sessions count
    $activeSessions = SessionManager::getActiveSessionsCount();
    if (is_numeric($activeSessions)) {
        $success[] = "✅ Active sessions count working ($activeSessions active)";
    } else {
        $errors[] = "❌ Active sessions count failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Database integration test failed: " . $e->getMessage();
}

// Test 8: Security features
echo "<h2>8️⃣ Security Features Test</h2>";

try {
    // Test input sanitization
    $maliciousInput = '<script>alert("xss")</script>';
    $sanitized = ValidationHelper::sanitize($maliciousInput);
    if (strpos($sanitized, '<script>') === false) {
        $success[] = "✅ Input sanitization working (XSS protection)";
    } else {
        $errors[] = "❌ Input sanitization failed - XSS vulnerability";
    }
    
    // Test CSRF token uniqueness
    $token1 = CSRFProtection::generateToken();
    $token2 = CSRFProtection::generateToken();
    if ($token1 === $token2) {
        $success[] = "✅ CSRF tokens are consistent within session";
    } else {
        $warnings[] = "⚠️ CSRF tokens are changing - this may be expected behavior";
    }
    
    // Test password hashing
    $password = 'testpassword123';
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    if (password_verify($password, $hashedPassword)) {
        $success[] = "✅ Password hashing and verification working";
    } else {
        $errors[] = "❌ Password hashing and verification failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Security features test failed: " . $e->getMessage();
}

// Test 9: Authentication pages
echo "<h2>9️⃣ Authentication Pages Test</h2>";

try {
    // Test if authentication pages exist
    $authPages = [
        'login.php' => 'Login page',
        'register.php' => 'Registration page',
        'forgot-password.php' => 'Forgot password page',
        'reset-password.php' => 'Password reset page',
        'verify-email.php' => 'Email verification page'
    ];
    
    foreach ($authPages as $page => $description) {
        if (file_exists(__DIR__ . '/../' . $page)) {
            $success[] = "✅ $description exists ($page)";
        } else {
            $errors[] = "❌ $description missing ($page)";
        }
    }
    
    // Test if controller classes exist
    $controllerClasses = ['AuthController'];
    foreach ($controllerClasses as $class) {
        if (class_exists($class)) {
            $success[] = "✅ $class exists and loaded";
        } else {
            $errors[] = "❌ $class missing or not loaded";
        }
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Authentication pages test failed: " . $e->getMessage();
}

// Display Results
echo "<h2>📊 Test Results Summary</h2>";

// Success messages
if (!empty($success)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Successful Tests (" . count($success) . " items)</h3>";
    foreach ($success as $message) {
        echo "<p style='color: #155724; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Warnings
if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Warnings (" . count($warnings) . " items)</h3>";
    foreach ($warnings as $message) {
        echo "<p style='color: #856404; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Errors
if (!empty($errors)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors (" . count($errors) . " items)</h3>";
    foreach ($errors as $message) {
        echo "<p style='color: #721c24; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Final Status
echo "<h2>🎯 Task 3 Completion Status</h2>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 TASK 3 COMPLETED SUCCESSFULLY!</h3>";
    echo "<p style='color: #155724; margin: 10px 0;'>Authentication and session management system has been implemented and tested.</p>";
    echo "<p style='color: #155724; margin: 10px 0;'><strong>Ready to proceed to Task 4: Create layout system and responsive UI framework</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ TASK 3 HAS ISSUES</h3>";
    echo "<p style='color: #721c24; margin: 10px 0;'>Please resolve the errors above before proceeding to the next task.</p>";
    echo "</div>";
}

// Implementation Summary
echo "<h2>📋 Authentication System Summary</h2>";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<ol style='color: #383d41;'>";
echo "<li><strong>AuthenticationManager</strong> - Complete login/logout/registration system</li>";
echo "<li><strong>SessionManager</strong> - Secure session handling with timeout and validation</li>";
echo "<li><strong>CSRFProtection</strong> - CSRF token generation and validation</li>";
echo "<li><strong>AuthController</strong> - Request handling for all auth operations</li>";
echo "<li><strong>Authentication Pages</strong> - Login, register, forgot password, reset password, email verification</li>";
echo "<li><strong>Security Features</strong> - Input sanitization, password hashing, rate limiting</li>";
echo "<li><strong>Email Integration</strong> - Welcome emails, password reset, email verification</li>";
echo "<li><strong>Validation System</strong> - Comprehensive input validation and error handling</li>";
echo "<li><strong>Database Integration</strong> - User lookup, session storage, audit logging</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>