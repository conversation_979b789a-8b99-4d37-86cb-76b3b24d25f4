/**
 * Trading Plans JavaScript
 * Handles plan selection, deposit modal, and form validation
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize trading plans functionality
    initTradingPlans();
});

function initTradingPlans() {
    // Plan selection buttons
    const selectPlanButtons = document.querySelectorAll('.btn-select-plan');
    const depositModal = new bootstrap.Modal(document.getElementById('depositModal'));
    const depositForm = document.getElementById('depositForm');
    const amountInput = document.getElementById('depositAmount');
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const paymentDetails = document.getElementById('paymentDetails');
    const paymentInstructions = document.getElementById('paymentInstructions');
    
    // Handle plan selection
    selectPlanButtons.forEach(button => {
        button.addEventListener('click', function() {
            const planId = this.dataset.planId;
            const planName = this.dataset.planName;
            const minDeposit = parseFloat(this.dataset.minDeposit);
            const maxDeposit = this.dataset.maxDeposit ? parseFloat(this.dataset.maxDeposit) : null;
            const planReturn = this.closest('.trading-plan-card').querySelector('.return-rate').textContent;
            
            // Update modal with plan information
            updateDepositModal(planId, planName, minDeposit, maxDeposit, planReturn);
            
            // Show modal
            depositModal.show();
        });
    });
    
    // Handle amount validation
    amountInput.addEventListener('input', function() {
        validateDepositAmount();
    });
    
    // Handle payment method selection
    paymentMethodSelect.addEventListener('change', function() {
        showPaymentDetails();
    });
    
    // Handle form submission
    depositForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateDepositForm()) {
            submitDepositForm();
        }
    });
}

function updateDepositModal(planId, planName, minDeposit, maxDeposit, planReturn) {
    // Update hidden field
    document.getElementById('selectedPlanId').value = planId;
    
    // Update plan information display
    document.getElementById('selectedPlanName').textContent = planName;
    document.getElementById('selectedPlanReturn').textContent = planReturn;
    
    // Update deposit range display
    const rangeText = maxDeposit ? 
        `${formatCurrency(minDeposit)} - ${formatCurrency(maxDeposit)}` : 
        `${formatCurrency(minDeposit)}+`;
    document.getElementById('selectedPlanRange').textContent = rangeText;
    
    // Update amount input constraints
    const amountInput = document.getElementById('depositAmount');
    amountInput.min = minDeposit;
    if (maxDeposit) {
        amountInput.max = maxDeposit;
    } else {
        amountInput.removeAttribute('max');
    }
    
    // Clear previous values
    amountInput.value = '';
    document.getElementById('paymentMethod').value = '';
    document.getElementById('transactionId').value = '';
    document.getElementById('proofOfPayment').value = '';
    document.getElementById('paymentDetails').style.display = 'none';
    document.getElementById('amountValidation').textContent = '';
}

function validateDepositAmount() {
    const amountInput = document.getElementById('depositAmount');
    const validationDiv = document.getElementById('amountValidation');
    const amount = parseFloat(amountInput.value);
    const minDeposit = parseFloat(amountInput.min);
    const maxDeposit = amountInput.max ? parseFloat(amountInput.max) : null;
    
    if (!amount || amount <= 0) {
        validationDiv.textContent = '';
        validationDiv.className = '';
        return false;
    }
    
    if (amount < minDeposit) {
        validationDiv.textContent = `Minimum deposit is ${formatCurrency(minDeposit)}`;
        validationDiv.className = 'amount-invalid';
        return false;
    }
    
    if (maxDeposit && amount > maxDeposit) {
        validationDiv.textContent = `Maximum deposit is ${formatCurrency(maxDeposit)}`;
        validationDiv.className = 'amount-invalid';
        return false;
    }
    
    validationDiv.textContent = `✓ Valid deposit amount`;
    validationDiv.className = 'amount-valid';
    return true;
}

function showPaymentDetails() {
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const paymentDetails = document.getElementById('paymentDetails');
    const paymentInstructions = document.getElementById('paymentInstructions');
    
    const selectedOption = paymentMethodSelect.options[paymentMethodSelect.selectedIndex];
    
    if (selectedOption.value) {
        const paymentType = selectedOption.dataset.type;
        const paymentDetailsText = selectedOption.dataset.details;
        
        // Show payment instructions
        paymentInstructions.innerHTML = `
            <strong>Payment Type:</strong> ${paymentType}<br>
            <strong>Details:</strong> ${paymentDetailsText}
        `;
        
        paymentDetails.style.display = 'block';
    } else {
        paymentDetails.style.display = 'none';
    }
}

function validateDepositForm() {
    const form = document.getElementById('depositForm');
    const formData = new FormData(form);
    let isValid = true;
    let errors = [];
    
    // Validate amount
    const amount = parseFloat(formData.get('amount'));
    if (!amount || amount <= 0) {
        errors.push('Please enter a valid deposit amount');
        isValid = false;
    } else if (!validateDepositAmount()) {
        errors.push('Deposit amount is outside the allowed range');
        isValid = false;
    }
    
    // Validate payment method
    if (!formData.get('payment_method_id')) {
        errors.push('Please select a payment method');
        isValid = false;
    }
    
    // Validate plan selection
    if (!formData.get('plan_id')) {
        errors.push('Please select a trading plan');
        isValid = false;
    }
    
    // Show errors if any
    if (!isValid) {
        showAlert('danger', 'Please correct the following errors:<br>• ' + errors.join('<br>• '));
    }
    
    return isValid;
}

function submitDepositForm() {
    const submitButton = document.getElementById('submitDeposit');
    const originalText = submitButton.innerHTML;
    
    // Show loading state
    submitButton.classList.add('btn-loading');
    submitButton.disabled = true;
    
    // Create FormData object
    const form = document.getElementById('depositForm');
    const formData = new FormData(form);
    
    // Submit form via AJAX
    fetch('/user/deposit/process.php', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showAlert('success', data.message || 'Deposit submitted successfully! You will be notified once it\'s processed.');
            
            // Close modal after delay
            setTimeout(() => {
                bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
                
                // Optionally redirect to deposits page
                if (data.redirect) {
                    window.location.href = data.redirect;
                } else {
                    // Refresh page to show updated balance
                    window.location.reload();
                }
            }, 2000);
            
        } else {
            // Show error message
            showAlert('danger', data.message || 'An error occurred while processing your deposit. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'A network error occurred. Please check your connection and try again.');
    })
    .finally(() => {
        // Reset button state
        submitButton.classList.remove('btn-loading');
        submitButton.disabled = false;
        submitButton.innerHTML = originalText;
    });
}

function showAlert(type, message) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.modal-body .alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Insert at the beginning of modal body
    const modalBody = document.querySelector('#depositModal .modal-body');
    modalBody.insertBefore(alertDiv, modalBody.firstChild);
    
    // Auto-dismiss success alerts
    if (type === 'success') {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// Utility function to calculate potential profit
function calculatePotentialProfit(amount, dailyReturn, duration) {
    const totalReturn = (dailyReturn / 100) * duration;
    return amount * totalReturn;
}

// Add profit calculator functionality
function addProfitCalculator() {
    const amountInput = document.getElementById('depositAmount');
    
    amountInput.addEventListener('input', function() {
        const amount = parseFloat(this.value);
        if (amount && amount > 0) {
            // Get plan details from modal
            const planCard = document.querySelector(`[data-plan-id="${document.getElementById('selectedPlanId').value}"]`);
            if (planCard) {
                const dailyReturn = parseFloat(planCard.querySelector('.return-rate').textContent);
                const duration = parseInt(planCard.querySelector('.detail-value').textContent);
                
                const profit = calculatePotentialProfit(amount, dailyReturn, duration);
                const total = amount + profit;
                
                // Show profit calculation (you can add this to the UI)
                console.log(`Investment: ${formatCurrency(amount)}, Profit: ${formatCurrency(profit)}, Total: ${formatCurrency(total)}`);
            }
        }
    });
}

// Initialize profit calculator when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    addProfitCalculator();
});