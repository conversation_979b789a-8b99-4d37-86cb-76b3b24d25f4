<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * SupportTicket Model - Handles support ticket data and operations
 */
class SupportTicket extends BaseModel {
    protected $table = 'support_tickets';
    protected $fillable = [
        'user_id', 'subject', 'message', 'status', 'priority', 'category',
        'admin_reply', 'replied_by', 'replied_at'
    ];
    
    // Ticket statuses
    const STATUS_PENDING = 'pending';
    const STATUS_ANSWERED = 'answered';
    const STATUS_CLOSED = 'closed';
    
    // Ticket priorities
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';
    
    // Ticket categories
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_DEPOSIT = 'deposit';
    const CATEGORY_WITHDRAWAL = 'withdrawal';
    const CATEGORY_TECHNICAL = 'technical';
    const CATEGORY_ACCOUNT = 'account';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // User ID validation
        if (empty($this->user_id)) {
            $errors['user_id'] = 'User ID is required';
        } elseif (!is_numeric($this->user_id)) {
            $errors['user_id'] = 'User ID must be numeric';
        }
        
        // Subject validation
        if (empty($this->subject)) {
            $errors['subject'] = 'Subject is required';
        } elseif (strlen($this->subject) < 5) {
            $errors['subject'] = 'Subject must be at least 5 characters';
        } elseif (strlen($this->subject) > 200) {
            $errors['subject'] = 'Subject must not exceed 200 characters';
        }
        
        // Message validation
        if (empty($this->message)) {
            $errors['message'] = 'Message is required';
        } elseif (strlen($this->message) < 10) {
            $errors['message'] = 'Message must be at least 10 characters';
        } elseif (strlen($this->message) > 5000) {
            $errors['message'] = 'Message must not exceed 5000 characters';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [
            self::STATUS_PENDING, self::STATUS_ANSWERED, self::STATUS_CLOSED
        ])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Priority validation
        if (!empty($this->priority) && !in_array($this->priority, [
            self::PRIORITY_LOW, self::PRIORITY_MEDIUM, self::PRIORITY_HIGH, self::PRIORITY_URGENT
        ])) {
            $errors['priority'] = 'Invalid priority';
        }
        
        // Category validation
        if (!empty($this->category) && !in_array($this->category, [
            self::CATEGORY_GENERAL, self::CATEGORY_DEPOSIT, self::CATEGORY_WITHDRAWAL,
            self::CATEGORY_TECHNICAL, self::CATEGORY_ACCOUNT
        ])) {
            $errors['category'] = 'Invalid category';
        }
        
        // Admin reply validation (when replying)
        if (!empty($this->admin_reply) && strlen($this->admin_reply) > 5000) {
            $errors['admin_reply'] = 'Admin reply must not exceed 5000 characters';
        }
        
        return $errors;
    }
    
    /**
     * Override save to set default values
     */
    public function save() {
        // Set default status if not provided
        if (!isset($this->status)) {
            $this->status = self::STATUS_PENDING;
        }
        
        // Set default priority if not provided
        if (!isset($this->priority)) {
            $this->priority = self::PRIORITY_MEDIUM;
        }
        
        // Set default category if not provided
        if (!isset($this->category)) {
            $this->category = self::CATEGORY_GENERAL;
        }
        
        return parent::save();
    }
    
    /**
     * Get the user who created this ticket
     */
    public function getUser() {
        require_once __DIR__ . '/User.php';
        return User::find($this->user_id);
    }
    
    /**
     * Get the admin who replied to this ticket
     */
    public function getRepliedBy() {
        if (!$this->replied_by) {
            return null;
        }
        
        require_once __DIR__ . '/User.php';
        return User::find($this->replied_by);
    }
    
    /**
     * Reply to the ticket
     */
    public function reply($adminId, $replyMessage) {
        $this->admin_reply = $replyMessage;
        $this->replied_by = $adminId;
        $this->replied_at = date('Y-m-d H:i:s');
        $this->status = self::STATUS_ANSWERED;
        
        return $this->save();
    }
    
    /**
     * Close the ticket
     */
    public function close($adminId = null) {
        $this->status = self::STATUS_CLOSED;
        
        if ($adminId && !$this->replied_by) {
            $this->replied_by = $adminId;
            $this->replied_at = date('Y-m-d H:i:s');
        }
        
        return $this->save();
    }
    
    /**
     * Reopen the ticket
     */
    public function reopen() {
        $this->status = self::STATUS_PENDING;
        return $this->save();
    }
    
    /**
     * Check if ticket is pending
     */
    public function isPending() {
        return $this->status === self::STATUS_PENDING;
    }
    
    /**
     * Check if ticket is answered
     */
    public function isAnswered() {
        return $this->status === self::STATUS_ANSWERED;
    }
    
    /**
     * Check if ticket is closed
     */
    public function isClosed() {
        return $this->status === self::STATUS_CLOSED;
    }
    
    /**
     * Get priority display name
     */
    public function getPriorityDisplayName() {
        $priorities = [
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_MEDIUM => 'Medium',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent'
        ];
        
        return $priorities[$this->priority] ?? $this->priority;
    }
    
    /**
     * Get priority CSS class
     */
    public function getPriorityClass() {
        $classes = [
            self::PRIORITY_LOW => 'success',
            self::PRIORITY_MEDIUM => 'info',
            self::PRIORITY_HIGH => 'warning',
            self::PRIORITY_URGENT => 'danger'
        ];
        
        return $classes[$this->priority] ?? 'secondary';
    }
    
    /**
     * Get status display name
     */
    public function getStatusDisplayName() {
        $statuses = [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_ANSWERED => 'Answered',
            self::STATUS_CLOSED => 'Closed'
        ];
        
        return $statuses[$this->status] ?? $this->status;
    }
    
    /**
     * Get status CSS class
     */
    public function getStatusClass() {
        $classes = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_ANSWERED => 'info',
            self::STATUS_CLOSED => 'success'
        ];
        
        return $classes[$this->status] ?? 'secondary';
    }
    
    /**
     * Get category display name
     */
    public function getCategoryDisplayName() {
        $categories = [
            self::CATEGORY_GENERAL => 'General',
            self::CATEGORY_DEPOSIT => 'Deposit',
            self::CATEGORY_WITHDRAWAL => 'Withdrawal',
            self::CATEGORY_TECHNICAL => 'Technical',
            self::CATEGORY_ACCOUNT => 'Account'
        ];
        
        return $categories[$this->category] ?? $this->category;
    }
    
    /**
     * Get truncated message
     */
    public function getTruncatedMessage($length = 100) {
        if (strlen($this->message) <= $length) {
            return $this->message;
        }
        
        return substr($this->message, 0, $length) . '...';
    }
    
    /**
     * Get tickets by status
     */
    public static function getByStatus($status, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE status = :status ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
    
    /**
     * Get tickets by user
     */
    public static function getByUser($userId, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE user_id = :user_id ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
    
    /**
     * Get tickets by priority
     */
    public static function getByPriority($priority, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE priority = :priority ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['priority' => $priority]);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
    
    /**
     * Get tickets by category
     */
    public static function getByCategory($category, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE category = :category ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['category' => $category]);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
    
    /**
     * Get tickets with user information
     */
    public static function getWithUserDetails($limit = null, $offset = 0, $filters = []) {
        $instance = new static();
        $sql = "SELECT st.*, u.username, u.first_name, u.last_name, u.email,
                       admin.username as admin_username, admin.first_name as admin_first_name, admin.last_name as admin_last_name
                FROM {$instance->table} st
                LEFT JOIN users u ON st.user_id = u.id
                LEFT JOIN users admin ON st.replied_by = admin.id";
        
        $whereConditions = [];
        $params = [];
        
        if (!empty($filters['status'])) {
            $whereConditions[] = "st.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['priority'])) {
            $whereConditions[] = "st.priority = :priority";
            $params['priority'] = $filters['priority'];
        }
        
        if (!empty($filters['category'])) {
            $whereConditions[] = "st.category = :category";
            $params['category'] = $filters['category'];
        }
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "st.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['search'])) {
            $whereConditions[] = "(st.subject LIKE :search OR st.message LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }
        
        $sql .= " ORDER BY st.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get pending tickets count
     */
    public static function getPendingCount() {
        return static::count("status = 'pending'");
    }
    
    /**
     * Get urgent tickets count
     */
    public static function getUrgentCount() {
        return static::count("priority = 'urgent' AND status != 'closed'");
    }
    
    /**
     * Get ticket statistics
     */
    public static function getStatistics() {
        $instance = new static();
        
        $sql = "SELECT 
                    COUNT(*) as total_tickets,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_tickets,
                    SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered_tickets,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_tickets,
                    SUM(CASE WHEN priority = 'urgent' AND status != 'closed' THEN 1 ELSE 0 END) as urgent_tickets,
                    AVG(CASE WHEN replied_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(HOUR, created_at, replied_at) 
                        ELSE NULL END) as avg_response_time_hours
                FROM {$instance->table}";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    /**
     * Get tickets by date range
     */
    public static function getByDateRange($dateFrom, $dateTo, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE created_at >= :date_from AND created_at <= :date_to 
                ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute([
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ]);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
    
    /**
     * Search tickets
     */
    public static function search($query, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} 
                WHERE subject LIKE :query OR message LIKE :query 
                ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['query' => '%' . $query . '%']);
        $results = $stmt->fetchAll();
        
        $tickets = [];
        foreach ($results as $data) {
            $tickets[] = new static($data);
        }
        
        return $tickets;
    }
}
?>