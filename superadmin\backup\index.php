<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Database Backup';
$user = getCurrentUser();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_backup':
                // Implementation for creating backup
                $response = ['success' => true, 'message' => 'Backup created successfully'];
                break;
                
            case 'restore_backup':
                // Implementation for restoring backup
                $response = ['success' => true, 'message' => 'Database restored successfully'];
                break;
                
            case 'delete_backup':
                // Implementation for deleting backup
                $response = ['success' => true, 'message' => 'Backup deleted successfully'];
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for page reload
    $_SESSION['backup_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get response from session if available
$response = $_SESSION['backup_response'] ?? null;
unset($_SESSION['backup_response']);

// Get backup directory and files
$backupDir = __DIR__ . '/../../backups/';
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
}

$backupFiles = [];
if (is_dir($backupDir)) {
    $files = scandir($backupDir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
            $filePath = $backupDir . $file;
            $backupFiles[] = [
                'name' => $file,
                'size' => filesize($filePath),
                'created' => filemtime($filePath),
                'path' => $filePath
            ];
        }
    }
    
    // Sort by creation date (newest first)
    usort($backupFiles, function($a, $b) {
        return $b['created'] - $a['created'];
    });
}

// Get database info
$db = getDB();
$dbName = DB_NAME;

// Get database size
$stmt = $db->query("SELECT 
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS db_size_mb
    FROM information_schema.tables 
    WHERE table_schema = '$dbName'");
$dbSize = $stmt->fetch()['db_size_mb'] ?? 0;

// Get table count
$stmt = $db->query("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '$dbName'");
$tableCount = $stmt->fetch()['table_count'] ?? 0;

$siteName = SystemSetting::getValue('site_name', 'Coinage Trading');
$siteLogo = SystemSetting::getValue('site_logo', '/assets/images/logo.png');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $siteName; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo SystemSetting::getValue('primary_color', '#007bff'); ?>;
            --sidebar-bg: <?php echo SystemSetting::getValue('sidebar_bg_color', '#343a40'); ?>;
            --sidebar-text: <?php echo SystemSetting::getValue('sidebar_text_color', '#ffffff'); ?>;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            min-height: 100vh;
            color: var(--sidebar-text);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: var(--sidebar-text);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: #fff;
            transform: translateX(5px);
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 20px;
        }
        
        .backup-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .btn-custom {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .superadmin-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .backup-file {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .backup-file:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 10px rgba(0,123,255,0.1);
        }
        
        .progress-custom {
            height: 8px;
            border-radius: 10px;
            background-color: #e9ecef;
        }
        
        .progress-bar-custom {
            border-radius: 10px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 px-0">
            <div class="sidebar d-flex flex-column p-3">
                <div class="text-center mb-4">
                    <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="navbar-brand img-fluid" style="max-height: 50px;">
                    <h5 class="mt-2"><?php echo $siteName; ?></h5>
                    <div class="superadmin-badge mt-2">SUPER ADMIN</div>
                </div>
                
                <div class="user-info mb-4 text-center">
                    <div class="avatar bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="mt-2">
                        <small>Super Admin:</small><br>
                        <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                    </div>
                </div>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../settings/">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../appearance/">
                            <i class="fas fa-palette me-2"></i>Appearance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../email-templates/">
                            <i class="fas fa-envelope-open-text me-2"></i>Email Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../audit/">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../backup/">
                            <i class="fas fa-database me-2"></i>Database Backup
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../security/">
                            <i class="fas fa-shield-alt me-2"></i>Security Center
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><i class="fas fa-database me-2"></i>Database Backup</h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-custom" onclick="createBackup()">
                            <i class="fas fa-plus me-1"></i>Create Backup
                        </button>
                        <button class="btn btn-outline-info btn-custom" onclick="scheduleBackup()">
                            <i class="fas fa-clock me-1"></i>Schedule Backup
                        </button>
                    </div>
                </div>
                
                <?php if ($response): ?>
                    <div class="alert alert-<?php echo $response['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show alert-custom">
                        <?php echo htmlspecialchars($response['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Database Overview -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Database Size</h6>
                                    <h3 class="mb-0"><?php echo $dbSize; ?> MB</h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-database fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Total Tables</h6>
                                    <h3 class="mb-0"><?php echo $tableCount; ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-table fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Backup Files</h6>
                                    <h3 class="mb-0"><?php echo count($backupFiles); ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-archive fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <!-- Backup Files List -->
                        <div class="backup-card">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0"><i class="fas fa-archive me-2"></i>Backup Files</h5>
                                <button class="btn btn-outline-danger btn-sm" onclick="cleanupOldBackups()">
                                    <i class="fas fa-trash me-1"></i>Cleanup Old
                                </button>
                            </div>

                            <?php if (empty($backupFiles)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">No backup files found</h6>
                                    <p class="text-muted">Create your first backup to get started</p>
                                    <button class="btn btn-primary btn-custom" onclick="createBackup()">
                                        <i class="fas fa-plus me-1"></i>Create First Backup
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="backup-files-list">
                                    <?php foreach ($backupFiles as $backup): ?>
                                        <div class="backup-file">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-file-archive text-primary me-2"></i>
                                                        <div>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($backup['name']); ?></div>
                                                            <small class="text-muted">
                                                                <?php echo date('M j, Y H:i:s', $backup['created']); ?> •
                                                                <?php echo number_format($backup['size'] / 1024 / 1024, 2); ?> MB
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex gap-2">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('<?php echo htmlspecialchars($backup['name']); ?>')">
                                                        <i class="fas fa-download"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-warning" onclick="restoreBackup('<?php echo htmlspecialchars($backup['name']); ?>')">
                                                        <i class="fas fa-undo"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('<?php echo htmlspecialchars($backup['name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Backup Settings -->
                        <div class="backup-card">
                            <h5 class="mb-4"><i class="fas fa-cog me-2"></i>Backup Settings</h5>

                            <form method="POST" id="backupSettingsForm">
                                <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                <input type="hidden" name="action" value="update_backup_settings">

                                <div class="mb-3">
                                    <label class="form-label">Auto Backup Frequency</label>
                                    <select class="form-select" name="backup_frequency">
                                        <option value="disabled">Disabled</option>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="monthly">Monthly</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Retention Period</label>
                                    <select class="form-select" name="retention_days">
                                        <option value="7">7 days</option>
                                        <option value="30">30 days</option>
                                        <option value="90">90 days</option>
                                        <option value="365">1 year</option>
                                        <option value="0">Keep all</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="compress_backups" id="compressBackups">
                                        <label class="form-check-label" for="compressBackups">
                                            Compress Backups
                                        </label>
                                    </div>
                                    <small class="text-muted">Reduce backup file size</small>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="email_notifications" id="emailNotifications">
                                        <label class="form-check-label" for="emailNotifications">
                                            Email Notifications
                                        </label>
                                    </div>
                                    <small class="text-muted">Get notified of backup status</small>
                                </div>

                                <button type="submit" class="btn btn-primary btn-custom w-100">
                                    <i class="fas fa-save me-1"></i>Save Settings
                                </button>
                            </form>
                        </div>

                        <!-- Quick Actions -->
                        <div class="backup-card">
                            <h5 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>

                            <div class="d-grid gap-2">
                                <button class="btn btn-success btn-custom" onclick="createBackup()">
                                    <i class="fas fa-plus me-1"></i>Create Backup Now
                                </button>
                                <button class="btn btn-info btn-custom" onclick="testBackupSystem()">
                                    <i class="fas fa-vial me-1"></i>Test Backup System
                                </button>
                                <button class="btn btn-warning btn-custom" onclick="optimizeDatabase()">
                                    <i class="fas fa-tools me-1"></i>Optimize Database
                                </button>
                                <button class="btn btn-secondary btn-custom" onclick="exportSchema()">
                                    <i class="fas fa-code me-1"></i>Export Schema Only
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Processing...</h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                </div>
                <div class="progress progress-custom">
                    <div class="progress-bar progress-bar-custom" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="text-center mt-2">
                    <small class="text-muted" id="progressText">Initializing...</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function createBackup() {
    if (confirm('Create a new database backup? This may take a few minutes.')) {
        showProgress('Creating backup...');
        // Implementation for creating backup
        setTimeout(() => {
            hideProgress();
            alert('Backup creation functionality will be implemented');
        }, 2000);
    }
}

function restoreBackup(filename) {
    if (confirm('Restore database from backup "' + filename + '"? This will overwrite all current data!')) {
        showProgress('Restoring backup...');
        // Implementation for restoring backup
        setTimeout(() => {
            hideProgress();
            alert('Backup restore functionality will be implemented');
        }, 2000);
    }
}

function deleteBackup(filename) {
    if (confirm('Delete backup "' + filename + '"? This action cannot be undone.')) {
        // Implementation for deleting backup
        alert('Backup deletion functionality will be implemented');
    }
}

function downloadBackup(filename) {
    // Implementation for downloading backup
    alert('Backup download functionality will be implemented');
}

function cleanupOldBackups() {
    if (confirm('Delete old backup files based on retention settings?')) {
        // Implementation for cleanup
        alert('Backup cleanup functionality will be implemented');
    }
}

function scheduleBackup() {
    // Implementation for scheduling backup
    alert('Backup scheduling functionality will be implemented');
}

function testBackupSystem() {
    showProgress('Testing backup system...');
    // Implementation for testing backup system
    setTimeout(() => {
        hideProgress();
        alert('Backup system test functionality will be implemented');
    }, 1500);
}

function optimizeDatabase() {
    if (confirm('Optimize database tables? This may take a few minutes.')) {
        showProgress('Optimizing database...');
        // Implementation for database optimization
        setTimeout(() => {
            hideProgress();
            alert('Database optimization functionality will be implemented');
        }, 3000);
    }
}

function exportSchema() {
    // Implementation for exporting schema
    alert('Schema export functionality will be implemented');
}

function showProgress(text) {
    document.getElementById('progressText').textContent = text;
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        document.querySelector('.progress-bar').style.width = progress + '%';
    }, 200);

    // Store interval for cleanup
    window.progressInterval = interval;
}

function hideProgress() {
    if (window.progressInterval) {
        clearInterval(window.progressInterval);
    }
    document.querySelector('.progress-bar').style.width = '100%';
    setTimeout(() => {
        const modal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
        if (modal) modal.hide();
    }, 500);
}
</script>

</body>
</html>
