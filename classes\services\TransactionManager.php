<?php
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Deposit.php';
require_once __DIR__ . '/../models/Withdrawal.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';
require_once __DIR__ . '/NotificationService.php';

/**
 * TransactionManager - Handles all financial transaction operations
 * Provides centralized transaction recording, balance updates, and audit trails
 */
class TransactionManager {
    
    /**
     * Record a new transaction with proper audit trail
     */
    public static function recordTransaction($data) {
        // Validate required fields
        $rules = [
            'user_id' => ['required' => true, 'numeric' => ['min' => 1]],
            'type' => ['required' => true, 'in' => [
                Transaction::TYPE_DEPOSIT, Transaction::TYPE_WITHDRAWAL, Transaction::TYPE_BONUS,
                Transaction::TYPE_TRADE_PROFIT, Transaction::TYPE_TRADE_LOSS, Transaction::TYPE_TRANSFER_IN,
                Transaction::TYPE_TRANSFER_OUT, Transaction::TYPE_ADMIN_CREDIT, Transaction::TYPE_ADMIN_DEBIT
            ]],
            'amount' => ['required' => true, 'numeric' => ['min' => 0.01, 'max' => 999999999.99]],
            'description' => ['required' => true, 'length' => ['max' => 500]]
        ];
        
        $errors = ValidationHelper::validateFields($data, $rules);
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Get user and current balance
        $user = User::find($data['user_id']);
        if (!$user) {
            return ['success' => false, 'errors' => ['user_id' => 'User not found']];
        }
        
        $balanceBefore = $user->balance;
        
        // Calculate new balance based on transaction type
        $transaction = new Transaction();
        $transaction->fill($data);
        
        if ($transaction->isCredit()) {
            $balanceAfter = $balanceBefore + abs($data['amount']);
        } else {
            $balanceAfter = $balanceBefore - abs($data['amount']);
            
            // Check for sufficient balance on debit transactions
            if ($balanceAfter < 0 && !in_array($data['type'], [Transaction::TYPE_ADMIN_DEBIT])) {
                return ['success' => false, 'errors' => ['amount' => 'Insufficient balance']];
            }
        }
        
        // Set balance information
        $transaction->balance_before = $balanceBefore;
        $transaction->balance_after = max(0, $balanceAfter); // Prevent negative balance
        
        // Set default values
        $transaction->status = $data['status'] ?? Transaction::STATUS_COMPLETED;
        $transaction->reference_type = $data['reference_type'] ?? Transaction::REF_TYPE_MANUAL;
        $transaction->reference_id = $data['reference_id'] ?? null;
        $transaction->processed_by = $data['processed_by'] ?? null;
        
        // Begin database transaction
        $transaction->beginTransaction();
        
        try {
            // Save transaction record
            if (!$transaction->save()) {
                throw new Exception('Failed to save transaction record');
            }
            
            // Update user balance if transaction is completed
            if ($transaction->status === Transaction::STATUS_COMPLETED) {
                $user->balance = $transaction->balance_after;
                if (!$user->save()) {
                    throw new Exception('Failed to update user balance');
                }
            }
            
            $transaction->commit();
            return ['success' => true, 'transaction' => $transaction];
            
        } catch (Exception $e) {
            $transaction->rollback();
            error_log("Transaction recording error: " . $e->getMessage());
            return ['success' => false, 'errors' => ['general' => 'Failed to record transaction']];
        }
    }
    
    /**
     * Update user balance with transaction recording
     */
    public static function updateBalance($userId, $amount, $type, $description, $referenceData = []) {
        $data = [
            'user_id' => $userId,
            'type' => $type,
            'amount' => $amount,
            'description' => $description,
            'reference_id' => $referenceData['reference_id'] ?? null,
            'reference_type' => $referenceData['reference_type'] ?? Transaction::REF_TYPE_MANUAL,
            'processed_by' => $referenceData['processed_by'] ?? null,
            'status' => $referenceData['status'] ?? Transaction::STATUS_COMPLETED
        ];
        
        return self::recordTransaction($data);
    }
    
    /**
     * Process deposit transaction
     */
    public static function processDeposit($depositId, $adminId = null) {
        $deposit = Deposit::find($depositId);
        if (!$deposit) {
            return ['success' => false, 'error' => 'Deposit not found'];
        }
        
        if (!$deposit->isPending()) {
            return ['success' => false, 'error' => 'Deposit is not pending'];
        }
        
        $deposit->beginTransaction();
        
        try {
            // Record main deposit transaction
            $depositResult = self::recordTransaction([
                'user_id' => $deposit->user_id,
                'type' => Transaction::TYPE_DEPOSIT,
                'amount' => $deposit->amount,
                'description' => "Deposit approved - Amount: $" . number_format($deposit->amount, 2) . 
                               ($deposit->plan_id ? " (Plan: " . $deposit->getPlan()->name . ")" : ""),
                'reference_id' => $deposit->getId(),
                'reference_type' => Transaction::REF_TYPE_DEPOSIT,
                'processed_by' => $adminId,
                'status' => Transaction::STATUS_COMPLETED
            ]);
            
            if (!$depositResult['success']) {
                throw new Exception('Failed to record deposit transaction');
            }
            
            // Record bonus transaction if applicable
            if ($deposit->bonus_amount > 0) {
                $bonusResult = self::recordTransaction([
                    'user_id' => $deposit->user_id,
                    'type' => Transaction::TYPE_BONUS,
                    'amount' => $deposit->bonus_amount,
                    'description' => "Deposit bonus - " . number_format($deposit->getBonusPercentage(), 1) . "% of $" . 
                                   number_format($deposit->amount, 2),
                    'reference_id' => $deposit->getId(),
                    'reference_type' => Transaction::REF_TYPE_DEPOSIT,
                    'processed_by' => $adminId,
                    'status' => Transaction::STATUS_COMPLETED
                ]);
                
                if (!$bonusResult['success']) {
                    throw new Exception('Failed to record bonus transaction');
                }
                
                // Update user bonus balance
                $user = User::find($deposit->user_id);
                $user->bonus += $deposit->bonus_amount;
                if (!$user->save()) {
                    throw new Exception('Failed to update user bonus balance');
                }
            }
            
            // Update deposit status
            $deposit->status = Deposit::STATUS_APPROVED;
            $deposit->processed_by = $adminId;
            $deposit->processed_at = date('Y-m-d H:i:s');
            
            if (!$deposit->save()) {
                throw new Exception('Failed to update deposit status');
            }
            
            // Update user totals
            $user = User::find($deposit->user_id);
            $user->total_deposit += $deposit->amount;
            if (!$user->save()) {
                throw new Exception('Failed to update user deposit total');
            }
            
            $deposit->commit();
            
            // Send notification
            NotificationService::sendDepositApprovalNotification($deposit->getId());
            
            return ['success' => true, 'deposit' => $deposit, 'transactions' => [
                'deposit' => $depositResult['transaction'],
                'bonus' => $bonusResult['transaction'] ?? null
            ]];
            
        } catch (Exception $e) {
            $deposit->rollback();
            error_log("Deposit processing error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Process withdrawal transaction
     */
    public static function processWithdrawal($withdrawalId, $adminId = null) {
        $withdrawal = Withdrawal::find($withdrawalId);
        if (!$withdrawal) {
            return ['success' => false, 'error' => 'Withdrawal not found'];
        }
        
        if (!$withdrawal->isPending()) {
            return ['success' => false, 'error' => 'Withdrawal is not pending'];
        }
        
        $withdrawal->beginTransaction();
        
        try {
            // Record withdrawal transaction
            $result = self::recordTransaction([
                'user_id' => $withdrawal->user_id,
                'type' => Transaction::TYPE_WITHDRAWAL,
                'amount' => $withdrawal->amount,
                'description' => "Withdrawal approved - Amount: $" . number_format($withdrawal->amount, 2) . 
                               " via " . $withdrawal->withdrawal_method,
                'reference_id' => $withdrawal->getId(),
                'reference_type' => Transaction::REF_TYPE_WITHDRAWAL,
                'processed_by' => $adminId,
                'status' => Transaction::STATUS_COMPLETED
            ]);
            
            if (!$result['success']) {
                throw new Exception('Failed to record withdrawal transaction');
            }
            
            // Update withdrawal status
            $withdrawal->status = Withdrawal::STATUS_APPROVED;
            $withdrawal->processed_by = $adminId;
            $withdrawal->processed_at = date('Y-m-d H:i:s');
            
            if (!$withdrawal->save()) {
                throw new Exception('Failed to update withdrawal status');
            }
            
            // Update user totals
            $user = User::find($withdrawal->user_id);
            $user->total_withdrawal += $withdrawal->amount;
            if (!$user->save()) {
                throw new Exception('Failed to update user withdrawal total');
            }
            
            $withdrawal->commit();
            
            // Send notification
            NotificationService::sendWithdrawalApprovalNotification($withdrawal->getId());
            
            return ['success' => true, 'withdrawal' => $withdrawal, 'transaction' => $result['transaction']];
            
        } catch (Exception $e) {
            $withdrawal->rollback();
            error_log("Withdrawal processing error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }  
  
    /**
     * Get transaction history with filtering and pagination
     */
    public static function getTransactionHistory($filters = [], $limit = 20, $offset = 0) {
        $allowedFilters = ['user_id', 'type', 'status', 'date_from', 'date_to', 'reference_type'];
        $validFilters = array_intersect_key($filters, array_flip($allowedFilters));
        
        return Transaction::getWithUserDetails($limit, $offset, $validFilters);
    }
    
    /**
     * Get user transaction history
     */
    public static function getUserTransactionHistory($userId, $limit = 20, $offset = 0, $type = null) {
        return Transaction::getByUser($userId, $limit, $offset, $type);
    }
    
    /**
     * Get transaction statistics
     */
    public static function getTransactionStatistics($userId = null, $dateFrom = null, $dateTo = null) {
        return Transaction::getStatistics($userId, $dateFrom, $dateTo);
    }
    
    /**
     * Admin credit user account
     */
    public static function adminCreditAccount($userId, $amount, $description, $adminId) {
        return self::recordTransaction([
            'user_id' => $userId,
            'type' => Transaction::TYPE_ADMIN_CREDIT,
            'amount' => abs($amount),
            'description' => $description,
            'processed_by' => $adminId,
            'reference_type' => Transaction::REF_TYPE_MANUAL,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }
    
    /**
     * Admin debit user account
     */
    public static function adminDebitAccount($userId, $amount, $description, $adminId) {
        return self::recordTransaction([
            'user_id' => $userId,
            'type' => Transaction::TYPE_ADMIN_DEBIT,
            'amount' => abs($amount),
            'description' => $description,
            'processed_by' => $adminId,
            'reference_type' => Transaction::REF_TYPE_MANUAL,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }
    
    /**
     * Record trade profit transaction
     */
    public static function recordTradeProfit($userId, $amount, $tradeId, $description = '') {
        return self::recordTransaction([
            'user_id' => $userId,
            'type' => Transaction::TYPE_TRADE_PROFIT,
            'amount' => abs($amount),
            'description' => $description ?: "Trade profit - $" . number_format($amount, 2),
            'reference_id' => $tradeId,
            'reference_type' => Transaction::REF_TYPE_TRADE,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }
    
    /**
     * Record trade loss transaction
     */
    public static function recordTradeLoss($userId, $amount, $tradeId, $description = '') {
        return self::recordTransaction([
            'user_id' => $userId,
            'type' => Transaction::TYPE_TRADE_LOSS,
            'amount' => abs($amount),
            'description' => $description ?: "Trade loss - $" . number_format($amount, 2),
            'reference_id' => $tradeId,
            'reference_type' => Transaction::REF_TYPE_TRADE,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }
    
    /**
     * Get pending transactions requiring admin attention
     */
    public static function getPendingTransactions($limit = 50) {
        return Transaction::getByStatus(Transaction::STATUS_PENDING, $limit);
    }
    
    /**
     * Update transaction status
     */
    public static function updateTransactionStatus($transactionId, $status, $adminId = null) {
        $transaction = Transaction::find($transactionId);
        if (!$transaction) {
            return ['success' => false, 'error' => 'Transaction not found'];
        }
        
        $validStatuses = [
            Transaction::STATUS_PENDING,
            Transaction::STATUS_COMPLETED,
            Transaction::STATUS_FAILED,
            Transaction::STATUS_CANCELLED
        ];
        
        if (!in_array($status, $validStatuses)) {
            return ['success' => false, 'error' => 'Invalid status'];
        }
        
        $transaction->beginTransaction();
        
        try {
            $oldStatus = $transaction->status;
            $transaction->status = $status;
            
            if ($adminId) {
                $transaction->processed_by = $adminId;
            }
            
            if (!$transaction->save()) {
                throw new Exception('Failed to update transaction status');
            }
            
            // If changing from pending to completed, update user balance
            if ($oldStatus === Transaction::STATUS_PENDING && $status === Transaction::STATUS_COMPLETED) {
                $user = User::find($transaction->user_id);
                if ($user) {
                    $user->balance = $transaction->balance_after;
                    if (!$user->save()) {
                        throw new Exception('Failed to update user balance');
                    }
                }
            }
            
            // If changing from completed to failed/cancelled, revert user balance
            if ($oldStatus === Transaction::STATUS_COMPLETED && in_array($status, [Transaction::STATUS_FAILED, Transaction::STATUS_CANCELLED])) {
                $user = User::find($transaction->user_id);
                if ($user) {
                    $user->balance = $transaction->balance_before;
                    if (!$user->save()) {
                        throw new Exception('Failed to revert user balance');
                    }
                }
            }
            
            $transaction->commit();
            
            // Send notification for status changes
            NotificationService::sendTransactionStatusNotification($transaction->getId(), $oldStatus, $status);
            
            return ['success' => true, 'transaction' => $transaction];
            
        } catch (Exception $e) {
            $transaction->rollback();
            error_log("Transaction status update error: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get transaction summary for a user
     */
    public static function getUserTransactionSummary($userId) {
        $db = getDB();
        
        $sql = "SELECT 
                    type,
                    COUNT(*) as count,
                    SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_credit,
                    SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_debit,
                    MAX(created_at) as last_transaction
                FROM transactions 
                WHERE user_id = :user_id AND status = 'completed'
                GROUP BY type
                ORDER BY type";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Validate transaction data before processing
     */
    private static function validateTransactionData($data) {
        $errors = [];
        
        // Check if user exists and is active
        if (!empty($data['user_id'])) {
            $user = User::find($data['user_id']);
            if (!$user) {
                $errors['user_id'] = 'User not found';
            } elseif (!$user->isActive()) {
                $errors['user_id'] = 'User account is not active';
            }
        }
        
        // Validate amount precision (2 decimal places)
        if (!empty($data['amount'])) {
            $amount = (float) $data['amount'];
            if (round($amount, 2) != $amount) {
                $errors['amount'] = 'Amount must have maximum 2 decimal places';
            }
        }
        
        // Validate reference data consistency
        if (!empty($data['reference_id']) && !empty($data['reference_type'])) {
            switch ($data['reference_type']) {
                case Transaction::REF_TYPE_DEPOSIT:
                    if (!Deposit::find($data['reference_id'])) {
                        $errors['reference_id'] = 'Referenced deposit not found';
                    }
                    break;
                    
                case Transaction::REF_TYPE_WITHDRAWAL:
                    if (!Withdrawal::find($data['reference_id'])) {
                        $errors['reference_id'] = 'Referenced withdrawal not found';
                    }
                    break;
            }
        }
        
        return $errors;
    }
    
    /**
     * Get audit trail for a specific transaction
     */
    public static function getTransactionAuditTrail($transactionId) {
        $transaction = Transaction::find($transactionId);
        if (!$transaction) {
            return null;
        }
        
        $auditTrail = [
            'transaction' => $transaction,
            'user' => $transaction->getUser(),
            'processed_by' => $transaction->getProcessedBy(),
            'related_record' => $transaction->getRelatedRecord(),
            'balance_changes' => [
                'before' => $transaction->balance_before,
                'after' => $transaction->balance_after,
                'difference' => $transaction->balance_after - $transaction->balance_before
            ]
        ];
        
        return $auditTrail;
    }
    
    /**
     * Bulk process transactions (for admin operations)
     */
    public static function bulkProcessTransactions($transactionIds, $action, $adminId) {
        $results = [];
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($transactionIds as $transactionId) {
            switch ($action) {
                case 'approve':
                    $result = self::updateTransactionStatus($transactionId, Transaction::STATUS_COMPLETED, $adminId);
                    break;
                    
                case 'reject':
                    $result = self::updateTransactionStatus($transactionId, Transaction::STATUS_FAILED, $adminId);
                    break;
                    
                case 'cancel':
                    $result = self::updateTransactionStatus($transactionId, Transaction::STATUS_CANCELLED, $adminId);
                    break;
                    
                default:
                    $result = ['success' => false, 'error' => 'Invalid action'];
            }
            
            $results[$transactionId] = $result;
            
            if ($result['success']) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }
        
        return [
            'success' => $errorCount === 0,
            'processed' => count($transactionIds),
            'successful' => $successCount,
            'failed' => $errorCount,
            'results' => $results
        ];
    }
}
?>