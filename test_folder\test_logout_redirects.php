<?php
// test_folder/test_logout_redirects.php

// Use output buffering to prevent header errors in CLI environment
ob_start();

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

function run_logout_test($role, $expectedPath, $testName) {
    // Ensure a clean session state for each test
    if (session_status() == PHP_SESSION_ACTIVE) {
        session_destroy();
    }
    session_start();

    // Set the role for the current test
    $_SESSION['role'] = $role;

    // Replicate the logic from logout.php to determine redirect path
    $role_to_check = getUserRole();
    $redirectPath = '';
    switch ($role_to_check) {
        case 'admin':
            $redirectPath = 'admin/login.php';
            break;
        case 'superadmin':
            $redirectPath = 'superadmin/login.php';
            break;
        default:
            $redirectPath = 'login.php';
            break;
    }

    $actualUrl = url($redirectPath);
    $expectedUrl = url($expectedPath);

    // Perform the assertion
    if ($actualUrl === $expectedUrl) {
        echo "PASSED: $testName\n";
        return true;
    } else {
        echo "FAILED: $testName\n";
        echo "  Expected: $expectedUrl\n";
        echo "  Actual:   $actualUrl\n";
        return false;
    }
}

echo "Running Logout Redirect Tests...\n";
$all_tests_passed = true;

// Test Cases
if (!run_logout_test('admin', 'admin/login.php', 'Admin Logout Redirect')) $all_tests_passed = false;
if (!run_logout_test('superadmin', 'superadmin/login.php', 'Super Admin Logout Redirect')) $all_tests_passed = false;
if (!run_logout_test('user', 'login.php', 'User Logout Redirect')) $all_tests_passed = false;

if ($all_tests_passed) {
    echo "All logout redirect tests passed\n";
} else {
    echo "Some logout redirect tests failed.\n";
    exit(1);
}

// Clean up the final session and output buffer
if (session_status() == PHP_SESSION_ACTIVE) {
    session_destroy();
}
ob_end_flush();

?>