/**
 * Admin Deposit Management JavaScript
 */

// Global variables
let selectedDeposits = [];

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeDepositManagement();
});

/**
 * Initialize deposit management functionality
 */
function initializeDepositManagement() {
    // Initialize select all checkbox
    const selectAllCheckbox = document.getElementById('selectAllDeposits');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }
    
    // Initialize individual checkboxes
    const depositCheckboxes = document.querySelectorAll('.deposit-checkbox');
    depositCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedDeposits);
    });
    
    // Initialize search functionality
    const searchInput = document.getElementById('searchDeposits');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchDeposits();
            }
        });
    }
    
    // Initialize form submissions
    initializeFormSubmissions();
    
    // Initialize bulk actions
    initializeBulkActions();
}

/**
 * Toggle select all checkboxes
 */
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllDeposits');
    const depositCheckboxes = document.querySelectorAll('.deposit-checkbox');
    
    depositCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateSelectedDeposits();
}

/**
 * Update selected deposits array
 */
function updateSelectedDeposits() {
    selectedDeposits = [];
    const depositCheckboxes = document.querySelectorAll('.deposit-checkbox:checked');
    
    depositCheckboxes.forEach(checkbox => {
        selectedDeposits.push(parseInt(checkbox.value));
    });
    
    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAllDeposits');
    const allCheckboxes = document.querySelectorAll('.deposit-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = selectedDeposits.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedDeposits.length > 0 && selectedDeposits.length < allCheckboxes.length;
    }
    
    // Update selected count in bulk actions modal
    const selectedCountElement = document.getElementById('selectedCount');
    if (selectedCountElement) {
        selectedCountElement.textContent = selectedDeposits.length;
    }
}

/**
 * Filter deposits by status
 */
function filterDeposits(status) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('status', status);
    currentUrl.searchParams.delete('page'); // Reset to first page
    window.location.href = currentUrl.toString();
}

/**
 * Search deposits
 */
function searchDeposits() {
    const searchInput = document.getElementById('searchDeposits');
    const searchTerm = searchInput.value.trim();
    
    if (searchTerm.length < 2) {
        showAlert('Please enter at least 2 characters to search', 'warning');
        return;
    }
    
    // Implement search functionality
    // This would typically involve AJAX call to search endpoint
    console.log('Searching for:', searchTerm);
}

/**
 * Refresh deposits list
 */
function refreshDeposits() {
    window.location.reload();
}

/**
 * View deposit details
 */
function viewDepositDetails(depositId) {
    showLoadingSpinner();
    
    fetch(`api/details.php?id=${depositId}`)
        .then(response => response.json())
        .then(data => {
            hideLoadingSpinner();
            
            if (data.success) {
                displayDepositDetails(data.data);
            } else {
                showAlert(data.message || 'Failed to load deposit details', 'danger');
            }
        })
        .catch(error => {
            hideLoadingSpinner();
            console.error('Error:', error);
            showAlert('An error occurred while loading deposit details', 'danger');
        });
}

/**
 * Display deposit details in modal
 */
function displayDepositDetails(deposit) {
    const modalContent = document.getElementById('depositDetailsContent');
    
    const statusBadge = getStatusBadge(deposit.status);
    const bonusInfo = deposit.bonus_amount > 0 ? 
        `<span class="text-success">+$${deposit.bonus_amount.toFixed(2)} bonus</span>` : 
        '<span class="text-muted">No bonus</span>';
    
    modalContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted">Deposit Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Amount:</strong></td>
                        <td>$${deposit.amount.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td><strong>Bonus:</strong></td>
                        <td>${bonusInfo}</td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>${statusBadge}</td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>${formatDateTime(deposit.created_at)}</td>
                    </tr>
                    ${deposit.approved_at ? `
                    <tr>
                        <td><strong>Approved:</strong></td>
                        <td>${formatDateTime(deposit.approved_at)}</td>
                    </tr>` : ''}
                    ${deposit.rejected_at ? `
                    <tr>
                        <td><strong>Rejected:</strong></td>
                        <td>${formatDateTime(deposit.rejected_at)}</td>
                    </tr>` : ''}
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-muted">User Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td>${deposit.user.first_name} ${deposit.user.last_name}</td>
                    </tr>
                    <tr>
                        <td><strong>Username:</strong></td>
                        <td>@${deposit.user.username}</td>
                    </tr>
                    <tr>
                        <td><strong>Email:</strong></td>
                        <td>${deposit.user.email}</td>
                    </tr>
                    <tr>
                        <td><strong>Balance:</strong></td>
                        <td>$${deposit.user.balance.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td><strong>Bonus:</strong></td>
                        <td>$${deposit.user.bonus.toFixed(2)}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        ${deposit.payment_method ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted">Payment Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Payment Method:</strong></td>
                        <td>${deposit.payment_method.name} (${deposit.payment_method.type})</td>
                    </tr>
                </table>
            </div>
        </div>` : ''}
        
        ${deposit.trading_plan ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted">Trading Plan</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>Plan:</strong></td>
                        <td>${deposit.trading_plan.name}</td>
                    </tr>
                    <tr>
                        <td><strong>Range:</strong></td>
                        <td>$${deposit.trading_plan.minimum_amount.toFixed(2)} - $${deposit.trading_plan.maximum_amount.toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td><strong>Bonus Rate:</strong></td>
                        <td>${deposit.trading_plan.bonus_percentage}%</td>
                    </tr>
                </table>
            </div>
        </div>` : ''}
        
        ${deposit.admin_note || deposit.rejection_reason ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-muted">Admin Notes</h6>
                ${deposit.admin_note ? `<p><strong>Note:</strong> ${deposit.admin_note}</p>` : ''}
                ${deposit.rejection_reason ? `<p><strong>Rejection Reason:</strong> ${deposit.rejection_reason}</p>` : ''}
            </div>
        </div>` : ''}
    `;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('depositDetailsModal'));
    modal.show();
}

/**
 * Approve deposit
 */
function approveDeposit(depositId) {
    // Pre-fill approve modal
    document.getElementById('approveDepositId').value = depositId;
    
    // Load deposit details for the form
    fetch(`api/details.php?id=${depositId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const deposit = data.data;
                document.getElementById('approveDepositAmount').value = `$${deposit.amount.toFixed(2)}`;
                
                // Calculate suggested bonus
                if (deposit.trading_plan && deposit.trading_plan.bonus_percentage > 0) {
                    const suggestedBonus = (deposit.amount * deposit.trading_plan.bonus_percentage / 100);
                    document.getElementById('approveBonusAmount').value = suggestedBonus.toFixed(2);
                }
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('approveDepositModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load deposit details', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading deposit details', 'danger');
        });
}

/**
 * Reject deposit
 */
function rejectDeposit(depositId) {
    // Pre-fill reject modal
    document.getElementById('rejectDepositId').value = depositId;
    
    // Load deposit details for the form
    fetch(`api/details.php?id=${depositId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const deposit = data.data;
                document.getElementById('rejectDepositAmount').value = `$${deposit.amount.toFixed(2)}`;
                
                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('rejectDepositModal'));
                modal.show();
            } else {
                showAlert(data.message || 'Failed to load deposit details', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while loading deposit details', 'danger');
        });
}

/**
 * Show bulk actions modal
 */
function showBulkActions() {
    if (selectedDeposits.length === 0) {
        showAlert('Please select at least one deposit', 'warning');
        return;
    }
    
    // Update selected count
    document.getElementById('selectedCount').textContent = selectedDeposits.length;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('bulkActionsModal'));
    modal.show();
}

/**
 * Initialize form submissions
 */
function initializeFormSubmissions() {
    // Approve deposit form
    const approveForm = document.getElementById('approveDepositForm');
    if (approveForm) {
        approveForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitApproveForm();
        });
    }
    
    // Reject deposit form
    const rejectForm = document.getElementById('rejectDepositForm');
    if (rejectForm) {
        rejectForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitRejectForm();
        });
    }
    
    // Bulk actions form
    const bulkForm = document.getElementById('bulkActionsForm');
    if (bulkForm) {
        bulkForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitBulkActionsForm();
        });
    }
}

/**
 * Initialize bulk actions functionality
 */
function initializeBulkActions() {
    // Show/hide reason field based on action
    const actionSelect = document.querySelector('#bulkActionsForm select[name="action"]');
    const reasonField = document.getElementById('bulkReasonField');
    
    if (actionSelect && reasonField) {
        actionSelect.addEventListener('change', function() {
            if (this.value === 'reject') {
                reasonField.style.display = 'block';
                reasonField.querySelector('textarea').required = true;
            } else {
                reasonField.style.display = 'none';
                reasonField.querySelector('textarea').required = false;
            }
        });
    }
}

/**
 * Submit approve form
 */
function submitApproveForm() {
    const form = document.getElementById('approveDepositForm');
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Disable submit button
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Approving...';
    
    fetch('api/approve.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Deposit approved successfully', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('approveDepositModal'));
            modal.hide();
            
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Failed to approve deposit', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while approving deposit', 'danger');
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-check"></i> Approve Deposit';
    });
}

/**
 * Submit reject form
 */
function submitRejectForm() {
    const form = document.getElementById('rejectDepositForm');
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Disable submit button
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Rejecting...';
    
    fetch('api/reject.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Deposit rejected successfully', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('rejectDepositModal'));
            modal.hide();
            
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Failed to reject deposit', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while rejecting deposit', 'danger');
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-times"></i> Reject Deposit';
    });
}

/**
 * Submit bulk actions form
 */
function submitBulkActionsForm() {
    const form = document.getElementById('bulkActionsForm');
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Add selected deposit IDs
    formData.append('deposit_ids', JSON.stringify(selectedDeposits));
    
    // Disable submit button
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    
    fetch('api/bulk-action.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
            modal.hide();
            
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'Bulk action failed', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred during bulk action', 'danger');
    })
    .finally(() => {
        // Re-enable submit button
        submitButton.disabled = false;
        submitButton.innerHTML = 'Execute Action';
    });
}

/**
 * Utility functions
 */

function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">Pending</span>',
        'approved': '<span class="badge bg-success">Approved</span>',
        'rejected': '<span class="badge bg-danger">Rejected</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function formatDateTime(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showLoadingSpinner() {
    // Implementation for loading spinner
    console.log('Loading...');
}

function hideLoadingSpinner() {
    // Implementation for hiding loading spinner
    console.log('Loading complete');
}