<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';
require_once __DIR__ . '/SessionManager.php';
require_once __DIR__ . '/AuditTrailService.php';

/**
 * AuthenticationManager - Handles user authentication operations
 */
class AuthenticationManager {
    
    private static $maxLoginAttempts = 5;
    private static $lockoutDuration = 30; // minutes
    
    /**
     * Authenticate user login
     */
    public static function login($identifier, $password, $remember = false) {
        // Sanitize input
        $identifier = ValidationHelper::sanitize($identifier);
        
        // Find user by username or email
        $user = User::findByUsername($identifier);
        if (!$user) {
            $user = User::findByEmail($identifier);
        }
        
        if (!$user) {
            return [
                'success' => false,
                'error' => 'Invalid username/email or password',
                'field' => 'identifier'
            ];
        }
        
        // Check if account is locked
        if ($user->isLocked()) {
            $lockTime = new DateTime($user->locked_until);
            $now = new DateTime();
            $remainingTime = $lockTime->diff($now);
            
            return [
                'success' => false,
                'error' => 'Account is locked. Try again in ' . $remainingTime->format('%i minutes'),
                'field' => 'general'
            ];
        }
        
        // Verify password
        if (!$user->verifyPassword($password)) {
            // Log failed login attempt
            self::logAuthEvent($user->getId(), 'login_failed', $_SERVER['REMOTE_ADDR'] ?? '');
            
            // Increment login attempts
            $user->incrementLoginAttempts();
            
            $attemptsLeft = self::$maxLoginAttempts - $user->login_attempts;
            
            if ($attemptsLeft <= 0) {
                return [
                    'success' => false,
                    'error' => 'Account has been locked due to too many failed login attempts',
                    'field' => 'general'
                ];
            }
            
            return [
                'success' => false,
                'error' => "Invalid username/email or password. $attemptsLeft attempts remaining",
                'field' => 'password'
            ];
        }
        
        // Check account status
        if ($user->status === User::STATUS_SUSPENDED) {
            return [
                'success' => false,
                'error' => 'Your account has been suspended. Please contact support',
                'field' => 'general'
            ];
        }
        
        if ($user->status === User::STATUS_PENDING) {
            if (!$user->email_verified && defined('ENABLE_EMAIL_VERIFICATION') && ENABLE_EMAIL_VERIFICATION) {
                return [
                    'success' => false,
                    'error' => 'Please verify your email address before logging in',
                    'field' => 'general',
                    'action' => 'email_verification'
                ];
            }
        }
        
        // Reset login attempts on successful login
        $user->resetLoginAttempts();
        $user->updateLastLogin();
        
        // Create session
        SessionManager::startSession();
        SessionManager::setUserSession($user);
        
        // Handle remember me
        if ($remember) {
            self::setRememberMeCookie($user);
        }
        
        // Log successful login
        self::logAuthEvent($user->getId(), 'login_success', $_SERVER['REMOTE_ADDR'] ?? '');
        
        return [
            'success' => true,
            'user' => $user,
            'redirect' => self::getRedirectUrl($user->role)
        ];
    }
    
    /**
     * Register new user
     */
    public static function register($userData) {
        // Validate CSRF token
        if (!isset($userData['csrf_token']) || !ValidationHelper::validateCSRFToken($userData['csrf_token'])) {
            return [
                'success' => false,
                'errors' => ['csrf_token' => 'Invalid security token']
            ];
        }
        
        // Use UserService to create user
        require_once __DIR__ . '/UserService.php';
        $result = UserService::createUser($userData);
        
        if (!$result['success']) {
            return $result;
        }
        
        $user = $result['user'];
        
        // Send welcome email and verification email if needed
        if (defined('ENABLE_EMAIL_VERIFICATION') && ENABLE_EMAIL_VERIFICATION && !$user->email_verified) {
            self::sendEmailVerification($user);
        } else {
            self::sendWelcomeEmail($user);
        }
        
        // Log registration
        self::logAuthEvent($user->getId(), 'user_registered', $_SERVER['REMOTE_ADDR'] ?? '');
        
        return [
            'success' => true,
            'user' => $user,
            'message' => $user->email_verified ? 
                'Registration successful! You can now log in.' : 
                'Registration successful! Please check your email to verify your account.'
        ];
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        $userId = SessionManager::getUserId();
        
        if ($userId) {
            // Log logout
            self::logAuthEvent($userId, 'logout', $_SERVER['REMOTE_ADDR'] ?? '');
        }
        
        // Clear remember me cookie
        self::clearRememberMeCookie();
        
        // Destroy session
        SessionManager::destroySession();
        
        return ['success' => true];
    }
    
    /**
     * Verify email address
     */
    public static function verifyEmail($token) {
        if (empty($token)) {
            return [
                'success' => false,
                'error' => 'Invalid verification token'
            ];
        }
        
        $user = User::findByEmailVerificationToken($token);
        if (!$user) {
            return [
                'success' => false,
                'error' => 'Invalid or expired verification token'
            ];
        }
        
        // Verify email
        $user->verifyEmail();
        
        // Send welcome email
        self::sendWelcomeEmail($user);
        
        // Log email verification
        self::logAuthEvent($user->getId(), 'email_verified', $_SERVER['REMOTE_ADDR'] ?? '');
        
        return [
            'success' => true,
            'message' => 'Email verified successfully! You can now log in.'
        ];
    }
    
    /**
     * Request password reset
     */
    public static function requestPasswordReset($email) {
        $email = ValidationHelper::sanitize($email);
        
        // Validate email
        $emailError = ValidationHelper::validateEmail($email);
        if ($emailError) {
            return [
                'success' => false,
                'error' => $emailError
            ];
        }
        
        $user = User::findByEmail($email);
        if (!$user) {
            // Don't reveal if email exists or not
            return [
                'success' => true,
                'message' => 'If the email address exists, you will receive password reset instructions.'
            ];
        }
        
        // Generate reset token
        $user->generatePasswordResetToken();
        
        // Send reset email
        self::sendPasswordResetEmail($user);
        
        // Log password reset request
        self::logAuthEvent($user->getId(), 'password_reset_requested', $_SERVER['REMOTE_ADDR'] ?? '');
        
        return [
            'success' => true,
            'message' => 'If the email address exists, you will receive password reset instructions.'
        ];
    }
    
    /**
     * Reset password with token
     */
    public static function resetPassword($token, $newPassword, $confirmPassword) {
        if (empty($token)) {
            return [
                'success' => false,
                'errors' => ['token' => 'Invalid reset token']
            ];
        }
        
        // Validate passwords
        $errors = [];
        
        $passwordError = ValidationHelper::validatePassword($newPassword);
        if ($passwordError) {
            $errors['password'] = $passwordError;
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors['confirm_password'] = 'Passwords do not match';
        }
        
        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors
            ];
        }
        
        // Find user by token
        $user = User::findByPasswordResetToken($token);
        if (!$user) {
            return [
                'success' => false,
                'errors' => ['token' => 'Invalid or expired reset token']
            ];
        }
        
        // Reset password
        $user->resetPassword($newPassword);
        
        // Log password reset
        self::logAuthEvent($user->getId(), 'password_reset_completed', $_SERVER['REMOTE_ADDR'] ?? '');
        
        return [
            'success' => true,
            'message' => 'Password reset successfully! You can now log in with your new password.'
        ];
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        return SessionManager::isLoggedIn();
    }
    
    /**
     * Get current authenticated user
     */
    public static function getCurrentUser() {
        return SessionManager::getCurrentUser();
    }
    
    /**
     * Require authentication
     */
    public static function requireAuth($redirectUrl = null) {
        if (!self::isAuthenticated()) {
            $redirectUrl = $redirectUrl ?: '/login.php';
            header('Location: ' . $redirectUrl);
            exit();
        }
    }
    
    /**
     * Require specific role
     */
    public static function requireRole($role, $redirectUrl = null) {
        self::requireAuth();
        
        $user = self::getCurrentUser();
        if (!$user || $user->role !== $role) {
            $redirectUrl = $redirectUrl ?: '/unauthorized.php';
            header('Location: ' . $redirectUrl);
            exit();
        }
    }
    
    /**
     * Check if user has role
     */
    public static function hasRole($role) {
        $user = self::getCurrentUser();
        return $user && $user->role === $role;
    }
    
    /**
     * Check if user has any of the specified roles
     */
    public static function hasAnyRole($roles) {
        $user = self::getCurrentUser();
        return $user && in_array($user->role, $roles);
    }
    
    /**
     * Set remember me cookie
     */
    private static function setRememberMeCookie($user) {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days
        
        // Store token in database (you might want to create a remember_tokens table)
        // For now, we'll use a simple approach
        setcookie('remember_token', $token, $expiry, '/', '', true, true);
        setcookie('remember_user', $user->getId(), $expiry, '/', '', true, true);
    }
    
    /**
     * Clear remember me cookie
     */
    private static function clearRememberMeCookie() {
        setcookie('remember_token', '', time() - 3600, '/', '', true, true);
        setcookie('remember_user', '', time() - 3600, '/', '', true, true);
    }
    
    /**
     * Check remember me cookie
     */
    public static function checkRememberMe() {
        if (isset($_COOKIE['remember_token']) && isset($_COOKIE['remember_user'])) {
            $userId = $_COOKIE['remember_user'];
            $user = User::find($userId);
            
            if ($user && $user->isActive()) {
                SessionManager::startSession();
                SessionManager::setUserSession($user);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get redirect URL based on user role
     */
    private static function getRedirectUrl($role) {
        switch ($role) {
            case User::ROLE_SUPERADMIN:
                return '/superadmin/dashboard/';
            case User::ROLE_ADMIN:
                return '/admin/dashboard/';
            case User::ROLE_USER:
            default:
                return '/user/dashboard/';
        }
    }
    
    /**
     * Send email verification
     */
    private static function sendEmailVerification($user) {
        if (!class_exists('EmailService')) {
            return false;
        }
        
        $verificationUrl = BASE_URL . 'verify-email.php?token=' . $user->email_verification_token;
        
        $subject = 'Verify Your Email - ' . SITE_NAME;
        $message = "
            <h2>Email Verification Required</h2>
            <p>Dear {$user->first_name},</p>
            <p>Thank you for registering with " . SITE_NAME . ". Please click the link below to verify your email address:</p>
            <p><a href='{$verificationUrl}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Verify Email</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{$verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
            <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        ";
        
        try {
            $emailService = new EmailService();
            return $emailService->sendEmail($user->email, $subject, $message);
        } catch (Exception $e) {
            error_log("Email verification send failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send welcome email
     */
    private static function sendWelcomeEmail($user) {
        if (!class_exists('EmailService')) {
            return false;
        }
        
        $subject = 'Welcome to ' . SITE_NAME;
        $message = "
            <h2>Welcome to " . SITE_NAME . ", {$user->first_name}!</h2>
            <p>Your account has been successfully created and verified.</p>
            <p><strong>Account Details:</strong></p>
            <ul>
                <li>Username: {$user->username}</li>
                <li>Email: {$user->email}</li>
                <li>Registration Date: " . date('Y-m-d H:i:s') . "</li>
            </ul>
            <p>You can now log in and start using our platform.</p>
            <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        ";
        
        try {
            $emailService = new EmailService();
            return $emailService->sendEmail($user->email, $subject, $message);
        } catch (Exception $e) {
            error_log("Welcome email send failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send password reset email
     */
    private static function sendPasswordResetEmail($user) {
        if (!class_exists('EmailService')) {
            return false;
        }
        
        $resetUrl = BASE_URL . 'reset-password.php?token=' . $user->password_reset_token;
        
        $subject = 'Password Reset Request - ' . SITE_NAME;
        $message = "
            <h2>Password Reset Request</h2>
            <p>Dear {$user->first_name},</p>
            <p>You have requested to reset your password for your " . SITE_NAME . " account.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href='{$resetUrl}' style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p>{$resetUrl}</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
            <p>Best regards,<br>The " . SITE_NAME . " Team</p>
        ";
        
        try {
            $emailService = new EmailService();
            return $emailService->sendEmail($user->email, $subject, $message);
        } catch (Exception $e) {
            error_log("Password reset email send failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log authentication events
     */
    private static function logAuthEvent($userId, $event, $ipAddress) {
        try {
            $success = !str_contains($event, 'failed');
            AuditTrailService::logAuthEvent($event, $userId, $success, [
                'ip_address' => $ipAddress,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
        } catch (Exception $e) {
            error_log("Auth event logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Get login attempts for IP address
     */
    public static function getIPLoginAttempts($ipAddress, $timeWindow = 3600) {
        try {
            $db = getDB();
            $stmt = $db->prepare("
                SELECT COUNT(*) FROM audit_logs 
                WHERE ip_address = ? AND action = 'login_failed' 
                AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            
            $stmt->execute([$ipAddress, $timeWindow]);
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("IP login attempts check failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Check if IP is rate limited
     */
    public static function isIPRateLimited($ipAddress, $maxAttempts = 10, $timeWindow = 3600) {
        return self::getIPLoginAttempts($ipAddress, $timeWindow) >= $maxAttempts;
    }
}
?>