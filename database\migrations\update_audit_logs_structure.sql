-- Migration: Update audit_logs table structure for enhanced audit trail
-- Date: 2025-01-26
-- Description: Update audit_logs table to match AuditTrailService requirements

-- Check if the table exists and has old structure
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = 'audit_logs');

-- If table exists, update its structure
SET @sql = IF(@table_exists > 0,
    'ALTER TABLE audit_logs 
     DROP COLUMN IF EXISTS table_name,
     DROP COLUMN IF EXISTS record_id,
     DROP COLUMN IF EXISTS old_values,
     DROP COLUMN IF EXISTS new_values,
     DROP COLUMN IF EXISTS session_id,
     ADD COLUMN IF NOT EXISTS entity_type VARCHAR(50) NOT NULL AFTER action,
     ADD COLUMN IF NOT EXISTS entity_id VARCHAR(50) NOT NULL AFTER entity_type,
     ADD COLUMN IF NOT EXISTS changes JSON AFTER entity_id,
     ADD COLUMN IF NOT EXISTS additional_data JSON AFTER user_agent,
     DROP INDEX IF EXISTS idx_table_name,
     DROP INDEX IF EXISTS idx_record_id,
     ADD INDEX IF NOT EXISTS idx_entity_type (entity_type),
     ADD INDEX IF NOT EXISTS idx_entity_id (entity_id)',
    'SELECT "Table audit_logs does not exist, will be created by main schema"'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;