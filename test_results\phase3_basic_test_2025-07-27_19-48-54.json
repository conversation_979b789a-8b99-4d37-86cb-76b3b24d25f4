{"started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:54", "total_suites": 3, "total_tests": 10, "pass": 10, "fail": 0, "skip": 0, "error": 0, "suites": [{"name": "Error Handling Basic Tests", "type": "system", "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:54", "tests": [{"name": "Error Handler Initialization", "result": "pass", "message": "Test passed", "duration": 0.14, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Error handler should be initialized", "result": true}], "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:53"}, {"name": "Application Error Logging", "result": "pass", "message": "Test passed", "duration": 37.54, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Error ID should be generated", "result": true}, {"type": "assertStringContains", "needle": "ERR_", "haystack": "ERR_20250727_4c763c35", "message": "Error ID should have correct format", "result": true}], "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:53"}, {"name": "Database Error <PERSON>ling", "result": "pass", "message": "Test passed", "duration": 29.87, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Database error should return failure", "result": true}, {"type": "assertTrue", "condition": true, "message": "Error ID should be provided", "result": true}, {"type": "assertEqual", "expected": "Database operation failed. Please try again.", "actual": "Database operation failed. Please try again.", "message": "User-friendly error message should be provided", "result": true}], "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:53"}, {"name": "Authentication Error <PERSON>", "result": "pass", "message": "Test passed", "duration": 32.45, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Authentication error should return failure", "result": true}, {"type": "assertTrue", "condition": true, "message": "Error ID should be provided", "result": true}, {"type": "assertEqual", "expected": "Authentication failed. Please check your credentials.", "actual": "Authentication failed. Please check your credentials.", "message": "User-friendly error message should be provided", "result": true}], "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:53"}, {"name": "File Upload Error Handling", "result": "pass", "message": "Test passed", "duration": 221.15, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "File upload error should return failure", "result": true}, {"type": "assertTrue", "condition": true, "message": "Error ID should be provided", "result": true}, {"type": "assertStringContains", "needle": "exceeds", "haystack": "File exceeds maximum allowed size", "message": "Error message should describe the issue", "result": true}], "started_at": "2025-07-27 19:48:53", "completed_at": "2025-07-27 19:48:54"}, {"name": "Validation Error <PERSON>", "result": "pass", "message": "Test passed", "duration": 19.66, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Validation error should return failure", "result": true}, {"type": "assertTrue", "condition": true, "message": "Error ID should be provided", "result": true}, {"type": "assertEqual", "expected": {"email": "Invalid email format", "password": "Password too short"}, "actual": {"email": "Invalid email format", "password": "Password too short"}, "message": "Validation errors should be returned", "result": true}], "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54"}], "pass": 6, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "System Health Basic Tests", "type": "system", "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54", "tests": [{"name": "Health Service Initialization", "result": "pass", "message": "Test passed", "duration": 0.07, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Health service should be initialized", "result": true}], "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54"}, {"name": "Basic Health Check", "result": "pass", "message": "Test passed", "duration": 744.38, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Health check should return array", "result": true}, {"type": "assertTrue", "condition": true, "message": "Health check should return results", "result": true}, {"type": "assertTrue", "condition": true, "message": "memory check should have status", "result": true}, {"type": "assertTrue", "condition": true, "message": "file_system check should have status", "result": true}], "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54"}], "pass": 2, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "Testing Framework Tests", "type": "unit", "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54", "tests": [{"name": "Test Context Assertions", "result": "pass", "message": "Test passed", "duration": 0.21, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "True should be true", "result": true}, {"type": "assertTrue", "condition": true, "message": "False should be false", "result": true}, {"type": "assertEqual", "expected": 1, "actual": 1, "message": "Numbers should be equal", "result": true}, {"type": "assertEqual", "expected": "test", "actual": "test", "message": "Strings should be equal", "result": true}, {"type": "assertNotEqual", "expected": 1, "actual": 2, "message": "Different numbers should not be equal", "result": true}, {"type": "assertTrue", "condition": true, "message": "Null should be null", "result": true}, {"type": "assertTrue", "condition": true, "message": "String should not be null", "result": true}, {"type": "assertContains", "needle": "apple", "haystack": ["apple", "banana", "cherry"], "message": "Array should contain apple", "result": true}, {"type": "assertStringContains", "needle": "world", "haystack": "Hello world", "message": "String should contain substring", "result": true}], "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54"}, {"name": "Test Exception Handling", "result": "pass", "message": "Test passed", "duration": 0.08, "memory_usage": 0, "assertions": [{"type": "assertThrows", "expected_exception": "Exception", "actual_exception": "Exception", "message": "Should throw exception", "result": true}], "started_at": "2025-07-27 19:48:54", "completed_at": "2025-07-27 19:48:54"}], "pass": 2, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}]}