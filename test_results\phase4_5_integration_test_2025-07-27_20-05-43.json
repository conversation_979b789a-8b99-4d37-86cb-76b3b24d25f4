{"started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:43", "total_suites": 5, "total_tests": 23, "pass": 22, "fail": 1, "skip": 0, "error": 0, "suites": [{"name": "Landing Page Tests", "type": "integration", "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41", "tests": [{"name": "Landing Page File Exists", "result": "pass", "message": "Test passed", "duration": 0.36, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Landing page should exist", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "About Page Exists", "result": "pass", "message": "Test passed", "duration": 0.28, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "About page should exist", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "Contact Page Exists", "result": "pass", "message": "Test passed", "duration": 0.29, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Contact page should exist", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "Terms and Privacy Pages Exist", "result": "pass", "message": "Test passed", "duration": 0.37, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Terms page should exist", "result": true}, {"type": "assertTrue", "condition": true, "message": "Privacy page should exist", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "Landing Page CSS Exists", "result": "pass", "message": "Test passed", "duration": 0.35, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Landing page CSS should exist", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}], "pass": 5, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "Cache Service Tests", "type": "system", "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:43", "tests": [{"name": "Cache Service Initialization", "result": "pass", "message": "Test passed", "duration": 6.98, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Cache service should be initialized", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "<PERSON><PERSON> Set and Get", "result": "pass", "message": "Test passed", "duration": 6.14, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Cache set should succeed", "result": true}, {"type": "assertEqual", "expected": {"test": "data", "number": 123}, "actual": {"test": "data", "number": 123}, "message": "Retrieved data should match stored data", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:41"}, {"name": "Cache Expiration", "result": "pass", "message": "Test passed", "duration": 2016.89, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Cache should exist immediately", "result": true}, {"type": "assertTrue", "condition": true, "message": "Cache should be expired", "result": true}], "started_at": "2025-07-27 20:05:41", "completed_at": "2025-07-27 20:05:43"}, {"name": "Cache Namespaces", "result": "fail", "message": "Namespace 1 data should be correct (Expected: 'data1', Actual: NULL); Namespace 2 data should be correct (Expected: 'data2', Actual: NULL)", "duration": 77.94, "memory_usage": 0, "assertions": [{"type": "assertEqual", "expected": "data1", "actual": null, "message": "Namespace 1 data should be correct", "result": false}, {"type": "assertEqual", "expected": "data2", "actual": null, "message": "Namespace 2 data should be correct", "result": false}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "<PERSON><PERSON>", "result": "pass", "message": "Test passed", "duration": 2.07, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Stats should be an array", "result": true}, {"type": "assertTrue", "condition": true, "message": "Stats should include total files", "result": true}, {"type": "assertTrue", "condition": true, "message": "Stats should include namespaces", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}], "pass": 4, "fail": 1, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "Database Optimization Tests", "type": "system", "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43", "tests": [{"name": "Database Optimization Service Initialization", "result": "pass", "message": "Test passed", "duration": 0.15, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Database optimization service should be initialized", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Query Performance Logging", "result": "pass", "message": "Test passed", "duration": 10.27, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Query should return array result", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should have logged queries", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Database Size Information", "result": "pass", "message": "Test passed", "duration": 283.85, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Should return total size", "result": true}, {"type": "assertTrue", "condition": true, "message": "Size should be non-negative", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Table Performance Analysis", "result": "pass", "message": "Test passed", "duration": 1.86, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Analysis should be an array", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should analyze users table", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Index Optimization", "result": "pass", "message": "Test passed", "duration": 5.95, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Should report created indexes", "result": true}, {"type": "assertTrue", "condition": true, "message": "Created count should be non-negative", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}], "pass": 5, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "Backup Service Tests", "type": "system", "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43", "tests": [{"name": "Backup Service Initialization", "result": "pass", "message": "Test passed", "duration": 5.82, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Backup service should be initialized", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Database Backup Creation", "result": "pass", "message": "Test passed", "duration": 219.16, "memory_usage": 2097152, "assertions": [{"type": "assertTrue", "condition": true, "message": "Database backup should succeed", "result": true}, {"type": "assertTrue", "condition": true, "message": "Backup filename should be provided", "result": true}, {"type": "assertTrue", "condition": true, "message": "Backup file should have content", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Backup Listing", "result": "pass", "message": "Test passed", "duration": 1.13, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Backup list should be an array", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should list database backups", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should have at least one database backup", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Backup Statistics", "result": "pass", "message": "Test passed", "duration": 0.62, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Stats should be an array", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should include total backups", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should have backups", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Automated Backup Scheduling", "result": "pass", "message": "Test passed", "duration": 2.54, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Backup scheduling should succeed", "result": true}, {"type": "assertTrue", "condition": true, "message": "Should return configuration", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}], "pass": 5, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}, {"name": "System Integration Tests", "type": "integration", "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43", "tests": [{"name": "Cache and Database Integration", "result": "pass", "message": "Test passed", "duration": 4.93, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Query result should be cached", "result": true}, {"type": "assertEqual", "expected": [{"count": 4}], "actual": [{"count": 4}], "message": "Cached result should match database result", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "System Performance Monitoring", "result": "pass", "message": "Test passed", "duration": 2.54, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Cache stats should be available", "result": true}, {"type": "assertTrue", "condition": true, "message": "Query stats should be available", "result": true}, {"type": "assertTrue", "condition": true, "message": "Backup stats should be available", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}, {"name": "Error <PERSON>", "result": "pass", "message": "Test passed", "duration": 1.68, "memory_usage": 0, "assertions": [{"type": "assertTrue", "condition": true, "message": "Non-existent cache key should return null", "result": true}, {"type": "assertTrue", "condition": true, "message": "Cache cleanup should return non-negative count", "result": true}], "started_at": "2025-07-27 20:05:43", "completed_at": "2025-07-27 20:05:43"}], "pass": 3, "fail": 0, "skip": 0, "error": 0, "setup_error": null, "teardown_error": null}]}