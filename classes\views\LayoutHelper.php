<?php

require_once __DIR__ . '/../services/ThemeManager.php';
require_once __DIR__ . '/../../includes/ui_components.php';

class LayoutHelper {
    private $themeManager;
    private $user;
    private $role;
    
    public function __construct($user = null) {
        $this->themeManager = ThemeManager::getInstance();
        $this->user = $user ?: getCurrentUser();
        $this->role = $this->user['role'] ?? 'guest';
    }
    
    /**
     * Render layout for specific role
     */
    public function renderLayout($pageTitle, $content, $additionalCSS = '', $additionalJS = '') {
        $layoutFile = $this->getLayoutFile();
        
        if (!file_exists($layoutFile)) {
            throw new Exception("Layout file not found: {$layoutFile}");
        }
        
        // Set variables for layout template
        $config = $this->themeManager->getLayoutConfig($this->role);
        $themeColors = $config['theme_colors'];
        $siteName = $config['branding']['site_name'];
        $siteLogo = $config['branding']['site_logo'];
        $user = $this->user;
        
        // Start output buffering
        ob_start();
        include $layoutFile;
        return ob_get_clean();
    }
    
    /**
     * Get appropriate layout file based on user role
     */
    private function getLayoutFile() {
        $layoutsDir = __DIR__ . '/../../layouts/';
        
        switch ($this->role) {
            case 'user':
                return $layoutsDir . 'user_layout.php';
            case 'admin':
                return $layoutsDir . 'admin_layout.php';
            case 'superadmin':
                return $layoutsDir . 'superadmin_layout.php';
            default:
                return $layoutsDir . 'guest_layout.php';
        }
    }
    
    /**
     * Generate responsive navigation menu
     */
    public function generateNavigation() {
        $navItems = $this->getNavigationItems();
        $currentPath = $_SERVER['REQUEST_URI'];
        
        $html = '<ul class="nav flex-column">';
        
        foreach ($navItems as $item) {
            $activeClass = $this->isActiveNavItem($item['path'], $currentPath) ? 'active' : '';
            $badge = isset($item['badge']) ? "<span class=\"badge bg-{$item['badge']['color']} ms-2\">{$item['badge']['text']}</span>" : '';
            
            $html .= "
            <li class=\"nav-item\">
                <a class=\"nav-link {$activeClass}\" href=\"{$item['path']}\">
                    <i class=\"{$item['icon']} me-2\"></i>
                    {$item['label']}
                    {$badge}
                </a>
            </li>";
        }
        
        $html .= '</ul>';
        return $html;
    }
    
    /**
     * Get navigation items based on user role
     */
    private function getNavigationItems() {
        switch ($this->role) {
            case 'user':
                return [
                    ['path' => '/user/dashboard/', 'label' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                    ['path' => '/user/deposit/', 'label' => 'Deposit', 'icon' => 'fas fa-plus-circle'],
                    ['path' => '/user/withdraw/', 'label' => 'Withdraw', 'icon' => 'fas fa-minus-circle'],
                    ['path' => '/user/transactions/', 'label' => 'Transactions', 'icon' => 'fas fa-history'],
                    ['path' => '/user/profile/', 'label' => 'Profile', 'icon' => 'fas fa-user'],
                    ['path' => '/user/support/', 'label' => 'Support', 'icon' => 'fas fa-headset']
                ];
                
            case 'admin':
                return [
                    ['path' => '/admin/dashboard/', 'label' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                    ['path' => '/admin/users/', 'label' => 'Users', 'icon' => 'fas fa-users'],
                    ['path' => '/admin/deposits/', 'label' => 'Deposits', 'icon' => 'fas fa-plus-circle'],
                    ['path' => '/admin/withdrawals/', 'label' => 'Withdrawals', 'icon' => 'fas fa-minus-circle'],
                    ['path' => '/admin/plans/', 'label' => 'Trading Plans', 'icon' => 'fas fa-chart-line'],
                    ['path' => '/admin/transactions/', 'label' => 'Transactions', 'icon' => 'fas fa-history'],
                    ['path' => '/admin/support/', 'label' => 'Support', 'icon' => 'fas fa-headset'],
                    ['path' => '/admin/reports/', 'label' => 'Reports', 'icon' => 'fas fa-chart-bar']
                ];
                
            case 'superadmin':
                return [
                    ['path' => '/superadmin/dashboard/', 'label' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                    ['path' => '/superadmin/settings/', 'label' => 'System Settings', 'icon' => 'fas fa-cogs'],
                    ['path' => '/superadmin/admins/', 'label' => 'Admin Management', 'icon' => 'fas fa-user-shield'],
                    ['path' => '/superadmin/email-templates/', 'label' => 'Email Templates', 'icon' => 'fas fa-envelope-open-text'],
                    ['path' => '/superadmin/audit/', 'label' => 'Audit Logs', 'icon' => 'fas fa-clipboard-list'],
                    ['path' => '/superadmin/backup/', 'label' => 'Database Backup', 'icon' => 'fas fa-database'],
                    ['path' => '/superadmin/maintenance/', 'label' => 'Maintenance', 'icon' => 'fas fa-tools'],
                    ['path' => '/superadmin/security/', 'label' => 'Security Center', 'icon' => 'fas fa-shield-alt']
                ];
                
            default:
                return [];
        }
    }
    
    /**
     * Check if navigation item is active
     */
    private function isActiveNavItem($itemPath, $currentPath) {
        // Exact match
        if ($itemPath === $currentPath) {
            return true;
        }
        
        // Check if current path starts with item path (for subdirectories)
        if (strlen($itemPath) > 1 && strpos($currentPath, $itemPath) === 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Generate breadcrumb navigation
     */
    public function generateBreadcrumb($items) {
        return UIComponents::breadcrumb($items);
    }
    
    /**
     * Generate page header with title and actions
     */
    public function generatePageHeader($title, $subtitle = '', $actions = []) {
        $html = '<div class="d-flex justify-content-between align-items-center mb-4">';
        $html .= '<div>';
        $html .= "<h1 class=\"h3 mb-0\">{$title}</h1>";
        
        if ($subtitle) {
            $html .= "<p class=\"text-muted mb-0\">{$subtitle}</p>";
        }
        
        $html .= '</div>';
        
        if (!empty($actions)) {
            $html .= '<div class="d-flex gap-2">';
            foreach ($actions as $action) {
                $html .= "<a href=\"{$action['url']}\" class=\"btn btn-{$action['color']}\">";
                $html .= "<i class=\"{$action['icon']} me-1\"></i> {$action['label']}";
                $html .= "</a>";
            }
            $html .= '</div>';
        }
        
        $html .= '</div>';
        return $html;
    }
    
    /**
     * Generate responsive stats cards
     */
    public function generateStatsCards($stats) {
        $html = '<div class="row">';
        
        foreach ($stats as $stat) {
            $html .= UIComponents::statsCard(
                $stat['title'],
                $stat['value'],
                $stat['icon'],
                $stat['color'] ?? 'primary',
                $stat['change'] ?? null
            );
        }
        
        $html .= '</div>';
        return $html;
    }
    
    /**
     * Generate mobile-friendly data table
     */
    public function generateResponsiveTable($id, $headers, $data, $actions = []) {
        // Mobile card view for small screens
        $mobileHTML = '<div class="d-md-none">';
        foreach ($data as $index => $row) {
            $mobileHTML .= '<div class="card mb-3">';
            $mobileHTML .= '<div class="card-body">';
            
            foreach ($headers as $i => $header) {
                if (isset($row[$i])) {
                    $mobileHTML .= "<div class=\"d-flex justify-content-between mb-2\">";
                    $mobileHTML .= "<strong>{$header}:</strong>";
                    $mobileHTML .= "<span>" . htmlspecialchars($row[$i]) . "</span>";
                    $mobileHTML .= "</div>";
                }
            }
            
            if (!empty($actions)) {
                $mobileHTML .= '<div class="mt-3 d-flex gap-2">';
                foreach ($actions as $action) {
                    $onclick = str_replace('{id}', $index, $action['onclick']);
                    $mobileHTML .= "<button class=\"btn btn-sm btn-{$action['color']}\" onclick=\"{$onclick}\">";
                    $mobileHTML .= "<i class=\"{$action['icon']}\"></i> {$action['label']}";
                    $mobileHTML .= "</button>";
                }
                $mobileHTML .= '</div>';
            }
            
            $mobileHTML .= '</div>';
            $mobileHTML .= '</div>';
        }
        $mobileHTML .= '</div>';
        
        // Desktop table view
        $desktopHTML = '<div class="d-none d-md-block">';
        $desktopHTML .= UIComponents::dataTable($id, $headers, $data, $actions);
        $desktopHTML .= '</div>';
        
        return $mobileHTML . $desktopHTML;
    }
    
    /**
     * Generate theme CSS variables
     */
    public function generateThemeCSS() {
        return $this->themeManager->generateCSSVariables();
    }
    
    /**
     * Generate responsive CSS
     */
    public function generateResponsiveCSS() {
        return $this->themeManager->getResponsiveCSS();
    }
    
    /**
     * Get current theme configuration
     */
    public function getThemeConfig() {
        return $this->themeManager->getLayoutConfig($this->role);
    }
    
    /**
     * Generate alert messages
     */
    public function generateAlerts($messages) {
        $html = '';
        
        if (is_array($messages)) {
            foreach ($messages as $type => $messageList) {
                if (is_array($messageList)) {
                    foreach ($messageList as $message) {
                        $html .= UIComponents::alert($message, $type);
                    }
                } else {
                    $html .= UIComponents::alert($messageList, $type);
                }
            }
        } else {
            $html .= UIComponents::alert($messages);
        }
        
        return $html;
    }
    
    /**
     * Generate loading state
     */
    public function generateLoadingState($text = 'Loading...') {
        return UIComponents::loadingSpinner('md', $text);
    }
}