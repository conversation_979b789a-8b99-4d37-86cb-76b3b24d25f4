<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/../models/SystemSetting.php';

/**
 * Super Admin System Settings View
 * Handles rendering of system configuration interface
 */
class SuperAdminSystemSettingsView extends BaseView {
    
    private $settingsData;
    
    public function __construct($settingsData = []) {
        parent::__construct('System Settings');
        $this->settingsData = $settingsData;
    }
    
    /**
     * Render the complete system settings interface
     */
    protected function renderContent() {
        $output = '<style>
        .logo-preview-container {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .logo-preview-item {
            flex: 1;
        }
        .preview-label {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 5px;
            color: #6c757d;
        }
        .logo-preview {
            text-align: center;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .logo-preview img {
            max-width: 100%;
            height: auto;
        }
        </style>';

        $output .= '<div class="system-settings-container">';
        
        // Settings navigation tabs
        $output .= $this->renderSettingsTabs();
        
        // Tab content
        $output .= '<div class="tab-content mt-4">';
        $output .= $this->renderGeneralSettings($this->settingsData['general'] ?? []);
        $output .= $this->renderFinancialSettings($this->settingsData['financial'] ?? []);
        $output .= $this->renderSecuritySettings($this->settingsData['security'] ?? []);
        $output .= $this->renderAppearanceSettings($this->settingsData['appearance'] ?? []);
        $output .= $this->renderEmailSettings($this->settingsData['notifications'] ?? []);
        $output .= $this->renderEmailTemplatesSettings();
        $output .= $this->renderSystemSettings($this->settingsData['system'] ?? []);
        $output .= '</div>';
        
        $output .= '</div>';
        
        return $output;
    }
    
    /**
     * Render settings navigation tabs
     */
    private function renderSettingsTabs() {
        return '
        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>General
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab">
                    <i class="fas fa-dollar-sign me-2"></i>Financial
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                    <i class="fas fa-shield-alt me-2"></i>Security
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                    <i class="fas fa-palette me-2"></i>Appearance
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                    <i class="fas fa-envelope me-2"></i>Email & SMTP
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="email-templates-tab" data-bs-toggle="tab" data-bs-target="#email-templates" type="button" role="tab">
                    <i class="fas fa-file-alt me-2"></i>Email Templates
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                    <i class="fas fa-server me-2"></i>System
                </button>
            </li>
        </ul>';
    }
    
    /**
     * Render general settings tab
     */
    private function renderGeneralSettings($settings) {
        $siteName = $this->getSettingValue($settings, 'site_name', 'Coinage Trading');
        $siteCurrency = $this->getSettingValue($settings, 'site_currency', 'USD');
        $currencySymbol = $this->getSettingValue($settings, 'currency_symbol', '$');
        $baseUrl = $this->getSettingValue($settings, 'base_url', '');
        $contactEmail = $this->getSettingValue($settings, 'contact_email', '');
        $contactPhone = $this->getSettingValue($settings, 'contact_phone', '');
        $companyAddress = $this->getSettingValue($settings, 'company_address', '');
        
        return '
        <div class="tab-pane fade show active" id="general" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>General Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="generalSettingsForm" method="POST">
                        <input type="hidden" name="action" value="update_general">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">Site Name</label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" 
                                           value="' . htmlspecialchars($siteName) . '" required>
                                    <div class="form-text">The name of your trading platform</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="site_currency" class="form-label">Default Currency</label>
                                    <select class="form-select" id="site_currency" name="site_currency" required>
                                        <option value="USD"' . ($siteCurrency === 'USD' ? ' selected' : '') . '>USD - US Dollar</option>
                                        <option value="EUR"' . ($siteCurrency === 'EUR' ? ' selected' : '') . '>EUR - Euro</option>
                                        <option value="GBP"' . ($siteCurrency === 'GBP' ? ' selected' : '') . '>GBP - British Pound</option>
                                        <option value="BTC"' . ($siteCurrency === 'BTC' ? ' selected' : '') . '>BTC - Bitcoin</option>
                                        <option value="ETH"' . ($siteCurrency === 'ETH' ? ' selected' : '') . '>ETH - Ethereum</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                    <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                                           value="' . htmlspecialchars($currencySymbol) . '" maxlength="5" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="base_url" class="form-label">Base URL</label>
                            <input type="url" class="form-control" id="base_url" name="base_url" 
                                   value="' . htmlspecialchars($baseUrl) . '" required>
                            <div class="form-text">The base URL of your platform (e.g., https://yoursite.com/)</div>
                        </div>
                        
                        <hr class="my-4">
                        <h6 class="mb-3">Contact Information</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">Contact Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                           value="' . htmlspecialchars($contactEmail) . '">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">Contact Phone</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                           value="' . htmlspecialchars($contactPhone) . '">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company_address" class="form-label">Company Address</label>
                            <textarea class="form-control" id="company_address" name="company_address" rows="3">' . htmlspecialchars($companyAddress) . '</textarea>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save General Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Render financial settings tab
     */
    private function renderFinancialSettings($settings) {
        $registrationBonus = $this->getSettingValue($settings, 'registration_bonus', 0);
        $depositBonusPercent = $this->getSettingValue($settings, 'deposit_bonus_percent', 10);
        $minWithdrawal = $this->getSettingValue($settings, 'min_withdrawal_amount', 50);
        $maxWithdrawal = $this->getSettingValue($settings, 'max_withdrawal_amount', 10000);
        $withdrawalFee = $this->getSettingValue($settings, 'withdrawal_fee_percent', 2);
        
        return '
        <div class="tab-pane fade" id="financial" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>Financial Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="financialSettingsForm" method="POST">
                        <input type="hidden" name="action" value="update_financial">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registration_bonus" class="form-label">Registration Bonus</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="registration_bonus" name="registration_bonus" 
                                               value="' . $registrationBonus . '" min="0" step="0.01">
                                    </div>
                                    <div class="form-text">Bonus amount given to new users upon registration</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deposit_bonus_percent" class="form-label">Deposit Bonus Percentage</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="deposit_bonus_percent" name="deposit_bonus_percent" 
                                               value="' . $depositBonusPercent . '" min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                    <div class="form-text">Percentage bonus applied to deposits</div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        <h6 class="mb-3">Withdrawal Settings</h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="min_withdrawal_amount" class="form-label">Minimum Withdrawal</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="min_withdrawal_amount" name="min_withdrawal_amount" 
                                               value="' . $minWithdrawal . '" min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_withdrawal_amount" class="form-label">Maximum Withdrawal</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="max_withdrawal_amount" name="max_withdrawal_amount" 
                                               value="' . $maxWithdrawal . '" min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="withdrawal_fee_percent" class="form-label">Withdrawal Fee</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="withdrawal_fee_percent" name="withdrawal_fee_percent" 
                                               value="' . $withdrawalFee . '" min="0" max="100" step="0.1">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Financial Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Render security settings tab
     */
    private function renderSecuritySettings($settings) {
        $emailVerification = $this->getSettingValue($settings, 'email_verification_required', true);
        $twoFaEnforcement = $this->getSettingValue($settings, 'two_fa_enforcement', false);
        $kycRequired = $this->getSettingValue($settings, 'kyc_verification_required', false);
        $maxLoginAttempts = $this->getSettingValue($settings, 'max_login_attempts', 5);
        $lockoutMinutes = $this->getSettingValue($settings, 'account_lockout_minutes', 30);
        
        return '
        <div class="tab-pane fade" id="security" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Security Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="securitySettingsForm" method="POST">
                        <input type="hidden" name="action" value="update_security">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <div class="mb-4">
                            <h6 class="mb-3">Authentication Requirements</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_verification_required" 
                                       name="email_verification_required"' . ($emailVerification ? ' checked' : '') . '>
                                <label class="form-check-label" for="email_verification_required">
                                    <strong>Require Email Verification</strong>
                                    <div class="form-text">Users must verify their email before accessing the platform</div>
                                </label>
                            </div>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="two_fa_enforcement" 
                                       name="two_fa_enforcement"' . ($twoFaEnforcement ? ' checked' : '') . '>
                                <label class="form-check-label" for="two_fa_enforcement">
                                    <strong>Enforce Two-Factor Authentication</strong>
                                    <div class="form-text">Require all users to enable 2FA for enhanced security</div>
                                </label>
                            </div>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="kyc_verification_required" 
                                       name="kyc_verification_required"' . ($kycRequired ? ' checked' : '') . '>
                                <label class="form-check-label" for="kyc_verification_required">
                                    <strong>Require KYC Verification</strong>
                                    <div class="form-text">Users must complete identity verification for withdrawals</div>
                                </label>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        <h6 class="mb-3">Login Security</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_login_attempts" class="form-label">Maximum Login Attempts</label>
                                    <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                                           value="' . $maxLoginAttempts . '" min="1" max="20" required>
                                    <div class="form-text">Number of failed attempts before account lockout</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="account_lockout_minutes" class="form-label">Account Lockout Duration</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="account_lockout_minutes" name="account_lockout_minutes" 
                                               value="' . $lockoutMinutes . '" min="1" max="1440" required>
                                        <span class="input-group-text">minutes</span>
                                    </div>
                                    <div class="form-text">How long accounts remain locked after failed attempts</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Security Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Render appearance settings tab
     */
    private function renderAppearanceSettings($settings) {
        $primaryColor = $this->getSettingValue($settings, 'primary_color', '#007bff');
        $secondaryColor = $this->getSettingValue($settings, 'secondary_color', '#6c757d');
        $logoUrl = $this->getSettingValue($settings, 'logo_url', '');
        
        return '
        <div class="tab-pane fade" id="appearance" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-palette me-2"></i>Appearance Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="appearanceSettingsForm" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_appearance">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <div class="mb-4">
                            <h6 class="mb-3">Brand Colors</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="primary_color" class="form-label">Primary Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="primary_color" 
                                                   name="primary_color" value="' . $primaryColor . '" title="Choose primary color">
                                            <input type="text" class="form-control" value="' . $primaryColor . '" readonly>
                                        </div>
                                        <div class="form-text">Main brand color used for buttons and highlights</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="secondary_color" class="form-label">Secondary Color</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" id="secondary_color" 
                                                   name="secondary_color" value="' . $secondaryColor . '" title="Choose secondary color">
                                            <input type="text" class="form-control" value="' . $secondaryColor . '" readonly>
                                        </div>
                                        <div class="form-text">Secondary color for accents and borders</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="color-preview-container mt-3">
                                <div class="color-preview">
                                    <div class="preview-buttons">
                                        <button type="button" class="btn btn-primary me-2" id="primary-preview">Primary Button</button>
                                        <button type="button" class="btn btn-secondary" id="secondary-preview">Secondary Button</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        <h6 class="mb-3">Logo & Branding</h6>

                        <!-- Main Logo -->
                        <div class="mb-4">
                            <label for="logo_file" class="form-label">Main Platform Logo</label>
                            <input type="file" class="form-control" id="logo_file" name="logo_file" accept="image/*">
                            <div class="form-text">Upload main logo (PNG, JPG, SVG recommended. Max size: 2MB)</div>

                            ' . ($logoUrl ? '
                            <div class="current-logo mt-3">
                                <label class="form-label">Current Logo Preview:</label>
                                <div class="logo-preview-container">
                                    <div class="logo-preview-item">
                                        <div class="preview-label">Light Background</div>
                                        <div class="logo-preview bg-light p-3 border rounded">
                                            <img src="' . htmlspecialchars($logoUrl) . '" alt="Current Logo" style="max-height: 60px;">
                                        </div>
                                    </div>
                                    <div class="logo-preview-item">
                                        <div class="preview-label">Dark Background</div>
                                        <div class="logo-preview bg-dark p-3 border rounded">
                                            <img src="' . htmlspecialchars($logoUrl) . '" alt="Current Logo" style="max-height: 60px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            ' : '') . '
                        </div>

                        <!-- Logo Dark Variant -->
                        <div class="mb-4">
                            <label for="logo_dark_file" class="form-label">Logo Dark Variant <span class="text-muted">(Optional)</span></label>
                            <input type="file" class="form-control" id="logo_dark_file" name="logo_dark_file" accept="image/*">
                            <div class="form-text">Upload dark version of logo for light backgrounds (PNG, JPG, SVG recommended)</div>

                            ' . ($this->getSettingValue($settings, 'logo_dark_url') ? '
                            <div class="current-logo mt-3">
                                <label class="form-label">Current Dark Logo:</label>
                                <div class="logo-preview bg-light p-3 border rounded d-inline-block">
                                    <img src="' . htmlspecialchars($this->getSettingValue($settings, 'logo_dark_url')) . '" alt="Current Dark Logo" style="max-height: 60px;">
                                </div>
                            </div>
                            ' : '') . '
                        </div>

                        <!-- Logo Light Variant -->
                        <div class="mb-4">
                            <label for="logo_light_file" class="form-label">Logo Light Variant <span class="text-muted">(Optional)</span></label>
                            <input type="file" class="form-control" id="logo_light_file" name="logo_light_file" accept="image/*">
                            <div class="form-text">Upload light version of logo for dark backgrounds (PNG, JPG, SVG recommended)</div>

                            ' . ($this->getSettingValue($settings, 'logo_light_url') ? '
                            <div class="current-logo mt-3">
                                <label class="form-label">Current Light Logo:</label>
                                <div class="logo-preview bg-dark p-3 border rounded d-inline-block">
                                    <img src="' . htmlspecialchars($this->getSettingValue($settings, 'logo_light_url')) . '" alt="Current Light Logo" style="max-height: 60px;">
                                </div>
                            </div>
                            ' : '') . '
                        </div>

                        <!-- Favicon -->
                        <div class="mb-4">
                            <label for="favicon_file" class="form-label">Favicon <span class="text-muted">(Optional)</span></label>
                            <input type="file" class="form-control" id="favicon_file" name="favicon_file" accept="image/x-icon,image/png">
                            <div class="form-text">Upload favicon (ICO or PNG, 16x16 or 32x32 pixels recommended)</div>

                            ' . ($this->getSettingValue($settings, 'favicon_url') ? '
                            <div class="current-logo mt-3">
                                <label class="form-label">Current Favicon:</label>
                                <div class="logo-preview bg-light p-2 border rounded d-inline-block">
                                    <img src="' . htmlspecialchars($this->getSettingValue($settings, 'favicon_url')) . '" alt="Current Favicon" style="max-height: 32px;">
                                </div>
                            </div>
                            ' : '') . '
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Appearance Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Render email settings tab
     */
    private function renderEmailSettings($settings) {
        // Get current SMTP settings from config
        $smtpHost = defined('SMTP_HOST') ? SMTP_HOST : '';
        $smtpPort = defined('SMTP_PORT') ? SMTP_PORT : 587;
        $smtpUsername = defined('SMTP_USERNAME') ? SMTP_USERNAME : '';
        $smtpFromName = defined('SMTP_FROM_NAME') ? SMTP_FROM_NAME : '';
        $smtpFromEmail = defined('SMTP_FROM_EMAIL') ? SMTP_FROM_EMAIL : '';
        
        $emailNotifications = $this->getSettingValue($settings, 'email_notifications', true);
        
        return '
        <div class="tab-pane fade" id="email" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>Email & SMTP Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> SMTP settings are configured in the config.php file. 
                        Use the test button below to verify your current configuration.
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="mb-3">Current SMTP Configuration</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">SMTP Host</label>
                                    <input type="text" class="form-control" value="' . htmlspecialchars($smtpHost) . '" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">SMTP Port</label>
                                    <input type="text" class="form-control" value="' . $smtpPort . '" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">SMTP Username</label>
                                    <input type="text" class="form-control" value="' . htmlspecialchars($smtpUsername) . '" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">From Email</label>
                                    <input type="text" class="form-control" value="' . htmlspecialchars($smtpFromEmail) . '" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="testSMTPConnection()">
                                <i class="fas fa-paper-plane me-1"></i>Test SMTP Connection
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="sendTestEmail()">
                                <i class="fas fa-envelope me-1"></i>Send Test Email
                            </button>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <form id="emailSettingsForm" method="POST">
                        <input type="hidden" name="action" value="update_features">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <h6 class="mb-3">Email Notification Settings</h6>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="email_notifications" 
                                   name="email_notifications"' . ($emailNotifications ? ' checked' : '') . '>
                            <label class="form-check-label" for="email_notifications">
                                <strong>Enable Email Notifications</strong>
                                <div class="form-text">Send automated emails for registration, deposits, withdrawals, etc.</div>
                            </label>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save Email Settings
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="email-templates-section">
                        <h6 class="mb-3">Email Templates</h6>
                        <p class="text-muted mb-3">Manage email templates for automated notifications</p>
                        
                        <div class="d-flex gap-2">
                            <a href="/superadmin/email-templates/" class="btn btn-outline-primary">
                                <i class="fas fa-edit me-1"></i>Manage Email Templates
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Render system settings tab
     */
    private function renderSystemSettings($settings) {
        $maintenanceMode = $this->getSettingValue($settings, 'maintenance_mode', false);
        $debugMode = $this->getSettingValue($settings, 'debug_mode', false);
        $sessionTimeout = $this->getSettingValue($settings, 'session_timeout_minutes', 60);
        
        return '
        <div class="tab-pane fade" id="system" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>System Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="systemSettingsForm" method="POST">
                        <input type="hidden" name="action" value="update_features">
                        <input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">
                        
                        <div class="mb-4">
                            <h6 class="mb-3">System Status</h6>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" 
                                       name="maintenance_mode"' . ($maintenanceMode ? ' checked' : '') . '>
                                <label class="form-check-label" for="maintenance_mode">
                                    <strong>Maintenance Mode</strong>
                                    <div class="form-text text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        When enabled, only super admins can access the platform
                                    </div>
                                </label>
                            </div>
                            
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="debug_mode" 
                                       name="debug_mode"' . ($debugMode ? ' checked' : '') . '>
                                <label class="form-check-label" for="debug_mode">
                                    <strong>Debug Mode</strong>
                                    <div class="form-text text-warning">
                                        <i class="fas fa-bug me-1"></i>
                                        Enable detailed error reporting (disable in production)
                                    </div>
                                </label>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        <h6 class="mb-3">Session Management</h6>
                        
                        <div class="mb-3">
                            <label for="session_timeout_minutes" class="form-label">Session Timeout</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="session_timeout_minutes" 
                                       name="session_timeout_minutes" value="' . $sessionTimeout . '" min="5" max="1440" required>
                                <span class="input-group-text">minutes</span>
                            </div>
                            <div class="form-text">How long user sessions remain active without activity</div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Save System Settings
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="system-info-section">
                        <h6 class="mb-3">System Information</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">PHP Version</div>
                                    <div class="info-value">' . PHP_VERSION . '</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-card">
                                    <div class="info-label">Server Software</div>
                                    <div class="info-value">' . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . '</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2 mt-3">
                            <a href="/superadmin/system/" class="btn btn-outline-primary">
                                <i class="fas fa-chart-line me-1"></i>System Health
                            </a>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearSystemCache()">
                                <i class="fas fa-broom me-1"></i>Clear Cache
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    /**
     * Get setting value from settings array
     */
    private function getSettingValue($settings, $key, $default = '') {
        foreach ($settings as $setting) {
            if ($setting->setting_key === $key) {
                return $setting->getTypedValue();
            }
        }
        return $default;
    }
    
    /**
     * Handle logo file upload
     */
    public function handleLogoUpload($file) {
        try {
            // Validate file
            $allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/gif'];
            $maxSize = 2 * 1024 * 1024; // 2MB
            
            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('Invalid file type. Only JPG, PNG, SVG, and GIF are allowed.');
            }
            
            if ($file['size'] > $maxSize) {
                throw new Exception('File size too large. Maximum size is 2MB.');
            }
            
            // Create upload directory if it doesn't exist
            $uploadDir = __DIR__ . '/../../assets/uploads/logos/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'logo_' . time() . '_' . uniqid() . '.' . $extension;
            $uploadPath = $uploadDir . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                return '/assets/uploads/logos/' . $filename;
            } else {
                throw new Exception('Failed to upload file.');
            }
            
        } catch (Exception $e) {
            error_log('Logo upload error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle favicon file upload
     */
    public function handleFaviconUpload($file) {
        try {
            // Validate file
            $allowedTypes = ['image/x-icon', 'image/png', 'image/vnd.microsoft.icon'];
            $maxSize = 1 * 1024 * 1024; // 1MB

            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('Invalid file type. Only ICO and PNG files are allowed for favicon.');
            }

            if ($file['size'] > $maxSize) {
                throw new Exception('File size too large. Maximum size is 1MB.');
            }

            // Create upload directory if it doesn't exist
            $uploadDir = __DIR__ . '/../../assets/uploads/favicons/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'favicon_' . time() . '_' . uniqid() . '.' . $extension;
            $uploadPath = $uploadDir . $filename;

            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                return '/assets/uploads/favicons/' . $filename;
            } else {
                throw new Exception('Failed to upload favicon.');
            }

        } catch (Exception $e) {
            error_log('Favicon upload error: ' . $e->getMessage());
            return false;
        }
    }
}
?>  
  
    /**
     * Render email templates settings tab
     */
    private function renderEmailTemplatesSettings() {
        return '
        <div class="tab-pane fade" id="email-templates" role="tabpanel">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Email Templates Management
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <p class="text-muted mb-4">
                                Manage email templates with dynamic placeholders for automated notifications.
                                Templates support both HTML and plain text formats.
                            </p>
                            
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <div>
                                    <h6 class="mb-0">Available Templates</h6>
                                    <small class="text-muted">Click on a template to edit or preview</small>
                                </div>
                                <a href="/superadmin/email-templates/" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-2"></i>Manage Templates
                                </a>
                            </div>
                            
                            <div class="row" id="emailTemplatesPreview">
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-primary">
                                        <div class="card-body text-center">
                                            <i class="fas fa-user-plus fa-2x text-primary mb-3"></i>
                                            <h6 class="card-title">Welcome Email</h6>
                                            <p class="card-text small text-muted">
                                                Sent to new users after registration
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-success">
                                        <div class="card-body text-center">
                                            <i class="fas fa-credit-card fa-2x text-success mb-3"></i>
                                            <h6 class="card-title">Deposit Confirmation</h6>
                                            <p class="card-text small text-muted">
                                                Sent when deposits are processed
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-bill-wave fa-2x text-warning mb-3"></i>
                                            <h6 class="card-title">Withdrawal Notification</h6>
                                            <p class="card-text small text-muted">
                                                Sent for withdrawal status updates
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-danger">
                                        <div class="card-body text-center">
                                            <i class="fas fa-key fa-2x text-danger mb-3"></i>
                                            <h6 class="card-title">Password Reset</h6>
                                            <p class="card-text small text-muted">
                                                Sent for password reset requests
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-info">
                                        <div class="card-body text-center">
                                            <i class="fas fa-envelope-open fa-2x text-info mb-3"></i>
                                            <h6 class="card-title">Email Verification</h6>
                                            <p class="card-text small text-muted">
                                                Sent for email address verification
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card border-secondary">
                                        <div class="card-body text-center">
                                            <i class="fas fa-balance-scale fa-2x text-secondary mb-3"></i>
                                            <h6 class="card-title">Balance Update</h6>
                                            <p class="card-text small text-muted">
                                                Sent for account balance changes
                                            </p>
                                            <span class="badge bg-success">Active</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mt-4">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>Template Features
                                </h6>
                                <ul class="mb-0">
                                    <li><strong>Dynamic Placeholders:</strong> Use variables like {{user_name}}, {{amount}}, {{site_name}}</li>
                                    <li><strong>HTML & Text Support:</strong> Create rich HTML emails with plain text fallbacks</li>
                                    <li><strong>Live Preview:</strong> Preview templates with sample data before sending</li>
                                    <li><strong>Template Testing:</strong> Send test emails to verify formatting and content</li>
                                </ul>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-code me-2"></i>Available Placeholders
                                            </h6>
                                            <div class="small">
                                                <code>{{user_name}}</code> - Full user name<br>
                                                <code>{{first_name}}</code> - First name only<br>
                                                <code>{{email}}</code> - User email address<br>
                                                <code>{{site_name}}</code> - Site name<br>
                                                <code>{{currency_symbol}}</code> - Currency symbol<br>
                                                <code>{{amount}}</code> - Transaction amount<br>
                                                <code>{{date}}</code> - Current date
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-tools me-2"></i>Template Actions
                                            </h6>
                                            <div class="d-grid gap-2">
                                                <a href="/superadmin/email-templates/" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-edit me-2"></i>Edit Templates
                                                </a>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="testEmailTemplates()">
                                                    <i class="fas fa-paper-plane me-2"></i>Send Test Emails
                                                </button>
                                                <button type="button" class="btn btn-outline-info btn-sm" onclick="previewTemplates()">
                                                    <i class="fas fa-eye me-2"></i>Preview Templates
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }