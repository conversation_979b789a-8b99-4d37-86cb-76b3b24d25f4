<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $userIds = $_POST['user_ids'] ?? [];
    $asset = trim($_POST['asset'] ?? '');
    $tradeType = $_POST['trade_type'] ?? '';
    $amount = floatval($_POST['amount'] ?? 0);
    $entryPrice = floatval($_POST['entry_price'] ?? 0);
    $openedAt = $_POST['opened_at'] ?? '';
    
    // Validation
    $errors = [];
    
    if (empty($userIds) || !is_array($userIds)) {
        $errors['user_ids'] = 'At least one user must be selected';
    } else {
        // Validate user IDs
        $userIds = array_map('intval', $userIds);
        $userIds = array_filter($userIds, function($id) { return $id > 0; });
        
        if (empty($userIds)) {
            $errors['user_ids'] = 'Valid users must be selected';
        } else {
            // Check if all users exist
            $placeholders = str_repeat('?,', count($userIds) - 1) . '?';
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE id IN ($placeholders) AND role = 'user'");
            $stmt->execute($userIds);
            $validUsers = $stmt->fetch()['count'];
            
            if ($validUsers != count($userIds)) {
                $errors['user_ids'] = 'Some selected users are invalid';
            }
        }
    }
    
    if (empty($asset)) {
        $errors['asset'] = 'Asset is required';
    } elseif (strlen($asset) > 50) {
        $errors['asset'] = 'Asset name is too long';
    }
    
    if (!in_array($tradeType, ['buy', 'sell'])) {
        $errors['trade_type'] = 'Invalid trade type';
    }
    
    if ($amount <= 0) {
        $errors['amount'] = 'Amount must be greater than zero';
    }
    
    if ($entryPrice <= 0) {
        $errors['entry_price'] = 'Entry price must be greater than zero';
    }
    
    if (empty($openedAt)) {
        $errors['opened_at'] = 'Opened date is required';
    } elseif (strtotime($openedAt) > time()) {
        $errors['opened_at'] = 'Opened date cannot be in the future';
    }
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    $pdo->beginTransaction();
    
    try {
        $createdCount = 0;
        $createdTrades = [];
        $currentUserId = getCurrentUser()['id'];
        
        // Prepare the insert statement
        $sql = "INSERT INTO trades (user_id, asset, trade_type, amount, entry_price, opened_at, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        
        // Create trades for each user
        foreach ($userIds as $userId) {
            $result = $stmt->execute([
                $userId,
                $asset,
                $tradeType,
                $amount,
                $entryPrice,
                $openedAt,
                $currentUserId
            ]);
            
            if ($result) {
                $tradeId = $pdo->lastInsertId();
                $createdTrades[] = $tradeId;
                $createdCount++;
            }
        }
        
        $pdo->commit();
        
        // Log the action
        AuditTrailService::log(
            'bulk_trades_created',
            'trade',
            'multiple',
            [
                'user_ids' => $userIds,
                'asset' => $asset,
                'trade_type' => $tradeType,
                'amount' => $amount,
                'entry_price' => $entryPrice,
                'opened_at' => $openedAt,
                'created_count' => $createdCount,
                'trade_ids' => $createdTrades
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => "Successfully created {$createdCount} trades",
            'created_count' => $createdCount,
            'trade_ids' => $createdTrades
        ]);
        
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Bulk create trades error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while creating trades'
    ]);
}
?>