<?php
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/TransactionManager.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// Get transaction ID
$transactionId = (int)($_GET['id'] ?? 0);

if (!$transactionId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Transaction ID is required']);
    exit;
}

try {
    // Get transaction
    $transaction = Transaction::find($transactionId);
    
    if (!$transaction) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Transaction not found']);
        exit;
    }
    
    // Check if transaction belongs to current user
    if ($transaction->user_id != $user->getId()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
    
    // Get audit trail
    $auditTrail = TransactionManager::getTransactionAuditTrail($transactionId);
    
    // Prepare response data
    $responseData = [
        'id' => $transaction->getId(),
        'type' => $transaction->type,
        'type_display' => $transaction->getTypeDisplayName(),
        'amount' => $transaction->amount,
        'formatted_amount' => $transaction->getFormattedAmount(),
        'is_credit' => $transaction->isCredit(),
        'balance_before' => $transaction->balance_before,
        'balance_after' => $transaction->balance_after,
        'description' => $transaction->description,
        'status' => $transaction->status,
        'status_display' => $transaction->getStatusDisplayName(),
        'reference_id' => $transaction->reference_id,
        'reference_type' => $transaction->reference_type,
        'processed_by' => $transaction->processed_by,
        'processed_by_name' => null,
        'processed_at' => null,
        'created_at' => $transaction->created_at
    ];
    
    // Add processed by information if available
    if ($auditTrail && $auditTrail['processed_by']) {
        $processedBy = $auditTrail['processed_by'];
        $responseData['processed_by_name'] = $processedBy->first_name . ' ' . $processedBy->last_name;
        $responseData['processed_at'] = $transaction->updated_at;
    }
    
    echo json_encode([
        'success' => true,
        'transaction' => $responseData
    ]);
    
} catch (Exception $e) {
    error_log("Transaction details API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>