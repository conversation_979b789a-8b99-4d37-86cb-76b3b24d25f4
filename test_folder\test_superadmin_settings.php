<?php
/**
 * Test Super Admin System Settings Functionality
 */

require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/SystemSetting.php';

echo "<h2>Testing Super Admin System Settings</h2>\n";

try {
    // Test 1: Initialize default settings
    echo "<h3>Test 1: Initialize Default Settings</h3>\n";
    $result = SystemSetting::initializeDefaults();
    echo $result ? "✅ Default settings initialized successfully\n" : "❌ Failed to initialize default settings\n";
    
    // Test 2: Get settings by category
    echo "<h3>Test 2: Get Settings by Category</h3>\n";
    $generalSettings = SystemSetting::getByCategory('general');
    echo "✅ Found " . count($generalSettings) . " general settings\n";
    
    $financialSettings = SystemSetting::getByCategory('financial');
    echo "✅ Found " . count($financialSettings) . " financial settings\n";
    
    // Test 3: Get grouped settings
    echo "<h3>Test 3: Get Grouped Settings</h3>\n";
    $groupedSettings = SystemSetting::getGroupedByCategory();
    echo "✅ Found " . count($groupedSettings) . " setting categories\n";
    
    foreach ($groupedSettings as $category => $settings) {
        echo "  - $category: " . count($settings) . " settings\n";
    }
    
    // Test 4: Update multiple settings
    echo "<h3>Test 4: Update Multiple Settings</h3>\n";
    $updates = [
        'site_name' => 'Test Trading Platform',
        'registration_bonus' => 25.00,
        'email_verification_required' => true
    ];
    
    $result = SystemSetting::updateMultiple($updates, 1);
    echo $result ? "✅ Multiple settings updated successfully\n" : "❌ Failed to update multiple settings\n";
    
    // Test 5: Verify updates
    echo "<h3>Test 5: Verify Updates</h3>\n";
    $siteName = SystemSetting::getValue('site_name');
    $bonus = SystemSetting::getValue('registration_bonus');
    $emailVerification = SystemSetting::getValue('email_verification_required');
    
    echo "Site Name: " . ($siteName === 'Test Trading Platform' ? "✅ $siteName" : "❌ Expected 'Test Trading Platform', got '$siteName'") . "\n";
    echo "Registration Bonus: " . ($bonus == 25.00 ? "✅ $bonus" : "❌ Expected 25.00, got '$bonus'") . "\n";
    echo "Email Verification: " . ($emailVerification === true ? "✅ Enabled" : "❌ Expected true, got " . var_export($emailVerification, true)) . "\n";
    
    // Test 6: Get public settings
    echo "<h3>Test 6: Get Public Settings</h3>\n";
    $publicSettings = SystemSetting::getPublicSettings();
    echo "✅ Found " . count($publicSettings) . " public settings\n";
    
    // Test 7: Search settings
    echo "<h3>Test 7: Search Settings</h3>\n";
    $searchResults = SystemSetting::search('bonus');
    echo "✅ Found " . count($searchResults) . " settings matching 'bonus'\n";
    
    // Test 8: Get statistics
    echo "<h3>Test 8: Get Statistics</h3>\n";
    $stats = SystemSetting::getStatistics();
    if ($stats) {
        echo "✅ Statistics retrieved:\n";
        echo "  - Total settings: " . $stats['total_settings'] . "\n";
        echo "  - Public settings: " . $stats['public_settings'] . "\n";
        echo "  - Categories: " . $stats['categories_count'] . "\n";
        echo "  - Types: " . $stats['types_count'] . "\n";
    } else {
        echo "❌ Failed to get statistics\n";
    }
    
    // Test 9: Test typed values
    echo "<h3>Test 9: Test Typed Values</h3>\n";
    $setting = SystemSetting::getByKey('registration_bonus');
    if ($setting) {
        echo "Raw value: " . $setting->setting_value . "\n";
        echo "Typed value: " . $setting->getTypedValue() . "\n";
        echo "Type: " . $setting->setting_type . "\n";
        echo $setting->getTypedValue() === 25.0 ? "✅ Typed value correct\n" : "❌ Typed value incorrect\n";
    }
    
    // Test 10: Reset to defaults
    echo "<h3>Test 10: Reset to Defaults</h3>\n";
    $defaults = [
        'site_name' => 'Coinage Trading',
        'registration_bonus' => 0,
        'email_verification_required' => true
    ];
    
    $result = SystemSetting::resetToDefaults($defaults, 1);
    echo $result ? "✅ Settings reset to defaults successfully\n" : "❌ Failed to reset settings\n";
    
    // Verify reset
    $siteName = SystemSetting::getValue('site_name');
    $bonus = SystemSetting::getValue('registration_bonus');
    
    echo "Site Name after reset: " . ($siteName === 'Coinage Trading' ? "✅ $siteName" : "❌ Expected 'Coinage Trading', got '$siteName'") . "\n";
    echo "Registration Bonus after reset: " . ($bonus == 0 ? "✅ $bonus" : "❌ Expected 0, got '$bonus'") . "\n";
    
    echo "\n<h3>✅ All Super Admin System Settings tests completed successfully!</h3>\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n<hr>\n";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
h3 { color: #34495e; margin-top: 20px; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
.success { color: #27ae60; }
.error { color: #e74c3c; }
</style>