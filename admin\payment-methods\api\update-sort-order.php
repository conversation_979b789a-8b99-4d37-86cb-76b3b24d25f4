<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/PaymentMethod.php';
require_once '../../../classes/services/CSRFProtection.php';

header('Content-Type: application/json');

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Verify CSRF token
    if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    // Get method IDs array
    $methodIdsJson = $_POST['method_ids'] ?? '';
    $methodIds = json_decode($methodIdsJson, true);
    
    if (!is_array($methodIds) || empty($methodIds)) {
        echo json_encode(['success' => false, 'message' => 'Invalid method IDs']);
        exit;
    }
    
    // Validate that all IDs are numeric
    foreach ($methodIds as $id) {
        if (!is_numeric($id) || (int)$id <= 0) {
            echo json_encode(['success' => false, 'message' => 'Invalid method ID format']);
            exit;
        }
    }
    
    // Update sort orders
    if (PaymentMethod::updateSortOrders($methodIds)) {
        echo json_encode([
            'success' => true,
            'message' => 'Sort order updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update sort order'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Sort order update error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating sort order'
    ]);
}
?>