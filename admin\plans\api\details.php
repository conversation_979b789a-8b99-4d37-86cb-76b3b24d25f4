<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $planId = intval($_GET['id'] ?? 0);
    
    if (!$planId) {
        echo json_encode(['success' => false, 'message' => 'Plan ID is required']);
        exit;
    }
    
    // Load the plan
    $plan = TradingPlan::find($planId);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'Trading plan not found']);
        exit;
    }
    
    // Prepare plan data
    $planData = [
        'id' => $plan->getId(),
        'name' => $plan->name,
        'min_deposit' => $plan->min_deposit,
        'max_deposit' => $plan->max_deposit,
        'daily_return' => $plan->daily_return,
        'duration_days' => $plan->duration_days,
        'status' => $plan->status,
        'description' => $plan->description,
        'features' => $plan->getFeaturesArray(),
        'sort_order' => $plan->sort_order,
        'created_at' => $plan->created_at,
        'updated_at' => $plan->updated_at,
        
        // Calculated values
        'total_return' => $plan->getTotalReturn(),
        'formatted_daily_return' => $plan->getFormattedDailyReturn(),
        'formatted_total_return' => $plan->getFormattedTotalReturn(),
        'deposit_range' => $plan->getDepositRange(),
        
        // Statistics
        'deposits_count' => $plan->getDepositsCount(),
        'total_deposits_amount' => $plan->getTotalDepositsAmount()
    ];
    
    echo json_encode([
        'success' => true,
        'plan' => $planData
    ]);
    
} catch (Exception $e) {
    error_log("Get trading plan details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading plan details'
    ]);
}
?>