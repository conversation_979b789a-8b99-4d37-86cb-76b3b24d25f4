<?php
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/models/Withdrawal.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit;
}

// Get withdrawal ID
$withdrawalId = (int)($_GET['id'] ?? 0);

if (!$withdrawalId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Withdrawal ID is required']);
    exit;
}

try {
    // Get withdrawal
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        http_response_code(404);
        echo json_encode(['success' => false, 'error' => 'Withdrawal not found']);
        exit;
    }
    
    // Check if withdrawal belongs to current user
    if ($withdrawal->user_id != $user->getId()) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Access denied']);
        exit;
    }
    
    // Get processed by information
    $processedBy = $withdrawal->getProcessedBy();
    
    // Prepare response data
    $responseData = [
        'id' => $withdrawal->getId(),
        'amount' => $withdrawal->amount,
        'withdrawal_method' => $withdrawal->withdrawal_method,
        'account_details' => $withdrawal->getAccountDetailsArray(),
        'status' => $withdrawal->status,
        'status_display' => ucfirst($withdrawal->status),
        'admin_note' => $withdrawal->admin_note,
        'processed_by' => $withdrawal->processed_by,
        'processed_by_name' => null,
        'processed_at' => $withdrawal->processed_at,
        'created_at' => $withdrawal->created_at
    ];
    
    // Add processed by information if available
    if ($processedBy) {
        $responseData['processed_by_name'] = $processedBy->first_name . ' ' . $processedBy->last_name;
    }
    
    echo json_encode([
        'success' => true,
        'withdrawal' => $responseData
    ]);
    
} catch (Exception $e) {
    error_log("Withdrawal details API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Internal server error']);
}
?>