<?php
require_once __DIR__ . '/../includes/db_connect.php';

/**
 * Create Security Tables Migration
 * Creates the necessary tables for 2FA and security audit system
 */

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h1>Creating Security Tables</h1>\n";
    echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px;'>\n";
    
    // Create backup codes table
    echo "<h2>Creating user_backup_codes table...</h2>\n";
    $backupCodesSQL = "
        CREATE TABLE IF NOT EXISTS user_backup_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            code_hash VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            used_at TIMESTAMP NULL,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            
            INDEX idx_user_id (user_id),
            INDEX idx_used_at (used_at)
        )
    ";
    
    $db->exec($backupCodesSQL);
    echo "✅ user_backup_codes table created successfully\n";
    
    // Create security audit logs table
    echo "<h2>Creating security_audit_logs table...</h2>\n";
    $auditLogsSQL = "
        CREATE TABLE IF NOT EXISTS security_audit_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category ENUM('auth', 'financial', 'admin', 'system') NOT NULL,
            event VARCHAR(100) NOT NULL,
            user_id INT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            request_uri VARCHAR(500),
            http_method VARCHAR(10),
            session_id VARCHAR(128),
            details JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
            
            INDEX idx_category (category),
            INDEX idx_event (event),
            INDEX idx_user_id (user_id),
            INDEX idx_ip_address (ip_address),
            INDEX idx_created_at (created_at),
            INDEX idx_category_event (category, event),
            INDEX idx_user_created (user_id, created_at)
        )
    ";
    
    $db->exec($auditLogsSQL);
    echo "✅ security_audit_logs table created successfully\n";
    
    // Verify tables exist
    echo "<h2>Verifying tables...</h2>\n";
    
    $tables = ['user_backup_codes', 'security_audit_logs'];
    foreach ($tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' not found\n";
        }
    }
    
    // Check if users table has 2FA fields
    echo "<h2>Checking 2FA fields in users table...</h2>\n";
    $stmt = $db->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
    
    $requiredFields = ['two_fa_enabled', 'two_fa_secret'];
    foreach ($requiredFields as $field) {
        if (in_array($field, $columns)) {
            echo "✅ Field '$field' exists in users table\n";
        } else {
            echo "❌ Field '$field' missing from users table\n";
            
            // Add missing field
            if ($field === 'two_fa_enabled') {
                $db->exec("ALTER TABLE users ADD COLUMN two_fa_enabled BOOLEAN DEFAULT FALSE");
                echo "✅ Added 'two_fa_enabled' field\n";
            } elseif ($field === 'two_fa_secret') {
                $db->exec("ALTER TABLE users ADD COLUMN two_fa_secret VARCHAR(32) NULL");
                echo "✅ Added 'two_fa_secret' field\n";
            }
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #155724;'>✅ Security Tables Created Successfully!</h3>\n";
    echo "<p style='color: #155724;'>All required tables and fields for the security system have been created.</p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px 0; border-radius: 5px;'>\n";
    echo "<h3 style='color: #721c24;'>❌ Error Creating Security Tables</h3>\n";
    echo "<p style='color: #721c24;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</div>\n";
?>