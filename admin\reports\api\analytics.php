<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuthenticationManager.php';
require_once '../../../classes/services/FinancialReportingService.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
    $dateTo = $_GET['date_to'] ?? date('Y-m-d');
    $interval = $_GET['interval'] ?? 'day';
    
    // Validate inputs
    if (!DateTime::createFromFormat('Y-m-d', $dateFrom) || !DateTime::createFromFormat('Y-m-d', $dateTo)) {
        throw new InvalidArgumentException('Invalid date format');
    }
    
    if (!in_array($interval, ['hour', 'day', 'week', 'month'])) {
        throw new InvalidArgumentException('Invalid interval');
    }
    
    $reportingService = new FinancialReportingService();
    $auditService = new AuditTrailService();
    
    $analytics = [
        'volume_trends' => $reportingService->getVolumeTrends($dateFrom, $dateTo, $interval),
        'audit_statistics' => $auditService->getAuditStatistics($dateFrom, $dateTo),
        'suspicious_activity' => $auditService->getSuspiciousActivity(24),
        'key_metrics' => calculateKeyMetrics($dateFrom, $dateTo)
    ];
    
    // Process volume trends for chart display
    $chartData = [
        'labels' => [],
        'datasets' => [
            'deposits' => ['label' => 'Deposits', 'data' => [], 'backgroundColor' => 'rgba(54, 162, 235, 0.2)', 'borderColor' => 'rgba(54, 162, 235, 1)'],
            'withdrawals' => ['label' => 'Withdrawals', 'data' => [], 'backgroundColor' => 'rgba(255, 99, 132, 0.2)', 'borderColor' => 'rgba(255, 99, 132, 1)'],
            'bonuses' => ['label' => 'Bonuses', 'data' => [], 'backgroundColor' => 'rgba(75, 192, 192, 0.2)', 'borderColor' => 'rgba(75, 192, 192, 1)']
        ]
    ];
    
    $periodData = [];
    foreach ($analytics['volume_trends'] as $trend) {
        $period = $trend['period'];
        if (!isset($periodData[$period])) {
            $periodData[$period] = ['deposit' => 0, 'withdrawal' => 0, 'bonus' => 0];
        }
        $periodData[$period][$trend['type']] = (float) $trend['total_volume'];
    }
    
    foreach ($periodData as $period => $data) {
        $chartData['labels'][] = $period;
        $chartData['datasets']['deposits']['data'][] = $data['deposit'];
        $chartData['datasets']['withdrawals']['data'][] = $data['withdrawal'];
        $chartData['datasets']['bonuses']['data'][] = $data['bonus'];
    }
    
    $analytics['chart_data'] = $chartData;
    
    echo json_encode([
        'success' => true,
        'data' => $analytics
    ]);
    
} catch (Exception $e) {
    error_log("Analytics API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Calculate key metrics for the analytics dashboard
 */
function calculateKeyMetrics($dateFrom, $dateTo) {
    $db = getDB();
    
    // Transaction velocity (transactions per day)
    $sql = "SELECT COUNT(*) / DATEDIFF(:date_to, :date_from) as avg_transactions_per_day
            FROM transactions 
            WHERE created_at >= :date_from AND created_at <= :date_to AND status = 'completed'";
    $stmt = $db->prepare($sql);
    $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
    $transactionVelocity = $stmt->fetchColumn();
    
    // Average transaction value
    $sql = "SELECT AVG(ABS(amount)) as avg_transaction_value
            FROM transactions 
            WHERE created_at >= :date_from AND created_at <= :date_to AND status = 'completed'";
    $stmt = $db->prepare($sql);
    $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
    $avgTransactionValue = $stmt->fetchColumn();
    
    // User engagement (active users / total users)
    $sql = "SELECT 
                COUNT(DISTINCT t.user_id) as active_users,
                (SELECT COUNT(*) FROM users WHERE role = 'user') as total_users
            FROM transactions t
            WHERE t.created_at >= :date_from AND t.created_at <= :date_to";
    $stmt = $db->prepare($sql);
    $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
    $engagement = $stmt->fetch();
    $engagementRate = $engagement['total_users'] > 0 ? ($engagement['active_users'] / $engagement['total_users']) * 100 : 0;
    
    // Deposit success rate
    $sql = "SELECT 
                COUNT(*) as total_deposits,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_deposits
            FROM deposits 
            WHERE created_at >= :date_from AND created_at <= :date_to";
    $stmt = $db->prepare($sql);
    $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
    $deposits = $stmt->fetch();
    $depositSuccessRate = $deposits['total_deposits'] > 0 ? ($deposits['approved_deposits'] / $deposits['total_deposits']) * 100 : 0;
    
    // Withdrawal processing time (average hours)
    $sql = "SELECT AVG(TIMESTAMPDIFF(HOUR, created_at, processed_at)) as avg_processing_hours
            FROM withdrawals 
            WHERE created_at >= :date_from AND created_at <= :date_to 
              AND status IN ('approved', 'completed') AND processed_at IS NOT NULL";
    $stmt = $db->prepare($sql);
    $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
    $avgProcessingTime = $stmt->fetchColumn();
    
    return [
        'transaction_velocity' => round($transactionVelocity, 2),
        'avg_transaction_value' => round($avgTransactionValue, 2),
        'engagement_rate' => round($engagementRate, 2),
        'deposit_success_rate' => round($depositSuccessRate, 2),
        'avg_processing_time' => round($avgProcessingTime, 2)
    ];
}
?>