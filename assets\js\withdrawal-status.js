/**
 * Withdrawal Status JavaScript
 * Handles withdrawal details modal, cancellation, and status updates
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeWithdrawalStatus();
});

let currentWithdrawalId = null;

function initializeWithdrawalStatus() {
    // Initialize tooltips
    initializeTooltips();
    
    // Setup auto-refresh for pending withdrawals
    setupAutoRefresh();
    
    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
    
    // Initialize status filter
    initializeStatusFilter();
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function setupAutoRefresh() {
    // Check if there are pending withdrawals
    const pendingBadges = document.querySelectorAll('.badge.bg-warning');
    
    if (pendingBadges.length > 0) {
        // Refresh page every 30 seconds if there are pending withdrawals
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    }
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + N for new withdrawal
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            window.location.href = 'request.php';
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            });
        }
    });
}

function initializeStatusFilter() {
    const statusSelect = document.getElementById('status');
    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            // Auto-submit form when status changes
            this.form.submit();
        });
    }
}

function viewWithdrawalDetails(withdrawalId) {
    const modal = new bootstrap.Modal(document.getElementById('withdrawalDetailsModal'));
    const contentDiv = document.getElementById('withdrawalDetailsContent');
    
    // Show loading state
    contentDiv.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading withdrawal details...</p>
        </div>
    `;
    
    modal.show();
    
    // Fetch withdrawal details
    fetch(`api/withdrawal-details.php?id=${withdrawalId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayWithdrawalDetails(data.withdrawal);
            } else {
                contentDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Error loading withdrawal details: ${data.error || 'Unknown error'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error fetching withdrawal details:', error);
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load withdrawal details. Please try again.
                </div>
            `;
        });
}

function displayWithdrawalDetails(withdrawal) {
    const contentDiv = document.getElementById('withdrawalDetailsContent');
    
    const statusClass = getStatusClass(withdrawal.status);
    const statusIcon = getStatusIcon(withdrawal.status);
    
    contentDiv.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted mb-3">Withdrawal Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td class="fw-medium">Withdrawal ID:</td>
                        <td>#${withdrawal.id}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Amount:</td>
                        <td class="fw-bold text-danger">$${parseFloat(withdrawal.amount).toFixed(2)}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Method:</td>
                        <td>${withdrawal.withdrawal_method}</td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Status:</td>
                        <td>
                            <span class="badge bg-${statusClass}">
                                ${statusIcon} ${withdrawal.status_display}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="fw-medium">Requested:</td>
                        <td>${formatDateTime(withdrawal.created_at)}</td>
                    </tr>
                    ${withdrawal.processed_at ? `
                    <tr>
                        <td class="fw-medium">Processed:</td>
                        <td>${formatDateTime(withdrawal.processed_at)}</td>
                    </tr>
                    ` : ''}
                </table>
            </div>
            
            <div class="col-md-6">
                <h6 class="text-muted mb-3">Account Details</h6>
                <div class="account-details">
                    ${formatAccountDetails(withdrawal.account_details)}
                </div>
                
                ${withdrawal.admin_note ? `
                    <h6 class="text-muted mb-3 mt-4">Admin Note</h6>
                    <div class="alert alert-info">
                        ${withdrawal.admin_note}
                    </div>
                ` : ''}
                
                ${withdrawal.processed_by_name ? `
                    <h6 class="text-muted mb-3 mt-4">Processing Information</h6>
                    <table class="table table-sm">
                        <tr>
                            <td class="fw-medium">Processed By:</td>
                            <td>${withdrawal.processed_by_name}</td>
                        </tr>
                    </table>
                ` : ''}
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${withdrawal.status === 'pending' ? `
                            <button class="btn btn-outline-danger btn-sm" onclick="cancelWithdrawal(${withdrawal.id})">
                                <i class="fas fa-times"></i> Cancel Request
                            </button>
                        ` : ''}
                    </div>
                    <div>
                        <a href="../transactions/?type=withdrawal" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-receipt"></i> View Transaction
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function formatAccountDetails(accountDetails) {
    if (!accountDetails || typeof accountDetails !== 'object') {
        return '<p class="text-muted">No account details available</p>';
    }
    
    let html = '<table class="table table-sm">';
    
    for (const [key, value] of Object.entries(accountDetails)) {
        const label = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        html += `
            <tr>
                <td class="fw-medium">${label}:</td>
                <td>${value}</td>
            </tr>
        `;
    }
    
    html += '</table>';
    return html;
}

function cancelWithdrawal(withdrawalId) {
    currentWithdrawalId = withdrawalId;
    
    const modal = new bootstrap.Modal(document.getElementById('cancelConfirmModal'));
    modal.show();
    
    // Setup confirm button
    const confirmBtn = document.getElementById('confirmCancelBtn');
    confirmBtn.onclick = function() {
        performCancellation(withdrawalId);
    };
}

function performCancellation(withdrawalId) {
    const confirmBtn = document.getElementById('confirmCancelBtn');
    const originalText = confirmBtn.innerHTML;
    
    // Show loading state
    confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Cancelling...';
    confirmBtn.disabled = true;
    
    // Send cancellation request
    fetch('api/cancel-withdrawal.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            withdrawal_id: withdrawalId,
            csrf_token: getCsrfToken()
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Withdrawal request cancelled successfully', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('cancelConfirmModal'));
            modal.hide();
            
            // Refresh page after short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.error || 'Failed to cancel withdrawal', 'danger');
        }
    })
    .catch(error => {
        console.error('Error cancelling withdrawal:', error);
        showAlert('Failed to cancel withdrawal. Please try again.', 'danger');
    })
    .finally(() => {
        // Reset button state
        confirmBtn.innerHTML = originalText;
        confirmBtn.disabled = false;
    });
}

function getStatusClass(status) {
    const classes = {
        'pending': 'warning',
        'approved': 'info',
        'processing': 'primary',
        'completed': 'success',
        'rejected': 'danger'
    };
    return classes[status] || 'secondary';
}

function getStatusIcon(status) {
    const icons = {
        'pending': '<i class="fas fa-clock"></i>',
        'approved': '<i class="fas fa-check"></i>',
        'processing': '<i class="fas fa-cog fa-spin"></i>',
        'completed': '<i class="fas fa-check-double"></i>',
        'rejected': '<i class="fas fa-times"></i>'
    };
    return icons[status] || '<i class="fas fa-question"></i>';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getCsrfToken() {
    // Get CSRF token from meta tag or form
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const formToken = document.querySelector('input[name="csrf_token"]');
    if (formToken) {
        return formToken.value;
    }
    
    return '';
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'danger' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Auto-refresh functionality for real-time updates
function startAutoRefresh() {
    setInterval(() => {
        // Only refresh if there are pending withdrawals
        const pendingElements = document.querySelectorAll('.badge.bg-warning');
        if (pendingElements.length > 0) {
            // Silently refresh data without full page reload
            refreshWithdrawalData();
        }
    }, 30000); // 30 seconds
}

function refreshWithdrawalData() {
    fetch(window.location.href, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Parse the response and update only the table content
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const newTableBody = doc.querySelector('.table tbody');
        const currentTableBody = document.querySelector('.table tbody');
        
        if (newTableBody && currentTableBody) {
            currentTableBody.innerHTML = newTableBody.innerHTML;
            
            // Re-initialize tooltips for new content
            initializeTooltips();
        }
    })
    .catch(error => {
        console.error('Error refreshing withdrawal data:', error);
    });
}

// Start auto-refresh when page loads
document.addEventListener('DOMContentLoaded', function() {
    startAutoRefresh();
});