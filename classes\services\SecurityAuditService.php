<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../../includes/db_connect.php';

/**
 * Security Audit Service
 * Comprehensive security monitoring and audit logging
 */
class SecurityAuditService {
    private static $instance = null;
    private $db;
    
    private function __construct() {
        $this->db = Database::getInstance()->getConnection();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Log authentication events
     */
    public function logAuthEvent($event, $userId, $details = []) {
        $this->logSecurityEvent('auth', $event, $userId, $details);
    }
    
    /**
     * Log financial events
     */
    public function logFinancialEvent($event, $userId, $details = []) {
        $this->logSecurityEvent('financial', $event, $userId, $details);
    }
    
    /**
     * Log admin events
     */
    public function logAdminEvent($event, $adminId, $targetUserId = null, $details = []) {
        $details['target_user_id'] = $targetUserId;
        $this->logSecurityEvent('admin', $event, $adminId, $details);
    }
    
    /**
     * Log system events
     */
    public function logSystemEvent($event, $details = []) {
        $this->logSecurityEvent('system', $event, null, $details);
    }
    
    /**
     * Core security event logging
     */
    private function logSecurityEvent($category, $event, $userId = null, $details = []) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO security_audit_logs (
                    category, event, user_id, ip_address, user_agent, 
                    request_uri, http_method, session_id, details, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $category,
                $event,
                $userId,
                $this->getClientIP(),
                $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                $_SERVER['REQUEST_URI'] ?? 'Unknown',
                $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
                session_id(),
                json_encode($details)
            ]);
            
            // Also log to file for backup
            $this->logToFile($category, $event, $userId, $details);
            
        } catch (Exception $e) {
            error_log("Security audit logging error: " . $e->getMessage());
            // Fallback to file logging only
            $this->logToFile($category, $event, $userId, $details);
        }
    }
    
    /**
     * File-based logging fallback
     */
    private function logToFile($category, $event, $userId, $details) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'category' => $category,
            'event' => $event,
            'user_id' => $userId,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'details' => $details
        ];
        
        $logEntry = json_encode($logData) . "\n";
        
        $logFile = __DIR__ . "/../../logs/security_audit_{$category}.log";
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get client IP address with proxy support
     */
    private function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Monitor failed login attempts
     */
    public function monitorFailedLogins($email, $ip) {
        // Log the failed attempt
        $this->logAuthEvent('login_failed', null, [
            'email' => $email,
            'ip' => $ip
        ]);
        
        // Check for brute force patterns
        $this->checkBruteForcePattern($email, $ip);
    }
    
    /**
     * Check for brute force attack patterns
     */
    private function checkBruteForcePattern($email, $ip) {
        try {
            // Check failed attempts in last 15 minutes
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as attempts 
                FROM security_audit_logs 
                WHERE event = 'login_failed' 
                AND (JSON_EXTRACT(details, '$.email') = ? OR ip_address = ?)
                AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)
            ");
            
            $stmt->execute([$email, $ip]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['attempts'] >= 10) {
                $this->logSystemEvent('brute_force_detected', [
                    'email' => $email,
                    'ip' => $ip,
                    'attempts' => $result['attempts']
                ]);
                
                // Could trigger additional security measures here
                $this->triggerSecurityAlert('brute_force', $email, $ip);
            }
            
        } catch (Exception $e) {
            error_log("Brute force monitoring error: " . $e->getMessage());
        }
    }
    
    /**
     * Monitor suspicious financial activity
     */
    public function monitorFinancialActivity($userId, $action, $amount, $details = []) {
        $this->logFinancialEvent($action, $userId, array_merge($details, [
            'amount' => $amount
        ]));
        
        // Check for suspicious patterns
        $this->checkSuspiciousFinancialActivity($userId, $action, $amount);
    }
    
    /**
     * Check for suspicious financial patterns
     */
    private function checkSuspiciousFinancialActivity($userId, $action, $amount) {
        try {
            // Check for rapid transactions
            if (in_array($action, ['deposit_request', 'withdrawal_request'])) {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) as count, SUM(CAST(JSON_EXTRACT(details, '$.amount') AS DECIMAL(15,2))) as total
                    FROM security_audit_logs 
                    WHERE category = 'financial' 
                    AND user_id = ? 
                    AND event = ?
                    AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ");
                
                $stmt->execute([$userId, $action]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Flag if more than 5 transactions in 1 hour or total > $10,000
                if ($result['count'] > 5 || $result['total'] > 10000) {
                    $this->logSystemEvent('suspicious_financial_activity', [
                        'user_id' => $userId,
                        'action' => $action,
                        'count' => $result['count'],
                        'total' => $result['total']
                    ]);
                }
            }
            
        } catch (Exception $e) {
            error_log("Financial activity monitoring error: " . $e->getMessage());
        }
    }
    
    /**
     * Monitor admin actions
     */
    public function monitorAdminAction($adminId, $action, $targetUserId = null, $details = []) {
        $this->logAdminEvent($action, $adminId, $targetUserId, $details);
        
        // Check for suspicious admin activity
        $this->checkSuspiciousAdminActivity($adminId, $action);
    }
    
    /**
     * Check for suspicious admin activity
     */
    private function checkSuspiciousAdminActivity($adminId, $action) {
        try {
            // Check for excessive admin actions in short time
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count
                FROM security_audit_logs 
                WHERE category = 'admin' 
                AND user_id = ? 
                AND created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE)
            ");
            
            $stmt->execute([$adminId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] > 50) {
                $this->logSystemEvent('suspicious_admin_activity', [
                    'admin_id' => $adminId,
                    'action_count' => $result['count']
                ]);
            }
            
        } catch (Exception $e) {
            error_log("Admin activity monitoring error: " . $e->getMessage());
        }
    }
    
    /**
     * Generate security report
     */
    public function generateSecurityReport($startDate, $endDate, $category = null) {
        try {
            $sql = "
                SELECT 
                    category,
                    event,
                    COUNT(*) as count,
                    DATE(created_at) as date
                FROM security_audit_logs 
                WHERE created_at BETWEEN ? AND ?
            ";
            
            $params = [$startDate, $endDate];
            
            if ($category) {
                $sql .= " AND category = ?";
                $params[] = $category;
            }
            
            $sql .= " GROUP BY category, event, DATE(created_at) ORDER BY created_at DESC";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Security report generation error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get recent security events
     */
    public function getRecentEvents($limit = 100, $category = null) {
        try {
            $sql = "
                SELECT 
                    category,
                    event,
                    user_id,
                    ip_address,
                    details,
                    created_at
                FROM security_audit_logs
            ";
            
            $params = [];
            
            if ($category) {
                $sql .= " WHERE category = ?";
                $params[] = $category;
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Recent events retrieval error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Trigger security alert
     */
    private function triggerSecurityAlert($type, $email = null, $ip = null) {
        // This could send notifications to administrators
        // For now, just log the alert
        $this->logSystemEvent('security_alert_triggered', [
            'alert_type' => $type,
            'email' => $email,
            'ip' => $ip,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // Could integrate with email notifications, Slack, etc.
    }
    
    /**
     * Clean old audit logs
     */
    public function cleanOldLogs($daysToKeep = 90) {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM security_audit_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            
            $stmt->execute([$daysToKeep]);
            
            $deletedRows = $stmt->rowCount();
            
            $this->logSystemEvent('audit_logs_cleaned', [
                'deleted_rows' => $deletedRows,
                'days_kept' => $daysToKeep
            ]);
            
            return $deletedRows;
            
        } catch (Exception $e) {
            error_log("Audit log cleanup error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get security statistics
     */
    public function getSecurityStats($days = 7) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    category,
                    event,
                    COUNT(*) as count
                FROM security_audit_logs 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY category, event
                ORDER BY count DESC
            ");
            
            $stmt->execute([$days]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Security stats error: " . $e->getMessage());
            return [];
        }
    }
}
?>