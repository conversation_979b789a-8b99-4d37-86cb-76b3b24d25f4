<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/controllers/UserController.php';
require_once __DIR__ . '/../../classes/views/SettingsView.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: /login.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'update_settings':
                $result = UserController::updateSettings();
                echo json_encode($result);
                exit();
                
            default:
                echo json_encode(['success' => false, 'error' => 'Invalid action']);
                exit();
        }
    } catch (Exception $e) {
        error_log("Settings action error: " . $e->getMessage());
        echo json_encode(['success' => false, 'error' => 'System error occurred']);
        exit();
    }
}

try {
    // Get settings data
    $settingsData = UserController::settings();
    
    // Create and render the settings view
    $view = new SettingsView($settingsData['user']);
    $view->render();
    
} catch (Exception $e) {
    error_log("Settings error: " . $e->getMessage());
    header('Location: /user/dashboard/?error=system_error');
    exit();
}
?>