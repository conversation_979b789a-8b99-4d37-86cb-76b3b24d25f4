<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'classes/controllers/AuthController.php';

// Process login
$loginData = AuthController::login();
$errors = $loginData['errors'];
$message = $loginData['message'];
$csrfToken = $loginData['csrf_token'];

// Check for remember me cookie
if (!AuthenticationManager::isAuthenticated()) {
    AuthenticationManager::checkRememberMe();
    if (AuthenticationManager::isAuthenticated()) {
        $user = AuthenticationManager::getCurrentUser();
        header('Location: ' . AuthController::getRedirectUrl($user->role));
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <?php echo CSRFProtection::getTokenMeta(); ?>
    <style>
        body {
            background: linear-gradient(135deg, <?php echo PRIMARY_COLOR; ?> 0%, <?php echo SECONDARY_COLOR; ?> 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        .login-header {
            background: <?php echo PRIMARY_COLOR; ?>;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: <?php echo PRIMARY_COLOR; ?>;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .btn-primary {
            background-color: <?php echo PRIMARY_COLOR; ?>;
            border-color: <?php echo PRIMARY_COLOR; ?>;
        }
        .btn-primary:hover {
            background-color: <?php echo SECONDARY_COLOR; ?>;
            border-color: <?php echo SECONDARY_COLOR; ?>;
        }
        .alert {
            border-radius: 10px;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .form-control:focus + .input-group-text {
            border-color: <?php echo PRIMARY_COLOR; ?>;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="login-container">
                    <div class="login-header">
                        <h2><i class="fas fa-coins"></i> <?php echo SITE_NAME; ?></h2>
                        <p class="mb-0">Welcome Back</p>
                    </div>
                    
                    <div class="login-body">
                        <?php if ($message): ?>
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors['general'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($errors['general']); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($errors['rate_limit'])): ?>
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <i class="fas fa-clock"></i> <?php echo htmlspecialchars($errors['rate_limit']); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="login.php" id="loginForm">
                            <?php echo CSRFProtection::getTokenField(); ?>
                            
                            <div class="mb-3">
                                <label for="identifier" class="form-label">Username or Email</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" 
                                           class="form-control <?php echo !empty($errors['identifier']) ? 'is-invalid' : ''; ?>" 
                                           id="identifier" 
                                           name="identifier" 
                                           value="<?php echo htmlspecialchars($_POST['identifier'] ?? ''); ?>"
                                           required 
                                           autocomplete="username">
                                </div>
                                <?php if (!empty($errors['identifier'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?php echo htmlspecialchars($errors['identifier']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" 
                                           class="form-control <?php echo !empty($errors['password']) ? 'is-invalid' : ''; ?>" 
                                           id="password" 
                                           name="password" 
                                           required 
                                           autocomplete="current-password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <?php if (!empty($errors['password'])): ?>
                                    <div class="invalid-feedback d-block">
                                        <?php echo htmlspecialchars($errors['password']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Remember me for 30 days
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt"></i> Sign In
                                </button>
                            </div>
                        </form>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-3"><strong>Select Your Login Portal:</strong></p>
                            <div class="row mb-3">
                                <div class="col-4">
                                    <a href="<?php echo getBaseUrl(); ?>user/login.php" class="btn btn-primary btn-sm w-100">
                                        <i class="fas fa-user me-1"></i>User
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="<?php echo getBaseUrl(); ?>admin/login.php" class="btn btn-danger btn-sm w-100">
                                        <i class="fas fa-shield-alt me-1"></i>Admin
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="<?php echo getBaseUrl(); ?>superadmin/login.php" class="btn btn-dark btn-sm w-100">
                                        <i class="fas fa-crown me-1"></i>Super
                                    </a>
                                </div>
                            </div>
                            <hr>
                            <p class="mb-2">
                                <a href="<?php echo url('forgot-password.php'); ?>" class="text-decoration-none">
                                    <i class="fas fa-key"></i> Forgot your password?
                                </a>
                            </p>
                            <p class="mb-0">
                                Don't have an account? 
                                <a href="<?php echo url('register.php'); ?>" class="text-decoration-none fw-bold">
                                    <i class="fas fa-user-plus"></i> Sign Up
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const password = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (password.type === 'password') {
                password.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const identifier = document.getElementById('identifier').value.trim();
            const password = document.getElementById('password').value;
            
            if (!identifier || !password) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>