<?php
/**
 * StatusColorTrait - Shared methods for status color mapping
 */
trait StatusColorTrait {
    
    protected function getTransactionTypeColor($type) {
        $colors = [
            'deposit' => 'success',
            'withdrawal' => 'warning', 
            'bonus' => 'info',
            'trade_profit' => 'success',
            'trade_loss' => 'danger',
            'admin_credit' => 'primary',
            'admin_debit' => 'danger',
            'transfer_in' => 'info',
            'transfer_out' => 'warning'
        ];
        
        return $colors[$type] ?? 'secondary';
    }
    
    protected function getStatusColor($status) {
        $colors = [
            'completed' => 'success',
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            'cancelled' => 'secondary',
            'failed' => 'danger',
            'processing' => 'info',
            'active' => 'success',
            'suspended' => 'danger',
            'inactive' => 'secondary'
        ];
        
        return $colors[$status] ?? 'secondary';
    }
    
    protected function getKycStatusColor($status) {
        $colors = [
            'verified' => 'success',
            'pending' => 'warning', 
            'unverified' => 'secondary',
            'rejected' => 'danger'
        ];
        
        return $colors[$status] ?? 'secondary';
    }
    
    protected function getPriorityColor($priority) {
        $colors = [
            'low' => 'success',
            'medium' => 'info',
            'high' => 'warning',
            'urgent' => 'danger'
        ];
        
        return $colors[$priority] ?? 'secondary';
    }
}
?>