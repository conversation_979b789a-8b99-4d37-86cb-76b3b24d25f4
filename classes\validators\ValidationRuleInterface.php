<?php
/**
 * ValidationRuleInterface - Contract for validation rules
 */
interface ValidationRuleInterface {
    /**
     * Validate a value against the rule
     * 
     * @param mixed $value The value to validate
     * @param array $parameters Rule parameters
     * @return bool True if valid, false otherwise
     */
    public function validate($value, array $parameters = []);
    
    /**
     * Get error message for failed validation
     * 
     * @param string $field Field name
     * @param array $parameters Rule parameters
     * @return string Error message
     */
    public function getErrorMessage($field, array $parameters = []);
}
?>