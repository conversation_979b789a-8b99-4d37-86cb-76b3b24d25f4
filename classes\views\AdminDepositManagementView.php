<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * Admin Deposit Management View
 */
class AdminDepositManagementView extends BaseView {
    use StatusColorTrait;

    public function __construct() {
        parent::__construct();
        $this->setTitle('Deposit Management - Admin Panel');
    }

    public function render($data = []) {
        $this->data = $data;
        parent::render();
    }

    protected function renderContent() {
        $deposits = $this->data['deposits'] ?? [];
        $stats = $this->data['stats'] ?? [];
        $currentStatus = $this->data['currentStatus'] ?? 'all';
        $currentPage = $this->data['currentPage'] ?? 1;
        $totalPages = $this->data['totalPages'] ?? 1;
        $totalDeposits = $this->data['totalDeposits'] ?? 0;
        $csrfToken = $this->data['csrfToken'] ?? '';

        echo $this->renderHeader() .
             $this->renderStats($stats) .
             $this->renderFilters($currentStatus) .
             $this->renderDepositsTable($deposits, $csrfToken) .
             $this->renderPagination($currentPage, $totalPages) .
             $this->renderModals($csrfToken);
    }
    
    private function renderHeader() {
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Deposit Management</h1>
                            <p class="text-muted">Manage user deposits and approval processes</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="refreshDeposits()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button class="btn btn-success" onclick="showBulkActions()">
                                <i class="fas fa-tasks"></i> Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderStats($stats) {
        return '
        <div class="container-fluid mb-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['pending'] ?? 0) . '</h4>
                                    <p class="mb-0">Pending Deposits</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['approved'] ?? 0) . '</h4>
                                    <p class="mb-0">Approved Deposits</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['rejected'] ?? 0) . '</h4>
                                    <p class="mb-0">Rejected Deposits</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">$' . number_format($stats['total_amount'] ?? 0, 2) . '</h4>
                                    <p class="mb-0">Total Approved</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderFilters($currentStatus) {
        $statuses = [
            'all' => 'All Deposits',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected'
        ];
        
        $filterButtons = '';
        foreach ($statuses as $status => $label) {
            $activeClass = $currentStatus === $status ? 'active' : '';
            $filterButtons .= '
                <button class="btn btn-outline-primary ' . $activeClass . '" 
                        onclick="filterDeposits(\'' . $status . '\')">
                    ' . $label . '
                </button>';
        }
        
        return '
        <div class="container-fluid mb-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    ' . $filterButtons . '
                                </div>
                                <div class="d-flex align-items-center">
                                    <input type="text" class="form-control me-2" id="searchDeposits" 
                                           placeholder="Search by user, amount, or transaction ID...">
                                    <button class="btn btn-outline-secondary" onclick="searchDeposits()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderDepositsTable($deposits, $csrfToken) {
        if (empty($deposits)) {
            return '
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No deposits found</h5>
                                <p class="text-muted">There are no deposits matching your current filters.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
        
        $tableRows = '';
        foreach ($deposits as $deposit) {
            $statusBadge = $this->getStatusBadge($deposit['status']);
            $actionButtons = $this->getActionButtons($deposit, $csrfToken);
            
            $tableRows .= '
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input deposit-checkbox" 
                           value="' . $deposit['id'] . '">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">' . htmlspecialchars($deposit['first_name'] . ' ' . $deposit['last_name']) . '</div>
                            <small class="text-muted">@' . htmlspecialchars($deposit['username']) . '</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="fw-bold">$' . number_format($deposit['amount'], 2) . '</div>
                    ' . ($deposit['bonus_amount'] > 0 ? '<small class="text-success">+$' . number_format($deposit['bonus_amount'], 2) . ' bonus</small>' : '') . '
                </td>
                <td>' . htmlspecialchars($deposit['payment_method_name'] ?? 'N/A') . '</td>
                <td>' . htmlspecialchars($deposit['plan_name'] ?? 'N/A') . '</td>
                <td>' . $statusBadge . '</td>
                <td>' . date('M j, Y g:i A', strtotime($deposit['created_at'])) . '</td>
                <td>' . $actionButtons . '</td>
            </tr>';
        }
        
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Deposits List</h5>
                                <div>
                                    <input type="checkbox" class="form-check-input" id="selectAllDeposits">
                                    <label class="form-check-label ms-1" for="selectAllDeposits">Select All</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40"></th>
                                            <th>User</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Trading Plan</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th width="120">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ' . $tableRows . '
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function getActionButtons($deposit, $csrfToken) {
        $buttons = '
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary" onclick="viewDepositDetails(' . $deposit['id'] . ')" 
                    title="View Details">
                <i class="fas fa-eye"></i>
            </button>';
        
        if ($deposit['status'] === 'pending') {
            $buttons .= '
            <button class="btn btn-outline-success" onclick="approveDeposit(' . $deposit['id'] . ')" 
                    title="Approve">
                <i class="fas fa-check"></i>
            </button>
            <button class="btn btn-outline-danger" onclick="rejectDeposit(' . $deposit['id'] . ')" 
                    title="Reject">
                <i class="fas fa-times"></i>
            </button>';
        }
        
        $buttons .= '</div>';
        
        return $buttons;
    }
    
    private function renderPagination($currentPage, $totalPages) {
        if ($totalPages <= 1) {
            return '';
        }
        
        $pagination = '<nav aria-label="Deposits pagination"><ul class="pagination justify-content-center">';
        
        // Previous button
        if ($currentPage > 1) {
            $pagination .= '<li class="page-item"><a class="page-link" href="?page=' . ($currentPage - 1) . '">Previous</a></li>';
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $active = $i === $currentPage ? 'active' : '';
            $pagination .= '<li class="page-item ' . $active . '"><a class="page-link" href="?page=' . $i . '">' . $i . '</a></li>';
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $pagination .= '<li class="page-item"><a class="page-link" href="?page=' . ($currentPage + 1) . '">Next</a></li>';
        }
        
        $pagination .= '</ul></nav>';
        
        return '
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    ' . $pagination . '
                </div>
            </div>
        </div>';
    }
    
    private function renderModals($csrfToken) {
        return '
        <!-- Deposit Details Modal -->
        <div class="modal fade" id="depositDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Deposit Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="depositDetailsContent">
                        <!-- Content loaded via AJAX -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Approve Deposit Modal -->
        <div class="modal fade" id="approveDepositModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Approve Deposit</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="approveDepositForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="deposit_id" id="approveDepositId">
                            
                            <div class="mb-3">
                                <label class="form-label">Deposit Amount</label>
                                <input type="text" class="form-control" id="approveDepositAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Bonus Amount</label>
                                <input type="number" class="form-control" name="bonus_amount" 
                                       id="approveBonusAmount" step="0.01" min="0">
                                <small class="form-text text-muted">Leave empty to use calculated bonus</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Admin Note (Optional)</label>
                                <textarea class="form-control" name="admin_note" rows="3" 
                                          placeholder="Add any notes about this approval..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> Approve Deposit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Reject Deposit Modal -->
        <div class="modal fade" id="rejectDepositModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Deposit</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="rejectDepositForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="deposit_id" id="rejectDepositId">
                            
                            <div class="mb-3">
                                <label class="form-label">Deposit Amount</label>
                                <input type="text" class="form-control" id="rejectDepositAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Reason for Rejection <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="reason" rows="4" required
                                          placeholder="Please provide a reason for rejecting this deposit..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times"></i> Reject Deposit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Bulk Actions Modal -->
        <div class="modal fade" id="bulkActionsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bulk Actions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="bulkActionsForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            
                            <div class="mb-3">
                                <label class="form-label">Action</label>
                                <select class="form-select" name="action" required>
                                    <option value="">Select action...</option>
                                    <option value="approve">Approve Selected</option>
                                    <option value="reject">Reject Selected</option>
                                </select>
                            </div>
                            
                            <div class="mb-3" id="bulkReasonField" style="display: none;">
                                <label class="form-label">Reason (for rejection)</label>
                                <textarea class="form-control" name="reason" rows="3"></textarea>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <span id="selectedCount">0</span> deposits selected
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Execute Action</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    protected function getAdditionalCSS() {
        return '
        <link href="../../assets/css/admin-deposit-management.css" rel="stylesheet">
        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            
            .table th {
                border-top: none;
                font-weight: 600;
                color: #495057;
            }
            
            .btn-group-sm > .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
            
            .card {
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                border: 1px solid rgba(0, 0, 0, 0.125);
            }
            
            .table-responsive {
                border-radius: 0.375rem;
            }
            
            .pagination {
                margin-bottom: 0;
            }
            
            .form-check-input:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
        </style>';
    }
    
    protected function getAdditionalJS() {
        return '
        <script src="../../assets/js/admin-deposit-management.js"></script>';
    }
}
?>