<?php
/**
 * Test Payment Methods Functionality
 */

require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/PaymentMethod.php';

echo "<h2>Testing Payment Methods Functionality</h2>\n";

try {
    // Test 1: Create a new payment method
    echo "<h3>Test 1: Creating Payment Method</h3>\n";
    
    $paymentMethod = new PaymentMethod();
    $paymentMethod->name = 'Test Bitcoin Wallet';
    $paymentMethod->type = 'crypto';
    $paymentMethod->details = [
        'address' => 'bc1qtest123456789',
        'network' => 'Bitcoin',
        'confirmations' => 3,
        'fee' => '0.0005 BTC'
    ];
    $paymentMethod->status = 'active';
    
    if ($paymentMethod->save()) {
        echo "✅ Payment method created successfully with ID: " . $paymentMethod->getId() . "\n";
    } else {
        echo "❌ Failed to create payment method\n";
    }
    
    // Test 2: Retrieve payment method
    echo "<h3>Test 2: Retrieving Payment Method</h3>\n";
    
    $retrieved = PaymentMethod::find($paymentMethod->getId());
    if ($retrieved) {
        echo "✅ Payment method retrieved successfully\n";
        echo "Name: " . $retrieved->name . "\n";
        echo "Type: " . $retrieved->type . "\n";
        echo "Status: " . $retrieved->status . "\n";
        
        $details = $retrieved->getDetailsArray();
        echo "Details: " . json_encode($details) . "\n";
        
        $formatted = $retrieved->getFormattedDetails();
        echo "Formatted Details: " . json_encode($formatted) . "\n";
    } else {
        echo "❌ Failed to retrieve payment method\n";
    }
    
    // Test 3: Update payment method
    echo "<h3>Test 3: Updating Payment Method</h3>\n";
    
    $retrieved->name = 'Updated Bitcoin Wallet';
    $retrieved->setDetail('fee', '0.001 BTC');
    
    if ($retrieved->save()) {
        echo "✅ Payment method updated successfully\n";
        echo "New name: " . $retrieved->name . "\n";
        echo "New fee: " . $retrieved->getDetail('fee') . "\n";
    } else {
        echo "❌ Failed to update payment method\n";
    }
    
    // Test 4: Test validation
    echo "<h3>Test 4: Testing Validation</h3>\n";
    
    $invalidMethod = new PaymentMethod();
    $invalidMethod->name = ''; // Empty name should fail
    $invalidMethod->type = 'invalid_type'; // Invalid type should fail
    
    $errors = $invalidMethod->validate();
    if (!empty($errors)) {
        echo "✅ Validation working correctly. Errors found:\n";
        foreach ($errors as $field => $error) {
            echo "- {$field}: {$error}\n";
        }
    } else {
        echo "❌ Validation not working - no errors found for invalid data\n";
    }
    
    // Test 5: Test static methods
    echo "<h3>Test 5: Testing Static Methods</h3>\n";
    
    $activeMethods = PaymentMethod::getActiveMethods();
    echo "✅ Active methods count: " . count($activeMethods) . "\n";
    
    $orderedMethods = PaymentMethod::getOrderedMethods();
    echo "✅ Ordered methods count: " . count($orderedMethods) . "\n";
    
    $statistics = PaymentMethod::getStatistics();
    echo "✅ Statistics: " . json_encode($statistics) . "\n";
    
    // Test 6: Test type-specific methods
    echo "<h3>Test 6: Testing Type-Specific Methods</h3>\n";
    
    echo "Type display name: " . $retrieved->getTypeDisplayName() . "\n";
    echo "Type icon class: " . $retrieved->getTypeIconClass() . "\n";
    
    // Test 7: Test status methods
    echo "<h3>Test 7: Testing Status Methods</h3>\n";
    
    echo "Is active: " . ($retrieved->isActive() ? 'Yes' : 'No') . "\n";
    
    if ($retrieved->deactivate()) {
        echo "✅ Payment method deactivated successfully\n";
        echo "Is active after deactivation: " . ($retrieved->isActive() ? 'Yes' : 'No') . "\n";
    }
    
    if ($retrieved->activate()) {
        echo "✅ Payment method activated successfully\n";
        echo "Is active after activation: " . ($retrieved->isActive() ? 'Yes' : 'No') . "\n";
    }
    
    // Cleanup: Delete test payment method
    echo "<h3>Cleanup: Deleting Test Payment Method</h3>\n";
    
    if ($retrieved->delete()) {
        echo "✅ Test payment method deleted successfully\n";
    } else {
        echo "❌ Failed to delete test payment method\n";
    }
    
    echo "<h3>All Tests Completed!</h3>\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>