<?php
/**
 * Base View Class
 * Implements Template Method pattern for consistent HTML structure
 */
abstract class BaseView {
    protected $title;
    protected $data;
    protected $cssFiles = [];
    protected $jsFiles = [];
    protected $inlineStyles = [];
    protected $inlineScripts = [];
    
    public function __construct($title = '', $data = []) {
        $this->title = $title;
        $this->data = $data;
        $this->init();
    }
    
    /**
     * Set page title
     */
    public function setTitle($title) {
        $this->title = $title;
        return $this;
    }
    
    /**
     * Add CSS file with versioning
     */
    public function addCss($file, $version = null) {
        $version = $version ?? filemtime($_SERVER['DOCUMENT_ROOT'] . $file);
        $this->cssFiles[] = $file . '?v=' . $version;
        return $this;
    }
    
    /**
     * Add JS file with versioning
     */
    public function addJs($file, $version = null) {
        $version = $version ?? filemtime($_SERVER['DOCUMENT_ROOT'] . $file);
        $this->jsFiles[] = $file . '?v=' . $version;
        return $this;
    }
    
    /**
     * Add inline CSS
     */
    public function addInlineStyle($css) {
        $this->inlineStyles[] = $css;
        return $this;
    }
    
    /**
     * Add inline JavaScript
     */
    public function addInlineScript($js) {
        $this->inlineScripts[] = $js;
        return $this;
    }
    
    /**
     * Initialize view-specific assets
     */
    protected function init() {
        // Override in child classes
    }
    
    /**
     * Render the complete view
     */
    public function render() {
        try {
            ob_start();
            $this->renderHead();
            $this->renderBody();
            $this->renderScripts();
            return ob_get_clean();
        } catch (Exception $e) {
            ob_end_clean();
            error_log("View rendering error: " . $e->getMessage());
            return $this->renderErrorPage($e);
        }
    }
    
    /**
     * Render error page
     */
    protected function renderErrorPage($exception) {
        ob_start();
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error - <?php echo SITE_NAME ?? 'Crypto Trading Platform'; ?></title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        </head>
        <body>
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                <h4>Something went wrong</h4>
                                <p class="text-muted">We're sorry, but there was an error loading this page.</p>
                                <?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
                                    <div class="alert alert-danger text-start">
                                        <strong>Debug Info:</strong><br>
                                        <?php echo htmlspecialchars($exception->getMessage()); ?>
                                    </div>
                                <?php endif; ?>
                                <a href="/" class="btn btn-primary">Go Home</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        return ob_get_clean();
    }
    
    /**
     * Render HTML head section
     */
    protected function renderHead() {
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title><?php echo htmlspecialchars($this->getPageTitle()); ?></title>
            
            <!-- CSRF Token -->
            <?php if (class_exists('CSRFProtection')): ?>
                <meta name="csrf-token" content="<?php echo CSRFProtection::generateToken(); ?>">
            <?php endif; ?>
            
            <!-- Bootstrap CSS -->
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            
            <?php foreach ($this->cssFiles as $css): ?>
                <link href="<?php echo htmlspecialchars($css); ?>" rel="stylesheet">
            <?php endforeach; ?>
            
            <?php $this->renderCustomStyles(); ?>
        </head>
        <?php
    }
    
    /**
     * Render body content
     */
    protected function renderBody() {
        echo '<body>';
        $this->renderContent();
        echo '</body>';
    }
    
    /**
     * Render JavaScript files
     */
    protected function renderScripts() {
        ?>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        
        <?php foreach ($this->jsFiles as $js): ?>
            <script src="<?php echo htmlspecialchars($js); ?>"></script>
        <?php endforeach; ?>
        
        <?php $this->renderCustomScripts(); ?>
        </html>
        <?php
    }
    
    /**
     * Get page title
     */
    protected function getPageTitle() {
        $siteName = SITE_NAME ?? 'Crypto Trading Platform';
        return $this->title ? $this->title . ' - ' . $siteName : $siteName;
    }
    
    /**
     * Abstract methods to be implemented by child classes
     */
    abstract protected function renderContent();
    
    /**
     * Optional methods for customization
     */
    protected function renderCustomStyles() {
        if (!empty($this->inlineStyles)) {
            echo '<style>';
            foreach ($this->inlineStyles as $style) {
                echo $style . "\n";
            }
            echo '</style>';
        }
    }
    
    protected function renderCustomScripts() {
        if (!empty($this->inlineScripts)) {
            echo '<script>';
            foreach ($this->inlineScripts as $script) {
                echo $script . "\n";
            }
            echo '</script>';
        }
    }
}
?>