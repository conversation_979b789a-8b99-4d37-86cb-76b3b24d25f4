<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = getDB();
    
    // Read and execute the migration
    $migration = file_get_contents(__DIR__ . '/migrations/update_audit_logs_structure.sql');
    
    // Split by semicolons and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $migration)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            $db->exec($statement);
        }
    }
    
    echo "Migration completed successfully\n";
    
} catch (Exception $e) {
    echo "Migration error: " . $e->getMessage() . "\n";
    exit(1);
}
?>