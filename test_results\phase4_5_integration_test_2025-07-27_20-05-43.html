<!DOCTYPE html>
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Test Results - 2025-07-27 20:05:43</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
            <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
            <style>
                .test-pass { color: #28a745; }
                .test-fail { color: #dc3545; }
                .test-skip { color: #ffc107; }
                .test-error { color: #fd7e14; }
                .suite-header { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .test-item { padding: 10px; border-left: 4px solid #e9ecef; margin: 5px 0; }
                .test-item.pass { border-left-color: #28a745; }
                .test-item.fail { border-left-color: #dc3545; }
                .test-item.skip { border-left-color: #ffc107; }
                .test-item.error { border-left-color: #fd7e14; }
            </style>
        </head>
        <body class='bg-light'>
            <div class='container mt-4'>
                <h1><i class='fas fa-vial me-2'></i>Test Results</h1>
                
                <div class='row mb-4'>
                    <div class='col-md-3'>
                        <div class='card bg-success text-white'>
                            <div class='card-body text-center'>
                                <h3>22</h3>
                                <p class='mb-0'>Passed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-danger text-white'>
                            <div class='card-body text-center'>
                                <h3>1</h3>
                                <p class='mb-0'>Failed</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-warning text-white'>
                            <div class='card-body text-center'>
                                <h3>0</h3>
                                <p class='mb-0'>Skipped</p>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-3'>
                        <div class='card bg-info text-white'>
                            <div class='card-body text-center'>
                                <h3>0</h3>
                                <p class='mb-0'>Errors</p>
                            </div>
                        </div>
                    </div>
                </div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Landing Page Tests (integration)</h4>
                    <small class='text-muted'>
                        Passed: 5 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Landing Page File Exists</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.36ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>About Page Exists</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.28ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Contact Page Exists</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.29ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Terms and Privacy Pages Exist</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.37ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Landing Page CSS Exists</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.35ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Cache Service Tests (system)</h4>
                    <small class='text-muted'>
                        Passed: 4 | Failed: 1 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Cache Service Initialization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>6.98ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Cache Set and Get</strong>
                    <span class='float-end'>
                        <small class='text-muted'>6.14ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Cache Expiration</strong>
                    <span class='float-end'>
                        <small class='text-muted'>2016.89ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item fail'>
                    <i class='fas fa-times-circle test-fail me-2'></i>
                    <strong>Cache Namespaces</strong>
                    <span class='float-end'>
                        <small class='text-muted'>77.94ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Namespace 1 data should be correct (Expected: 'data1', Actual: NULL); Namespace 2 data should be correct (Expected: 'data2', Actual: NULL)</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Cache Statistics</strong>
                    <span class='float-end'>
                        <small class='text-muted'>2.07ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Database Optimization Tests (system)</h4>
                    <small class='text-muted'>
                        Passed: 5 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Database Optimization Service Initialization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.15ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Query Performance Logging</strong>
                    <span class='float-end'>
                        <small class='text-muted'>10.27ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Database Size Information</strong>
                    <span class='float-end'>
                        <small class='text-muted'>283.85ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Table Performance Analysis</strong>
                    <span class='float-end'>
                        <small class='text-muted'>1.86ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Index Optimization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>5.95ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>Backup Service Tests (system)</h4>
                    <small class='text-muted'>
                        Passed: 5 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Backup Service Initialization</strong>
                    <span class='float-end'>
                        <small class='text-muted'>5.82ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Database Backup Creation</strong>
                    <span class='float-end'>
                        <small class='text-muted'>219.16ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Backup Listing</strong>
                    <span class='float-end'>
                        <small class='text-muted'>1.13ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Backup Statistics</strong>
                    <span class='float-end'>
                        <small class='text-muted'>0.62ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Automated Backup Scheduling</strong>
                    <span class='float-end'>
                        <small class='text-muted'>2.54ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div><div class='card mb-4'>
                <div class='card-header'>
                    <h4><i class='fas fa-folder me-2'></i>System Integration Tests (integration)</h4>
                    <small class='text-muted'>
                        Passed: 3 | Failed: 0 | 
                        Skipped: 0 | Errors: 0
                    </small>
                </div>
                <div class='card-body'><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Cache and Database Integration</strong>
                    <span class='float-end'>
                        <small class='text-muted'>4.93ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>System Performance Monitoring</strong>
                    <span class='float-end'>
                        <small class='text-muted'>2.54ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div><div class='test-item pass'>
                    <i class='fas fa-check-circle test-pass me-2'></i>
                    <strong>Error Handling Integration</strong>
                    <span class='float-end'>
                        <small class='text-muted'>1.68ms</small>
                    </span>
                    <br>
                    <small class='text-muted'>Test passed</small>
                </div></div></div></div>
        <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
        </body>
        </html>