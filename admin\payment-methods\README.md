# Payment Methods Management Interface

This interface allows administrators to configure and manage payment methods for user deposits. The system supports flexible payment method configuration with different types and custom details.

## Features

### Payment Method Types
- **Cryptocurrency**: Bitcoin, Ethereum, and other crypto wallets
- **Bank Transfer**: Traditional bank account details
- **PayPal**: PayPal account configuration
- **Other**: Custom payment methods with flexible fields

### Key Functionality
- ✅ Create, edit, and delete payment methods
- ✅ Activate/deactivate payment methods
- ✅ Drag-and-drop sorting for display order
- ✅ View detailed payment method information
- ✅ Track usage statistics (deposits, amounts)
- ✅ Flexible field configuration per payment type
- ✅ Real-time form validation
- ✅ Responsive design for mobile and desktop

## Payment Method Configuration

### Cryptocurrency Fields
- **Wallet Address**: The receiving wallet address
- **Network**: Blockchain network (Bitcoin, Ethereum, etc.)
- **Required Confirmations**: Number of confirmations needed
- **Network Fee**: Transaction fee information

### Bank Transfer Fields
- **Account Name**: Account holder name
- **Account Number**: Bank account number
- **Bank Name**: Name of the bank
- **Routing Number**: Bank routing number (optional)
- **SWIFT Code**: International SWIFT code (optional)
- **Branch**: Bank branch information (optional)

### PayPal Fields
- **PayPal Email**: PayPal account email address
- **Fee Percentage**: Transaction fee percentage (optional)

### Other Payment Methods
- **Payment Instructions**: Detailed instructions for users
- **Contact Information**: Contact details if needed

## Usage Statistics

The interface displays comprehensive statistics for each payment method:
- Total number of deposits
- Total deposit amount
- Pending deposits count
- Usage trends and analytics

## Security Features

- CSRF protection on all forms
- Admin authentication required
- Input validation and sanitization
- Audit logging for all changes
- Safe deletion (prevents deletion if deposits exist)

## API Endpoints

- `POST /api/create.php` - Create new payment method
- `POST /api/update.php` - Update existing payment method
- `GET /api/details.php` - Get payment method details
- `POST /api/toggle-status.php` - Activate/deactivate payment method
- `POST /api/delete.php` - Delete payment method
- `POST /api/update-sort-order.php` - Update display order
- `GET /api/list.php` - Refresh payment methods list

## User Integration

When users make deposits, they can:
1. Select from active payment methods
2. View payment details and instructions
3. Upload proof of payment
4. Track deposit status

The payment methods configured here are automatically available in the user deposit interface.

## Technical Implementation

### Database Schema
```sql
CREATE TABLE payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type ENUM('crypto', 'bank', 'paypal', 'other') NOT NULL,
    details JSON NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Model Features
- Active Record pattern with BaseModel inheritance
- JSON field handling for flexible details storage
- Validation rules for each payment type
- Relationship methods for deposits
- Statistics and reporting methods

### Frontend Features
- Bootstrap 5 responsive design
- SortableJS for drag-and-drop ordering
- Dynamic form fields based on payment type
- Real-time validation and error handling
- Modal-based editing interface

## Best Practices

1. **Always test payment methods** after configuration
2. **Keep payment details up to date** to avoid failed deposits
3. **Monitor usage statistics** to optimize payment options
4. **Use clear, descriptive names** for payment methods
5. **Provide detailed instructions** for manual payment methods
6. **Regularly review and update** inactive payment methods

## Troubleshooting

### Common Issues
- **Payment method not showing for users**: Check if status is "Active"
- **Deposits failing**: Verify payment method details are correct
- **Sort order not updating**: Check browser console for JavaScript errors
- **Form validation errors**: Ensure all required fields are filled

### Error Handling
The system includes comprehensive error handling:
- Database connection errors
- Validation failures
- CSRF token mismatches
- Permission denied errors
- Network connectivity issues

All errors are logged for debugging and user-friendly messages are displayed.