<?php
require_once __DIR__ . '/../classes/views/UserLayoutHelper.php';

try {
    $layoutHelper = new UserLayoutHelper();
    $layoutHelper->validateAccess();
    
    $siteConfig = $layoutHelper->getSiteConfig();
    $themeColors = $layoutHelper->getThemeColors();
    $user = $layoutHelper->getUser();
    $navigationItems = $layoutHelper->getNavigationItems();
    $userBalance = $layoutHelper->getUserBalance();
    
} catch (Exception $e) {
    error_log('User Layout Error: ' . $e->getMessage());
    redirectTo('login.php');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <title><?php echo $layoutHelper->getPageTitle($pageTitle ?? null); ?></title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="<?php echo getBaseUrl(); ?>assets/css/user-layout.css" as="style">
    <link rel="preload" href="<?php echo getBaseUrl(); ?>assets/js/user-layout.js" as="script">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?php echo getBaseUrl(); ?>assets/css/user-layout.css" rel="stylesheet">
    
    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            <?php echo $layoutHelper->generateCSSVariables(); ?>
        }
    </style>
    
    <?php if (isset($additionalCSS)): ?>
        <?php echo $additionalCSS; ?>
    <?php endif; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <!-- Logo -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <img src="<?php echo htmlspecialchars($siteConfig['logo']); ?>" 
                             alt="<?php echo htmlspecialchars($siteConfig['name']); ?>" 
                             class="img-fluid" style="max-height: 60px;">
                        <h5 class="text-dark mt-2 mb-1"><?php echo htmlspecialchars($siteConfig['name']); ?></h5>
                    </div>
                    
                    <!-- User Info -->
                    <div class="text-center mb-4 pb-3 border-bottom">
                        <div class="user-avatar mx-auto mb-2">
                            <?php echo $layoutHelper->getUserInitials(); ?>
                        </div>
                        <small class="text-muted">Welcome back,</small>
                        <div class="text-dark fw-bold"><?php echo htmlspecialchars($user['first_name']); ?></div>
                    </div>
                    
                    <!-- Navigation -->
                    <ul class="nav flex-column">
                        <?php foreach ($navigationItems as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $item['active'] ? 'active' : ''; ?>" href="<?php echo htmlspecialchars($item['url']); ?>">
                                <i class="<?php echo htmlspecialchars($item['icon']); ?> me-2"></i>
                                <?php echo htmlspecialchars($item['label']); ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                        <li class="nav-item mt-3">
                            <a class="nav-link text-danger" href="<?php echo url('logout.php'); ?>">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top navbar for mobile -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom d-md-none">
                    <button class="btn btn-outline-primary" type="button" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="h4 mb-0"><?php echo isset($pageTitle) ? $pageTitle : 'Dashboard'; ?></h1>
                    <div class="user-avatar">
                        <?php echo $layoutHelper->getUserInitials(); ?>
                    </div>
                </div>
                
                <!-- Page content -->
                <div class="content-wrapper">
                    <?php if (isset($content)): ?>
                        <?php echo $content; ?>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Theme Colors for JavaScript -->
    <script>
        window.themeColors = <?php echo json_encode($themeColors); ?>;
        window.userBalance = <?php echo json_encode($userBalance); ?>;
        window.csrfToken = '<?php echo generateCSRFToken(); ?>';
    </script>
    
    <!-- Custom JS -->
    <script src="<?php echo getBaseUrl(); ?>assets/js/user-layout.js"></script>
    
    <?php if (isset($additionalJS)): ?>
        <?php echo $additionalJS; ?>
    <?php endif; ?>
</body>
</html>