<?php
require_once 'config.php';
require_once 'includes/functions.php';

echo "<h2>Dynamic URL Test</h2>";

echo "<h3>Base URL Functions:</h3>";
echo "getBaseUrl(): " . getBaseUrl() . "<br>";
echo "url('admin/dashboard/'): " . url('admin/dashboard/') . "<br>";
echo "asset('css/style.css'): " . asset('css/style.css') . "<br>";

echo "<h3>Test Navigation Links:</h3>";
echo '<a href="' . url('user/dashboard/') . '">User Dashboard</a><br>';
echo '<a href="' . url('admin/dashboard/') . '">Admin Dashboard</a><br>';
echo '<a href="' . url('superadmin/dashboard/') . '">Super Admin Dashboard</a><br>';
echo '<a href="' . url('superadmin/appearance/') . '">Super Admin Appearance</a><br>';

echo "<h3>Test Asset Links:</h3>";
echo '<img src="' . asset('images/logo.png') . '" alt="Logo" style="max-height: 50px;"><br>';
echo '<link rel="stylesheet" href="' . asset('css/user-layout.css') . '"><br>';
echo '<script src="' . asset('js/user-layout.js') . '"></script><br>';

echo "<h3>Environment Detection:</h3>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'Not set') . "<br>";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "<br>";

echo "<h3>Configuration:</h3>";
echo "BASE_URL: " . BASE_URL . "<br>";
echo "AUTO_BASE_URL: " . (defined('AUTO_BASE_URL') ? AUTO_BASE_URL : 'Not defined') . "<br>";
?>