<?php
require_once '../../includes/functions.php';
require_once '../../classes/services/SessionManager.php';
require_once '../../classes/services/TransactionManager.php';
require_once '../../classes/services/FinancialService.php';
require_once '../../classes/views/WithdrawalRequestView.php';

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    header('Location: ../../login.php');
    exit;
}

$errors = [];
$success = false;
$withdrawalData = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // CSRF protection
    if (!hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'] ?? '')) {
        $errors['general'] = 'Invalid request. Please try again.';
    } else {
        $withdrawalData = [
            'amount' => (float)($_POST['amount'] ?? 0),
            'withdrawal_method' => trim($_POST['withdrawal_method'] ?? ''),
            'account_details' => $_POST['account_details'] ?? []
        ];
        
        // Create withdrawal request
        $result = FinancialService::createWithdrawal($user->getId(), $withdrawalData);
        
        if ($result['success']) {
            $success = true;
            $_SESSION['success_message'] = 'Withdrawal request submitted successfully. It will be reviewed by our team.';
            header('Location: status.php');
            exit;
        } else {
            $errors = $result['errors'];
        }
    }
}

// Get user's withdrawal eligibility
$eligibilityCheck = Withdrawal::canUserWithdraw($user->getId(), 1); // Check with minimal amount
$canWithdraw = $eligibilityCheck['can_withdraw'];
$withdrawalMessage = $eligibilityCheck['reason'];

// Get minimum withdrawal amount
$minimumAmount = Withdrawal::getMinimumAmount();

// Get available withdrawal methods
$withdrawalMethods = PaymentMethod::getActiveWithdrawalMethods();

// Get user's recent withdrawals
$recentWithdrawals = Withdrawal::getByUser($user->getId(), 5);

// Render view
$view = new WithdrawalRequestView();
$view->render([
    'user' => $user,
    'errors' => $errors,
    'success' => $success,
    'withdrawal_data' => $withdrawalData,
    'can_withdraw' => $canWithdraw,
    'withdrawal_message' => $withdrawalMessage,
    'minimum_amount' => $minimumAmount,
    'withdrawal_methods' => $withdrawalMethods,
    'recent_withdrawals' => $recentWithdrawals,
    'csrf_token' => $_SESSION['csrf_token'] ?? ''
]);
?>