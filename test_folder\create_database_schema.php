<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';

echo "<h2>Database Schema Creation</h2>";

try {
    $db = getDB();
    
    // Users table
    $sql_users = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
        last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
        phone VARCHAR(20),
        role ENUM('user', 'admin', 'superadmin') DEFAULT 'user',
        status ENUM('active', 'suspended', 'pending') DEFAULT 'pending',
        balance DECIMAL(15,2) DEFAULT 0.00,
        bonus DECIMAL(15,2) DEFAULT 0.00,
        total_deposit DECIMAL(15,2) DEFAULT 0.00,
        total_withdrawal DECIMAL(15,2) DEFAULT 0.00,
        kyc_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified',
        two_fa_enabled BOOLEAN DEFAULT FALSE,
        two_fa_secret VARCHAR(32),
        email_verified BOOLEAN DEFAULT FALSE,
        email_verification_token VARCHAR(64),
        password_reset_token VARCHAR(64),
        password_reset_expires DATETIME,
        profile_picture VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Trading plans table
    $sql_plans = "CREATE TABLE IF NOT EXISTS trading_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        min_deposit DECIMAL(15,2) NOT NULL,
        max_deposit DECIMAL(15,2),
        daily_return DECIMAL(5,2) NOT NULL,
        duration_days INT NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Deposits table
    $sql_deposits = "CREATE TABLE IF NOT EXISTS deposits (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        plan_id INT,
        amount DECIMAL(15,2) NOT NULL,
        bonus_amount DECIMAL(15,2) DEFAULT 0.00,
        payment_method VARCHAR(50) NOT NULL,
        transaction_id VARCHAR(100),
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        proof_of_payment VARCHAR(255),
        admin_note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (plan_id) REFERENCES trading_plans(id) ON DELETE SET NULL
    )";
    
    // Withdrawals table
    $sql_withdrawals = "CREATE TABLE IF NOT EXISTS withdrawals (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        withdrawal_method VARCHAR(50) NOT NULL,
        account_details TEXT NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        admin_note TEXT,
        processed_at DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    // Transactions table
    $sql_transactions = "CREATE TABLE IF NOT EXISTS transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('deposit', 'withdrawal', 'bonus', 'trade', 'transfer') NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        balance_before DECIMAL(15,2) NOT NULL,
        balance_after DECIMAL(15,2) NOT NULL,
        description TEXT,
        reference_id VARCHAR(100),
        status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    // Support tickets table
    $sql_tickets = "CREATE TABLE IF NOT EXISTS support_tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        subject VARCHAR(200) NOT NULL,
        message TEXT NOT NULL,
        status ENUM('pending', 'answered', 'closed') DEFAULT 'pending',
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        admin_reply TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    // System settings table
    $sql_settings = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Payment methods table
    $sql_payment_methods = "CREATE TABLE IF NOT EXISTS payment_methods (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        type ENUM('crypto', 'bank', 'paypal', 'other') NOT NULL,
        details TEXT NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    // Audit logs table
    $sql_audit_logs = "CREATE TABLE IF NOT EXISTS audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(50),
        record_id INT,
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )";
    
    // Execute all table creation queries
    $tables = [
        'users' => $sql_users,
        'trading_plans' => $sql_plans,
        'deposits' => $sql_deposits,
        'withdrawals' => $sql_withdrawals,
        'transactions' => $sql_transactions,
        'support_tickets' => $sql_tickets,
        'system_settings' => $sql_settings,
        'payment_methods' => $sql_payment_methods,
        'audit_logs' => $sql_audit_logs
    ];
    
    foreach ($tables as $table_name => $sql) {
        if ($db->exec($sql) !== false) {
            echo "<p style='color: green;'>✓ Table '{$table_name}' created successfully!</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create table '{$table_name}'!</p>";
        }
    }
    
    // Insert default data
    echo "<h3>Inserting Default Data</h3>";
    
    // Default super admin user
    $default_password = hashPassword('admin123');
    $sql_admin = "INSERT IGNORE INTO users (username, email, password, first_name, last_name, role, status, email_verified) 
                  VALUES ('superadmin', '<EMAIL>', ?, 'Super', 'Admin', 'superadmin', 'active', 1)";
    $stmt = $db->prepare($sql_admin);
    if ($stmt->execute([$default_password])) {
        echo "<p style='color: green;'>✓ Default super admin created (username: superadmin, password: admin123)</p>";
    }
    
    // Default trading plans
    $plans = [
        ['BASIC PLAN', 3000, 5000, 2.5, 30, 'Basic trading plan with daily returns'],
        ['GOLD PLAN', 5000, 10000, 3.5, 30, 'Gold trading plan with higher returns'],
        ['VIP PLAN', 10000, 50000, 5.0, 30, 'VIP trading plan with premium returns']
    ];
    
    $sql_plan = "INSERT IGNORE INTO trading_plans (name, min_deposit, max_deposit, daily_return, duration_days, description) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt_plan = $db->prepare($sql_plan);
    
    foreach ($plans as $plan) {
        if ($stmt_plan->execute($plan)) {
            echo "<p style='color: green;'>✓ Trading plan '{$plan[0]}' created!</p>";
        }
    }
    
    // Default payment methods
    $payment_methods = [
        ['Bitcoin', 'crypto', '{"address": "**********************************", "network": "BTC"}'],
        ['Ethereum', 'crypto', '{"address": "******************************************", "network": "ETH"}'],
        ['PayPal', 'paypal', '{"email": "<EMAIL>"}'],
        ['Bank Transfer', 'bank', '{"account_name": "Coinage Trading", "account_number": "**********", "bank_name": "Sample Bank"}']
    ];
    
    $sql_payment = "INSERT IGNORE INTO payment_methods (name, type, details) VALUES (?, ?, ?)";
    $stmt_payment = $db->prepare($sql_payment);
    
    foreach ($payment_methods as $method) {
        if ($stmt_payment->execute($method)) {
            echo "<p style='color: green;'>✓ Payment method '{$method[0]}' created!</p>";
        }
    }
    
    echo "<hr>";
    echo "<p style='color: blue;'><strong>Database schema created successfully!</strong></p>";
    echo "<p>You can now proceed with creating the layout system and authentication.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

?>