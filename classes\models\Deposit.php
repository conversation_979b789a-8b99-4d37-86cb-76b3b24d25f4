<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * Deposit Model - Handles deposit data and operations
 */
class Deposit extends BaseModel {
    protected $table = 'deposits';
    protected $fillable = [
        'user_id', 'plan_id', 'amount', 'bonus_amount', 'payment_method_id',
        'transaction_id', 'status', 'proof_of_payment', 'admin_note',
        'approved_by', 'approved_at'
    ];
    
    // Deposit statuses
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // User ID validation
        if (empty($this->user_id)) {
            $errors['user_id'] = 'User ID is required';
        } elseif (!is_numeric($this->user_id)) {
            $errors['user_id'] = 'User ID must be numeric';
        }
        
        // Amount validation
        if (empty($this->amount)) {
            $errors['amount'] = 'Amount is required';
        } elseif (!is_numeric($this->amount)) {
            $errors['amount'] = 'Amount must be numeric';
        } elseif ($this->amount <= 0) {
            $errors['amount'] = 'Amount must be greater than zero';
        } elseif ($this->amount > 999999999.99) {
            $errors['amount'] = 'Amount is too large';
        }
        
        // Bonus amount validation
        if (isset($this->bonus_amount)) {
            if (!is_numeric($this->bonus_amount)) {
                $errors['bonus_amount'] = 'Bonus amount must be numeric';
            } elseif ($this->bonus_amount < 0) {
                $errors['bonus_amount'] = 'Bonus amount cannot be negative';
            }
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [self::STATUS_PENDING, self::STATUS_APPROVED, self::STATUS_REJECTED])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Transaction ID validation
        if (!empty($this->transaction_id) && strlen($this->transaction_id) > 100) {
            $errors['transaction_id'] = 'Transaction ID must not exceed 100 characters';
        }
        
        // Plan validation (if plan_id is provided)
        if (!empty($this->plan_id) && !$this->isValidPlan()) {
            $errors['plan_id'] = 'Invalid trading plan';
        }
        
        // Payment method validation (if payment_method_id is provided)
        if (!empty($this->payment_method_id) && !$this->isValidPaymentMethod()) {
            $errors['payment_method_id'] = 'Invalid payment method';
        }
        
        return $errors;
    }
    
    /**
     * Check if plan ID is valid
     */
    private function isValidPlan() {
        $sql = "SELECT id FROM trading_plans WHERE id = :plan_id AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['plan_id' => $this->plan_id]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Check if payment method ID is valid
     */
    private function isValidPaymentMethod() {
        $sql = "SELECT id FROM payment_methods WHERE id = :payment_method_id AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['payment_method_id' => $this->payment_method_id]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Get the user who made this deposit
     */
    public function getUser() {
        require_once __DIR__ . '/User.php';
        return User::find($this->user_id);
    }
    
    /**
     * Get the trading plan for this deposit
     */
    public function getTradingPlan() {
        if (!$this->plan_id) {
            return null;
        }
        
        require_once __DIR__ . '/TradingPlan.php';
        return TradingPlan::find($this->plan_id);
    }
    
    /**
     * Get the payment method for this deposit
     */
    public function getPaymentMethod() {
        if (!$this->payment_method_id) {
            return null;
        }
        
        $sql = "SELECT * FROM payment_methods WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['id' => $this->payment_method_id]);
        return $stmt->fetch();
    }
    
    /**
     * Get the admin who approved this deposit
     */
    public function getApprovedBy() {
        if (!$this->approved_by) {
            return null;
        }
        
        require_once __DIR__ . '/User.php';
        return User::find($this->approved_by);
    }
    
    /**
     * Approve the deposit
     */
    public function approve($adminId, $bonusAmount = null) {
        $this->beginTransaction();
        
        try {
            // Update deposit status
            $this->status = self::STATUS_APPROVED;
            $this->approved_by = $adminId;
            $this->approved_at = date('Y-m-d H:i:s');
            
            // Calculate bonus if not provided
            if ($bonusAmount !== null) {
                $this->bonus_amount = $bonusAmount;
            } elseif ($this->bonus_amount === null) {
                $this->bonus_amount = $this->calculateBonus();
            }
            
            if (!$this->save()) {
                throw new Exception('Failed to update deposit');
            }
            
            // Update user balance and totals
            $user = $this->getUser();
            if (!$user) {
                throw new Exception('User not found');
            }
            
            $user->balance += $this->amount;
            $user->bonus += $this->bonus_amount;
            $user->total_deposit += $this->amount;
            
            if (!$user->save()) {
                throw new Exception('Failed to update user balance');
            }
            
            // Create transaction record
            $this->createTransaction($user, 'deposit');
            
            // Create bonus transaction if bonus amount > 0
            if ($this->bonus_amount > 0) {
                $this->createTransaction($user, 'bonus');
            }
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            error_log("Deposit approval error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reject the deposit
     */
    public function reject($adminId, $reason = null) {
        $this->status = self::STATUS_REJECTED;
        $this->approved_by = $adminId;
        $this->approved_at = date('Y-m-d H:i:s');
        
        if ($reason) {
            $this->admin_note = $reason;
        }
        
        return $this->save();
    }
    
    /**
     * Calculate bonus amount based on trading plan or system settings
     */
    public function calculateBonus() {
        // Get system bonus percentage (this would come from system settings)
        $bonusPercentage = 0; // Default 0%
        
        // Check if there's a trading plan with specific bonus
        $plan = $this->getTradingPlan();
        if ($plan && isset($plan['bonus_percentage'])) {
            $bonusPercentage = $plan['bonus_percentage'];
        } else {
            // Get from system settings
            $sql = "SELECT setting_value FROM system_settings WHERE setting_key = 'deposit_bonus_percentage'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result) {
                $bonusPercentage = (float) $result['setting_value'];
            }
        }
        
        return ($this->amount * $bonusPercentage) / 100;
    }
    
    /**
     * Create transaction record
     */
    private function createTransaction($user, $type) {
        require_once __DIR__ . '/Transaction.php';
        
        $transaction = new Transaction();
        $transaction->user_id = $user->getId();
        $transaction->type = $type;
        $transaction->reference_id = $this->getId();
        $transaction->reference_type = 'deposit';
        
        if ($type === 'deposit') {
            $transaction->amount = $this->amount;
            $transaction->balance_before = $user->balance - $this->amount - $this->bonus_amount;
            $transaction->balance_after = $user->balance - $this->bonus_amount;
            $transaction->description = "Deposit approved - Amount: $" . number_format($this->amount, 2);
        } else { // bonus
            $transaction->amount = $this->bonus_amount;
            $transaction->balance_before = $user->balance - $this->bonus_amount;
            $transaction->balance_after = $user->balance;
            $transaction->description = "Deposit bonus - Amount: $" . number_format($this->bonus_amount, 2);
        }
        
        $transaction->status = 'completed';
        $transaction->processed_by = $this->approved_by;
        
        return $transaction->save();
    }
    
    /**
     * Check if deposit is pending
     */
    public function isPending() {
        return $this->status === self::STATUS_PENDING;
    }
    
    /**
     * Check if deposit is approved
     */
    public function isApproved() {
        return $this->status === self::STATUS_APPROVED;
    }
    
    /**
     * Check if deposit is rejected
     */
    public function isRejected() {
        return $this->status === self::STATUS_REJECTED;
    }
    
    /**
     * Get total amount (deposit + bonus)
     */
    public function getTotalAmount() {
        return $this->amount + ($this->bonus_amount ?? 0);
    }
    
    /**
     * Get deposits by status
     */
    public static function getByStatus($status, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE status = :status ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $results = $stmt->fetchAll();
        
        $deposits = [];
        foreach ($results as $data) {
            $deposits[] = new static($data);
        }
        
        return $deposits;
    }
    
    /**
     * Get deposits by user
     */
    public static function getByUser($userId, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE user_id = :user_id ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        $results = $stmt->fetchAll();
        
        $deposits = [];
        foreach ($results as $data) {
            $deposits[] = new static($data);
        }
        
        return $deposits;
    }
    
    /**
     * Get pending deposits count
     */
    public static function getPendingCount() {
        return static::count("status = 'pending'");
    }
    
    /**
     * Get total deposits amount by status
     */
    public static function getTotalAmountByStatus($status) {
        $instance = new static();
        $sql = "SELECT COALESCE(SUM(amount), 0) as total FROM {$instance->table} WHERE status = :status";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
    
    /**
     * Get deposits with user and plan information
     */
    public static function getWithDetails($limit = null, $offset = 0, $status = null) {
        $instance = new static();
        $sql = "SELECT d.*, u.username, u.first_name, u.last_name, u.email, 
                       tp.name as plan_name, pm.name as payment_method_name
                FROM {$instance->table} d
                LEFT JOIN users u ON d.user_id = u.id
                LEFT JOIN trading_plans tp ON d.plan_id = tp.id
                LEFT JOIN payment_methods pm ON d.payment_method_id = pm.id";
        
        if ($status) {
            $sql .= " WHERE d.status = :status";
        }
        
        $sql .= " ORDER BY d.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $params = [];
        if ($status) {
            $params['status'] = $status;
        }
        
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get total deposits amount by user and status
     */
    public static function getTotalAmountByUser($userId, $status = null) {
        $instance = new static();
        $sql = "SELECT COALESCE(SUM(amount), 0) as total FROM {$instance->table} WHERE user_id = :user_id";
        
        $params = ['user_id' => $userId];
        
        if ($status) {
            $sql .= " AND status = :status";
            $params['status'] = $status;
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
    
    /**
     * Get deposit with full details including user, payment method, and trading plan
     */
    public static function getWithFullDetails($depositId) {
        $instance = new static();
        $sql = "SELECT d.*, 
                       u.username, u.first_name, u.last_name, u.email, u.balance as user_balance, u.bonus as user_bonus,
                       tp.name as plan_name, tp.minimum_amount as plan_minimum_amount, 
                       tp.maximum_amount as plan_maximum_amount, tp.bonus_percentage as plan_bonus_percentage,
                       pm.name as payment_method_name, pm.type as payment_method_type,
                       approver.username as approver_name, rejecter.username as rejecter_name
                FROM {$instance->table} d
                LEFT JOIN users u ON d.user_id = u.id
                LEFT JOIN trading_plans tp ON d.plan_id = tp.id
                LEFT JOIN payment_methods pm ON d.payment_method_id = pm.id
                LEFT JOIN users approver ON d.approved_by = approver.id
                LEFT JOIN users rejecter ON d.rejected_by = rejecter.id
                WHERE d.id = :id";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['id' => $depositId]);
        return $stmt->fetch();
    }
    

}
?>