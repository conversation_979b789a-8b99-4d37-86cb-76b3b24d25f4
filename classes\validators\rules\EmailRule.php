<?php
require_once __DIR__ . '/../ValidationRuleInterface.php';

/**
 * EmailRule - Validates email addresses
 */
class EmailRule implements ValidationRuleInterface {
    public function validate($value, array $parameters = []) {
        return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public function getErrorMessage($field, array $parameters = []) {
        return "Please enter a valid email address for " . ucfirst($field);
    }
}
?>