<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/models/TradingPlan.php';
require_once '../../classes/views/AdminTradingPlansView.php';

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: ../../login.php');
    exit;
}

try {
    // Get all trading plans with statistics
    $plansData = TradingPlan::getWithDepositStats();
    $statistics = TradingPlan::getStatistics();
    
    // Render the view
    $view = new AdminTradingPlansView();
    $view->render([
        'plans' => $plansData,
        'statistics' => $statistics,
        'user' => getCurrentUser()
    ]);
    
} catch (Exception $e) {
    error_log("Admin trading plans error: " . $e->getMessage());
    
    // Show error page
    $view = new AdminTradingPlansView();
    $view->render([
        'error' => 'Unable to load trading plans. Please try again.',
        'plans' => [],
        'statistics' => [],
        'user' => getCurrentUser()
    ]);
}
?>