<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/models/Deposit.php';
require_once '../../../classes/models/User.php';
require_once '../../../classes/models/Transaction.php';
require_once '../../../classes/services/TransactionManager.php';
require_once '../../../classes/services/NotificationService.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Verify CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit();
}

try {
    $action = $_POST['action'] ?? '';
    $depositIds = $_POST['deposit_ids'] ?? [];
    $reason = trim($_POST['reason'] ?? '');
    
    if (!in_array($action, ['approve', 'reject'])) {
        throw new Exception('Invalid action');
    }
    
    if (empty($depositIds) || !is_array($depositIds)) {
        throw new Exception('No deposits selected');
    }
    
    if ($action === 'reject' && empty($reason)) {
        throw new Exception('Rejection reason is required');
    }
    
    // Validate deposit IDs
    $depositIds = array_map('intval', $depositIds);
    $depositIds = array_filter($depositIds, function($id) { return $id > 0; });
    
    if (empty($depositIds)) {
        throw new Exception('Invalid deposit IDs');
    }
    
    $results = [
        'success' => [],
        'failed' => [],
        'total_processed' => 0
    ];
    
    $pdo = Database::getConnection();
    $transactionManager = new TransactionManager();
    $notificationService = new NotificationService();
    
    foreach ($depositIds as $depositId) {
        try {
            // Start individual transaction for each deposit
            $pdo->beginTransaction();
            
            // Get deposit details
            $deposit = Deposit::findById($depositId);
            if (!$deposit) {
                throw new Exception("Deposit {$depositId} not found");
            }
            
            if ($deposit->status !== 'pending') {
                throw new Exception("Deposit {$depositId} is not in pending status");
            }
            
            // Get user details
            $user = User::findById($deposit->user_id);
            if (!$user) {
                throw new Exception("User not found for deposit {$depositId}");
            }
            
            if ($action === 'approve') {
                // Approve deposit
                $bonusAmount = $deposit->calculateBonus();
                
                $deposit->status = 'approved';
                $deposit->approved_at = date('Y-m-d H:i:s');
                $deposit->approved_by = $_SESSION['user_id'];
                $deposit->bonus_amount = $bonusAmount;
                $deposit->admin_note = 'Bulk approval';
                
                if (!$deposit->save()) {
                    throw new Exception("Failed to update deposit {$depositId}");
                }
                
                // Update user balance
                $user->balance += $deposit->amount;
                if ($bonusAmount > 0) {
                    $user->bonus += $bonusAmount;
                }
                
                if (!$user->save()) {
                    throw new Exception("Failed to update user balance for deposit {$depositId}");
                }
                
                // Create transaction records
                $transactionManager->createTransaction([
                    'user_id' => $user->id,
                    'type' => 'deposit',
                    'amount' => $deposit->amount,
                    'status' => 'completed',
                    'reference_id' => $deposit->id,
                    'reference_type' => 'deposit',
                    'description' => 'Deposit approved (bulk action)',
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                if ($bonusAmount > 0) {
                    $transactionManager->createTransaction([
                        'user_id' => $user->id,
                        'type' => 'bonus',
                        'amount' => $bonusAmount,
                        'status' => 'completed',
                        'reference_id' => $deposit->id,
                        'reference_type' => 'deposit_bonus',
                        'description' => 'Deposit bonus (bulk action)',
                        'admin_id' => $_SESSION['user_id']
                    ]);
                }
                
                // Send notification
                try {
                    $notificationService->sendDepositApprovalNotification($user, $deposit);
                } catch (Exception $e) {
                    error_log("Failed to send approval notification for deposit {$depositId}: " . $e->getMessage());
                }
                
            } else { // reject
                $deposit->status = 'rejected';
                $deposit->rejected_at = date('Y-m-d H:i:s');
                $deposit->rejected_by = $_SESSION['user_id'];
                $deposit->rejection_reason = $reason;
                
                if (!$deposit->save()) {
                    throw new Exception("Failed to update deposit {$depositId}");
                }
                
                // Create transaction record
                $transactionManager->createTransaction([
                    'user_id' => $user->id,
                    'type' => 'deposit',
                    'amount' => $deposit->amount,
                    'status' => 'rejected',
                    'reference_id' => $deposit->id,
                    'reference_type' => 'deposit',
                    'description' => 'Deposit rejected (bulk action) - ' . $reason,
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                // Send notification
                try {
                    $notificationService->sendDepositRejectionNotification($user, $deposit, $reason);
                } catch (Exception $e) {
                    error_log("Failed to send rejection notification for deposit {$depositId}: " . $e->getMessage());
                }
            }
            
            $pdo->commit();
            $results['success'][] = $depositId;
            $results['total_processed']++;
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $results['failed'][] = [
                'deposit_id' => $depositId,
                'error' => $e->getMessage()
            ];
            error_log("Bulk action failed for deposit {$depositId}: " . $e->getMessage());
        }
    }
    
    // Log bulk action
    $successCount = count($results['success']);
    $failedCount = count($results['failed']);
    error_log("Admin {$_SESSION['user_id']} performed bulk {$action} on {$successCount} deposits ({$failedCount} failed)");
    
    echo json_encode([
        'success' => true,
        'message' => "Bulk {$action} completed: {$successCount} successful, {$failedCount} failed",
        'data' => $results
    ]);
    
} catch (Exception $e) {
    error_log("Bulk action error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>