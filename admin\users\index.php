<?php
session_start();

require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/controllers/AdminController.php';
require_once __DIR__ . '/../../classes/views/AdminUserManagementView.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: /login.php');
    exit();
}

try {
    // Get filters from request
    $filters = [
        'search' => $_GET['search'] ?? '',
        'status' => $_GET['status'] ?? '',
        'sort' => $_GET['sort'] ?? 'created_at'
    ];
    
    // Get pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 20;
    
    // Get users with filtering and pagination
    $result = AdminController::getAllUsers($filters, $page, $limit);
    if ($result === false) {
        throw new Exception('Failed to load users');
    }
    
    // Get trading plans for assignment
    $tradingPlans = AdminController::getActiveTradingPlans();
    
    // Render the user management page
    $view = new AdminUserManagementView(
        $result['users'],
        [
            'total' => $result['total'],
            'pages' => $result['pages'],
            'current_page' => $result['current_page']
        ],
        $filters,
        $tradingPlans
    );
    $view->render('admin');
    
} catch (Exception $e) {
    error_log("Admin user management error: " . $e->getMessage());
    
    // Show error page
    $view = new AdminUserManagementView([], [], [], []);
    $view->render('admin');
}
?>