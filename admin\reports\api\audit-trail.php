<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuthenticationManager.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $page = max(1, (int) ($_GET['page'] ?? 1));
    $limit = min(100, max(10, (int) ($_GET['limit'] ?? 50)));
    
    // Build filters
    $filters = [];
    if (!empty($_GET['user_id'])) {
        $filters['user_id'] = (int) $_GET['user_id'];
    }
    if (!empty($_GET['action'])) {
        $filters['action'] = $_GET['action'];
    }
    if (!empty($_GET['entity_type'])) {
        $filters['entity_type'] = $_GET['entity_type'];
    }
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = $_GET['date_from'];
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = $_GET['date_to'];
    }
    if (!empty($_GET['ip_address'])) {
        $filters['ip_address'] = $_GET['ip_address'];
    }
    if (!empty($_GET['search'])) {
        $filters['search'] = $_GET['search'];
    }
    
    $auditService = new AuditTrailService();
    $auditData = $auditService->getAuditTrail($filters, $page, $limit);
    
    // Format audit logs for display
    foreach ($auditData['audit_logs'] as &$log) {
        $log['user_display'] = $log['username'] ?? 'System';
        if ($log['first_name'] && $log['last_name']) {
            $log['user_display'] = $log['first_name'] . ' ' . $log['last_name'] . ' (@' . $log['username'] . ')';
        }
        
        $log['action_display'] = ucwords(str_replace('_', ' ', $log['action']));
        $log['entity_display'] = ucwords(str_replace('_', ' ', $log['entity_type']));
        
        // Truncate user agent for display
        $log['user_agent_short'] = strlen($log['user_agent']) > 50 
            ? substr($log['user_agent'], 0, 50) . '...' 
            : $log['user_agent'];
        
        // Add severity indicator
        $log['severity'] = 'info';
        if (isset($log['additional_data_decoded']['financial_impact']) && $log['additional_data_decoded']['financial_impact']) {
            $log['severity'] = 'warning';
        }
        if (isset($log['additional_data_decoded']['critical']) && $log['additional_data_decoded']['critical']) {
            $log['severity'] = 'danger';
        }
        if ($log['action'] === 'login_failed') {
            $log['severity'] = 'warning';
        }
    }
    
    echo json_encode([
        'success' => true,
        'data' => $auditData
    ]);
    
} catch (Exception $e) {
    error_log("Audit trail API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>