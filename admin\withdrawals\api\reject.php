<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/services/NotificationService.php';
require_once '../../../classes/models/Withdrawal.php';
require_once '../../../classes/models/User.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get form data
$withdrawalId = (int)($_POST['withdrawal_id'] ?? 0);
$reason = trim($_POST['reason'] ?? '');

if (!$withdrawalId) {
    echo json_encode(['success' => false, 'message' => 'Invalid withdrawal ID']);
    exit;
}

if (empty($reason)) {
    echo json_encode(['success' => false, 'message' => 'Rejection reason is required']);
    exit;
}

try {
    // Get withdrawal
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal not found']);
        exit;
    }
    
    // Check if withdrawal is pending
    if (!$withdrawal->isPending()) {
        echo json_encode(['success' => false, 'message' => 'Only pending withdrawals can be rejected']);
        exit;
    }
    
    // Get current admin
    $currentUser = SessionManager::getCurrentUser();
    
    // Reject the withdrawal
    if ($withdrawal->reject($currentUser->getId(), $reason)) {
        // Send notification to user
        try {
            $user = $withdrawal->getUser();
            if ($user) {
                NotificationService::sendWithdrawalRejectionNotification($user, $withdrawal, $reason);
            }
        } catch (Exception $e) {
            error_log("Failed to send withdrawal rejection notification: " . $e->getMessage());
            // Don't fail the rejection if notification fails
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal rejected successfully. User has been notified.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to reject withdrawal. Please try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error rejecting withdrawal: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while rejecting the withdrawal']);
}
?>