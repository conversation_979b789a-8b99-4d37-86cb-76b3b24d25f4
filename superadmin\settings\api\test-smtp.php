<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../includes/email_functions.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    // Get current user for test email
    $currentUser = getCurrentUser();
    $testEmail = $currentUser['email'];
    
    // Test SMTP connection and send test email
    $subject = 'SMTP Test - ' . date('Y-m-d H:i:s');
    $message = '
    <h2>SMTP Configuration Test</h2>
    <p>This is a test email to verify your SMTP configuration is working correctly.</p>
    <p><strong>Test Details:</strong></p>
    <ul>
        <li>Date: ' . date('Y-m-d H:i:s') . '</li>
        <li>Sent to: ' . htmlspecialchars($testEmail) . '</li>
        <li>SMTP Host: ' . (defined('SMTP_HOST') ? SMTP_HOST : 'Not configured') . '</li>
        <li>SMTP Port: ' . (defined('SMTP_PORT') ? SMTP_PORT : 'Not configured') . '</li>
    </ul>
    <p>If you received this email, your SMTP configuration is working properly!</p>
    ';
    
    if (sendEmail($testEmail, $subject, $message)) {
        echo json_encode([
            'success' => true,
            'message' => 'SMTP test successful! Test email sent to ' . $testEmail
        ]);
    } else {
        throw new Exception('Failed to send test email');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>