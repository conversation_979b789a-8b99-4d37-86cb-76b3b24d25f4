<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/services/AuthenticationManager.php';
require_once '../../classes/views/AdminFinancialReportsView.php';

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    header('Location: ../../login.php');
    exit;
}

$view = new AdminFinancialReportsView();
echo $view->render();
?>