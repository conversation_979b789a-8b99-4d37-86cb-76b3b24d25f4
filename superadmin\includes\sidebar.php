<?php
$currentPath = $_SERVER['REQUEST_URI'];
$user = getCurrentUser();
?>
<nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebar">
    <div class="position-sticky pt-3">
        <!-- Logo -->
        <div class="text-center mb-4 pb-3 border-bottom">
            <img src="<?php echo getBaseUrl(); ?>assets/images/logo.png" alt="Coinage Trading" class="img-fluid" style="max-height: 60px;">
            <h5 class="text-dark mt-2 mb-1">Coinage Trading</h5>
            <div class="badge bg-danger">SUPER ADMIN</div>
        </div>
        
        <!-- User Info -->
        <div class="text-center mb-4 pb-3 border-bottom">
            <div class="user-avatar mx-auto mb-2">
                <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
            </div>
            <small class="text-muted">Super Admin:</small>
            <div class="text-dark fw-bold"><?php echo htmlspecialchars($user['first_name']); ?></div>
        </div>
        
        <!-- Navigation -->
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/dashboard') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/dashboard/">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/settings') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/settings/">
                    <i class="fas fa-cogs me-2"></i>
                    System Settings
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/appearance') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/appearance/">
                    <i class="fas fa-palette me-2"></i>
                    Appearance
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/email-templates') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/email-templates/">
                    <i class="fas fa-envelope-open-text me-2"></i>
                    Email Templates
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/audit') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/audit/">
                    <i class="fas fa-clipboard-list me-2"></i>
                    Audit Logs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/backup') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/backup/">
                    <i class="fas fa-database me-2"></i>
                    Database Backup
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo strpos($currentPath, '/superadmin/security') !== false ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>superadmin/security/">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security Center
                </a>
            </li>
            <li class="nav-item mt-3">
                <a class="nav-link text-danger" href="<?php echo getBaseUrl(); ?>logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>
                    Logout
                </a>
            </li>
        </ul>
    </div>
</nav>

<style>
.sidebar {
    min-height: 100vh;
    background: #ffffff;
    box-shadow: 2px 0 15px rgba(0,0,0,0.08);
    border-right: 1px solid #e9ecef;
}

.sidebar .nav-link {
    color: #495057;
    padding: 12px 20px;
    margin: 2px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.sidebar .nav-link:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.08);
    border-color: rgba(220, 53, 69, 0.2);
    transform: translateX(3px);
}

.sidebar .nav-link.active {
    color: white;
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #dc3545;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
</style>