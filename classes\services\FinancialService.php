<?php
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Deposit.php';
require_once __DIR__ . '/../models/Withdrawal.php';
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../models/TradingPlan.php';
require_once __DIR__ . '/../models/PaymentMethod.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * FinancialService - Business logic for financial operations
 */
class FinancialService {
    
    /**
     * Process deposit request
     */
    public static function createDeposit($userId, $depositData) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'errors' => ['general' => 'User not found']];
        }
        
        // Validate input data
        $rules = [
            'amount' => ['required' => true, 'numeric' => ['min' => 1, 'max' => *********.99]],
            'plan_id' => ['numeric' => ['min' => 1]],
            'payment_method_id' => ['numeric' => ['min' => 1]]
        ];
        
        $errors = ValidationHelper::validateFields($depositData, $rules);
        
        // Validate trading plan if provided
        if (!empty($depositData['plan_id'])) {
            $plan = TradingPlan::find($depositData['plan_id']);
            if (!$plan || !$plan->isActive()) {
                $errors['plan_id'] = 'Invalid or inactive trading plan';
            } elseif (!$plan->isAmountValid($depositData['amount'])) {
                $errors['amount'] = 'Amount not within plan limits: ' . $plan->getDepositRange();
            }
        }
        
        // Validate payment method if provided
        if (!empty($depositData['payment_method_id'])) {
            $paymentMethod = PaymentMethod::find($depositData['payment_method_id']);
            if (!$paymentMethod || !$paymentMethod->isActive()) {
                $errors['payment_method_id'] = 'Invalid or inactive payment method';
            }
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Create deposit
        $deposit = new Deposit();
        $deposit->user_id = $userId;
        $deposit->amount = $depositData['amount'];
        $deposit->plan_id = $depositData['plan_id'] ?? null;
        $deposit->payment_method_id = $depositData['payment_method_id'] ?? null;
        $deposit->transaction_id = $depositData['transaction_id'] ?? null;
        $deposit->status = Deposit::STATUS_PENDING;
        
        // Calculate bonus
        $deposit->bonus_amount = $deposit->calculateBonus();
        
        if ($deposit->save()) {
            return ['success' => true, 'deposit' => $deposit];
        }
        
        return ['success' => false, 'errors' => ['general' => 'Failed to create deposit']];
    }
    
    /**
     * Approve deposit
     */
    public static function approveDeposit($depositId, $adminId, $bonusAmount = null) {
        $deposit = Deposit::find($depositId);
        if (!$deposit) {
            return ['success' => false, 'error' => 'Deposit not found'];
        }
        
        if (!$deposit->isPending()) {
            return ['success' => false, 'error' => 'Deposit is not pending'];
        }
        
        if ($deposit->approve($adminId, $bonusAmount)) {
            return ['success' => true, 'deposit' => $deposit];
        }
        
        return ['success' => false, 'error' => 'Failed to approve deposit'];
    }
    
    /**
     * Reject deposit
     */
    public static function rejectDeposit($depositId, $adminId, $reason = '') {
        $deposit = Deposit::find($depositId);
        if (!$deposit) {
            return ['success' => false, 'error' => 'Deposit not found'];
        }
        
        if (!$deposit->isPending()) {
            return ['success' => false, 'error' => 'Deposit is not pending'];
        }
        
        if ($deposit->reject($adminId, $reason)) {
            return ['success' => true, 'deposit' => $deposit];
        }
        
        return ['success' => false, 'error' => 'Failed to reject deposit'];
    }
    
    /**
     * Create withdrawal request
     */
    public static function createWithdrawal($userId, $withdrawalData) {
        $user = User::find($userId);
        if (!$user) {
            return ['success' => false, 'errors' => ['general' => 'User not found']];
        }
        
        // Validate input data
        $rules = [
            'amount' => ['required' => true, 'numeric' => ['min' => 1, 'max' => *********.99]],
            'withdrawal_method' => ['required' => true, 'length' => ['max' => 50]],
            'account_details' => ['required' => true]
        ];
        
        $errors = ValidationHelper::validateFields($withdrawalData, $rules);
        
        // Check withdrawal eligibility
        $eligibility = Withdrawal::canUserWithdraw($userId, $withdrawalData['amount']);
        if (!$eligibility['can_withdraw']) {
            $errors['amount'] = $eligibility['reason'];
        }
        
        if (!empty($errors)) {
            return ['success' => false, 'errors' => $errors];
        }
        
        // Create withdrawal
        $withdrawal = new Withdrawal();
        $withdrawal->user_id = $userId;
        $withdrawal->amount = $withdrawalData['amount'];
        $withdrawal->withdrawal_method = $withdrawalData['withdrawal_method'];
        $withdrawal->account_details = $withdrawalData['account_details'];
        $withdrawal->status = Withdrawal::STATUS_PENDING;
        
        if ($withdrawal->save()) {
            return ['success' => true, 'withdrawal' => $withdrawal];
        }
        
        return ['success' => false, 'errors' => ['general' => 'Failed to create withdrawal request']];
    }
    
    /**
     * Approve withdrawal
     */
    public static function approveWithdrawal($withdrawalId, $adminId) {
        $withdrawal = Withdrawal::find($withdrawalId);
        if (!$withdrawal) {
            return ['success' => false, 'error' => 'Withdrawal not found'];
        }
        
        if (!$withdrawal->isPending()) {
            return ['success' => false, 'error' => 'Withdrawal is not pending'];
        }
        
        if ($withdrawal->approve($adminId)) {
            return ['success' => true, 'withdrawal' => $withdrawal];
        }
        
        return ['success' => false, 'error' => 'Failed to approve withdrawal'];
    }
    
    /**
     * Reject withdrawal
     */
    public static function rejectWithdrawal($withdrawalId, $adminId, $reason = '') {
        $withdrawal = Withdrawal::find($withdrawalId);
        if (!$withdrawal) {
            return ['success' => false, 'error' => 'Withdrawal not found'];
        }
        
        if (!$withdrawal->isPending()) {
            return ['success' => false, 'error' => 'Withdrawal is not pending'];
        }
        
        if ($withdrawal->reject($adminId, $reason)) {
            return ['success' => true, 'withdrawal' => $withdrawal];
        }
        
        return ['success' => false, 'error' => 'Failed to reject withdrawal'];
    }
    
    /**
     * Get financial dashboard data
     */
    public static function getDashboardData($userId = null) {
        $db = getDB();
        
        $data = [
            'total_deposits' => 0,
            'total_withdrawals' => 0,
            'pending_deposits' => 0,
            'pending_withdrawals' => 0,
            'total_users' => 0,
            'active_users' => 0,
            'recent_deposits' => [],
            'recent_withdrawals' => []
        ];
        
        // If userId is provided, get user-specific data
        if ($userId) {
            $user = User::find($userId);
            if ($user) {
                $data['user_balance'] = $user->balance;
                $data['user_bonus'] = $user->bonus;
                $data['user_total_deposits'] = $user->total_deposit;
                $data['user_total_withdrawals'] = $user->total_withdrawal;
            }
            
            // Get user's recent transactions
            $stmt = $db->prepare("SELECT * FROM deposits WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
            $stmt->execute(['user_id' => $userId]);
            $deposits = $stmt->fetchAll();
            
            foreach ($deposits as $depositData) {
                $data['recent_deposits'][] = new Deposit($depositData);
            }
            
            $stmt = $db->prepare("SELECT * FROM withdrawals WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 5");
            $stmt->execute(['user_id' => $userId]);
            $withdrawals = $stmt->fetchAll();
            
            foreach ($withdrawals as $withdrawalData) {
                $data['recent_withdrawals'][] = new Withdrawal($withdrawalData);
            }
        } else {
            // Get system-wide statistics
            $stmt = $db->query("SELECT 
                COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as total_deposits,
                COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_deposits
                FROM deposits");
            $depositStats = $stmt->fetch();
            
            $data['total_deposits'] = $depositStats['total_deposits'];
            $data['pending_deposits'] = $depositStats['pending_deposits'];
            
            $stmt = $db->query("SELECT 
                COALESCE(SUM(CASE WHEN status IN ('approved', 'completed') THEN amount ELSE 0 END), 0) as total_withdrawals,
                COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_withdrawals
                FROM withdrawals");
            $withdrawalStats = $stmt->fetch();
            
            $data['total_withdrawals'] = $withdrawalStats['total_withdrawals'];
            $data['pending_withdrawals'] = $withdrawalStats['pending_withdrawals'];
            
            $stmt = $db->query("SELECT 
                COUNT(*) as total_users,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users
                FROM users WHERE role = 'user'");
            $userStats = $stmt->fetch();
            
            $data['total_users'] = $userStats['total_users'];
            $data['active_users'] = $userStats['active_users'];
            
            // Get recent system deposits
            $stmt = $db->query("SELECT d.*, u.username, u.first_name, u.last_name 
                FROM deposits d 
                LEFT JOIN users u ON d.user_id = u.id 
                ORDER BY d.created_at DESC LIMIT 10");
            $deposits = $stmt->fetchAll();
            
            foreach ($deposits as $depositData) {
                $deposit = new Deposit($depositData);
                $deposit->username = $depositData['username'];
                $deposit->user_name = $depositData['first_name'] . ' ' . $depositData['last_name'];
                $data['recent_deposits'][] = $deposit;
            }
            
            // Get recent system withdrawals
            $stmt = $db->query("SELECT w.*, u.username, u.first_name, u.last_name 
                FROM withdrawals w 
                LEFT JOIN users u ON w.user_id = u.id 
                ORDER BY w.created_at DESC LIMIT 10");
            $withdrawals = $stmt->fetchAll();
            
            foreach ($withdrawals as $withdrawalData) {
                $withdrawal = new Withdrawal($withdrawalData);
                $withdrawal->username = $withdrawalData['username'];
                $withdrawal->user_name = $withdrawalData['first_name'] . ' ' . $withdrawalData['last_name'];
                $data['recent_withdrawals'][] = $withdrawal;
            }
        }
        
        return $data;
    }
    
    /**
     * Get financial reports
     */
    public static function getFinancialReport($dateFrom, $dateTo, $type = 'all') {
        $db = getDB();
        
        $report = [
            'period' => ['from' => $dateFrom, 'to' => $dateTo],
            'deposits' => ['count' => 0, 'total' => 0, 'approved' => 0, 'pending' => 0, 'rejected' => 0],
            'withdrawals' => ['count' => 0, 'total' => 0, 'approved' => 0, 'pending' => 0, 'rejected' => 0],
            'net_flow' => 0,
            'top_users' => [],
            'popular_plans' => [],
            'payment_methods' => []
        ];
        
        // Deposit statistics
        $stmt = $db->prepare("SELECT 
            COUNT(*) as count,
            COALESCE(SUM(amount), 0) as total,
            COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as approved,
            COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending,
            COALESCE(SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END), 0) as rejected
            FROM deposits 
            WHERE created_at >= :date_from AND created_at <= :date_to");
        
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $depositStats = $stmt->fetch();
        $report['deposits'] = $depositStats;
        
        // Withdrawal statistics
        $stmt = $db->prepare("SELECT 
            COUNT(*) as count,
            COALESCE(SUM(amount), 0) as total,
            COALESCE(SUM(CASE WHEN status IN ('approved', 'completed') THEN amount ELSE 0 END), 0) as approved,
            COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending,
            COALESCE(SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END), 0) as rejected
            FROM withdrawals 
            WHERE created_at >= :date_from AND created_at <= :date_to");
        
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $withdrawalStats = $stmt->fetch();
        $report['withdrawals'] = $withdrawalStats;
        
        // Calculate net flow
        $report['net_flow'] = $report['deposits']['approved'] - $report['withdrawals']['approved'];
        
        // Top users by deposit amount
        $stmt = $db->prepare("SELECT 
            u.id, u.username, u.first_name, u.last_name,
            COUNT(d.id) as deposit_count,
            COALESCE(SUM(d.amount), 0) as total_deposits
            FROM users u
            LEFT JOIN deposits d ON u.id = d.user_id AND d.status = 'approved' 
                AND d.created_at >= :date_from AND d.created_at <= :date_to
            GROUP BY u.id
            HAVING total_deposits > 0
            ORDER BY total_deposits DESC
            LIMIT 10");
        
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $report['top_users'] = $stmt->fetchAll();
        
        // Popular trading plans
        $stmt = $db->prepare("SELECT 
            tp.id, tp.name,
            COUNT(d.id) as deposit_count,
            COALESCE(SUM(d.amount), 0) as total_amount
            FROM trading_plans tp
            LEFT JOIN deposits d ON tp.id = d.plan_id AND d.status = 'approved'
                AND d.created_at >= :date_from AND d.created_at <= :date_to
            GROUP BY tp.id
            HAVING deposit_count > 0
            ORDER BY total_amount DESC");
        
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $report['popular_plans'] = $stmt->fetchAll();
        
        // Payment method usage
        $stmt = $db->prepare("SELECT 
            pm.id, pm.name, pm.type,
            COUNT(d.id) as usage_count,
            COALESCE(SUM(d.amount), 0) as total_amount
            FROM payment_methods pm
            LEFT JOIN deposits d ON pm.id = d.payment_method_id AND d.status = 'approved'
                AND d.created_at >= :date_from AND d.created_at <= :date_to
            GROUP BY pm.id
            HAVING usage_count > 0
            ORDER BY total_amount DESC");
        
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $report['payment_methods'] = $stmt->fetchAll();
        
        return $report;
    }
    
    /**
     * Calculate platform statistics
     */
    public static function getPlatformStats() {
        $db = getDB();
        
        $stats = [
            'total_users' => 0,
            'active_users' => 0,
            'total_deposits' => 0,
            'total_withdrawals' => 0,
            'platform_balance' => 0,
            'pending_actions' => 0,
            'monthly_growth' => 0
        ];
        
        // User statistics
        $stmt = $db->query("SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            COALESCE(SUM(balance), 0) as platform_balance
            FROM users WHERE role = 'user'");
        $userStats = $stmt->fetch();
        
        $stats['total_users'] = $userStats['total_users'];
        $stats['active_users'] = $userStats['active_users'];
        $stats['platform_balance'] = $userStats['platform_balance'];
        
        // Financial statistics
        $stmt = $db->query("SELECT 
            COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as total_deposits
            FROM deposits");
        $stats['total_deposits'] = $stmt->fetchColumn();
        
        $stmt = $db->query("SELECT 
            COALESCE(SUM(CASE WHEN status IN ('approved', 'completed') THEN amount ELSE 0 END), 0) as total_withdrawals
            FROM withdrawals");
        $stats['total_withdrawals'] = $stmt->fetchColumn();
        
        // Pending actions
        $stmt = $db->query("SELECT 
            (SELECT COUNT(*) FROM deposits WHERE status = 'pending') +
            (SELECT COUNT(*) FROM withdrawals WHERE status = 'pending') +
            (SELECT COUNT(*) FROM support_tickets WHERE status = 'pending') as pending_actions");
        $stats['pending_actions'] = $stmt->fetchColumn();
        
        // Monthly growth (new users this month vs last month)
        $thisMonth = date('Y-m-01');
        $lastMonth = date('Y-m-01', strtotime('-1 month'));
        
        $stmt = $db->prepare("SELECT 
            SUM(CASE WHEN created_at >= :this_month THEN 1 ELSE 0 END) as this_month,
            SUM(CASE WHEN created_at >= :last_month AND created_at < :this_month THEN 1 ELSE 0 END) as last_month
            FROM users WHERE role = 'user'");
        $stmt->execute(['this_month' => $thisMonth, 'last_month' => $lastMonth]);
        $growth = $stmt->fetch();
        
        if ($growth['last_month'] > 0) {
            $stats['monthly_growth'] = (($growth['this_month'] - $growth['last_month']) / $growth['last_month']) * 100;
        }
        
        return $stats;
    }
}
?>