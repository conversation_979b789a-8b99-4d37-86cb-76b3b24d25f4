/* Withdrawal Request Styles */

.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.card-title {
    font-weight: 600;
    color: #5a5c69;
}

.card-body {
    padding: 1.25rem;
}

/* Balance Cards */
.bg-primary {
    background: linear-gradient(45deg, #4e73df, #224abe) !important;
}

.bg-info {
    background: linear-gradient(45deg, #36b9cc, #258391) !important;
}

.bg-warning {
    background: linear-gradient(45deg, #f6c23e, #dda20a) !important;
}

.opacity-75 {
    opacity: 0.75;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control.is-invalid, .form-select.is-invalid {
    border-color: #e74a3b;
}

.invalid-feedback {
    color: #e74a3b;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Input Group */
.input-group-text {
    background-color: #f8f9fc;
    border: 1px solid #d1d3e2;
    color: #5a5c69;
    font-weight: 600;
}

/* Dynamic Account Details */
#accountDetailsContainer {
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    padding: 1rem;
    margin-top: 1rem;
}

.account-detail-field {
    margin-bottom: 1rem;
}

.account-detail-field:last-child {
    margin-bottom: 0;
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.125rem;
}

/* Checkbox */
.form-check-input {
    border: 1px solid #d1d3e2;
}

.form-check-input:checked {
    background-color: #4e73df;
    border-color: #4e73df;
}

.form-check-label {
    color: #5a5c69;
    font-size: 0.875rem;
}

/* Withdrawal List */
.withdrawal-list {
    max-height: 300px;
    overflow-y: auto;
}

.withdrawal-item {
    transition: background-color 0.15s ease-in-out;
}

.withdrawal-item:hover {
    background-color: #f8f9fc;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin: -0.5rem;
    margin-bottom: 0.5rem;
}

.withdrawal-item:last-child {
    border-bottom: none !important;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

.bg-success {
    background-color: #1cc88a !important;
}

.bg-warning {
    background-color: #f6c23e !important;
    color: #5a5c69 !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}

.bg-secondary {
    background-color: #858796 !important;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.35rem;
    padding: 0.75rem 1rem;
}

.alert-warning {
    background-color: #fcf8e3;
    color: #856404;
    border-left: 4px solid #f6c23e;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #e74a3b;
}

.alert i {
    margin-right: 0.5rem;
}

/* Modal */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #5a5c69;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body h6 {
    color: #5a5c69;
    font-weight: 600;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.modal-body h6:first-child {
    margin-top: 0;
}

.modal-body p {
    color: #6c757d;
    margin-bottom: 1rem;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

/* Loading States */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
    }
    
    .withdrawal-item {
        font-size: 0.875rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    .form-control, .form-select {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Custom scrollbar for withdrawal list */
.withdrawal-list::-webkit-scrollbar {
    width: 6px;
}

.withdrawal-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.withdrawal-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.withdrawal-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Form validation styles */
.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: #1cc88a;
}

.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: #e74a3b;
}

.valid-feedback {
    color: #1cc88a;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Tooltip styles */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #5a5c69;
    color: white;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
}