<?php
/**
 * Layout Error Handler
 * Handles errors that occur during layout rendering
 */
class LayoutErrorHandler {
    
    /**
     * Handle layout errors gracefully
     */
    public static function handleError($error, $context = []) {
        $errorMessage = sprintf(
            'Layout Error: %s | Context: %s | Time: %s',
            $error,
            json_encode($context),
            date('Y-m-d H:i:s')
        );
        
        error_log($errorMessage);
        
        // In production, show generic error
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
            return 'An error occurred while loading the page. Please try again.';
        }
        
        // In development, show detailed error
        return $error;
    }
    
    /**
     * Render error page
     */
    public static function renderErrorPage($error, $statusCode = 500) {
        http_response_code($statusCode);
        
        $errorHtml = sprintf(
            '<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Error %d</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            </head>
            <body class="bg-light">
                <div class="container mt-5">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h1 class="display-1 text-danger">%d</h1>
                                    <h4 class="mb-3">Oops! Something went wrong</h4>
                                    <p class="text-muted">%s</p>
                                    <a href="/user/dashboard/" class="btn btn-primary">Go to Dashboard</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>',
            $statusCode,
            $statusCode,
            htmlspecialchars($error)
        );
        
        echo $errorHtml;
        exit;
    }
    
    /**
     * Log performance metrics
     */
    public static function logPerformance($startTime, $context = []) {
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        
        if ($executionTime > 1000) { // Log if over 1 second
            $logMessage = sprintf(
                'Slow Layout Render: %s ms | Context: %s',
                number_format($executionTime, 2),
                json_encode($context)
            );
            
            error_log($logMessage);
        }
    }
}