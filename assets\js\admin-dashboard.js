/**
 * Admin Dashboard JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

/**
 * Initialize dashboard functionality
 */
function initializeDashboard() {
    // Add event listeners
    setupEventListeners();
    
    // Initialize tooltips
    initializeTooltips();
    
    // Start auto-refresh if enabled
    startAutoRefresh();
    
    // Initialize charts if available
    initializeCharts();
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Refresh button
    const refreshBtn = document.querySelector('[onclick="refreshStats()"]');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            refreshStats();
        });
    }
    
    // Quick action buttons
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Add loading state
            this.classList.add('loading');
            
            // Remove loading state after navigation
            setTimeout(() => {
                this.classList.remove('loading');
            }, 1000);
        });
    });
    
    // Activity table row clicks
    document.querySelectorAll('.activity-table tbody tr').forEach(row => {
        row.addEventListener('click', function() {
            const userId = this.dataset.userId;
            if (userId) {
                viewUserDetails(userId);
            }
        });
   