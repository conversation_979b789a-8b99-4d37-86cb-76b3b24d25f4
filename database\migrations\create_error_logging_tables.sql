-- Create error logging tables for comprehensive error handling

-- Error logs table
CREATE TABLE IF NOT EXISTS error_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    error_id VARCHAR(50) NOT NULL UNIQUE,
    type ENUM('php_error', 'exception', 'fatal_error', 'application_error', 'database_error', 'authentication_error', 'file_upload_error', 'financial_error', 'validation_error') NOT NULL,
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    category ENUM('database', 'authentication', 'financial', 'file_upload', 'validation', 'system', 'security', 'api') NOT NULL,
    message TEXT NOT NULL,
    file VARCHAR(500),
    line INT,
    context JSON,
    user_id INT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by INT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    <PERSON>OREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    INDEX idx_error_id (error_id),
    INDEX idx_type (type),
    INDEX idx_severity (severity),
    INDEX idx_category (category),
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_created_at (created_at),
    INDEX idx_resolved (resolved),
    INDEX idx_severity_created (severity, created_at),
    INDEX idx_category_created (category, created_at)
);

-- Failed login attempts table for authentication error tracking
CREATE TABLE IF NOT EXISTS failed_login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_identifier (identifier),
    INDEX idx_ip_address (ip_address),
    INDEX idx_attempt_time (attempt_time),
    INDEX idx_identifier_time (identifier, attempt_time),
    INDEX idx_ip_time (ip_address, attempt_time)
);

-- System health monitoring table
CREATE TABLE IF NOT EXISTS system_health_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    check_type ENUM('database', 'file_system', 'memory', 'disk_space', 'email', 'external_api') NOT NULL,
    status ENUM('healthy', 'warning', 'critical', 'unknown') NOT NULL,
    message TEXT,
    metrics JSON,
    check_duration_ms INT,
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_check_type (check_type),
    INDEX idx_status (status),
    INDEX idx_checked_at (checked_at),
    INDEX idx_type_status (check_type, status),
    INDEX idx_type_checked (check_type, checked_at)
);

-- Error resolution tracking
CREATE TABLE IF NOT EXISTS error_resolutions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    error_id VARCHAR(50) NOT NULL,
    resolution_type ENUM('fixed', 'ignored', 'duplicate', 'wont_fix') NOT NULL,
    resolution_notes TEXT,
    resolved_by INT NOT NULL,
    resolved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_error_id (error_id),
    INDEX idx_resolution_type (resolution_type),
    INDEX idx_resolved_by (resolved_by),
    INDEX idx_resolved_at (resolved_at)
);

-- Performance monitoring table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_type ENUM('page_load', 'database_query', 'api_response', 'memory_usage', 'cpu_usage') NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    value DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    context JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_metric_type (metric_type),
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at),
    INDEX idx_type_name (metric_type, metric_name),
    INDEX idx_type_recorded (metric_type, recorded_at)
);