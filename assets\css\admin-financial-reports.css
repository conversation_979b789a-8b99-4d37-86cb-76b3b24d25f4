/* Financial Reports & Audit Trail Styles */

.financial-reports-container {
    background: #f8f9fa;
    min-height: 100vh;
}

.report-filters {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.metric-card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.metric-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.metric-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.metric-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.metric-card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 10px 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
    margin-top: 5px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.chart-container canvas {
    max-height: 100%;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: #dee2e6;
    color: #495057;
}

.nav-tabs .nav-link.active {
    background: none;
    border-bottom-color: #007bff;
    color: #007bff;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background: #d4edda;
    color: #155724;
}

.status-badge.warning {
    background: #fff3cd;
    color: #856404;
}

.status-badge.danger {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.secondary {
    background: #e2e3e5;
    color: #383d41;
}

.transaction-type {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.transaction-type i {
    width: 16px;
    text-align: center;
}

.amount-positive {
    color: #28a745;
    font-weight: 600;
}

.amount-negative {
    color: #dc3545;
    font-weight: 600;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
}

.audit-entry {
    padding: 12px;
    border-left: 4px solid #dee2e6;
    margin-bottom: 10px;
    background: white;
    border-radius: 0 8px 8px 0;
    transition: border-color 0.2s ease;
}

.audit-entry.severity-info {
    border-left-color: #17a2b8;
}

.audit-entry.severity-warning {
    border-left-color: #ffc107;
}

.audit-entry.severity-danger {
    border-left-color: #dc3545;
}

.audit-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.audit-action {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.audit-changes {
    font-size: 0.85rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.filter-group {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group .btn-group {
    margin-right: 10px;
}

.export-buttons {
    display: flex;
    gap: 10px;
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 40px;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #f5c6cb;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #c3e6cb;
}

.pagination {
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-link {
    border: none;
    color: #007bff;
    padding: 8px 16px;
    margin: 0 2px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.pagination .page-link:hover {
    background: #e9ecef;
    color: #0056b3;
}

.pagination .page-item.active .page-link {
    background: #007bff;
    color: white;
}

.search-box {
    position: relative;
}

.search-box input {
    padding-right: 40px;
}

.search-box .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.date-range-picker {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-range-picker input {
    max-width: 150px;
}

.quick-filters {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.quick-filters .btn {
    font-size: 0.8rem;
    padding: 4px 12px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.key-metrics {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.key-metrics .metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #dee2e6;
}

.key-metrics .metric-item:last-child {
    border-bottom: none;
}

.key-metrics .metric-name {
    font-weight: 500;
    color: #495057;
}

.key-metrics .metric-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #007bff;
}

.suspicious-activity {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.suspicious-activity.high {
    background: #f8d7da;
    border-color: #f5c6cb;
}

.suspicious-activity.medium {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.activity-alert {
    display: flex;
    align-items: center;
    gap: 10px;
}

.activity-alert i {
    font-size: 1.2rem;
}

.activity-alert.high i {
    color: #dc3545;
}

.activity-alert.medium i {
    color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .metric-card {
        margin-bottom: 15px;
    }
    
    .metric-value {
        font-size: 2rem;
    }
    
    .export-buttons {
        flex-direction: column;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .date-range-picker {
        flex-direction: column;
    }
    
    .date-range-picker input {
        max-width: none;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    .table-responsive {
        font-size: 0.85rem;
    }
    
    .metric-card {
        padding: 15px;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Print Styles */
@media print {
    .export-buttons,
    .filter-group,
    .nav-tabs,
    .pagination {
        display: none !important;
    }
    
    .report-card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .metric-card {
        background: white !important;
        color: black !important;
        border: 1px solid #dee2e6;
    }
}