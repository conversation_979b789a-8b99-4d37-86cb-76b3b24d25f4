<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Creating email templates table...\n";
    
    $sql = file_get_contents(__DIR__ . '/migrations/create_email_templates_table.sql');
    $db->exec($sql);
    
    echo "✅ Email templates table created successfully!\n";
    
    // Verify table exists
    $stmt = $db->query("SHOW TABLES LIKE 'email_templates'");
    if ($stmt->fetch()) {
        echo "✅ Table verified\n";
        
        // Check templates
        $stmt = $db->query("SELECT COUNT(*) as count FROM email_templates");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✅ Default templates inserted: " . $result['count'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>