/* Withdrawal Status Styles */

.card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.card-title {
    font-weight: 600;
    color: #5a5c69;
}

.card-body {
    padding: 1.25rem;
}

/* Statistics Cards */
.card.text-center .card-body {
    padding: 1rem;
}

.card.text-center h4 {
    font-weight: 700;
    color: #5a5c69;
}

.card.text-center h6 {
    font-weight: 600;
    color: #5a5c69;
}

.card.text-center small {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Table Styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    padding: 1rem 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Method Info */
.method-info {
    min-width: 150px;
}

.method-info .fw-medium {
    color: #5a5c69;
}

/* Processed Info */
.processed-info {
    min-width: 120px;
}

.processed-info .fw-medium {
    color: #5a5c69;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

.badge i {
    margin-right: 0.25rem;
}

.bg-success {
    background-color: #1cc88a !important;
}

.bg-warning {
    background-color: #f6c23e !important;
    color: #5a5c69 !important;
}

.bg-danger {
    background-color: #e74a3b !important;
}

.bg-info {
    background-color: #36b9cc !important;
}

.bg-secondary {
    background-color: #858796 !important;
}

.bg-primary {
    background-color: #4e73df !important;
}

/* Form Styles */
.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.form-select:focus {
    border-color: #bac8f3;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Buttons */
.btn {
    border-radius: 0.35rem;
    font-weight: 400;
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-primary:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
    transform: translateY(-1px);
}

.btn-outline-primary {
    color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-primary:hover {
    background-color: #4e73df;
    border-color: #4e73df;
}

.btn-outline-secondary {
    color: #858796;
    border-color: #858796;
}

.btn-outline-danger {
    color: #e74a3b;
    border-color: #e74a3b;
}

.btn-outline-info {
    color: #36b9cc;
    border-color: #36b9cc;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Empty State */
.text-center i.fa-3x {
    opacity: 0.3;
}

/* Pagination */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #4e73df;
    border-color: #dddfeb;
    padding: 0.5rem 0.75rem;
}

.page-link:hover {
    color: #224abe;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* Modal Styles */
.modal-header {
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

.modal-title {
    font-weight: 600;
    color: #5a5c69;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
}

/* Loading Spinner */
.spinner-border {
    width: 2rem;
    height: 2rem;
    color: #4e73df;
}

/* Status Colors for Text */
.text-success {
    color: #1cc88a !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-primary {
    color: #4e73df !important;
}

.text-secondary {
    color: #858796 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .method-info {
        min-width: 120px;
    }
    
    .processed-info {
        min-width: 100px;
    }
    
    .btn-group-sm > .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.6875rem;
    }
    
    .badge {
        font-size: 0.6875rem;
        padding: 0.25rem 0.5rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.8125rem;
    }
    
    .card.text-center .card-body {
        padding: 0.75rem;
    }
    
    .card.text-center h4 {
        font-size: 1.25rem;
    }
    
    .card.text-center h6 {
        font-size: 0.875rem;
    }
    
    .btn {
        font-size: 0.8125rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Custom scrollbar for table */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Withdrawal amount highlighting */
.fw-bold.text-danger {
    font-weight: 600 !important;
}

/* Status-specific styling */
.badge.bg-warning {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Processing status animation */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(359deg); }
}

/* Tooltip styles */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    background-color: #5a5c69;
    color: white;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
}

/* Print styles */
@media print {
    .btn, .pagination, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .table {
        font-size: 0.75rem;
    }
    
    .badge {
        border: 1px solid #000;
        background-color: transparent !important;
        color: #000 !important;
    }
}