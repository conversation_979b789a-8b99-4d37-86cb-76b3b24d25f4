<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to continue']);
    exit();
}

// Ensure user role is 'user'
if (!hasRole('user')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

try {
    // Get current user
    $user = getCurrentUser();
    if (!$user) {
        throw new Exception('User session expired');
    }
    
    // Check for recent updates (within last 5 minutes)
    $sql = "SELECT COUNT(*) as count FROM deposits 
            WHERE user_id = :user_id 
            AND (approved_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE) 
                 OR updated_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE))";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['user_id' => $user->getId()]);
    $result = $stmt->fetch();
    
    $hasUpdates = $result['count'] > 0;
    
    // Return response
    echo json_encode([
        'success' => true,
        'hasUpdates' => $hasUpdates,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    error_log("Check updates error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Failed to check for updates'
    ]);
}
?>