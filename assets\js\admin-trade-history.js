/**
 * Admin Trade History Management JavaScript
 */

class AdminTradeHistory {
    constructor() {
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setDefaultDateTime();
    }
    
    bindEvents() {
        // Create trade form submission
        document.getElementById('tradeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleTradeFormSubmit();
        });
        
        // Close trade form submission
        document.getElementById('closeTradeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleCloseTradeSubmit();
        });
        
        // Bulk trade form submission
        document.getElementById('bulkTradeForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleBulkTradeSubmit();
        });
        
        // Edit trade buttons
        document.querySelectorAll('.edit-trade-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tradeId = e.currentTarget.dataset.tradeId;
                this.editTrade(tradeId);
            });
        });
        
        // Close trade buttons
        document.querySelectorAll('.close-trade-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tradeId = e.currentTarget.dataset.tradeId;
                this.showCloseTradeModal(tradeId);
            });
        });
        
        // Delete trade buttons
        document.querySelectorAll('.delete-trade-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tradeId = e.currentTarget.dataset.tradeId;
                this.deleteTrade(tradeId);
            });
        });
        
        // Modal events
        document.getElementById('createTradeModal').addEventListener('hidden.bs.modal', () => {
            this.resetTradeForm();
        });
        
        document.getElementById('closeTradeModal').addEventListener('hidden.bs.modal', () => {
            this.resetCloseTradeForm();
        });
        
        document.getElementById('bulkTradeModal').addEventListener('hidden.bs.modal', () => {
            this.resetBulkTradeForm();
        });
        
        // Form validation
        document.querySelectorAll('#tradeForm input, #tradeForm select, #tradeForm textarea').forEach(field => {
            field.addEventListener('blur', () => {
                this.validateField(field);
            });
            
            field.addEventListener('input', () => {
                this.clearFieldError(field);
            });
        });
        
        // Set current datetime for bulk form
        document.getElementById('bulkOpenedAt').addEventListener('focus', () => {
            if (!document.getElementById('bulkOpenedAt').value) {
                this.setCurrentDateTime('bulkOpenedAt');
            }
        });
        
        document.getElementById('closedAt').addEventListener('focus', () => {
            if (!document.getElementById('closedAt').value) {
                this.setCurrentDateTime('closedAt');
            }
        });
    }
    
    setDefaultDateTime() {
        // Set current datetime for opened_at field
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16);
        
        document.getElementById('openedAt').value = localDateTime;
    }
    
    setCurrentDateTime(fieldId) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16);
        
        document.getElementById(fieldId).value = localDateTime;
    }
    
    handleTradeFormSubmit() {
        const form = document.getElementById('tradeForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        const btnText = submitBtn.querySelector('.btn-text');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        // Determine if this is create or update
        const tradeId = document.getElementById('tradeId').value;
        const url = tradeId ? 'api/update-trade.php' : 'api/create-trade.php';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(
                    tradeId ? 'Trade updated successfully' : 'Trade created successfully', 
                    'success'
                );
                
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('createTradeModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.handleFormErrors(data.errors || {});
                this.showNotification(data.message || 'Please correct the errors below', 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting trade form:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }
    
    handleCloseTradeSubmit() {
        const form = document.getElementById('closeTradeForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        fetch('api/close-trade.php', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Trade closed successfully', 'success');
                
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('closeTradeModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.handleFormErrors(data.errors || {}, 'closeTradeForm');
                this.showNotification(data.message || 'Please correct the errors below', 'error');
            }
        })
        .catch(error => {
            console.error('Error closing trade:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }
    
    handleBulkTradeSubmit() {
        const form = document.getElementById('bulkTradeForm');
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const spinner = submitBtn.querySelector('.spinner-border');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        
        fetch('api/bulk-create-trades.php', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(
                    `${data.created_count} trades created successfully`, 
                    'success'
                );
                
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('bulkTradeModal')).hide();
                setTimeout(() => location.reload(), 500);
            } else {
                this.handleFormErrors(data.errors || {}, 'bulkTradeForm');
                this.showNotification(data.message || 'Please correct the errors below', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating bulk trades:', error);
            this.showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading state
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }
    
    editTrade(tradeId) {
        fetch(`api/trade-details.php?id=${tradeId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.populateTradeForm(data.trade);
                
                // Update modal title
                document.querySelector('#createTradeModal .modal-title').textContent = 'Edit Trade';
                document.querySelector('#tradeForm .btn-text').textContent = 'Update Trade';
                
                // Show modal
                new bootstrap.Modal(document.getElementById('createTradeModal')).show();
            } else {
                this.showNotification(data.message || 'Failed to load trade details', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading trade:', error);
            this.showNotification('Failed to load trade details', 'error');
        });
    }
    
    populateTradeForm(trade) {
        document.getElementById('tradeId').value = trade.id;
        document.getElementById('tradeUserId').value = trade.user_id;
        document.getElementById('tradePlanId').value = trade.plan_id || '';
        document.getElementById('asset').value = trade.asset;
        document.getElementById('tradeType').value = trade.trade_type;
        document.getElementById('amount').value = trade.amount;
        document.getElementById('entryPrice').value = trade.entry_price;
        document.getElementById('openedAt').value = trade.opened_at.replace(' ', 'T');
        document.getElementById('notes').value = trade.notes || '';
    }
    
    showCloseTradeModal(tradeId) {
        document.getElementById('closeTradeId').value = tradeId;
        this.setCurrentDateTime('closedAt');
        
        new bootstrap.Modal(document.getElementById('closeTradeModal')).show();
    }
    
    deleteTrade(tradeId) {
        if (!confirm('Are you sure you want to delete this trade? This action cannot be undone.')) {
            return;
        }
        
        fetch('api/delete-trade.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ trade_id: tradeId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Trade deleted successfully', 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                this.showNotification(data.message || 'Failed to delete trade', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting trade:', error);
            this.showNotification('Failed to delete trade', 'error');
        });
    }
    
    validateField(field) {
        const value = field.value.trim();
        const name = field.name;
        let error = '';
        
        switch (name) {
            case 'user_id':
                if (!value) error = 'User is required';
                break;
                
            case 'asset':
                if (!value) error = 'Asset is required';
                else if (value.length > 50) error = 'Asset name is too long';
                break;
                
            case 'trade_type':
                if (!value) error = 'Trade type is required';
                else if (!['buy', 'sell'].includes(value)) error = 'Invalid trade type';
                break;
                
            case 'amount':
                if (!value) error = 'Amount is required';
                else if (isNaN(value) || parseFloat(value) <= 0) error = 'Amount must be greater than zero';
                break;
                
            case 'entry_price':
                if (!value) error = 'Entry price is required';
                else if (isNaN(value) || parseFloat(value) <= 0) error = 'Entry price must be greater than zero';
                break;
                
            case 'exit_price':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) error = 'Exit price must be greater than zero';
                break;
                
            case 'opened_at':
                if (!value) error = 'Opened date is required';
                else if (new Date(value) > new Date()) error = 'Opened date cannot be in the future';
                break;
                
            case 'closed_at':
                if (!value) error = 'Closed date is required';
                else if (new Date(value) > new Date()) error = 'Closed date cannot be in the future';
                break;
        }
        
        this.setFieldError(field, error);
        return !error;
    }
    
    setFieldError(field, error) {
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        
        if (error) {
            field.classList.add('is-invalid');
            if (feedback) feedback.textContent = error;
        } else {
            field.classList.remove('is-invalid');
            if (feedback) feedback.textContent = '';
        }
    }
    
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) feedback.textContent = '';
    }
    
    handleFormErrors(errors, formId = 'tradeForm') {
        // Clear all previous errors
        document.querySelectorAll(`#${formId} .is-invalid`).forEach(field => {
            this.clearFieldError(field);
        });
        
        // Set new errors
        Object.keys(errors).forEach(fieldName => {
            const field = document.querySelector(`#${formId} [name="${fieldName}"]`);
            if (field) {
                this.setFieldError(field, errors[fieldName]);
            }
        });
    }
    
    resetTradeForm() {
        const form = document.getElementById('tradeForm');
        form.reset();
        
        // Clear errors
        document.querySelectorAll('#tradeForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Reset modal title and button text
        document.querySelector('#createTradeModal .modal-title').textContent = 'Create New Trade';
        document.querySelector('#tradeForm .btn-text').textContent = 'Create Trade';
        
        // Clear trade ID
        document.getElementById('tradeId').value = '';
        
        // Set default datetime
        this.setDefaultDateTime();
    }
    
    resetCloseTradeForm() {
        const form = document.getElementById('closeTradeForm');
        form.reset();
        
        // Clear errors
        document.querySelectorAll('#closeTradeForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
        
        // Clear trade ID
        document.getElementById('closeTradeId').value = '';
    }
    
    resetBulkTradeForm() {
        const form = document.getElementById('bulkTradeForm');
        form.reset();
        
        // Clear errors
        document.querySelectorAll('#bulkTradeForm .is-invalid').forEach(field => {
            this.clearFieldError(field);
        });
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show notification-toast`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminTradeHistory();
});

// Additional utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDateTime(dateString) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(new Date(dateString));
}