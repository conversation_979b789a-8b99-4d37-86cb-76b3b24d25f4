/**
 * Deposit Status JavaScript
 * Handles deposit details modal and status tracking functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initDepositStatus();
});

function initDepositStatus() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-refresh pending deposits every 30 seconds
    if (document.querySelector('.badge.bg-warning')) {
        setInterval(checkForUpdates, 30000);
    }
}

/**
 * View deposit details in modal
 */
function viewDepositDetails(depositId) {
    const modal = new bootstrap.Modal(document.getElementById('depositDetailsModal'));
    const content = document.getElementById('depositDetailsContent');
    
    // Show loading state
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading deposit details...</p>
        </div>
    `;
    
    // Show modal
    modal.show();
    
    // Fetch deposit details
    fetch(`/user/deposit/details.php?id=${depositId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            content.innerHTML = renderDepositDetails(data.deposit);
        } else {
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${data.message || 'Failed to load deposit details'}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        content.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                Failed to load deposit details. Please try again.
            </div>
        `;
    });
}

/**
 * Render deposit details HTML
 */
function renderDepositDetails(deposit) {
    const statusColor = getStatusColor(deposit.status);
    const statusIcon = getStatusIcon(deposit.status);
    
    return `
        <div class="deposit-details">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6 class="text-muted">Reference ID</h6>
                    <p class="fw-bold">DEP-${deposit.id.toString().padStart(6, '0')}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-muted">Status</h6>
                    <span class="badge bg-${statusColor}">
                        <i class="${statusIcon}"></i> ${deposit.status.charAt(0).toUpperCase() + deposit.status.slice(1)}
                    </span>
                </div>
            </div>
            
            <div class="deposit-detail-item">
                <span class="detail-label">Deposit Amount:</span>
                <span class="detail-value fw-bold text-success">$${parseFloat(deposit.amount).toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
            </div>
            
            <div class="deposit-detail-item">
                <span class="detail-label">Bonus Amount:</span>
                <span class="detail-value fw-bold text-info">$${parseFloat(deposit.bonus_amount || 0).toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
            </div>
            
            <div class="deposit-detail-item">
                <span class="detail-label">Total Amount:</span>
                <span class="detail-value fw-bold text-primary">$${(parseFloat(deposit.amount) + parseFloat(deposit.bonus_amount || 0)).toLocaleString('en-US', {minimumFractionDigits: 2})}</span>
            </div>
            
            <div class="deposit-detail-item">
                <span class="detail-label">Trading Plan:</span>
                <span class="detail-value">${deposit.plan_name || 'N/A'}</span>
            </div>
            
            <div class="deposit-detail-item">
                <span class="detail-label">Payment Method:</span>
                <span class="detail-value">${deposit.payment_method_name || 'N/A'}</span>
            </div>
            
            ${deposit.transaction_id ? `
                <div class="deposit-detail-item">
                    <span class="detail-label">Transaction ID:</span>
                    <span class="detail-value">${deposit.transaction_id}</span>
                </div>
            ` : ''}
            
            <div class="deposit-detail-item">
                <span class="detail-label">Submission Date:</span>
                <span class="detail-value">${formatDateTime(deposit.created_at)}</span>
            </div>
            
            ${deposit.approved_at ? `
                <div class="deposit-detail-item">
                    <span class="detail-label">Processed Date:</span>
                    <span class="detail-value">${formatDateTime(deposit.approved_at)}</span>
                </div>
            ` : ''}
            
            ${deposit.approved_by_name ? `
                <div class="deposit-detail-item">
                    <span class="detail-label">Processed By:</span>
                    <span class="detail-value">${deposit.approved_by_name}</span>
                </div>
            ` : ''}
            
            ${deposit.admin_note ? `
                <div class="deposit-detail-item">
                    <span class="detail-label">Admin Note:</span>
                    <span class="detail-value">${deposit.admin_note}</span>
                </div>
            ` : ''}
            
            ${deposit.proof_of_payment ? `
                <div class="deposit-detail-item">
                    <span class="detail-label">Proof of Payment:</span>
                    <span class="detail-value">
                        <a href="/${deposit.proof_of_payment}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> View File
                        </a>
                    </span>
                </div>
            ` : ''}
            
            ${renderStatusTimeline(deposit)}
        </div>
    `;
}

/**
 * Render status timeline
 */
function renderStatusTimeline(deposit) {
    let timeline = `
        <div class="status-timeline">
            <h6 class="text-muted mb-3">Status Timeline</h6>
            
            <div class="timeline-item">
                <div class="timeline-icon bg-primary">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="timeline-content">
                    <h6>Deposit Submitted</h6>
                    <small>${formatDateTime(deposit.created_at)}</small>
                </div>
            </div>
    `;
    
    if (deposit.status === 'approved' && deposit.approved_at) {
        timeline += `
            <div class="timeline-item">
                <div class="timeline-icon bg-success">
                    <i class="fas fa-check"></i>
                </div>
                <div class="timeline-content">
                    <h6>Deposit Approved</h6>
                    <small>${formatDateTime(deposit.approved_at)}</small>
                    ${deposit.approved_by_name ? `<br><small>By: ${deposit.approved_by_name}</small>` : ''}
                </div>
            </div>
        `;
    } else if (deposit.status === 'rejected' && deposit.approved_at) {
        timeline += `
            <div class="timeline-item">
                <div class="timeline-icon bg-danger">
                    <i class="fas fa-times"></i>
                </div>
                <div class="timeline-content">
                    <h6>Deposit Rejected</h6>
                    <small>${formatDateTime(deposit.approved_at)}</small>
                    ${deposit.approved_by_name ? `<br><small>By: ${deposit.approved_by_name}</small>` : ''}
                </div>
            </div>
        `;
    } else {
        timeline += `
            <div class="timeline-item">
                <div class="timeline-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="timeline-content">
                    <h6>Pending Review</h6>
                    <small>Your deposit is being reviewed by our team</small>
                </div>
            </div>
        `;
    }
    
    timeline += '</div>';
    return timeline;
}

/**
 * Get status color for badges
 */
function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
    };
    return colors[status] || 'secondary';
}

/**
 * Get status icon
 */
function getStatusIcon(status) {
    const icons = {
        'pending': 'fas fa-clock',
        'approved': 'fas fa-check',
        'rejected': 'fas fa-times'
    };
    return icons[status] || 'fas fa-question';
}

/**
 * Format date and time
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Check for deposit status updates
 */
function checkForUpdates() {
    // Only check if there are pending deposits
    const pendingBadges = document.querySelectorAll('.badge.bg-warning');
    if (pendingBadges.length === 0) {
        return;
    }
    
    fetch('/user/deposit/check-updates.php', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.hasUpdates) {
            // Show notification
            showUpdateNotification();
            
            // Optionally auto-refresh the page
            setTimeout(() => {
                window.location.reload();
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Update check failed:', error);
    });
}

/**
 * Show update notification
 */
function showUpdateNotification() {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-info-circle"></i>
        Your deposit status has been updated!
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Export deposit data (if needed)
 */
function exportDepositData() {
    window.open('/user/deposit/export.php', '_blank');
}

/**
 * Filter deposits by status
 */
function filterByStatus(status) {
    const url = new URL(window.location);
    if (status) {
        url.searchParams.set('status', status);
    } else {
        url.searchParams.delete('status');
    }
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}

/**
 * Search deposits
 */
function searchDeposits(query) {
    const url = new URL(window.location);
    if (query) {
        url.searchParams.set('search', query);
    } else {
        url.searchParams.delete('search');
    }
    url.searchParams.delete('page'); // Reset to first page
    window.location.href = url.toString();
}