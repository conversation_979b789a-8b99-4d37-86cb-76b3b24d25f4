/**
 * Admin Payment Methods Management JavaScript
 */

class PaymentMethodsManager {
    constructor() {
        this.init();
        this.setupEventListeners();
        this.setupSortable();
    }
    
    init() {
        this.apiBase = 'api/';
        this.currentEditId = null;
        
        // Field templates for different payment types
        this.fieldTemplates = {
            crypto: [
                { name: 'address', label: 'Wallet Address', type: 'text', required: true, placeholder: 'Enter wallet address' },
                { name: 'network', label: 'Network', type: 'text', required: true, placeholder: 'e.g., Bitcoin, Ethereum' },
                { name: 'confirmations', label: 'Required Confirmations', type: 'number', required: false, placeholder: '3' },
                { name: 'fee', label: 'Network Fee', type: 'text', required: false, placeholder: 'e.g., 0.0005 BTC' }
            ],
            bank: [
                { name: 'account_name', label: 'Account Name', type: 'text', required: true, placeholder: 'Account holder name' },
                { name: 'account_number', label: 'Account Number', type: 'text', required: true, placeholder: 'Bank account number' },
                { name: 'bank_name', label: 'Bank Name', type: 'text', required: true, placeholder: 'Name of the bank' },
                { name: 'routing_number', label: 'Routing Number', type: 'text', required: false, placeholder: 'Bank routing number' },
                { name: 'swift_code', label: 'SWIFT Code', type: 'text', required: false, placeholder: 'International SWIFT code' },
                { name: 'branch', label: 'Branch', type: 'text', required: false, placeholder: 'Bank branch' }
            ],
            paypal: [
                { name: 'email', label: 'PayPal Email', type: 'email', required: true, placeholder: 'PayPal account email' },
                { name: 'fee_percent', label: 'Fee Percentage', type: 'number', required: false, placeholder: '3.5', step: '0.1' }
            ],
            other: [
                { name: 'instructions', label: 'Payment Instructions', type: 'textarea', required: true, placeholder: 'Detailed payment instructions for users' },
                { name: 'contact_info', label: 'Contact Information', type: 'text', required: false, placeholder: 'Contact details if needed' }
            ]
        };
    }
    
    setupEventListeners() {
        // Type change handlers
        document.getElementById('methodType').addEventListener('change', (e) => {
            this.generateDynamicFields(e.target.value, 'dynamicFields');
        });
        
        document.getElementById('editMethodType').addEventListener('change', (e) => {
            this.generateDynamicFields(e.target.value, 'editDynamicFields');
        });
        
        // Form submissions
        document.getElementById('addPaymentMethodForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAddPaymentMethod();
        });
        
        document.getElementById('editPaymentMethodForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleEditPaymentMethod();
        });
        
        // Modal events
        document.getElementById('addPaymentMethodModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('addPaymentMethodForm');
        });
        
        document.getElementById('editPaymentMethodModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('editPaymentMethodForm');
        });
    }
    
    setupSortable() {
        const tableBody = document.getElementById('paymentMethodsTableBody');
        if (tableBody) {
            new Sortable(tableBody, {
                handle: '.sort-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                onEnd: (evt) => {
                    this.updateSortOrder();
                }
            });
        }
    }
    
    generateDynamicFields(type, containerId) {
        const container = document.getElementById(containerId);
        if (!container || !type) {
            container.innerHTML = '';
            return;
        }
        
        const fields = this.fieldTemplates[type] || [];
        let html = '';
        
        if (fields.length > 0) {
            html = '<div class="dynamic-field-group"><h6>Payment Details</h6>';
            
            fields.forEach(field => {
                const fieldId = `${containerId}_${field.name}`;
                const required = field.required ? 'required' : '';
                const placeholder = field.placeholder || '';
                const step = field.step ? `step="${field.step}"` : '';
                
                html += `
                <div class="field-row">
                    <div class="mb-3">
                        <label for="${fieldId}" class="form-label">
                            ${field.label} ${field.required ? '*' : ''}
                        </label>`;
                
                if (field.type === 'textarea') {
                    html += `
                        <textarea class="form-control" id="${fieldId}" name="details[${field.name}]" 
                                  rows="3" placeholder="${placeholder}" ${required}></textarea>`;
                } else {
                    html += `
                        <input type="${field.type}" class="form-control" id="${fieldId}" 
                               name="details[${field.name}]" placeholder="${placeholder}" 
                               ${required} ${step}>`;
                }
                
                html += `
                    </div>
                </div>`;
            });
            
            html += '</div>';
        }
        
        container.innerHTML = html;
    }
    
    async handleAddPaymentMethod() {
        const form = document.getElementById('addPaymentMethodForm');
        const formData = new FormData(form);
        
        try {
            this.setLoading(form, true);
            
            const response = await fetch(this.apiBase + 'create.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', 'Payment method added successfully!');
                bootstrap.Modal.getInstance(document.getElementById('addPaymentMethodModal')).hide();
                this.refreshTable();
            } else {
                this.showAlert('danger', result.message || 'Failed to add payment method');
                this.displayFormErrors(result.errors || {});
            }
        } catch (error) {
            console.error('Error adding payment method:', error);
            this.showAlert('danger', 'An error occurred while adding the payment method');
        } finally {
            this.setLoading(form, false);
        }
    }
    
    async handleEditPaymentMethod() {
        const form = document.getElementById('editPaymentMethodForm');
        const formData = new FormData(form);
        
        try {
            this.setLoading(form, true);
            
            const response = await fetch(this.apiBase + 'update.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', 'Payment method updated successfully!');
                bootstrap.Modal.getInstance(document.getElementById('editPaymentMethodModal')).hide();
                this.refreshTable();
            } else {
                this.showAlert('danger', result.message || 'Failed to update payment method');
                this.displayFormErrors(result.errors || {}, 'edit');
            }
        } catch (error) {
            console.error('Error updating payment method:', error);
            this.showAlert('danger', 'An error occurred while updating the payment method');
        } finally {
            this.setLoading(form, false);
        }
    }
    
    async editPaymentMethod(id) {
        try {
            const response = await fetch(this.apiBase + `details.php?id=${id}`);
            const result = await response.json();
            
            if (result.success) {
                const method = result.data;
                this.populateEditForm(method);
                new bootstrap.Modal(document.getElementById('editPaymentMethodModal')).show();
            } else {
                this.showAlert('danger', 'Failed to load payment method details');
            }
        } catch (error) {
            console.error('Error loading payment method:', error);
            this.showAlert('danger', 'An error occurred while loading payment method details');
        }
    }
    
    populateEditForm(method) {
        document.getElementById('editMethodId').value = method.id;
        document.getElementById('editMethodName').value = method.name;
        document.getElementById('editMethodType').value = method.type;
        document.getElementById('editMethodStatus').value = method.status;
        document.getElementById('editSortOrder').value = method.sort_order;
        
        // Generate dynamic fields first
        this.generateDynamicFields(method.type, 'editDynamicFields');
        
        // Populate dynamic fields with existing data
        setTimeout(() => {
            const details = method.details_array || {};
            Object.keys(details).forEach(key => {
                const field = document.getElementById(`editDynamicFields_${key}`);
                if (field) {
                    field.value = details[key];
                }
            });
        }, 100);
    }
    
    async viewPaymentMethod(id) {
        try {
            const response = await fetch(this.apiBase + `details.php?id=${id}`);
            const result = await response.json();
            
            if (result.success) {
                const method = result.data;
                this.populateViewModal(method);
                new bootstrap.Modal(document.getElementById('viewPaymentMethodModal')).show();
            } else {
                this.showAlert('danger', 'Failed to load payment method details');
            }
        } catch (error) {
            console.error('Error loading payment method:', error);
            this.showAlert('danger', 'An error occurred while loading payment method details');
        }
    }
    
    populateViewModal(method) {
        const container = document.getElementById('viewPaymentMethodContent');
        const details = method.formatted_details || {};
        const stats = method.stats || {};
        
        let html = `
        <div class="row mb-3">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <div class="payment-detail-item">
                    <div class="label">Name</div>
                    <div class="value">${method.name}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="payment-detail-item">
                    <div class="label">Type</div>
                    <div class="value">
                        <i class="${this.getTypeIcon(method.type)} me-2"></i>
                        ${this.getTypeDisplayName(method.type)}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="payment-detail-item">
                    <div class="label">Status</div>
                    <div class="value">
                        <span class="badge bg-${method.status === 'active' ? 'success' : 'secondary'}">
                            ${method.status.charAt(0).toUpperCase() + method.status.slice(1)}
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="payment-detail-item">
                    <div class="label">Sort Order</div>
                    <div class="value">${method.sort_order}</div>
                </div>
            </div>
        </div>`;
        
        if (Object.keys(details).length > 0) {
            html += '<h6>Payment Details</h6><div class="payment-details-grid">';
            Object.keys(details).forEach(key => {
                html += `
                <div class="payment-detail-item">
                    <div class="label">${key}</div>
                    <div class="value">${details[key]}</div>
                </div>`;
            });
            html += '</div>';
        }
        
        html += `
        <div class="payment-stats">
            <h6>Usage Statistics</h6>
            <div class="stat-item">
                <span class="stat-label">Total Deposits</span>
                <span class="stat-value">${stats.deposits_count || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Total Amount</span>
                <span class="stat-value">$${parseFloat(stats.total_deposits || 0).toLocaleString()}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Pending Deposits</span>
                <span class="stat-value">${stats.pending_deposits || 0}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Created</span>
                <span class="stat-value">${new Date(method.created_at).toLocaleDateString()}</span>
            </div>
        </div>`;
        
        container.innerHTML = html;
    }
    
    async togglePaymentMethodStatus(id, currentStatus) {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        const action = newStatus === 'active' ? 'activate' : 'deactivate';
        
        if (!confirm(`Are you sure you want to ${action} this payment method?`)) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('id', id);
            formData.append('status', newStatus);
            formData.append('csrf_token', this.getCSRFToken());
            
            const response = await fetch(this.apiBase + 'toggle-status.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', `Payment method ${action}d successfully!`);
                this.refreshTable();
            } else {
                this.showAlert('danger', result.message || `Failed to ${action} payment method`);
            }
        } catch (error) {
            console.error('Error toggling status:', error);
            this.showAlert('danger', 'An error occurred while updating the status');
        }
    }
    
    async deletePaymentMethod(id) {
        if (!confirm('Are you sure you want to delete this payment method? This action cannot be undone.')) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('id', id);
            formData.append('csrf_token', this.getCSRFToken());
            
            const response = await fetch(this.apiBase + 'delete.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', 'Payment method deleted successfully!');
                this.refreshTable();
            } else {
                this.showAlert('danger', result.message || 'Failed to delete payment method');
            }
        } catch (error) {
            console.error('Error deleting payment method:', error);
            this.showAlert('danger', 'An error occurred while deleting the payment method');
        }
    }
    
    async updateSortOrder() {
        const rows = document.querySelectorAll('#paymentMethodsTableBody tr');
        const methodIds = Array.from(rows).map(row => row.dataset.methodId);
        
        try {
            const formData = new FormData();
            formData.append('method_ids', JSON.stringify(methodIds));
            formData.append('csrf_token', this.getCSRFToken());
            
            const response = await fetch(this.apiBase + 'update-sort-order.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', 'Sort order updated successfully!');
                // Update the sort order numbers in the UI
                rows.forEach((row, index) => {
                    const sortSpan = row.querySelector('.sort-handle span');
                    if (sortSpan) {
                        sortSpan.textContent = index + 1;
                    }
                });
            } else {
                this.showAlert('warning', 'Failed to update sort order');
                this.refreshTable(); // Refresh to restore original order
            }
        } catch (error) {
            console.error('Error updating sort order:', error);
            this.showAlert('warning', 'An error occurred while updating sort order');
            this.refreshTable();
        }
    }
    
    async refreshTable() {
        try {
            const response = await fetch(this.apiBase + 'list.php');
            const result = await response.json();
            
            if (result.success) {
                document.getElementById('paymentMethodsTableBody').innerHTML = result.html;
                this.setupSortable(); // Re-initialize sortable
            }
        } catch (error) {
            console.error('Error refreshing table:', error);
        }
    }
    
    getTypeIcon(type) {
        const icons = {
            crypto: 'fab fa-bitcoin text-warning',
            bank: 'fas fa-university text-primary',
            paypal: 'fab fa-paypal text-info',
            other: 'fas fa-credit-card text-secondary'
        };
        return icons[type] || 'fas fa-credit-card text-secondary';
    }
    
    getTypeDisplayName(type) {
        const names = {
            crypto: 'Cryptocurrency',
            bank: 'Bank Transfer',
            paypal: 'PayPal',
            other: 'Other'
        };
        return names[type] || type;
    }
    
    setLoading(form, loading) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const inputs = form.querySelectorAll('input, select, textarea');
        
        if (loading) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            inputs.forEach(input => input.disabled = true);
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = submitBtn.dataset.originalText || 
                (form.id === 'addPaymentMethodForm' ? 
                    '<i class="fas fa-save me-2"></i>Add Payment Method' : 
                    '<i class="fas fa-save me-2"></i>Update Payment Method');
            inputs.forEach(input => input.disabled = false);
        }
    }
    
    resetForm(formId) {
        const form = document.getElementById(formId);
        form.reset();
        
        // Clear dynamic fields
        const dynamicContainer = form.querySelector('[id$="DynamicFields"]');
        if (dynamicContainer) {
            dynamicContainer.innerHTML = '';
        }
        
        // Clear error messages
        form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
    }
    
    displayFormErrors(errors, prefix = '') {
        Object.keys(errors).forEach(field => {
            const fieldName = prefix ? `${prefix}${field.charAt(0).toUpperCase() + field.slice(1)}` : field;
            const input = document.getElementById(fieldName) || 
                         document.querySelector(`[name="${field}"]`) ||
                         document.querySelector(`[name="details[${field}]"]`);
            
            if (input) {
                input.classList.add('is-invalid');
                
                // Remove existing error message
                const existingError = input.parentNode.querySelector('.invalid-feedback');
                if (existingError) {
                    existingError.remove();
                }
                
                // Add new error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'invalid-feedback';
                errorDiv.textContent = errors[field];
                input.parentNode.appendChild(errorDiv);
            }
        });
    }
    
    showAlert(type, message) {
        // Remove existing alerts
        document.querySelectorAll('.alert').forEach(alert => alert.remove());
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    getCSRFToken() {
        return document.querySelector('input[name="csrf_token"]').value;
    }
}

// Global functions for onclick handlers
function editPaymentMethod(id) {
    window.paymentMethodsManager.editPaymentMethod(id);
}

function viewPaymentMethod(id) {
    window.paymentMethodsManager.viewPaymentMethod(id);
}

function togglePaymentMethodStatus(id, currentStatus) {
    window.paymentMethodsManager.togglePaymentMethodStatus(id, currentStatus);
}

function deletePaymentMethod(id) {
    window.paymentMethodsManager.deletePaymentMethod(id);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.paymentMethodsManager = new PaymentMethodsManager();
});