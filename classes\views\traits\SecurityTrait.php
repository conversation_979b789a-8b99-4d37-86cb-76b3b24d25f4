<?php
/**
 * SecurityTrait - Shared security methods for views
 */
trait SecurityTrait {
    
    /**
     * Safely output user data with HTML encoding
     */
    protected function safeOutput($data, $default = '') {
        if ($data === null || $data === '') {
            return htmlspecialchars($default, ENT_QUOTES, 'UTF-8');
        }
        
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Format currency with proper escaping
     */
    protected function formatCurrency($amount, $currency = '$') {
        $formatted = number_format((float)$amount, 2);
        return $this->safeOutput($currency) . $formatted;
    }
    
    /**
     * Format date with proper escaping
     */
    protected function formatDate($date, $format = 'M j, Y') {
        if (!$date) {
            return $this->safeOutput('Never');
        }
        
        return $this->safeOutput(date($format, strtotime($date)));
    }
    
    /**
     * Generate secure asset URL
     */
    protected function assetUrl($path) {
        $baseUrl = rtrim(BASE_URL ?? '', '/');
        $path = ltrim($path, '/');
        return $this->safeOutput($baseUrl . '/' . $path);
    }
    
    /**
     * Generate secure route URL
     */
    protected function routeUrl($path) {
        $baseUrl = rtrim(BASE_URL ?? '', '/');
        $path = ltrim($path, '/');
        return $this->safeOutput($baseUrl . '/' . $path);
    }
}
?>