<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../classes/models/SystemSetting.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    $currentUser = getCurrentUser();
    
    // Define default settings
    $defaultSettings = [
        // General settings
        'site_name' => 'Coinage Trading',
        'site_currency' => 'USD',
        'currency_symbol' => '$',
        'base_url' => '',
        'contact_email' => '',
        'contact_phone' => '',
        'company_address' => '',
        
        // Financial settings
        'registration_bonus' => 0,
        'deposit_bonus_percent' => 10,
        'min_withdrawal_amount' => 50,
        'max_withdrawal_amount' => 10000,
        'withdrawal_fee_percent' => 2,
        
        // Security settings
        'email_verification_required' => true,
        'two_fa_enforcement' => false,
        'kyc_verification_required' => false,
        'max_login_attempts' => 5,
        'account_lockout_minutes' => 30,
        
        // Feature settings
        'email_notifications' => true,
        'sms_notifications' => false,
        'maintenance_mode' => false,
        'debug_mode' => false,
        'session_timeout_minutes' => 60,
        
        // Appearance settings
        'primary_color' => '#007bff',
        'secondary_color' => '#6c757d',
        'logo_url' => ''
    ];
    
    // Reset all settings to defaults
    $success = SystemSetting::resetToDefaults($defaultSettings, $currentUser['id']);
    
    if ($success) {
        // Log the action
        logAuditTrail($currentUser['id'], 'system_settings', 'reset_defaults', 'All system settings reset to default values');
        
        echo json_encode([
            'success' => true,
            'message' => 'All settings have been reset to their default values'
        ]);
    } else {
        throw new Exception('Failed to reset settings to defaults');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>