<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * User Model - Handles user data and operations
 */
class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = [
        'id', 'username', 'email', 'password', 'first_name', 'last_name', 'phone',
        'role', 'status', 'balance', 'bonus', 'total_deposit', 'total_withdrawal',
        'kyc_status', 'two_fa_enabled', 'two_fa_secret', 'email_verified',
        'email_verification_token', 'password_reset_token', 'password_reset_expires',
        'profile_picture', 'last_login', 'login_attempts', 'locked_until'
    ];
    protected $hidden = ['password', 'two_fa_secret', 'password_reset_token'];
    
    // User roles
    const ROLE_USER = 'user';
    const ROLE_ADMIN = 'admin';
    const ROLE_SUPERADMIN = 'superadmin';
    
    // User statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_PENDING = 'pending';
    
    // KYC statuses
    const KYC_UNVERIFIED = 'unverified';
    const KYC_PENDING = 'pending';
    const KYC_VERIFIED = 'verified';
    const KYC_REJECTED = 'rejected';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // Username validation
        if (empty($this->username)) {
            $errors['username'] = 'Username is required';
        } elseif (strlen($this->username) < 3) {
            $errors['username'] = 'Username must be at least 3 characters';
        } elseif (strlen($this->username) > 50) {
            $errors['username'] = 'Username must not exceed 50 characters';
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $this->username)) {
            $errors['username'] = 'Username can only contain letters, numbers, and underscores';
        } elseif ($this->isUsernameTaken()) {
            $errors['username'] = 'Username is already taken';
        }
        
        // Email validation
        if (empty($this->email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email format';
        } elseif (strlen($this->email) > 100) {
            $errors['email'] = 'Email must not exceed 100 characters';
        } elseif ($this->isEmailTaken()) {
            $errors['email'] = 'Email is already registered';
        }
        
        // Password validation (only for new users or when password is being changed)
        if (!$this->exists && empty($this->password)) {
            $errors['password'] = 'Password is required';
        } elseif (!empty($this->password) && strlen($this->password) < 6) {
            $errors['password'] = 'Password must be at least 6 characters';
        }
        
        // First name validation
        if (empty($this->first_name)) {
            $errors['first_name'] = 'First name is required';
        } elseif (strlen($this->first_name) > 50) {
            $errors['first_name'] = 'First name must not exceed 50 characters';
        }
        
        // Last name validation
        if (empty($this->last_name)) {
            $errors['last_name'] = 'Last name is required';
        } elseif (strlen($this->last_name) > 50) {
            $errors['last_name'] = 'Last name must not exceed 50 characters';
        }
        
        // Phone validation (optional)
        if (!empty($this->phone) && strlen($this->phone) > 20) {
            $errors['phone'] = 'Phone number must not exceed 20 characters';
        }
        
        // Role validation
        if (!empty($this->role) && !in_array($this->role, [self::ROLE_USER, self::ROLE_ADMIN, self::ROLE_SUPERADMIN])) {
            $errors['role'] = 'Invalid role';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_SUSPENDED, self::STATUS_PENDING])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Balance validation
        if (isset($this->balance) && $this->balance < 0) {
            $errors['balance'] = 'Balance cannot be negative';
        }
        
        // Bonus validation
        if (isset($this->bonus) && $this->bonus < 0) {
            $errors['bonus'] = 'Bonus cannot be negative';
        }
        
        return $errors;
    }
    
    /**
     * Check if username is already taken
     */
    private function isUsernameTaken() {
        $sql = "SELECT id FROM {$this->table} WHERE username = :username";
        if ($this->exists) {
            $sql .= " AND id != :id";
        }
        
        $stmt = $this->db->prepare($sql);
        $params = ['username' => $this->username];
        if ($this->exists) {
            $params['id'] = $this->getId();
        }
        
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Check if email is already taken
     */
    private function isEmailTaken() {
        $sql = "SELECT id FROM {$this->table} WHERE email = :email";
        if ($this->exists) {
            $sql .= " AND id != :id";
        }
        
        $stmt = $this->db->prepare($sql);
        $params = ['email' => $this->email];
        if ($this->exists) {
            $params['id'] = $this->getId();
        }
        
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Hash password before saving
     */
    public function save() {
        if (!empty($this->password) && !password_get_info($this->password)['algo']) {
            $this->password = password_hash($this->password, PASSWORD_DEFAULT);
        }
        
        return parent::save();
    }
    
    /**
     * Verify password
     */
    public function verifyPassword($password) {
        return password_verify($password, $this->password);
    }
    
    /**
     * Update balance
     */
    public function updateBalance($amount, $type = 'credit') {
        $this->beginTransaction();
        
        try {
            $oldBalance = $this->balance;
            
            if ($type === 'credit') {
                $this->balance += $amount;
            } else {
                $this->balance -= $amount;
                if ($this->balance < 0) {
                    throw new Exception('Insufficient balance');
                }
            }
            
            if ($this->save()) {
                $this->commit();
                return true;
            } else {
                $this->rollback();
                return false;
            }
        } catch (Exception $e) {
            $this->rollback();
            error_log("Balance update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update bonus
     */
    public function updateBonus($amount, $type = 'credit') {
        if ($type === 'credit') {
            $this->bonus += $amount;
        } else {
            $this->bonus -= $amount;
            if ($this->bonus < 0) {
                $this->bonus = 0;
            }
        }
        
        return $this->save();
    }
    
    /**
     * Check if user is active
     */
    public function isActive() {
        return $this->status === self::STATUS_ACTIVE;
    }
    
    /**
     * Check if user is suspended
     */
    public function isSuspended() {
        return $this->status === self::STATUS_SUSPENDED;
    }
    
    /**
     * Check if user is admin
     */
    public function isAdmin() {
        return in_array($this->role, [self::ROLE_ADMIN, self::ROLE_SUPERADMIN]);
    }
    
    /**
     * Check if user is super admin
     */
    public function isSuperAdmin() {
        return $this->role === self::ROLE_SUPERADMIN;
    }
    
    /**
     * Check if account is locked
     */
    public function isLocked() {
        return $this->locked_until && strtotime($this->locked_until) > time();
    }
    
    /**
     * Lock account for specified minutes
     */
    public function lockAccount($minutes = 30) {
        $this->locked_until = date('Y-m-d H:i:s', time() + ($minutes * 60));
        return $this->save();
    }
    
    /**
     * Unlock account
     */
    public function unlockAccount() {
        $this->locked_until = null;
        $this->login_attempts = 0;
        return $this->save();
    }
    
    /**
     * Increment login attempts
     */
    public function incrementLoginAttempts() {
        $this->login_attempts++;
        
        // Lock account after 5 failed attempts
        if ($this->login_attempts >= 5) {
            $this->lockAccount();
        }
        
        return $this->save();
    }
    
    /**
     * Reset login attempts
     */
    public function resetLoginAttempts() {
        $this->login_attempts = 0;
        return $this->save();
    }
    
    /**
     * Update last login
     */
    public function updateLastLogin() {
        $this->last_login = date('Y-m-d H:i:s');
        return $this->save();
    }
    
    /**
     * Generate email verification token
     */
    public function generateEmailVerificationToken() {
        $this->email_verification_token = bin2hex(random_bytes(32));
        return $this->save();
    }
    
    /**
     * Verify email
     */
    public function verifyEmail() {
        $this->email_verified = true;
        $this->email_verification_token = null;
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }
    
    /**
     * Generate password reset token
     */
    public function generatePasswordResetToken() {
        $this->password_reset_token = bin2hex(random_bytes(32));
        $this->password_reset_expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
        return $this->save();
    }
    
    /**
     * Reset password
     */
    public function resetPassword($newPassword) {
        $this->password = $newPassword; // Will be hashed in save()
        $this->password_reset_token = null;
        $this->password_reset_expires = null;
        return $this->save();
    }
    
    /**
     * Get user's deposits
     */
    public function getDeposits($limit = null) {
        require_once __DIR__ . '/Deposit.php';
        
        $sql = "SELECT * FROM deposits WHERE user_id = :user_id ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $this->getId()]);
        $results = $stmt->fetchAll();
        
        $deposits = [];
        foreach ($results as $data) {
            $deposits[] = new Deposit($data);
        }
        
        return $deposits;
    }
    
    /**
     * Get user's withdrawals
     */
    public function getWithdrawals($limit = null) {
        require_once __DIR__ . '/Withdrawal.php';
        
        $sql = "SELECT * FROM withdrawals WHERE user_id = :user_id ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $this->getId()]);
        $results = $stmt->fetchAll();
        
        $withdrawals = [];
        foreach ($results as $data) {
            $withdrawals[] = new Withdrawal($data);
        }
        
        return $withdrawals;
    }
    
    /**
     * Get user's transactions
     */
    public function getTransactions($limit = null) {
        require_once __DIR__ . '/Transaction.php';
        
        $sql = "SELECT * FROM transactions WHERE user_id = :user_id ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['user_id' => $this->getId()]);
        $results = $stmt->fetchAll();
        
        $transactions = [];
        foreach ($results as $data) {
            $transactions[] = new Transaction($data);
        }
        
        return $transactions;
    }
    
    /**
     * Get full name
     */
    public function getFullName() {
        return trim($this->first_name . ' ' . $this->last_name);
    }
    
    /**
     * Find user by email
     */
    public static function findByEmail($email) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE email = :email LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['email' => $email]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Find user by username
     */
    public static function findByUsername($username) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE username = :username LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['username' => $username]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Find user by email verification token
     */
    public static function findByEmailVerificationToken($token) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE email_verification_token = :token LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['token' => $token]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Find user by password reset token
     */
    public static function findByPasswordResetToken($token) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE password_reset_token = :token 
                AND password_reset_expires > NOW() LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['token' => $token]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
}
?>