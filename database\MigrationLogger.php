<?php
/**
 * Migration Logger - Handles logging and display of migration results
 */
class MigrationLogger {
    private $errors = [];
    private $success = [];
    
    public function logError($message) {
        $this->errors[] = $message;
        error_log("Migration Error: " . $message);
    }
    
    public function logSuccess($message) {
        $this->success[] = $message;
    }
    
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    public function getErrors() {
        return $this->errors;
    }
    
    public function getSuccessMessages() {
        return $this->success;
    }
    
    public function displayResults() {
        echo "<h2>📊 Migration Results</h2>";
        
        $this->displaySuccessMessages();
        $this->displayErrorMessages();
        $this->displayDefaultCredentials();
        $this->displayNextSteps();
    }
    
    private function displaySuccessMessages() {
        if (empty($this->success)) return;
        
        echo $this->renderMessageBox(
            '✅ Success Messages',
            $this->success,
            '#d4edda',
            '#c3e6cb',
            '#155724'
        );
    }
    
    private function displayErrorMessages() {
        if (empty($this->errors)) return;
        
        echo $this->renderMessageBox(
            '❌ Error Messages',
            $this->errors,
            '#f8d7da',
            '#f5c6cb',
            '#721c24'
        );
    }
    
    private function renderMessageBox($title, $messages, $bgColor, $borderColor, $textColor) {
        $html = "<div style='background: {$bgColor}; border: 1px solid {$borderColor}; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        $html .= "<h3 style='color: {$textColor}; margin-top: 0;'>{$title}</h3>";
        
        foreach ($messages as $message) {
            $html .= "<p style='color: {$textColor}; margin: 5px 0;'>{$message}</p>";
        }
        
        $html .= "</div>";
        return $html;
    }
    
    private function displayDefaultCredentials() {
        $credentials = [
            'Super Admin' => ['username' => 'superadmin', 'password' => 'admin123'],
            'Admin' => ['username' => 'admin', 'password' => 'admin123'],
            'Demo User' => ['username' => 'demo', 'password' => 'demo123']
        ];
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404; margin-top: 0;'>🔑 Default Login Credentials</h3>";
        
        foreach ($credentials as $role => $creds) {
            echo "<p style='color: #856404;'><strong>{$role}:</strong></p>";
            echo "<p style='color: #856404; margin-left: 20px;'>Username: {$creds['username']}<br>Password: {$creds['password']}</p>";
        }
        
        echo "<p style='color: #d63384;'><strong>⚠️ IMPORTANT: Change these passwords immediately after first login!</strong></p>";
        echo "</div>";
    }
    
    private function displayNextSteps() {
        $steps = [
            'Test the database connection using <code>test_folder/test_connections.php</code>',
            'Create the layout system and authentication (Task 2)',
            'Change default passwords for security',
            'Configure SMTP settings for email functionality',
            'Customize system settings through the super admin panel'
        ];
        
        echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #383d41; margin-top: 0;'>🚀 Next Steps</h3>";
        echo "<ol style='color: #383d41;'>";
        
        foreach ($steps as $step) {
            echo "<li>{$step}</li>";
        }
        
        echo "</ol>";
        echo "</div>";
    }
}