<?php
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/services/SessionManager.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';
require_once __DIR__ . '/../../classes/models/Withdrawal.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/PaymentMethod.php';
require_once __DIR__ . '/../../classes/views/AdminWithdrawalManagementView.php';

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: ' . url('admin/login.php'));
    exit();
}

// CSRF protection is handled by SessionManager - no init needed

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Get withdrawals with details
$withdrawals = [];
$totalWithdrawals = 0;

try {
    if ($status === 'all') {
        $withdrawals = Withdrawal::getWithDetails($limit, $offset);
        $totalWithdrawals = Withdrawal::count();
    } else {
        $withdrawals = Withdrawal::getWithDetails($limit, $offset, $status);
        $totalWithdrawals = Withdrawal::count("status = ?", [$status]);
    }
} catch (Exception $e) {
    error_log("Error fetching withdrawals: " . $e->getMessage());
    $withdrawals = [];
}

// Get statistics
$stats = [
    'pending' => Withdrawal::getPendingCount(),
    'approved' => Withdrawal::count("status = 'approved'"),
    'processing' => Withdrawal::count("status = 'processing'"),
    'completed' => Withdrawal::count("status = 'completed'"),
    'rejected' => Withdrawal::count("status = 'rejected'"),
    'total_amount' => Withdrawal::getTotalAmountByStatus('completed')
];

// Calculate pagination
$totalPages = ceil($totalWithdrawals / $limit);

// Render view
$view = new AdminWithdrawalManagementView('Withdrawal Management', [
    'withdrawals' => $withdrawals,
    'stats' => $stats,
    'currentStatus' => $status,
    'currentPage' => $page,
    'totalPages' => $totalPages,
    'totalWithdrawals' => $totalWithdrawals,
    'csrfToken' => CSRFProtection::generateToken()
]);
$view->render();
?>