/**
 * Admin Withdrawal Management Styles
 */

/* Main container styles */
.withdrawal-management-container {
    padding: 20px;
}

/* Statistics cards */
.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h4 {
    font-size: 2rem;
    font-weight: 700;
}

.stats-card p {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Filter buttons */
.filter-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.filter-buttons .btn.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

/* Search input */
.search-container {
    max-width: 400px;
}

/* Table styles */
.withdrawals-table {
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.withdrawals-table .table {
    margin-bottom: 0;
}

.withdrawals-table .table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem 0.75rem;
}

.withdrawals-table .table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.withdrawals-table .table tbody tr:hover {
    background-color: #f8f9fa;
}

/* User avatar */
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    font-weight: 600;
    margin-right: 0.75rem;
}

/* Status badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-approved {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-processing {
    background-color: #cce7ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Action buttons */
.action-buttons .btn {
    margin-right: 0.25rem;
    padding: 0.375rem 0.5rem;
}

.action-buttons .btn:last-child {
    margin-right: 0;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Form styles */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.25rem;
}

.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Checkbox styles */
.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    margin-top: 0;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Loading states */
.btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .filter-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-buttons .btn {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .search-container {
        max-width: 100%;
        margin-top: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .action-buttons .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 576px) {
    .stats-card h4 {
        font-size: 1.5rem;
    }
    
    .stats-card p {
        font-size: 0.8rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }
    
    .status-badge {
        font-size: 0.625rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 0;
}

/* Utility classes */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.fw-bold {
    font-weight: 700 !important;
}

.text-muted {
    color: #6c757d !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.mb-0 {
    margin-bottom: 0 !important;
}