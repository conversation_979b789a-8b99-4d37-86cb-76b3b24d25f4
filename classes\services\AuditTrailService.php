<?php
require_once __DIR__ . '/../models/BaseModel.php';

/**
 * AuditTrailService - Comprehensive audit trail and logging system
 */
class AuditTrailService {
    
    /**
     * Log an audit event
     */
    public static function logEvent($action, $entityType, $entityId, $changes = [], $userId = null, $additionalData = []) {
        $db = getDB();
        
        // Get user information
        if (!$userId && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }
        
        // Get request information
        $ipAddress = self::getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Prepare audit data
        $auditData = [
            'user_id' => $userId,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'changes' => json_encode($changes),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'additional_data' => json_encode($additionalData),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $sql = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, changes, ip_address, user_agent, additional_data, created_at) 
                    VALUES (:user_id, :action, :entity_type, :entity_id, :changes, :ip_address, :user_agent, :additional_data, :created_at)";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($auditData);
            
            return $db->lastInsertId();
            
        } catch (Exception $e) {
            error_log("Audit logging error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log user authentication events
     */
    public static function logAuthEvent($action, $userId, $success = true, $additionalData = []) {
        $changes = [
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return self::logEvent($action, 'user', $userId, $changes, $userId, $additionalData);
    }
    
    /**
     * Log financial transaction events
     */
    public static function logFinancialEvent($action, $entityType, $entityId, $oldData, $newData, $userId = null) {
        $changes = self::calculateChanges($oldData, $newData);
        
        $additionalData = [
            'financial_impact' => true,
            'amount' => $newData['amount'] ?? $oldData['amount'] ?? 0
        ];
        
        return self::logEvent($action, $entityType, $entityId, $changes, $userId, $additionalData);
    }
    
    /**
     * Log admin actions
     */
    public static function logAdminAction($action, $entityType, $entityId, $changes = [], $adminId = null, $targetUserId = null) {
        $additionalData = [
            'admin_action' => true,
            'target_user_id' => $targetUserId
        ];
        
        return self::logEvent($action, $entityType, $entityId, $changes, $adminId, $additionalData);
    }
    
    /**
     * Log system configuration changes
     */
    public static function logSystemChange($action, $setting, $oldValue, $newValue, $userId = null) {
        $changes = [
            'setting' => $setting,
            'old_value' => $oldValue,
            'new_value' => $newValue
        ];
        
        $additionalData = [
            'system_change' => true,
            'critical' => self::isCriticalSetting($setting)
        ];
        
        return self::logEvent($action, 'system_setting', $setting, $changes, $userId, $additionalData);
    }
    
    /**
     * Get audit trail with filtering and pagination
     */
    public function getAuditTrail($filters = [], $page = 1, $limit = 50) {
        $db = getDB();
        $offset = ($page - 1) * $limit;
        
        $whereConditions = [];
        $params = [];
        
        // Apply filters
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "al.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['action'])) {
            $whereConditions[] = "al.action = :action";
            $params['action'] = $filters['action'];
        }
        
        if (!empty($filters['entity_type'])) {
            $whereConditions[] = "al.entity_type = :entity_type";
            $params['entity_type'] = $filters['entity_type'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "al.created_at >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "al.created_at <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['ip_address'])) {
            $whereConditions[] = "al.ip_address = :ip_address";
            $params['ip_address'] = $filters['ip_address'];
        }
        
        if (!empty($filters['search'])) {
            $whereConditions[] = "(al.action LIKE :search OR al.entity_type LIKE :search OR u.username LIKE :search)";
            $params['search'] = '%' . $filters['search'] . '%';
        }
        
        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
        
        // Get total count
        $countSql = "SELECT COUNT(*) FROM audit_logs al 
                     LEFT JOIN users u ON al.user_id = u.id 
                     {$whereClause}";
        $stmt = $db->prepare($countSql);
        $stmt->execute($params);
        $totalCount = $stmt->fetchColumn();
        
        // Get paginated results
        $sql = "SELECT 
                    al.*,
                    u.username, u.first_name, u.last_name, u.role
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT {$limit} OFFSET {$offset}";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $auditLogs = $stmt->fetchAll();
        
        // Process audit logs for display
        foreach ($auditLogs as &$log) {
            $log['changes_decoded'] = json_decode($log['changes'], true) ?? [];
            $log['additional_data_decoded'] = json_decode($log['additional_data'], true) ?? [];
            $log['formatted_changes'] = $this->formatChanges($log['changes_decoded']);
        }
        
        return [
            'audit_logs' => $auditLogs,
            'total_count' => (int) $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ];
    }
    
    /**
     * Get audit statistics
     */
    public function getAuditStatistics($dateFrom = null, $dateTo = null) {
        $db = getDB();
        
        $whereClause = '';
        $params = [];
        
        if ($dateFrom && $dateTo) {
            $whereClause = 'WHERE created_at >= :date_from AND created_at <= :date_to';
            $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
        }
        
        // Action statistics
        $sql = "SELECT action, COUNT(*) as count 
                FROM audit_logs 
                {$whereClause}
                GROUP BY action 
                ORDER BY count DESC";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $actionStats = $stmt->fetchAll();
        
        // Entity type statistics
        $sql = "SELECT entity_type, COUNT(*) as count 
                FROM audit_logs 
                {$whereClause}
                GROUP BY entity_type 
                ORDER BY count DESC";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $entityStats = $stmt->fetchAll();
        
        // User activity statistics
        $sql = "SELECT 
                    u.username, u.first_name, u.last_name, u.role,
                    COUNT(al.id) as activity_count
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                {$whereClause}
                GROUP BY al.user_id
                ORDER BY activity_count DESC
                LIMIT 10";
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $userActivity = $stmt->fetchAll();
        
        // Critical events (financial, admin actions, system changes)
        $criticalSql = "SELECT COUNT(*) as critical_events
                FROM audit_logs 
                WHERE JSON_EXTRACT(additional_data, '$.financial_impact') = true 
                   OR JSON_EXTRACT(additional_data, '$.admin_action') = true 
                   OR JSON_EXTRACT(additional_data, '$.critical') = true";
        
        if ($whereClause) {
            $criticalSql .= ' AND ' . str_replace('WHERE ', '', $whereClause);
        }
        
        $stmt = $db->prepare($criticalSql);
        $stmt->execute($params);
        $criticalEvents = $stmt->fetchColumn();
        
        return [
            'action_stats' => $actionStats,
            'entity_stats' => $entityStats,
            'user_activity' => $userActivity,
            'critical_events' => (int) $criticalEvents
        ];
    }
    
    /**
     * Get entity audit history
     */
    public function getEntityHistory($entityType, $entityId) {
        $db = getDB();
        
        $sql = "SELECT 
                    al.*,
                    u.username, u.first_name, u.last_name, u.role
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.entity_type = :entity_type AND al.entity_id = :entity_id
                ORDER BY al.created_at DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['entity_type' => $entityType, 'entity_id' => $entityId]);
        $history = $stmt->fetchAll();
        
        // Process history for display
        foreach ($history as &$entry) {
            $entry['changes_decoded'] = json_decode($entry['changes'], true) ?? [];
            $entry['additional_data_decoded'] = json_decode($entry['additional_data'], true) ?? [];
            $entry['formatted_changes'] = $this->formatChanges($entry['changes_decoded']);
        }
        
        return $history;
    }
    
    /**
     * Get suspicious activity alerts
     */
    public function getSuspiciousActivity($hours = 24) {
        $db = getDB();
        $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        $alerts = [];
        
        // Multiple failed login attempts
        $sql = "SELECT 
                    ip_address, 
                    COUNT(*) as failed_attempts,
                    MAX(created_at) as last_attempt
                FROM audit_logs 
                WHERE action = 'login_failed' 
                  AND created_at >= :since
                GROUP BY ip_address
                HAVING failed_attempts >= 5
                ORDER BY failed_attempts DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['since' => $since]);
        $failedLogins = $stmt->fetchAll();
        
        foreach ($failedLogins as $login) {
            $alerts[] = [
                'type' => 'multiple_failed_logins',
                'severity' => 'high',
                'message' => "Multiple failed login attempts from IP: {$login['ip_address']} ({$login['failed_attempts']} attempts)",
                'data' => $login
            ];
        }
        
        // Large financial transactions
        $sql = "SELECT 
                    al.*,
                    u.username
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.entity_type IN ('deposit', 'withdrawal')
                  AND al.created_at >= :since
                  AND JSON_EXTRACT(al.additional_data, '$.amount') > 10000
                ORDER BY JSON_EXTRACT(al.additional_data, '$.amount') DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['since' => $since]);
        $largeTransactions = $stmt->fetchAll();
        
        foreach ($largeTransactions as $transaction) {
            $amount = json_decode($transaction['additional_data'], true)['amount'] ?? 0;
            $alerts[] = [
                'type' => 'large_transaction',
                'severity' => 'medium',
                'message' => "Large {$transaction['entity_type']} of $" . number_format($amount, 2) . " by user: {$transaction['username']}",
                'data' => $transaction
            ];
        }
        
        // Rapid successive actions
        $sql = "SELECT 
                    user_id,
                    action,
                    COUNT(*) as action_count,
                    u.username
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.id
                WHERE al.created_at >= :since
                  AND al.user_id IS NOT NULL
                GROUP BY user_id, action
                HAVING action_count > 50
                ORDER BY action_count DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['since' => $since]);
        $rapidActions = $stmt->fetchAll();
        
        foreach ($rapidActions as $action) {
            $alerts[] = [
                'type' => 'rapid_actions',
                'severity' => 'medium',
                'message' => "User {$action['username']} performed {$action['action']} {$action['action_count']} times in {$hours} hours",
                'data' => $action
            ];
        }
        
        return $alerts;
    }
    
    /**
     * Export audit trail to CSV
     */
    public function exportAuditTrail($filters = []) {
        $auditData = $this->getAuditTrail($filters, 1, 10000);
        
        $csv = "Timestamp,User,Action,Entity Type,Entity ID,IP Address,Changes,User Agent\n";
        
        foreach ($auditData['audit_logs'] as $log) {
            $csv .= sprintf(
                "%s,%s,%s,%s,%s,%s,\"%s\",\"%s\"\n",
                $log['created_at'],
                $log['username'] ?? 'System',
                $log['action'],
                $log['entity_type'],
                $log['entity_id'],
                $log['ip_address'],
                str_replace('"', '""', $log['formatted_changes']),
                str_replace('"', '""', substr($log['user_agent'], 0, 100))
            );
        }
        
        return $csv;
    }
    
    /**
     * Calculate changes between old and new data
     */
    private static function calculateChanges($oldData, $newData) {
        $changes = [];
        
        if (!is_array($oldData)) $oldData = [];
        if (!is_array($newData)) $newData = [];
        
        // Find changed fields
        foreach ($newData as $key => $newValue) {
            $oldValue = $oldData[$key] ?? null;
            if ($oldValue !== $newValue) {
                $changes[$key] = [
                    'old' => $oldValue,
                    'new' => $newValue
                ];
            }
        }
        
        // Find removed fields
        foreach ($oldData as $key => $oldValue) {
            if (!array_key_exists($key, $newData)) {
                $changes[$key] = [
                    'old' => $oldValue,
                    'new' => null
                ];
            }
        }
        
        return $changes;
    }
    
    /**
     * Format changes for display
     */
    private function formatChanges($changes) {
        if (empty($changes)) {
            return 'No changes recorded';
        }
        
        $formatted = [];
        foreach ($changes as $field => $change) {
            if (isset($change['old']) && isset($change['new'])) {
                $formatted[] = "{$field}: '{$change['old']}' → '{$change['new']}'";
            } else {
                $formatted[] = "{$field}: " . json_encode($change);
            }
        }
        
        return implode('; ', $formatted);
    }
    
    /**
     * Get client IP address
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Check if a system setting is critical
     */
    private static function isCriticalSetting($setting) {
        $criticalSettings = [
            'site_maintenance',
            'registration_enabled',
            'withdrawal_enabled',
            'deposit_enabled',
            'two_factor_required',
            'kyc_required',
            'email_verification_required'
        ];
        
        return in_array($setting, $criticalSettings);
    }
    
    /**
     * Clean old audit logs (retention policy)
     */
    public static function cleanOldLogs($retentionDays = 365) {
        $db = getDB();
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$retentionDays} days"));
        
        try {
            $sql = "DELETE FROM audit_logs WHERE created_at < :cutoff_date";
            $stmt = $db->prepare($sql);
            $stmt->execute(['cutoff_date' => $cutoffDate]);
            
            $deletedCount = $stmt->rowCount();
            
            // Log the cleanup action
            self::logEvent('audit_cleanup', 'system', 'audit_logs', [
                'retention_days' => $retentionDays,
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate
            ]);
            
            return $deletedCount;
            
        } catch (Exception $e) {
            error_log("Audit log cleanup error: " . $e->getMessage());
            return false;
        }
    }
}
?>