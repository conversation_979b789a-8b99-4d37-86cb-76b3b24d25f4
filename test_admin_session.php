<?php
session_start();
require_once 'includes/functions.php';
require_once 'classes/services/AuthenticationManager.php';

echo "<h2>Admin Session Test</h2>";

echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Data:</strong></p>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";

echo "<p><strong>Is Logged In:</strong> " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";

if (isLoggedIn()) {
    $user = AuthenticationManager::getCurrentUser();
    if ($user) {
        echo "<p><strong>Current User:</strong></p>";
        echo "<p>ID: " . $user->id . "</p>";
        echo "<p>Username: " . $user->username . "</p>";
        echo "<p>Role: " . $user->role . "</p>";
        echo "<p>Status: " . $user->status . "</p>";
        
        echo "<p><strong>Has Admin Role:</strong> " . (hasRole(['admin', 'superadmin']) ? 'Yes' : 'No') . "</p>";
    } else {
        echo "<p><strong>Error:</strong> User object is null</p>";
    }
} else {
    echo "<p>User is not logged in</p>";
}
?>