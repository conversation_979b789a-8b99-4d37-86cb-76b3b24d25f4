<?php
require_once 'db_connect.php';

// Security Functions
function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Password Functions
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// User Authentication Functions
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function getUserRole() {
    return $_SESSION['role'] ?? $_SESSION['user_role'] ?? null;
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . 'login.php');
        exit();
    }
}

function requireRole($role) {
    requireLogin();
    if (getUserRole() !== $role) {
        header('Location: ' . BASE_URL . 'unauthorized.php');
        exit();
    }
}

function hasRole($role) {
    $userRole = getUserRole();
    
    // Handle array of roles
    if (is_array($role)) {
        return in_array($userRole, $role);
    }
    
    // Handle single role
    if ($role === 'user') {
        return in_array($userRole, ['user', 'admin', 'superadmin']);
    }
    if ($role === 'admin') {
        return in_array($userRole, ['admin', 'superadmin']);
    }
    return $userRole === $role;
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    try {
        $db = getDB();
        $stmt = $db->prepare('SELECT * FROM users WHERE id = ?');
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log('Error getting current user: ' . $e->getMessage());
        return null;
    }
}

// URL Helper Functions
function getBaseUrl() {
    // Always use the defined BASE_URL for consistency
    return BASE_URL;
}

function redirectTo($path) {
    // Use the url() function to ensure consistent URL generation
    header('Location: ' . url($path));
    exit();
}

function url($path = '') {
    return getBaseUrl() . ltrim($path, '/');
}

function asset($path) {
    return getBaseUrl() . 'assets/' . ltrim($path, '/');
}

// Database Helper Functions
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        return false;
    }
}

function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

// Utility Functions
function formatCurrency($amount) {
    return CURRENCY_SYMBOL . number_format($amount, 2);
}

function generateUniqueId($prefix = '') {
    return $prefix . uniqid() . random_int(1000, 9999);
}

function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

function displayAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        echo '<div class="alert alert-' . $alert['type'] . ' alert-dismissible fade show" role="alert">';
        echo $alert['message'];
        echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
        echo '</div>';
        unset($_SESSION['alert']);
    }
}
?>