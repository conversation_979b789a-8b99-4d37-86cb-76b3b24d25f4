<?php
require_once __DIR__ . '/CacheService.php';

/**
 * Database Optimization Service - Provides query optimization and indexing
 */
class DatabaseOptimizationService {
    private static $instance = null;
    private $db;
    private $cache;
    private $queryLog = [];
    private $slowQueryThreshold = 1.0; // 1 second
    
    private function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->cache = CacheService::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Execute optimized query with caching
     */
    public function executeQuery($sql, $params = [], $cacheKey = null, $cacheTTL = 300) {
        $startTime = microtime(true);
        
        // Try to get from cache first
        if ($cacheKey) {
            $cached = $this->cache->getCachedQuery($sql, $params);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // Execute query
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $executionTime = microtime(true) - $startTime;
        
        // Log query performance
        $this->logQuery($sql, $params, $executionTime);
        
        // Cache result if cache key provided
        if ($cacheKey && $executionTime < $this->slowQueryThreshold) {
            $this->cache->cacheQuery($sql, $params, $result, $cacheTTL);
        }
        
        return $result;
    }
    
    /**
     * Log query for performance analysis
     */
    private function logQuery($sql, $params, $executionTime) {
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'timestamp' => time(),
            'is_slow' => $executionTime > $this->slowQueryThreshold
        ];
        
        // Log slow queries to file
        if ($executionTime > $this->slowQueryThreshold) {
            $this->logSlowQuery($sql, $params, $executionTime);
        }
    }
    
    /**
     * Log slow queries to file
     */
    private function logSlowQuery($sql, $params, $executionTime) {
        $logDir = __DIR__ . '/../../logs/database';
        if (!file_exists($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'execution_time' => $executionTime,
            'sql' => $sql,
            'params' => $params,
            'backtrace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
        ];
        
        $logFile = $logDir . '/slow_queries_' . date('Y-m-d') . '.log';
        file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Create database indexes for optimization
     */
    public function createOptimizationIndexes() {
        $indexes = [
            // User table indexes
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified)",
            
            // Transaction table indexes
            "CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_user_type ON transactions(user_id, type)",
            "CREATE INDEX IF NOT EXISTS idx_transactions_user_status ON transactions(user_id, status)",
            
            // Deposit table indexes
            "CREATE INDEX IF NOT EXISTS idx_deposits_user_id ON deposits(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_deposits_status ON deposits(status)",
            "CREATE INDEX IF NOT EXISTS idx_deposits_created_at ON deposits(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_deposits_payment_method ON deposits(payment_method)",
            
            // Withdrawal table indexes
            "CREATE INDEX IF NOT EXISTS idx_withdrawals_user_id ON withdrawals(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_withdrawals_status ON withdrawals(status)",
            "CREATE INDEX IF NOT EXISTS idx_withdrawals_created_at ON withdrawals(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_withdrawals_payment_method ON withdrawals(payment_method)",
            
            // Support ticket indexes
            "CREATE INDEX IF NOT EXISTS idx_support_tickets_user_id ON support_tickets(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status)",
            "CREATE INDEX IF NOT EXISTS idx_support_tickets_priority ON support_tickets(priority)",
            "CREATE INDEX IF NOT EXISTS idx_support_tickets_category ON support_tickets(category)",
            "CREATE INDEX IF NOT EXISTS idx_support_tickets_created_at ON support_tickets(created_at)",
            
            // Trading plan indexes
            "CREATE INDEX IF NOT EXISTS idx_trading_plans_status ON trading_plans(status)",
            "CREATE INDEX IF NOT EXISTS idx_trading_plans_min_amount ON trading_plans(min_amount)",
            "CREATE INDEX IF NOT EXISTS idx_trading_plans_sort_order ON trading_plans(sort_order)",
            
            // System settings indexes
            "CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_public ON system_settings(is_public)",
            
            // Security audit indexes
            "CREATE INDEX IF NOT EXISTS idx_security_audit_user_id ON security_audit_logs(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_security_audit_event ON security_audit_logs(event_type)",
            "CREATE INDEX IF NOT EXISTS idx_security_audit_timestamp ON security_audit_logs(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_security_audit_ip ON security_audit_logs(ip_address)",
            
            // Error logs indexes
            "CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON error_logs(severity)",
            "CREATE INDEX IF NOT EXISTS idx_error_logs_category ON error_logs(category)",
            "CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON error_logs(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON error_logs(user_id)",
            
            // Email templates indexes
            "CREATE INDEX IF NOT EXISTS idx_email_templates_type ON email_templates(template_type)",
            "CREATE INDEX IF NOT EXISTS idx_email_templates_status ON email_templates(status)"
        ];
        
        $created = 0;
        $errors = [];
        
        foreach ($indexes as $indexSQL) {
            try {
                $this->db->exec($indexSQL);
                $created++;
            } catch (PDOException $e) {
                $errors[] = [
                    'sql' => $indexSQL,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return [
            'created' => $created,
            'errors' => $errors,
            'total' => count($indexes)
        ];
    }
    
    /**
     * Analyze table performance
     */
    public function analyzeTablePerformance() {
        $tables = [
            'users', 'transactions', 'deposits', 'withdrawals', 
            'support_tickets', 'trading_plans', 'system_settings',
            'security_audit_logs', 'error_logs', 'email_templates'
        ];
        
        $analysis = [];
        
        foreach ($tables as $table) {
            try {
                // Get table status
                $stmt = $this->db->prepare("SHOW TABLE STATUS LIKE ?");
                $stmt->execute([$table]);
                $status = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Get index information
                $stmt = $this->db->prepare("SHOW INDEX FROM `$table`");
                $stmt->execute();
                $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Get row count
                $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM `$table`");
                $stmt->execute();
                $rowCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                $analysis[$table] = [
                    'row_count' => $rowCount,
                    'data_length' => $status['Data_length'] ?? 0,
                    'index_length' => $status['Index_length'] ?? 0,
                    'data_free' => $status['Data_free'] ?? 0,
                    'engine' => $status['Engine'] ?? 'Unknown',
                    'collation' => $status['Collation'] ?? 'Unknown',
                    'indexes' => count($indexes),
                    'index_details' => $indexes
                ];
            } catch (PDOException $e) {
                $analysis[$table] = [
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $analysis;
    }
    
    /**
     * Optimize database tables
     */
    public function optimizeTables() {
        $tables = [
            'users', 'transactions', 'deposits', 'withdrawals', 
            'support_tickets', 'trading_plans', 'system_settings',
            'security_audit_logs', 'error_logs', 'email_templates'
        ];
        
        $results = [];
        
        foreach ($tables as $table) {
            try {
                $stmt = $this->db->prepare("OPTIMIZE TABLE `$table`");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $results[$table] = [
                    'status' => 'success',
                    'message' => $result['Msg_text'] ?? 'Optimized successfully'
                ];
            } catch (PDOException $e) {
                $results[$table] = [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Get query performance statistics
     */
    public function getQueryStats() {
        $totalQueries = count($this->queryLog);
        $slowQueries = array_filter($this->queryLog, function($query) {
            return $query['is_slow'];
        });
        
        $totalTime = array_sum(array_column($this->queryLog, 'execution_time'));
        $avgTime = $totalQueries > 0 ? $totalTime / $totalQueries : 0;
        
        return [
            'total_queries' => $totalQueries,
            'slow_queries' => count($slowQueries),
            'total_execution_time' => $totalTime,
            'average_execution_time' => $avgTime,
            'slow_query_threshold' => $this->slowQueryThreshold,
            'recent_queries' => array_slice($this->queryLog, -10)
        ];
    }
    
    /**
     * Clean old log files
     */
    public function cleanOldLogs($days = 30) {
        $logDir = __DIR__ . '/../../logs/database';
        if (!is_dir($logDir)) {
            return 0;
        }
        
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        $files = glob($logDir . '/*.log');
        $cleaned = 0;
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get database size information
     */
    public function getDatabaseSize() {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    table_schema as 'database_name',
                    SUM(data_length + index_length) as 'database_size',
                    SUM(data_length) as 'data_size',
                    SUM(index_length) as 'index_size'
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                GROUP BY table_schema
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            return [
                'database_name' => $result['database_name'] ?? 'Unknown',
                'total_size' => $result['database_size'] ?? 0,
                'data_size' => $result['data_size'] ?? 0,
                'index_size' => $result['index_size'] ?? 0
            ];
        } catch (PDOException $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check for missing indexes
     */
    public function checkMissingIndexes() {
        $recommendations = [];
        
        // Check for tables without primary keys
        try {
            $stmt = $this->db->prepare("
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name NOT IN (
                    SELECT table_name 
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE() 
                    AND index_name = 'PRIMARY'
                )
            ");
            $stmt->execute();
            $tablesWithoutPK = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($tablesWithoutPK as $table) {
                $recommendations[] = [
                    'type' => 'missing_primary_key',
                    'table' => $table,
                    'recommendation' => "Add a primary key to table '$table'"
                ];
            }
        } catch (PDOException $e) {
            // Ignore errors for this check
        }
        
        // Check for foreign key columns without indexes
        $foreignKeyColumns = [
            'transactions.user_id',
            'deposits.user_id',
            'withdrawals.user_id',
            'support_tickets.user_id'
        ];
        
        foreach ($foreignKeyColumns as $column) {
            list($table, $col) = explode('.', $column);
            
            try {
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) as index_count
                    FROM information_schema.statistics 
                    WHERE table_schema = DATABASE() 
                    AND table_name = ? 
                    AND column_name = ?
                ");
                $stmt->execute([$table, $col]);
                $indexCount = $stmt->fetch(PDO::FETCH_ASSOC)['index_count'];
                
                if ($indexCount == 0) {
                    $recommendations[] = [
                        'type' => 'missing_foreign_key_index',
                        'table' => $table,
                        'column' => $col,
                        'recommendation' => "Add index on $column for better join performance"
                    ];
                }
            } catch (PDOException $e) {
                // Ignore errors for this check
            }
        }
        
        return $recommendations;
    }
    
    /**
     * Set slow query threshold
     */
    public function setSlowQueryThreshold($threshold) {
        $this->slowQueryThreshold = $threshold;
    }
    
    /**
     * Format bytes to human readable
     */
    public function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
?>