<?php
// Test individual login pages
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Login Pages Test</h2>";

$pages = [
    'User Login' => 'user/login.php',
    'Admin Login' => 'admin/login.php',
    'Super Admin Login' => 'superadmin/login.php'
];

foreach ($pages as $name => $path) {
    echo "<h3>Testing $name</h3>";
    
    if (file_exists($path)) {
        echo "✅ File exists: $path<br>";
        
        // Test if file can be included without errors
        ob_start();
        try {
            // Capture any output to prevent it from displaying
            $content = file_get_contents($path);
            
            // Check for basic PHP syntax
            if (strpos($content, '<?php') !== false) {
                echo "✅ Contains PHP code<br>";
            }
            
            // Check for required functions
            if (strpos($content, 'isLoggedIn()') !== false) {
                echo "✅ Uses isLoggedIn() function<br>";
            }
            
            if (strpos($content, 'hasRole(') !== false) {
                echo "✅ Uses hasRole() function<br>";
            }
            
            if (strpos($content, 'redirectTo(') !== false) {
                echo "✅ Uses redirectTo() function<br>";
            }
            
            if (strpos($content, 'url(') !== false) {
                echo "✅ Uses url() function<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "<br>";
        }
        ob_end_clean();
        
    } else {
        echo "❌ File not found: $path<br>";
    }
    
    echo "<hr>";
}

echo "<h3>Test Direct Access:</h3>";
require_once 'includes/functions.php';

echo "Base URL: " . getBaseUrl() . "<br>";
foreach ($pages as $name => $path) {
    $fullUrl = url($path);
    echo "<a href='$fullUrl' target='_blank'>$name</a> - $fullUrl<br>";
}
?>