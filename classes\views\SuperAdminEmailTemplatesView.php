<?php

require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/../models/EmailTemplate.php';

class SuperAdminEmailTemplatesView extends BaseView {
    
    public function renderHead() {
        parent::renderHead();
        ?>
        <link href="/assets/css/superadmin-email-templates.css" rel="stylesheet">
        <?php
    }
    
    public function renderBody() {
        global $templates, $templateTypes;
        ?>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">Email Templates</h1>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="resetDefaults()">
                                <i class="fas fa-undo"></i> Reset Defaults
                            </button>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#templateModal">
                                <i class="fas fa-plus"></i> Add Template
                            </button>
                        </div>
                    </div>
                    
                    <!-- Templates List -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover" id="templatesTable">
                                    <thead>
                                        <tr>
                                            <th>Template Name</th>
                                            <th>Type</th>
                                            <th>Subject</th>
                                            <th>Status</th>
                                            <th>Last Updated</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($templates as $template): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($template['template_name']) ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= htmlspecialchars($templateTypes[$template['template_type']] ?? $template['template_type']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($template['subject']) ?>">
                                                    <?= htmlspecialchars($template['subject']) ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge <?= $template['is_active'] ? 'bg-success' : 'bg-secondary' ?>">
                                                    <?= $template['is_active'] ? 'Active' : 'Inactive' ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('M j, Y g:i A', strtotime($template['updated_at'])) ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button" class="btn btn-outline-primary" onclick="previewTemplate(<?= $template['id'] ?>)" title="Preview">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" onclick="editTemplate(<?= $template['id'] ?>)" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-<?= $template['is_active'] ? 'warning' : 'success' ?>" 
                                                            onclick="toggleStatus(<?= $template['id'] ?>)" 
                                                            title="<?= $template['is_active'] ? 'Deactivate' : 'Activate' ?>">
                                                        <i class="fas fa-<?= $template['is_active'] ? 'pause' : 'play' ?>"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(<?= $template['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Template Modal -->
        <div class="modal fade" id="templateModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="templateModalTitle">Add Email Template</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="templateForm" method="POST">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                            <input type="hidden" name="action" id="formAction" value="create_template">
                            <input type="hidden" name="template_id" id="templateId">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="templateName" class="form-label">Template Name</label>
                                        <input type="text" class="form-control" id="templateName" name="template_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="templateType" class="form-label">Template Type</label>
                                        <select class="form-select" id="templateType" name="template_type" required onchange="updatePlaceholders()">
                                            <option value="">Select Type</option>
                                            <?php foreach ($templateTypes as $type => $name): ?>
                                            <option value="<?= $type ?>"><?= htmlspecialchars($name) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="subject" class="form-label">Subject</label>
                                <input type="text" class="form-control" id="subject" name="subject" required>
                            </div>
                            
                            <!-- Placeholders Help -->
                            <div class="mb-3">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0">Available Placeholders</h6>
                                    </div>
                                    <div class="card-body" id="placeholdersHelp">
                                        <p class="text-muted">Select a template type to see available placeholders</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Template Content Tabs -->
                            <ul class="nav nav-tabs" id="contentTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="html-tab" data-bs-toggle="tab" data-bs-target="#html-content" type="button">
                                        HTML Content
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="text-tab" data-bs-toggle="tab" data-bs-target="#text-content" type="button">
                                        Text Content
                                    </button>
                                </li>
                            </ul>
                            
                            <div class="tab-content" id="contentTabsContent">
                                <div class="tab-pane fade show active" id="html-content">
                                    <div class="mb-3">
                                        <label for="bodyHtml" class="form-label">HTML Body</label>
                                        <textarea class="form-control" id="bodyHtml" name="body_html" rows="12" placeholder="Enter HTML content..."></textarea>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="text-content">
                                    <div class="mb-3">
                                        <label for="bodyText" class="form-label">Text Body</label>
                                        <textarea class="form-control" id="bodyText" name="body_text" rows="12" placeholder="Enter plain text content..."></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    Active Template
                                </label>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-info me-2" onclick="previewCurrentTemplate()">Preview</button>
                            <button type="submit" class="btn btn-primary">Save Template</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Preview Modal -->
        <div class="modal fade" id="previewModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Email Preview</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <strong>Subject:</strong>
                            <div id="previewSubject" class="border p-2 bg-light"></div>
                        </div>
                        
                        <ul class="nav nav-tabs" id="previewTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="preview-html-tab" data-bs-toggle="tab" data-bs-target="#preview-html" type="button">
                                    HTML Preview
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="preview-text-tab" data-bs-toggle="tab" data-bs-target="#preview-text" type="button">
                                    Text Preview
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="previewTabsContent">
                            <div class="tab-pane fade show active" id="preview-html">
                                <div class="border p-3 mt-3" style="min-height: 400px;">
                                    <div id="previewHtmlContent"></div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="preview-text">
                                <div class="border p-3 mt-3" style="min-height: 400px;">
                                    <pre id="previewTextContent" class="mb-0"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    public function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/superadmin-email-templates.js"></script>
        <script>
            // Pass PHP data to JavaScript
            window.templatePlaceholders = <?= json_encode(EmailTemplate::getAllPlaceholders()) ?>;
            window.templateTypes = <?= json_encode($templateTypes) ?>;
        </script>
        <?php
    }
    
    /**
     * Get sample data for template preview
     */
    public function getSampleData($templateType) {
        $baseData = [
            '{{site_name}}' => 'Coinage Trading',
            '{{currency_symbol}}' => '$',
            '{{user_name}}' => 'John Doe',
            '{{first_name}}' => 'John',
            '{{email}}' => '<EMAIL>',
            '{{date}}' => date('F j, Y'),
            '{{login_url}}' => 'https://example.com/login.php'
        ];
        
        switch ($templateType) {
            case EmailTemplate::TYPE_WELCOME:
                return array_merge($baseData, [
                    '{{registration_bonus}}' => '50.00'
                ]);
                
            case EmailTemplate::TYPE_DEPOSIT_CONFIRMATION:
                return array_merge($baseData, [
                    '{{amount}}' => '500.00',
                    '{{payment_method}}' => 'Bitcoin',
                    '{{transaction_id}}' => 'TXN123456789'
                ]);
                
            case EmailTemplate::TYPE_WITHDRAWAL_NOTIFICATION:
                return array_merge($baseData, [
                    '{{amount}}' => '250.00',
                    '{{status}}' => 'Approved',
                    '{{transaction_id}}' => 'WTH987654321'
                ]);
                
            case EmailTemplate::TYPE_PASSWORD_RESET:
                return array_merge($baseData, [
                    '{{reset_link}}' => 'https://example.com/reset-password.php?token=abc123',
                    '{{expiry_time}}' => '24 hours'
                ]);
                
            case EmailTemplate::TYPE_EMAIL_VERIFICATION:
                return array_merge($baseData, [
                    '{{verification_link}}' => 'https://example.com/verify-email.php?token=xyz789'
                ]);
                
            case EmailTemplate::TYPE_BALANCE_UPDATE:
                return array_merge($baseData, [
                    '{{amount}}' => '100.00',
                    '{{new_balance}}' => '1,250.00',
                    '{{update_type}}' => 'Credit',
                    '{{reason}}' => 'Admin adjustment'
                ]);
                
            case EmailTemplate::TYPE_PLAN_ASSIGNMENT:
                return array_merge($baseData, [
                    '{{plan_name}}' => 'Premium Trading Plan',
                    '{{plan_amount}}' => '1,000.00',
                    '{{expected_return}}' => '15%',
                    '{{duration}}' => '30 days'
                ]);
                
            default:
                return $baseData;
        }
    }

    /**
     * Required method from BaseView
     */
    protected function renderContent() {
        // This method is required by BaseView but we use render() method instead
        // for this specific implementation
        echo '<div class="container-fluid"><div class="row"><div class="col-12">';
        echo '<h1>Email Templates</h1>';
        echo '<p>Email template management functionality.</p>';
        echo '</div></div></div>';
    }
}