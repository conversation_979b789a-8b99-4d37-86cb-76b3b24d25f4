<?php
/**
 * Backup Service - Provides database backup and recovery functionality
 */
class BackupService {
    private static $instance = null;
    private $db;
    private $backupDir;
    
    private function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->backupDir = __DIR__ . '/../../backups';
        $this->ensureBackupDirectory();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Ensure backup directory exists
     */
    private function ensureBackupDirectory() {
        if (!file_exists($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
        
        // Create subdirectories
        $subdirs = ['database', 'files', 'logs', 'automated'];
        foreach ($subdirs as $subdir) {
            $path = $this->backupDir . '/' . $subdir;
            if (!file_exists($path)) {
                mkdir($path, 0755, true);
            }
        }
    }
    
    /**
     * Create full database backup
     */
    public function createDatabaseBackup($includeData = true, $compress = true) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "database_backup_{$timestamp}.sql";
        $filepath = $this->backupDir . '/database/' . $filename;
        
        try {
            // Get database name
            $stmt = $this->db->query("SELECT DATABASE() as db_name");
            $dbName = $stmt->fetch(PDO::FETCH_ASSOC)['db_name'];
            
            // Get all tables
            $stmt = $this->db->query("SHOW TABLES");
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $backup = "-- Database Backup\n";
            $backup .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
            $backup .= "-- Database: {$dbName}\n\n";
            $backup .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
            
            foreach ($tables as $table) {
                $backup .= $this->backupTable($table, $includeData);
            }
            
            $backup .= "SET FOREIGN_KEY_CHECKS=1;\n";
            
            // Write backup file
            if ($compress) {
                $compressedFile = $filepath . '.gz';
                $gz = gzopen($compressedFile, 'w9');
                gzwrite($gz, $backup);
                gzclose($gz);
                $filepath = $compressedFile;
                $filename .= '.gz';
            } else {
                file_put_contents($filepath, $backup);
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath),
                'tables' => count($tables),
                'compressed' => $compress
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup single table
     */
    private function backupTable($table, $includeData = true) {
        $backup = "-- Table: {$table}\n";
        
        // Get table structure
        $stmt = $this->db->query("SHOW CREATE TABLE `{$table}`");
        $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $backup .= "DROP TABLE IF EXISTS `{$table}`;\n";
        $backup .= $createTable['Create Table'] . ";\n\n";
        
        if ($includeData) {
            // Get table data
            $stmt = $this->db->query("SELECT * FROM `{$table}`");
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';
                
                $backup .= "INSERT INTO `{$table}` ({$columnList}) VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = $this->db->quote($value);
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                $backup .= implode(",\n", $values) . ";\n\n";
            }
        }
        
        return $backup;
    }
    
    /**
     * Create incremental backup (only changed data)
     */
    public function createIncrementalBackup($lastBackupTime) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "incremental_backup_{$timestamp}.sql";
        $filepath = $this->backupDir . '/database/' . $filename;
        
        try {
            $backup = "-- Incremental Database Backup\n";
            $backup .= "-- Generated: " . date('Y-m-d H:i:s') . "\n";
            $backup .= "-- Since: {$lastBackupTime}\n\n";
            
            // Tables with timestamp columns for incremental backup
            $incrementalTables = [
                'users' => 'created_at',
                'transactions' => 'created_at',
                'deposits' => 'created_at',
                'withdrawals' => 'created_at',
                'support_tickets' => 'created_at',
                'security_audit_logs' => 'timestamp',
                'error_logs' => 'created_at'
            ];
            
            foreach ($incrementalTables as $table => $timestampColumn) {
                $backup .= $this->backupIncrementalTable($table, $timestampColumn, $lastBackupTime);
            }
            
            file_put_contents($filepath, $backup);
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath),
                'since' => $lastBackupTime
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Backup incremental table data
     */
    private function backupIncrementalTable($table, $timestampColumn, $since) {
        $backup = "-- Incremental data for table: {$table}\n";
        
        try {
            $stmt = $this->db->prepare("SELECT * FROM `{$table}` WHERE `{$timestampColumn}` > ?");
            $stmt->execute([$since]);
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($rows)) {
                $columns = array_keys($rows[0]);
                $columnList = '`' . implode('`, `', $columns) . '`';
                
                $backup .= "INSERT IGNORE INTO `{$table}` ({$columnList}) VALUES\n";
                
                $values = [];
                foreach ($rows as $row) {
                    $rowValues = [];
                    foreach ($row as $value) {
                        if ($value === null) {
                            $rowValues[] = 'NULL';
                        } else {
                            $rowValues[] = $this->db->quote($value);
                        }
                    }
                    $values[] = '(' . implode(', ', $rowValues) . ')';
                }
                
                $backup .= implode(",\n", $values) . ";\n\n";
            } else {
                $backup .= "-- No new records since {$since}\n\n";
            }
            
        } catch (Exception $e) {
            $backup .= "-- Error backing up {$table}: " . $e->getMessage() . "\n\n";
        }
        
        return $backup;
    }
    
    /**
     * Restore database from backup
     */
    public function restoreDatabase($backupFile, $confirmRestore = false) {
        if (!$confirmRestore) {
            return [
                'success' => false,
                'error' => 'Restore confirmation required'
            ];
        }
        
        $filepath = $this->backupDir . '/database/' . $backupFile;
        
        if (!file_exists($filepath)) {
            return [
                'success' => false,
                'error' => 'Backup file not found'
            ];
        }
        
        try {
            // Read backup file
            if (pathinfo($filepath, PATHINFO_EXTENSION) === 'gz') {
                $backup = gzfile($filepath);
                $backup = implode('', $backup);
            } else {
                $backup = file_get_contents($filepath);
            }
            
            // Execute backup SQL
            $statements = explode(';', $backup);
            $executed = 0;
            $errors = [];
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $this->db->exec($statement);
                    $executed++;
                } catch (PDOException $e) {
                    $errors[] = [
                        'statement' => substr($statement, 0, 100) . '...',
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            return [
                'success' => true,
                'executed_statements' => $executed,
                'errors' => $errors,
                'backup_file' => $backupFile
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create file system backup
     */
    public function createFileBackup($directories = null) {
        if ($directories === null) {
            $directories = [
                'uploads' => __DIR__ . '/../../uploads',
                'assets' => __DIR__ . '/../../assets',
                'config' => __DIR__ . '/../../config'
            ];
        }
        
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "files_backup_{$timestamp}.tar.gz";
        $filepath = $this->backupDir . '/files/' . $filename;
        
        try {
            $tar = new PharData($filepath);
            
            foreach ($directories as $name => $path) {
                if (is_dir($path)) {
                    $tar->buildFromDirectory($path, '/.*/', $name);
                }
            }
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath),
                'directories' => array_keys($directories)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Schedule automated backups
     */
    public function scheduleAutomatedBackup($frequency = 'daily', $keepDays = 30) {
        $config = [
            'frequency' => $frequency,
            'keep_days' => $keepDays,
            'last_run' => null,
            'next_run' => $this->calculateNextRun($frequency),
            'enabled' => true
        ];
        
        $configFile = $this->backupDir . '/automated/backup_schedule.json';
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        
        return [
            'success' => true,
            'config' => $config
        ];
    }
    
    /**
     * Run automated backup if scheduled
     */
    public function runAutomatedBackup() {
        $configFile = $this->backupDir . '/automated/backup_schedule.json';
        
        if (!file_exists($configFile)) {
            return [
                'success' => false,
                'error' => 'No backup schedule configured'
            ];
        }
        
        $config = json_decode(file_get_contents($configFile), true);
        
        if (!$config['enabled']) {
            return [
                'success' => false,
                'error' => 'Automated backups are disabled'
            ];
        }
        
        if (time() < strtotime($config['next_run'])) {
            return [
                'success' => false,
                'error' => 'Not yet time for next backup'
            ];
        }
        
        // Run backup
        $dbBackup = $this->createDatabaseBackup(true, true);
        $fileBackup = $this->createFileBackup();
        
        // Update schedule
        $config['last_run'] = date('Y-m-d H:i:s');
        $config['next_run'] = $this->calculateNextRun($config['frequency']);
        file_put_contents($configFile, json_encode($config, JSON_PRETTY_PRINT));
        
        // Clean old backups
        $this->cleanOldBackups($config['keep_days']);
        
        return [
            'success' => true,
            'database_backup' => $dbBackup,
            'file_backup' => $fileBackup,
            'next_run' => $config['next_run']
        ];
    }
    
    /**
     * Calculate next backup run time
     */
    private function calculateNextRun($frequency) {
        switch ($frequency) {
            case 'hourly':
                return date('Y-m-d H:i:s', strtotime('+1 hour'));
            case 'daily':
                return date('Y-m-d H:i:s', strtotime('+1 day'));
            case 'weekly':
                return date('Y-m-d H:i:s', strtotime('+1 week'));
            case 'monthly':
                return date('Y-m-d H:i:s', strtotime('+1 month'));
            default:
                return date('Y-m-d H:i:s', strtotime('+1 day'));
        }
    }
    
    /**
     * Clean old backup files
     */
    public function cleanOldBackups($keepDays = 30) {
        $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
        $cleaned = 0;
        
        $directories = ['database', 'files'];
        
        foreach ($directories as $dir) {
            $backupPath = $this->backupDir . '/' . $dir;
            if (!is_dir($backupPath)) {
                continue;
            }
            
            $files = glob($backupPath . '/*');
            
            foreach ($files as $file) {
                if (is_file($file) && filemtime($file) < $cutoffTime) {
                    if (unlink($file)) {
                        $cleaned++;
                    }
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * List available backups
     */
    public function listBackups() {
        $backups = [
            'database' => [],
            'files' => []
        ];
        
        foreach (['database', 'files'] as $type) {
            $backupPath = $this->backupDir . '/' . $type;
            if (!is_dir($backupPath)) {
                continue;
            }
            
            $files = glob($backupPath . '/*');
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $backups[$type][] = [
                        'filename' => basename($file),
                        'size' => filesize($file),
                        'created' => date('Y-m-d H:i:s', filemtime($file)),
                        'age_days' => floor((time() - filemtime($file)) / (24 * 60 * 60))
                    ];
                }
            }
            
            // Sort by creation time (newest first)
            usort($backups[$type], function($a, $b) {
                return strtotime($b['created']) - strtotime($a['created']);
            });
        }
        
        return $backups;
    }
    
    /**
     * Get backup statistics
     */
    public function getBackupStats() {
        $backups = $this->listBackups();
        
        $stats = [
            'total_backups' => 0,
            'total_size' => 0,
            'database_backups' => count($backups['database']),
            'file_backups' => count($backups['files']),
            'oldest_backup' => null,
            'newest_backup' => null
        ];
        
        $allBackups = array_merge($backups['database'], $backups['files']);
        $stats['total_backups'] = count($allBackups);
        
        foreach ($allBackups as $backup) {
            $stats['total_size'] += $backup['size'];
            
            if ($stats['oldest_backup'] === null || $backup['created'] < $stats['oldest_backup']) {
                $stats['oldest_backup'] = $backup['created'];
            }
            
            if ($stats['newest_backup'] === null || $backup['created'] > $stats['newest_backup']) {
                $stats['newest_backup'] = $backup['created'];
            }
        }
        
        return $stats;
    }
    
    /**
     * Format bytes to human readable
     */
    public function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
?>