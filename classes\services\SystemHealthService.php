<?php
require_once __DIR__ . '/ErrorHandlingService.php';
require_once __DIR__ . '/EmailNotificationService.php';
require_once __DIR__ . '/../models/SystemSetting.php';

/**
 * System Health Monitoring Service
 * Monitors system health and performance metrics
 */
class SystemHealthService {
    private static $instance = null;
    private $errorHandler;
    private $emailService;
    
    // Health check types
    const CHECK_DATABASE = 'database';
    const CHECK_FILE_SYSTEM = 'file_system';
    const CHECK_MEMORY = 'memory';
    const CHECK_DISK_SPACE = 'disk_space';
    const CHECK_EMAIL = 'email';
    const CHECK_EXTERNAL_API = 'external_api';
    
    // Health statuses
    const STATUS_HEALTHY = 'healthy';
    const STATUS_WARNING = 'warning';
    const STATUS_CRITICAL = 'critical';
    const STATUS_UNKNOWN = 'unknown';
    
    private function __construct() {
        $this->errorHandler = ErrorHandlingService::getInstance();
        $this->emailService = EmailNotificationService::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Run comprehensive system health check
     */
    public function runHealthCheck() {
        $results = [];
        
        $results[self::CHECK_DATABASE] = $this->checkDatabase();
        $results[self::CHECK_FILE_SYSTEM] = $this->checkFileSystem();
        $results[self::CHECK_MEMORY] = $this->checkMemory();
        $results[self::CHECK_DISK_SPACE] = $this->checkDiskSpace();
        $results[self::CHECK_EMAIL] = $this->checkEmail();
        $results[self::CHECK_EXTERNAL_API] = $this->checkExternalAPIs();
        
        // Log results to database
        $this->logHealthCheckResults($results);
        
        // Check for critical issues and alert
        $this->checkForCriticalIssues($results);
        
        return $results;
    }
    
    /**
     * Check database connectivity and performance
     */
    private function checkDatabase() {
        $startTime = microtime(true);
        
        try {
            $db = Database::getInstance()->getConnection();
            
            // Test basic connectivity
            $stmt = $db->query("SELECT 1");
            $stmt->fetch();
            
            // Check database size
            $stmt = $db->query("
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");
            $dbSize = $stmt->fetchColumn();
            
            // Check slow queries (if enabled)
            $slowQueries = 0;
            try {
                $stmt = $db->query("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $slowQueries = $result['Value'] ?? 0;
            } catch (Exception $e) {
                // Ignore if slow query log is not enabled
            }
            
            // Check connection count
            $connections = 0;
            try {
                $stmt = $db->query("SHOW STATUS LIKE 'Threads_connected'");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $connections = $result['Value'] ?? 0;
            } catch (Exception $e) {
                // Ignore if not accessible
            }
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            $metrics = [
                'response_time_ms' => round($duration, 2),
                'database_size_mb' => $dbSize,
                'slow_queries' => $slowQueries,
                'connections' => $connections
            ];
            
            // Determine status
            $status = self::STATUS_HEALTHY;
            $message = 'Database is healthy';
            
            if ($duration > 1000) { // > 1 second
                $status = self::STATUS_CRITICAL;
                $message = 'Database response time is critical';
            } elseif ($duration > 500) { // > 500ms
                $status = self::STATUS_WARNING;
                $message = 'Database response time is slow';
            }
            
            if ($dbSize > 1000) { // > 1GB
                $status = max($status, self::STATUS_WARNING);
                $message .= '; Database size is large';
            }
            
            return [
                'status' => $status,
                'message' => $message,
                'metrics' => $metrics,
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_CRITICAL,
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Check file system health
     */
    private function checkFileSystem() {
        $startTime = microtime(true);
        
        try {
            $testDir = __DIR__ . '/../../logs/health_checks';
            $testFile = $testDir . '/test_' . time() . '.tmp';
            
            // Ensure directory exists
            if (!file_exists($testDir)) {
                mkdir($testDir, 0755, true);
            }
            
            // Test write permissions
            $testData = 'Health check test - ' . date('Y-m-d H:i:s');
            $writeResult = file_put_contents($testFile, $testData);
            
            if ($writeResult === false) {
                throw new Exception('Cannot write to file system');
            }
            
            // Test read permissions
            $readData = file_get_contents($testFile);
            if ($readData !== $testData) {
                throw new Exception('File system read/write mismatch');
            }
            
            // Clean up test file
            unlink($testFile);
            
            // Check important directories
            $directories = [
                'logs' => __DIR__ . '/../../logs',
                'uploads' => __DIR__ . '/../../assets/uploads',
                'cache' => __DIR__ . '/../../cache'
            ];
            
            $dirStatus = [];
            foreach ($directories as $name => $path) {
                $dirStatus[$name] = [
                    'exists' => file_exists($path),
                    'writable' => is_writable($path),
                    'readable' => is_readable($path)
                ];
            }
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => self::STATUS_HEALTHY,
                'message' => 'File system is healthy',
                'metrics' => [
                    'response_time_ms' => round($duration, 2),
                    'directories' => $dirStatus
                ],
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_CRITICAL,
                'message' => 'File system error: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Check memory usage
     */
    private function checkMemory() {
        $startTime = microtime(true);
        
        try {
            $memoryUsage = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);
            $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
            
            $usagePercent = ($memoryUsage / $memoryLimit) * 100;
            $peakPercent = ($peakMemory / $memoryLimit) * 100;
            
            $metrics = [
                'current_usage_bytes' => $memoryUsage,
                'current_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
                'peak_usage_bytes' => $peakMemory,
                'peak_usage_mb' => round($peakMemory / 1024 / 1024, 2),
                'memory_limit_bytes' => $memoryLimit,
                'memory_limit_mb' => round($memoryLimit / 1024 / 1024, 2),
                'usage_percent' => round($usagePercent, 2),
                'peak_percent' => round($peakPercent, 2)
            ];
            
            // Determine status
            $status = self::STATUS_HEALTHY;
            $message = 'Memory usage is normal';
            
            if ($usagePercent > 90) {
                $status = self::STATUS_CRITICAL;
                $message = 'Memory usage is critical';
            } elseif ($usagePercent > 75) {
                $status = self::STATUS_WARNING;
                $message = 'Memory usage is high';
            }
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => $status,
                'message' => $message,
                'metrics' => $metrics,
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNKNOWN,
                'message' => 'Memory check failed: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Check disk space
     */
    private function checkDiskSpace() {
        $startTime = microtime(true);
        
        try {
            $rootPath = __DIR__ . '/../../';
            $freeBytes = disk_free_space($rootPath);
            $totalBytes = disk_total_space($rootPath);
            
            if ($freeBytes === false || $totalBytes === false) {
                throw new Exception('Cannot determine disk space');
            }
            
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercent = ($usedBytes / $totalBytes) * 100;
            
            $metrics = [
                'free_bytes' => $freeBytes,
                'free_mb' => round($freeBytes / 1024 / 1024, 2),
                'free_gb' => round($freeBytes / 1024 / 1024 / 1024, 2),
                'used_bytes' => $usedBytes,
                'used_mb' => round($usedBytes / 1024 / 1024, 2),
                'used_gb' => round($usedBytes / 1024 / 1024 / 1024, 2),
                'total_bytes' => $totalBytes,
                'total_gb' => round($totalBytes / 1024 / 1024 / 1024, 2),
                'usage_percent' => round($usagePercent, 2)
            ];
            
            // Determine status
            $status = self::STATUS_HEALTHY;
            $message = 'Disk space is sufficient';
            
            if ($usagePercent > 95) {
                $status = self::STATUS_CRITICAL;
                $message = 'Disk space is critically low';
            } elseif ($usagePercent > 85) {
                $status = self::STATUS_WARNING;
                $message = 'Disk space is running low';
            }
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => $status,
                'message' => $message,
                'metrics' => $metrics,
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNKNOWN,
                'message' => 'Disk space check failed: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Check email system
     */
    private function checkEmail() {
        $startTime = microtime(true);
        
        try {
            // Check SMTP configuration
            $smtpHost = SystemSetting::getValue('smtp_host', '');
            $smtpPort = SystemSetting::getValue('smtp_port', '587');
            $smtpUser = SystemSetting::getValue('smtp_username', '');
            
            if (empty($smtpHost) || empty($smtpUser)) {
                return [
                    'status' => self::STATUS_WARNING,
                    'message' => 'SMTP not configured',
                    'metrics' => ['configured' => false],
                    'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }
            
            // Test SMTP connection (basic check)
            $connection = @fsockopen($smtpHost, $smtpPort, $errno, $errstr, 10);
            
            if (!$connection) {
                return [
                    'status' => self::STATUS_CRITICAL,
                    'message' => "SMTP connection failed: $errstr",
                    'metrics' => ['configured' => true, 'reachable' => false],
                    'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }
            
            fclose($connection);
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => self::STATUS_HEALTHY,
                'message' => 'Email system is healthy',
                'metrics' => [
                    'configured' => true,
                    'reachable' => true,
                    'smtp_host' => $smtpHost,
                    'smtp_port' => $smtpPort
                ],
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNKNOWN,
                'message' => 'Email check failed: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Check external APIs
     */
    private function checkExternalAPIs() {
        $startTime = microtime(true);
        
        try {
            $apis = [
                'google_charts' => 'https://chart.googleapis.com',
                'bootstrap_cdn' => 'https://cdn.jsdelivr.net',
                'fontawesome_cdn' => 'https://cdnjs.cloudflare.com'
            ];
            
            $results = [];
            $overallStatus = self::STATUS_HEALTHY;
            
            foreach ($apis as $name => $url) {
                $apiStart = microtime(true);
                
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 5,
                        'method' => 'HEAD'
                    ]
                ]);
                
                $response = @file_get_contents($url, false, $context);
                $apiDuration = (microtime(true) - $apiStart) * 1000;
                
                if ($response !== false) {
                    $results[$name] = [
                        'status' => 'reachable',
                        'response_time_ms' => round($apiDuration, 2)
                    ];
                } else {
                    $results[$name] = [
                        'status' => 'unreachable',
                        'response_time_ms' => round($apiDuration, 2)
                    ];
                    $overallStatus = self::STATUS_WARNING;
                }
            }
            
            $duration = (microtime(true) - $startTime) * 1000;
            
            return [
                'status' => $overallStatus,
                'message' => $overallStatus === self::STATUS_HEALTHY ? 'All external APIs are reachable' : 'Some external APIs are unreachable',
                'metrics' => $results,
                'duration_ms' => round($duration, 2)
            ];
            
        } catch (Exception $e) {
            return [
                'status' => self::STATUS_UNKNOWN,
                'message' => 'External API check failed: ' . $e->getMessage(),
                'metrics' => [],
                'duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ];
        }
    }
    
    /**
     * Log health check results to database
     */
    private function logHealthCheckResults($results) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                INSERT INTO system_health_checks (check_type, status, message, metrics, check_duration_ms) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($results as $checkType => $result) {
                $stmt->execute([
                    $checkType,
                    $result['status'],
                    $result['message'],
                    json_encode($result['metrics']),
                    $result['duration_ms']
                ]);
            }
        } catch (Exception $e) {
            // Ignore logging errors to prevent infinite loops
            error_log("Health check logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Check for critical issues and send alerts
     */
    private function checkForCriticalIssues($results) {
        $criticalIssues = [];
        
        foreach ($results as $checkType => $result) {
            if ($result['status'] === self::STATUS_CRITICAL) {
                $criticalIssues[] = [
                    'type' => $checkType,
                    'message' => $result['message'],
                    'metrics' => $result['metrics']
                ];
            }
        }
        
        if (!empty($criticalIssues)) {
            $this->sendCriticalAlert($criticalIssues);
        }
    }
    
    /**
     * Send critical system alert to administrators
     */
    private function sendCriticalAlert($issues) {
        try {
            // Check if alerts are enabled
            $alertsEnabled = SystemSetting::getValue('system_health_alerts', '1') === '1';
            if (!$alertsEnabled) {
                return;
            }
            
            // Get admin emails
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE role IN ('admin', 'superadmin') AND status = 'active'");
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($admins as $admin) {
                $this->sendHealthAlertEmail($admin, $issues);
            }
        } catch (Exception $e) {
            error_log("Failed to send health alert: " . $e->getMessage());
        }
    }
    
    /**
     * Send health alert email
     */
    private function sendHealthAlertEmail($admin, $issues) {
        $subject = "Critical System Health Alert - " . SystemSetting::getValue('site_name', 'Coinage Trading');
        
        $issuesList = '';
        foreach ($issues as $issue) {
            $issuesList .= "<li><strong>{$issue['type']}:</strong> {$issue['message']}</li>";
        }
        
        $body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #dc3545;'>Critical System Health Alert</h2>
            <p>Dear {$admin['first_name']},</p>
            <p>Critical system health issues have been detected:</p>
            
            <div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;'>
                <h3>Critical Issues:</h3>
                <ul>$issuesList</ul>
            </div>
            
            <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
            <p>Please investigate these issues immediately to ensure system stability.</p>
            <p>System Health Monitor</p>
        </div>";
        
        try {
            mail($admin['email'], $subject, $body, [
                'Content-Type' => 'text/html; charset=UTF-8',
                'From' => 'system@' . ($_SERVER['HTTP_HOST'] ?? 'localhost')
            ]);
        } catch (Exception $e) {
            error_log("Failed to send health alert email: " . $e->getMessage());
        }
    }
    
    /**
     * Get health check history
     */
    public function getHealthHistory($days = 7, $checkType = null) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $sql = "
                SELECT check_type, status, message, metrics, check_duration_ms, checked_at
                FROM system_health_checks 
                WHERE checked_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            ";
            
            $params = [$days];
            
            if ($checkType) {
                $sql .= " AND check_type = ?";
                $params[] = $checkType;
            }
            
            $sql .= " ORDER BY checked_at DESC";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get current system status summary
     */
    public function getSystemStatusSummary() {
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get latest status for each check type
            $stmt = $db->query("
                SELECT 
                    check_type,
                    status,
                    message,
                    checked_at,
                    check_duration_ms
                FROM system_health_checks s1
                WHERE checked_at = (
                    SELECT MAX(checked_at) 
                    FROM system_health_checks s2 
                    WHERE s2.check_type = s1.check_type
                )
                ORDER BY check_type
            ");
            
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate overall status
            $overallStatus = self::STATUS_HEALTHY;
            $criticalCount = 0;
            $warningCount = 0;
            
            foreach ($results as $result) {
                if ($result['status'] === self::STATUS_CRITICAL) {
                    $criticalCount++;
                    $overallStatus = self::STATUS_CRITICAL;
                } elseif ($result['status'] === self::STATUS_WARNING) {
                    $warningCount++;
                    if ($overallStatus !== self::STATUS_CRITICAL) {
                        $overallStatus = self::STATUS_WARNING;
                    }
                }
            }
            
            return [
                'overall_status' => $overallStatus,
                'critical_count' => $criticalCount,
                'warning_count' => $warningCount,
                'healthy_count' => count($results) - $criticalCount - $warningCount,
                'checks' => $results,
                'last_check' => !empty($results) ? max(array_column($results, 'checked_at')) : null
            ];
        } catch (Exception $e) {
            return [
                'overall_status' => self::STATUS_UNKNOWN,
                'critical_count' => 0,
                'warning_count' => 0,
                'healthy_count' => 0,
                'checks' => [],
                'last_check' => null,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Parse memory limit string to bytes
     */
    private function parseMemoryLimit($limit) {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $value = (int) $limit;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }
    
    /**
     * Clean old health check records
     */
    public function cleanOldHealthChecks($days = 30) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                DELETE FROM system_health_checks 
                WHERE checked_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            return 0;
        }
    }
}
?>