<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuthenticationManager.php';
require_once '../../../classes/services/FinancialReportingService.php';
require_once '../../../classes/services/AuditTrailService.php';

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $reportType = $_GET['type'] ?? 'summary';
    $format = $_GET['format'] ?? 'csv';
    $dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
    $dateTo = $_GET['date_to'] ?? date('Y-m-d');
    
    // Validate inputs
    if (!in_array($reportType, ['summary', 'transactions', 'deposits', 'withdrawals', 'audit'])) {
        throw new InvalidArgumentException('Invalid report type');
    }
    
    if (!in_array($format, ['csv', 'excel'])) {
        throw new InvalidArgumentException('Invalid export format');
    }
    
    if (!DateTime::createFromFormat('Y-m-d', $dateFrom) || !DateTime::createFromFormat('Y-m-d', $dateTo)) {
        throw new InvalidArgumentException('Invalid date format');
    }
    
    // Build filters from query parameters
    $filters = [];
    if (!empty($_GET['user_id'])) {
        $filters['user_id'] = (int) $_GET['user_id'];
    }
    if (!empty($_GET['status'])) {
        $filters['status'] = $_GET['status'];
    }
    if (!empty($_GET['type_filter'])) {
        $filters['type'] = $_GET['type_filter'];
    }
    
    // Generate filename
    $filename = "financial_report_{$reportType}_{$dateFrom}_to_{$dateTo}." . ($format === 'excel' ? 'xlsx' : 'csv');
    
    if ($reportType === 'audit') {
        // Export audit trail
        $auditService = new AuditTrailService();
        $csvData = $auditService->exportAuditTrail($filters);
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="audit_trail_' . $dateFrom . '_to_' . $dateTo . '.csv"');
        echo $csvData;
        
    } else {
        // Export financial reports
        $reportingService = new FinancialReportingService();
        
        if ($format === 'csv') {
            $csvData = $reportingService->exportToCSV($reportType, $dateFrom, $dateTo, $filters);
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            echo $csvData;
            
        } else if ($format === 'excel') {
            // For Excel export, we'll use a simple HTML table that Excel can import
            $data = $reportingService->getSummaryReport($dateFrom, $dateTo);
            
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            echo generateExcelReport($data, $reportType, $dateFrom, $dateTo);
        }
    }
    
} catch (Exception $e) {
    error_log("Export API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Generate Excel-compatible HTML report
 */
function generateExcelReport($data, $reportType, $dateFrom, $dateTo) {
    $html = '
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Financial Report - ' . ucfirst($reportType) . '</title>
        <style>
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .number { text-align: right; }
            .header { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="header">Financial Report - ' . ucfirst($reportType) . '</div>
        <p><strong>Period:</strong> ' . $dateFrom . ' to ' . $dateTo . '</p>
        <p><strong>Generated:</strong> ' . date('Y-m-d H:i:s') . '</p>
        
        <h3>Summary Statistics</h3>
        <table>
            <tr>
                <th>Metric</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Total Deposits</td>
                <td class="number">$' . number_format($data['deposits']['total'], 2) . '</td>
            </tr>
            <tr>
                <td>Approved Deposits</td>
                <td class="number">$' . number_format($data['deposits']['approved_amount'], 2) . '</td>
            </tr>
            <tr>
                <td>Total Withdrawals</td>
                <td class="number">$' . number_format($data['withdrawals']['total'], 2) . '</td>
            </tr>
            <tr>
                <td>Approved Withdrawals</td>
                <td class="number">$' . number_format($data['withdrawals']['approved_amount'], 2) . '</td>
            </tr>
            <tr>
                <td>Net Flow</td>
                <td class="number">$' . number_format($data['net_flow'], 2) . '</td>
            </tr>
            <tr>
                <td>Pending Actions</td>
                <td class="number">' . $data['pending_actions'] . '</td>
            </tr>
        </table>';
    
    if (!empty($data['top_users'])) {
        $html .= '
        <h3>Top Users</h3>
        <table>
            <tr>
                <th>Username</th>
                <th>Name</th>
                <th>Email</th>
                <th>Total Deposits</th>
                <th>Total Withdrawals</th>
                <th>Transaction Count</th>
            </tr>';
        
        foreach ($data['top_users'] as $user) {
            $html .= '
            <tr>
                <td>' . htmlspecialchars($user['username']) . '</td>
                <td>' . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . '</td>
                <td>' . htmlspecialchars($user['email']) . '</td>
                <td class="number">$' . number_format($user['total_deposits'], 2) . '</td>
                <td class="number">$' . number_format($user['total_withdrawals'], 2) . '</td>
                <td class="number">' . $user['transaction_count'] . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    if (!empty($data['payment_methods'])) {
        $html .= '
        <h3>Payment Method Usage</h3>
        <table>
            <tr>
                <th>Payment Method</th>
                <th>Type</th>
                <th>Usage Count</th>
                <th>Total Amount</th>
                <th>Average Amount</th>
            </tr>';
        
        foreach ($data['payment_methods'] as $method) {
            $html .= '
            <tr>
                <td>' . htmlspecialchars($method['name']) . '</td>
                <td>' . htmlspecialchars($method['type']) . '</td>
                <td class="number">' . $method['usage_count'] . '</td>
                <td class="number">$' . number_format($method['total_amount'], 2) . '</td>
                <td class="number">$' . number_format($method['average_amount'], 2) . '</td>
            </tr>';
        }
        
        $html .= '</table>';
    }
    
    $html .= '
    </body>
    </html>';
    
    return $html;
}
?>