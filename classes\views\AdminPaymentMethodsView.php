<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/../models/PaymentMethod.php';
require_once __DIR__ . '/../services/CSRFProtection.php';

/**
 * Admin Payment Methods Management View
 */
class AdminPaymentMethodsView extends BaseView {
    
    public function __construct() {
        parent::__construct();
        $this->setTitle('Payment Methods Management');
        $this->setLayout('admin');
    }
    
    protected function getHeadContent() {
        return '
        <link rel="stylesheet" href="../../assets/css/admin-payment-methods.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        ';
    }
    
    protected function getBodyContent() {
        $paymentMethods = PaymentMethod::getWithDepositStats();
        $statistics = PaymentMethod::getStatistics();
        
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Payment Methods Management</h1>
                            <p class="text-muted">Configure and manage payment methods for deposits</p>
                        </div>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPaymentMethodModal">
                            <i class="fas fa-plus me-2"></i>Add Payment Method
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . ($statistics['total_methods'] ?? 0) . '</h4>
                                    <p class="mb-0">Total Methods</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-credit-card fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . ($statistics['active_methods'] ?? 0) . '</h4>
                                    <p class="mb-0">Active Methods</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . ($statistics['inactive_methods'] ?? 0) . '</h4>
                                    <p class="mb-0">Inactive Methods</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-pause-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . ($statistics['types_count'] ?? 0) . '</h4>
                                    <p class="mb-0">Payment Types</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-layer-group fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Methods Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Payment Methods</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="paymentMethodsTable">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Deposits</th>
                                    <th>Total Amount</th>
                                    <th>Pending</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="paymentMethodsTableBody">
                                ' . $this->renderPaymentMethodsRows($paymentMethods) . '
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        ' . $this->renderModals() . '
        ';
    }
    
    private function renderPaymentMethodsRows($paymentMethods) {
        $html = '';
        
        foreach ($paymentMethods as $method) {
            $statusBadge = $method['status'] === 'active' 
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-secondary">Inactive</span>';
            
            $typeIcon = $this->getTypeIcon($method['type']);
            
            $html .= '
            <tr data-method-id="' . $method['id'] . '">
                <td>
                    <div class="sort-handle" style="cursor: move;">
                        <i class="fas fa-grip-vertical text-muted"></i>
                        <span class="ms-2">' . $method['sort_order'] . '</span>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="' . $typeIcon . ' me-2"></i>
                        <strong>' . htmlspecialchars($method['name']) . '</strong>
                    </div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">' . ucfirst($method['type']) . '</span>
                </td>
                <td>' . $statusBadge . '</td>
                <td>
                    <span class="badge bg-info">' . $method['deposits_count'] . '</span>
                </td>
                <td>
                    <strong>$' . number_format($method['total_deposits'], 2) . '</strong>
                </td>
                <td>
                    ' . ($method['pending_deposits'] > 0 
                        ? '<span class="badge bg-warning">' . $method['pending_deposits'] . '</span>'
                        : '<span class="badge bg-light text-muted">0</span>') . '
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="viewPaymentMethod(' . $method['id'] . ')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="editPaymentMethod(' . $method['id'] . ')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-' . ($method['status'] === 'active' ? 'warning' : 'success') . '" 
                                onclick="togglePaymentMethodStatus(' . $method['id'] . ', \'' . $method['status'] . '\')">
                            <i class="fas fa-' . ($method['status'] === 'active' ? 'pause' : 'play') . '"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deletePaymentMethod(' . $method['id'] . ')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>';
        }
        
        return $html;
    }
    
    private function getTypeIcon($type) {
        $icons = [
            'crypto' => 'fab fa-bitcoin text-warning',
            'bank' => 'fas fa-university text-primary',
            'paypal' => 'fab fa-paypal text-info',
            'other' => 'fas fa-credit-card text-secondary'
        ];
        
        return $icons[$type] ?? 'fas fa-credit-card text-secondary';
    }
    
    private function renderModals() {
        $csrfToken = CSRFProtection::generateToken();
        
        return '
        <!-- Add Payment Method Modal -->
        <div class="modal fade" id="addPaymentMethodModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add Payment Method</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="addPaymentMethodForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="methodName" class="form-label">Payment Method Name *</label>
                                        <input type="text" class="form-control" id="methodName" name="name" required>
                                        <div class="form-text">e.g., "Bitcoin (BTC)", "Chase Bank", "PayPal"</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="methodType" class="form-label">Payment Type *</label>
                                        <select class="form-select" id="methodType" name="type" required>
                                            <option value="">Select Type</option>
                                            <option value="crypto">Cryptocurrency</option>
                                            <option value="bank">Bank Transfer</option>
                                            <option value="paypal">PayPal</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Dynamic Fields Container -->
                            <div id="dynamicFields"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="methodStatus" class="form-label">Status</label>
                                        <select class="form-select" id="methodStatus" name="status">
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sortOrder" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="sortOrder" name="sort_order" min="1">
                                        <div class="form-text">Leave empty to add at the end</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Add Payment Method
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Edit Payment Method Modal -->
        <div class="modal fade" id="editPaymentMethodModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Payment Method</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="editPaymentMethodForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" id="editMethodId" name="id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editMethodName" class="form-label">Payment Method Name *</label>
                                        <input type="text" class="form-control" id="editMethodName" name="name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editMethodType" class="form-label">Payment Type *</label>
                                        <select class="form-select" id="editMethodType" name="type" required>
                                            <option value="crypto">Cryptocurrency</option>
                                            <option value="bank">Bank Transfer</option>
                                            <option value="paypal">PayPal</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Dynamic Fields Container -->
                            <div id="editDynamicFields"></div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editMethodStatus" class="form-label">Status</label>
                                        <select class="form-select" id="editMethodStatus" name="status">
                                            <option value="active">Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="editSortOrder" class="form-label">Sort Order</label>
                                        <input type="number" class="form-control" id="editSortOrder" name="sort_order" min="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Payment Method
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- View Payment Method Modal -->
        <div class="modal fade" id="viewPaymentMethodModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Payment Method Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="viewPaymentMethodContent">
                        <!-- Content loaded dynamically -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        ';
    }
    
    protected function getScriptsContent() {
        return '
        <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
        <script src="../../assets/js/admin-payment-methods.js"></script>
        ';
    }
}
?>