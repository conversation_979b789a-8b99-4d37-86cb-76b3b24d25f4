/**
 * Layout and Navigation JavaScript for Coinage Trading Platform
 */

class LayoutManager {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.sidebarOverlay = document.getElementById('sidebarOverlay');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.mobileBreakpoint = 768;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.handleResize();
        this.initializeTooltips();
        this.setupAutoHideAlerts();
        this.setupFormValidation();
        
        // Handle initial load
        window.addEventListener('load', () => {
            this.handleResize();
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }
    
    setupEventListeners() {
        // Mobile sidebar toggle
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSidebar();
            });
        }
        
        // Sidebar overlay click
        if (this.sidebarOverlay) {
            this.sidebarOverlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }
        
        // Close sidebar on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isSidebarOpen()) {
                this.closeSidebar();
            }
        });
        
        // Handle navigation link clicks on mobile
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (this.isMobile()) {
                    this.closeSidebar();
                }
            });
        });
        
        // Handle form submissions
        const forms = document.querySelectorAll('form[data-ajax="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleAjaxForm(e);
            });
        });
        
        // Handle confirmation buttons
        const confirmButtons = document.querySelectorAll('[data-confirm]');
        confirmButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const message = button.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                    return false;
                }
            });
        });
    }
    
    toggleSidebar() {
        if (this.sidebar && this.sidebarOverlay) {
            this.sidebar.classList.toggle('show');
            this.sidebarOverlay.classList.toggle('show');
            
            // Update aria attributes
            const isOpen = this.sidebar.classList.contains('show');
            this.sidebar.setAttribute('aria-hidden', !isOpen);
            
            if (isOpen) {
                // Focus first navigation item
                const firstNavLink = this.sidebar.querySelector('.nav-link');
                if (firstNavLink) {
                    firstNavLink.focus();
                }
            }
        }
    }
    
    closeSidebar() {
        if (this.sidebar && this.sidebarOverlay) {
            this.sidebar.classList.remove('show');
            this.sidebarOverlay.classList.remove('show');
            this.sidebar.setAttribute('aria-hidden', 'true');
        }
    }
    
    openSidebar() {
        if (this.sidebar && this.sidebarOverlay) {
            this.sidebar.classList.add('show');
            this.sidebarOverlay.classList.add('show');
            this.sidebar.setAttribute('aria-hidden', 'false');
        }
    }
    
    isSidebarOpen() {
        return this.sidebar && this.sidebar.classList.contains('show');
    }
    
    isMobile() {
        return window.innerWidth < this.mobileBreakpoint;
    }
    
    handleResize() {
        if (!this.isMobile()) {
            // Desktop view - ensure sidebar is properly positioned
            this.closeSidebar();
            if (this.sidebar) {
                this.sidebar.setAttribute('aria-hidden', 'false');
            }
        } else {
            // Mobile view - ensure sidebar is hidden by default
            if (this.sidebar) {
                this.sidebar.setAttribute('aria-hidden', 'true');
            }
        }
    }
    
    initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize Bootstrap popovers
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    setupAutoHideAlerts() {
        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                if (bootstrap.Alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);
    }
    
    setupFormValidation() {
        // Add Bootstrap validation classes
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', (event) => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
        
        // Real-time validation feedback
        const inputs = document.querySelectorAll('.form-control, .form-select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
            
            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }
    
    validateField(field) {
        const isValid = field.checkValidity();
        field.classList.remove('is-valid', 'is-invalid');
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');
        
        // Update feedback message
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback && !isValid) {
            feedback.textContent = field.validationMessage;
        }
    }
    
    handleAjaxForm(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Show loading state
        if (submitButton) {
            submitButton.disabled = true;
            const originalText = submitButton.textContent;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
            
            // Restore button after timeout
            setTimeout(() => {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }, 3000);
        }
        
        // Send AJAX request
        fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert(data.message || 'Operation completed successfully', 'success');
                if (data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }
            } else {
                this.showAlert(data.message || 'An error occurred', 'danger');
                if (data.errors) {
                    this.showFormErrors(form, data.errors);
                }
            }
        })
        .catch(error => {
            console.error('Ajax form error:', error);
            this.showAlert('An unexpected error occurred', 'danger');
        })
        .finally(() => {
            // Restore button
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = originalText;
            }
        });
    }
    
    showAlert(message, type = 'info', dismissible = true) {
        const alertContainer = document.getElementById('alert-container') || document.body;
        const alertId = 'alert_' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} ${dismissible ? 'alert-dismissible fade show' : ''}" role="alert">
                ${message}
                ${dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' : ''}
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('afterbegin', alertHTML);
        
        // Auto-hide after 5 seconds
        if (dismissible) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert && bootstrap.Alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    }
    
    showFormErrors(form, errors) {
        // Clear existing errors
        form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
        
        // Show new errors
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                const feedback = field.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.textContent = errors[fieldName];
                }
            }
        });
    }
    
    // Utility methods
    showLoading(element, text = 'Loading...') {
        if (element) {
            element.innerHTML = `
                <div class="d-flex justify-content-center align-items-center p-3">
                    <div class="spinner-border spinner-border-sm me-2"></div>
                    <span>${text}</span>
                </div>
            `;
        }
    }
    
    hideLoading(element, originalContent) {
        if (element) {
            element.innerHTML = originalContent;
        }
    }
    
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    }
    
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize layout manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.layoutManager = new LayoutManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LayoutManager;
}