<?php
// Core Configuration File
if (!defined('BASE_URL')) define('BASE_URL', 'http://localhost/coinage/');
if (!defined('SITE_NAME')) define('SITE_NAME', 'Coinage Trading');
if (!defined('DEBUG_MODE')) define('DEBUG_MODE', true);

// Enable detailed error reporting when in debug mode
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', __DIR__ . '/logs/errors/php_errors.log');
}

// Auto-detect base URL for different environments
if (!defined('AUTO_BASE_URL')) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $path = dirname($_SERVER['SCRIPT_NAME']);
    $path = $path === '/' ? '' : $path;
    define('AUTO_BASE_URL', $protocol . '://' . $host . $path . '/');
}

// Database Configuration
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_PORT')) define('DB_PORT', '3306');
if (!defined('DB_USER')) define('DB_USER', 'root');
if (!defined('DB_PASS')) define('DB_PASS', 'root');
if (!defined('DB_NAME')) define('DB_NAME', 'coinage');

// Theme Configuration (Dynamic Colors)
if (!defined('PRIMARY_COLOR')) define('PRIMARY_COLOR', '#007bff');
if (!defined('SECONDARY_COLOR')) define('SECONDARY_COLOR', '#6c757d');

// System Configuration
if (!defined('DEFAULT_CURRENCY')) define('DEFAULT_CURRENCY', 'USD');
if (!defined('CURRENCY_SYMBOL')) define('CURRENCY_SYMBOL', '$');
if (!defined('ENABLE_2FA')) define('ENABLE_2FA', false);
if (!defined('ENABLE_EMAIL_VERIFICATION')) define('ENABLE_EMAIL_VERIFICATION', false);
if (!defined('KYC_REQUIRED')) define('KYC_REQUIRED', false);
if (!defined('REGISTRATION_BONUS')) define('REGISTRATION_BONUS', 0);
if (!defined('DEPOSIT_BONUS_PERCENT')) define('DEPOSIT_BONUS_PERCENT', 10);
if (!defined('MIN_WITHDRAWAL_AMOUNT')) define('MIN_WITHDRAWAL_AMOUNT', 50);
if (!defined('MAX_WITHDRAWAL_AMOUNT')) define('MAX_WITHDRAWAL_AMOUNT', 10000);

// SMTP Configuration
if (!defined('SMTP_HOST')) define('SMTP_HOST', 'smtp.hostinger.com');
if (!defined('SMTP_PORT')) define('SMTP_PORT', 465);
if (!defined('SMTP_USERNAME')) define('SMTP_USERNAME', '<EMAIL>');
if (!defined('SMTP_PASSWORD')) define('SMTP_PASSWORD', 'Money2025@Demo#');
if (!defined('SMTP_FROM_NAME')) define('SMTP_FROM_NAME', 'Coinage Trading');
if (!defined('SMTP_FROM_EMAIL')) define('SMTP_FROM_EMAIL', '<EMAIL>');

// System Features (Can be controlled by Super Admin)
if (!defined('EMAIL_NOTIFICATIONS')) define('EMAIL_NOTIFICATIONS', true);
if (!defined('SMS_NOTIFICATIONS')) define('SMS_NOTIFICATIONS', false);
if (!defined('KYC_VERIFICATION')) define('KYC_VERIFICATION', true);
if (!defined('WITHDRAWAL_ON_HOLIDAYS')) define('WITHDRAWAL_ON_HOLIDAYS', false);
if (!defined('BALANCE_TRANSFER')) define('BALANCE_TRANSFER', true);
if (!defined('STAKING_MODULE')) define('STAKING_MODULE', true);
if (!defined('POOL_MODULE')) define('POOL_MODULE', true);
?>