<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * Admin Withdrawal Management View
 */
class AdminWithdrawalManagementView extends BaseView {
    use StatusColorTrait;
    
    protected function getTitle() {
        return 'Withdrawal Management - Admin Panel';
    }
    
    protected function renderContent() {
        $withdrawals = $this->data['withdrawals'] ?? [];
        $stats = $this->data['stats'] ?? [];
        $currentStatus = $this->data['currentStatus'] ?? 'all';
        $currentPage = $this->data['currentPage'] ?? 1;
        $totalPages = $this->data['totalPages'] ?? 1;
        $totalWithdrawals = $this->data['totalWithdrawals'] ?? 0;
        $csrfToken = $this->data['csrfToken'] ?? '';
        
        echo $this->renderHeader();
        echo $this->renderStats($stats);
        echo $this->renderFilters($currentStatus);
        echo $this->renderWithdrawalsTable($withdrawals, $csrfToken);
        echo $this->renderPagination($currentPage, $totalPages);
        echo $this->renderModals($csrfToken);
    }
    
    private function renderHeader() {
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Withdrawal Management</h1>
                            <p class="text-muted">Manage user withdrawal requests and processing</p>
                        </div>
                        <div>
                            <button class="btn btn-primary" onclick="refreshWithdrawals()">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button class="btn btn-success" onclick="showBulkActions()">
                                <i class="fas fa-tasks"></i> Bulk Actions
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    } 
   
    private function renderStats($stats) {
        return '
        <div class="container-fluid mb-4">
            <div class="row">
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['pending'] ?? 0) . '</h4>
                                    <p class="mb-0">Pending</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['approved'] ?? 0) . '</h4>
                                    <p class="mb-0">Approved</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['processing'] ?? 0) . '</h4>
                                    <p class="mb-0">Processing</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-cog fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['completed'] ?? 0) . '</h4>
                                    <p class="mb-0">Completed</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-double fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">' . number_format($stats['rejected'] ?? 0) . '</h4>
                                    <p class="mb-0">Rejected</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-dark text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">$' . number_format($stats['total_amount'] ?? 0, 2) . '</h4>
                                    <p class="mb-0">Total Paid</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-dollar-sign fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function renderFilters($currentStatus) {
        $statuses = [
            'all' => 'All Withdrawals',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'rejected' => 'Rejected'
        ];
        
        $filterButtons = '';
        foreach ($statuses as $status => $label) {
            $activeClass = $currentStatus === $status ? 'active' : '';
            $filterButtons .= '
                <button class="btn btn-outline-primary ' . $activeClass . '" 
                        onclick="filterWithdrawals(\'' . $status . '\')">
                    ' . $label . '
                </button>';
        }
        
        return '
        <div class="container-fluid mb-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    ' . $filterButtons . '
                                </div>
                                <div class="d-flex align-items-center">
                                    <input type="text" class="form-control me-2" id="searchWithdrawals" 
                                           placeholder="Search by user, amount, or method...">
                                    <button class="btn btn-outline-secondary" onclick="searchWithdrawals()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }    
 
   private function renderWithdrawalsTable($withdrawals, $csrfToken) {
        if (empty($withdrawals)) {
            return '
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No withdrawals found</h5>
                                <p class="text-muted">There are no withdrawal requests matching your current filters.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>';
        }
        
        $tableRows = '';
        foreach ($withdrawals as $withdrawal) {
            $statusBadge = $this->getStatusBadge($withdrawal['status']);
            $actionButtons = $this->getActionButtons($withdrawal, $csrfToken);
            $accountDetails = json_decode($withdrawal['account_details'] ?? '{}', true);
            
            $tableRows .= '
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input withdrawal-checkbox" 
                           value="' . $withdrawal['id'] . '">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <div class="fw-bold">' . htmlspecialchars($withdrawal['first_name'] . ' ' . $withdrawal['last_name']) . '</div>
                            <small class="text-muted">@' . htmlspecialchars($withdrawal['username']) . '</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="fw-bold">$' . number_format($withdrawal['amount'], 2) . '</div>
                    <small class="text-muted">Balance: $' . number_format($withdrawal['balance'] ?? 0, 2) . '</small>
                </td>
                <td>' . htmlspecialchars($withdrawal['withdrawal_method'] ?? 'N/A') . '</td>
                <td>
                    <small class="text-muted">' . $this->formatAccountDetails($accountDetails) . '</small>
                </td>
                <td>' . $statusBadge . '</td>
                <td>' . date('M j, Y g:i A', strtotime($withdrawal['created_at'])) . '</td>
                <td>' . $actionButtons . '</td>
            </tr>';
        }
        
        return '
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Withdrawal Requests</h5>
                                <div>
                                    <input type="checkbox" class="form-check-input" id="selectAllWithdrawals">
                                    <label class="form-check-label ms-1" for="selectAllWithdrawals">Select All</label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="40"></th>
                                            <th>User</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Account Details</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th width="150">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ' . $tableRows . '
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>';
    }
    
    private function formatAccountDetails($accountDetails) {
        if (empty($accountDetails)) {
            return 'N/A';
        }
        
        $formatted = [];
        foreach ($accountDetails as $key => $value) {
            if (!empty($value)) {
                $formatted[] = ucfirst($key) . ': ' . htmlspecialchars($value);
            }
        }
        
        return implode('<br>', $formatted);
    }   
 
    private function getActionButtons($withdrawal, $csrfToken) {
        $buttons = '
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-primary" onclick="viewWithdrawalDetails(' . $withdrawal['id'] . ')" 
                    title="View Details">
                <i class="fas fa-eye"></i>
            </button>';
        
        if ($withdrawal['status'] === 'pending') {
            $buttons .= '
            <button class="btn btn-outline-success" onclick="approveWithdrawal(' . $withdrawal['id'] . ')" 
                    title="Approve">
                <i class="fas fa-check"></i>
            </button>
            <button class="btn btn-outline-danger" onclick="rejectWithdrawal(' . $withdrawal['id'] . ')" 
                    title="Reject">
                <i class="fas fa-times"></i>
            </button>';
        } elseif ($withdrawal['status'] === 'approved') {
            $buttons .= '
            <button class="btn btn-outline-info" onclick="markAsProcessing(' . $withdrawal['id'] . ')" 
                    title="Mark as Processing">
                <i class="fas fa-cog"></i>
            </button>';
        } elseif ($withdrawal['status'] === 'processing') {
            $buttons .= '
            <button class="btn btn-outline-success" onclick="markAsCompleted(' . $withdrawal['id'] . ')" 
                    title="Mark as Completed">
                <i class="fas fa-check-double"></i>
            </button>';
        }
        
        $buttons .= '</div>';
        
        return $buttons;
    }
    
    private function renderPagination($currentPage, $totalPages) {
        if ($totalPages <= 1) {
            return '';
        }
        
        $pagination = '<nav aria-label="Withdrawals pagination"><ul class="pagination justify-content-center">';
        
        // Previous button
        if ($currentPage > 1) {
            $pagination .= '<li class="page-item"><a class="page-link" href="?page=' . ($currentPage - 1) . '">Previous</a></li>';
        }
        
        // Page numbers
        $start = max(1, $currentPage - 2);
        $end = min($totalPages, $currentPage + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            $active = $i === $currentPage ? 'active' : '';
            $pagination .= '<li class="page-item ' . $active . '"><a class="page-link" href="?page=' . $i . '">' . $i . '</a></li>';
        }
        
        // Next button
        if ($currentPage < $totalPages) {
            $pagination .= '<li class="page-item"><a class="page-link" href="?page=' . ($currentPage + 1) . '">Next</a></li>';
        }
        
        $pagination .= '</ul></nav>';
        
        return '
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    ' . $pagination . '
                </div>
            </div>
        </div>';
    }    
   
 private function renderModals($csrfToken) {
        return '
        <!-- Withdrawal Details Modal -->
        <div class="modal fade" id="withdrawalDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Withdrawal Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="withdrawalDetailsContent">
                        <!-- Content loaded via AJAX -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Approve Withdrawal Modal -->
        <div class="modal fade" id="approveWithdrawalModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Approve Withdrawal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="approveWithdrawalForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="withdrawal_id" id="approveWithdrawalId">
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Approving this withdrawal will deduct the amount from the user\'s balance.
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Withdrawal Amount</label>
                                <input type="text" class="form-control" id="approveWithdrawalAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">User Current Balance</label>
                                <input type="text" class="form-control" id="approveUserBalance" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Admin Note (Optional)</label>
                                <textarea class="form-control" name="admin_note" rows="3" 
                                          placeholder="Add any notes about this approval..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> Approve Withdrawal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Reject Withdrawal Modal -->
        <div class="modal fade" id="rejectWithdrawalModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Reject Withdrawal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="rejectWithdrawalForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="withdrawal_id" id="rejectWithdrawalId">
                            
                            <div class="mb-3">
                                <label class="form-label">Withdrawal Amount</label>
                                <input type="text" class="form-control" id="rejectWithdrawalAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Reason for Rejection <span class="text-danger">*</span></label>
                                <textarea class="form-control" name="reason" rows="4" required
                                          placeholder="Please provide a reason for rejecting this withdrawal..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times"></i> Reject Withdrawal
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div> 
       
        <!-- Mark as Processing Modal -->
        <div class="modal fade" id="processingModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Mark as Processing</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="processingForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="withdrawal_id" id="processingWithdrawalId">
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                This will mark the withdrawal as being processed. The user will be notified.
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Withdrawal Amount</label>
                                <input type="text" class="form-control" id="processingWithdrawalAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Processing Note (Optional)</label>
                                <textarea class="form-control" name="admin_note" rows="3" 
                                          placeholder="Add any notes about the processing status..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-cog"></i> Mark as Processing
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Mark as Completed Modal -->
        <div class="modal fade" id="completedModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Mark as Completed</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="completedForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            <input type="hidden" name="withdrawal_id" id="completedWithdrawalId">
                            
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                This will mark the withdrawal as completed. The user will be notified.
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Withdrawal Amount</label>
                                <input type="text" class="form-control" id="completedWithdrawalAmount" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Completion Note (Optional)</label>
                                <textarea class="form-control" name="admin_note" rows="3" 
                                          placeholder="Add any notes about the completion..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check-double"></i> Mark as Completed
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>        

        <!-- Bulk Actions Modal -->
        <div class="modal fade" id="bulkActionsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bulk Actions</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="bulkActionsForm">
                        <div class="modal-body">
                            <input type="hidden" name="csrf_token" value="' . $csrfToken . '">
                            
                            <div class="mb-3">
                                <label class="form-label">Action</label>
                                <select class="form-select" name="action" required>
                                    <option value="">Select action...</option>
                                    <option value="approve">Approve Selected</option>
                                    <option value="reject">Reject Selected</option>
                                    <option value="processing">Mark as Processing</option>
                                    <option value="completed">Mark as Completed</option>
                                </select>
                            </div>
                            
                            <div class="mb-3" id="bulkReasonField" style="display: none;">
                                <label class="form-label">Reason (for rejection)</label>
                                <textarea class="form-control" name="reason" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3" id="bulkNoteField" style="display: none;">
                                <label class="form-label">Admin Note (Optional)</label>
                                <textarea class="form-control" name="admin_note" rows="3"></textarea>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <span id="selectedCount">0</span> withdrawals selected
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Execute Action</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>';
    }
    
    protected function getAdditionalCSS() {
        return '
        <link href="../../assets/css/admin-withdrawal-management.css" rel="stylesheet">
        <style>
            .avatar-sm {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }
            
            .table th {
                border-top: none;
                font-weight: 600;
                color: #495057;
            }
            
            .btn-group-sm > .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.875rem;
            }
            
            .card {
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                border: 1px solid rgba(0, 0, 0, 0.125);
            }
            
            .table-responsive {
                border-radius: 0.375rem;
            }
            
            .pagination {
                margin-bottom: 0;
            }
            
            .form-check-input:checked {
                background-color: #0d6efd;
                border-color: #0d6efd;
            }
            
            .status-pending { color: #f39c12; }
            .status-approved { color: #17a2b8; }
            .status-processing { color: #007bff; }
            .status-completed { color: #28a745; }
            .status-rejected { color: #dc3545; }
        </style>';
    }
    
    protected function getAdditionalJS() {
        return '
        <script src="../../assets/js/admin-withdrawal-management.js"></script>';
    }
}
?>