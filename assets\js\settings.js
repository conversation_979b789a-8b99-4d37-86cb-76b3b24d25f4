// Settings JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize settings form
    initializeSettingsForm();
    
    // Initialize deactivate account modal
    initializeDeactivateModal();
    
    // Initialize switch toggles
    initializeSwitchToggles();
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function initializeSettingsForm() {
    const form = document.getElementById('securityForm');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        const formData = new FormData(form);
        formData.append('action', 'update_settings');
        
        fetch('/user/api/profile.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Settings updated successfully!', 'success');
            } else {
                showToast(data.error || 'Failed to update settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Remove loading state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
}

function initializeDeactivateModal() {
    const confirmBtn = document.getElementById('confirmDeactivate');
    if (!confirmBtn) return;
    
    confirmBtn.addEventListener('click', function() {
        const originalText = this.innerHTML;
        
        // Add loading state
        this.classList.add('loading');
        this.disabled = true;
        
        // Simulate API call for account deactivation
        setTimeout(() => {
            // In a real implementation, this would make an API call
            showToast('Account deactivation request submitted. You will receive an email confirmation.', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('deactivateModal'));
            modal.hide();
            
            // Remove loading state
            this.classList.remove('loading');
            this.disabled = false;
            this.innerHTML = originalText;
        }, 2000);
    });
}

function initializeSwitchToggles() {
    // Two-Factor Authentication toggle
    const twoFaToggle = document.getElementById('two_fa_enabled');
    if (twoFaToggle) {
        twoFaToggle.addEventListener('change', function() {
            const isEnabled = this.checked;
            
            if (isEnabled) {
                // Show 2FA setup modal or redirect to setup page
                show2FASetupModal();
            } else {
                // Show confirmation for disabling 2FA
                show2FADisableConfirmation();
            }
        });
    }
    
    // Email notification toggles
    const emailToggles = document.querySelectorAll('input[type="checkbox"][name^="email_"]');
    emailToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            // Auto-save email preferences
            saveEmailPreference(this.name, this.checked);
        });
    });
}

function show2FASetupModal() {
    // Create and show 2FA setup modal
    const modalHtml = `
        <div class="modal fade" id="twoFASetupModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Enable Two-Factor Authentication</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Two-Factor Authentication adds an extra layer of security to your account.
                        </div>
                        <p>To enable 2FA, you'll need to:</p>
                        <ol>
                            <li>Download an authenticator app (Google Authenticator, Authy, etc.)</li>
                            <li>Scan the QR code or enter the setup key</li>
                            <li>Enter the verification code from your app</li>
                        </ol>
                        <p><strong>Note:</strong> This feature is currently under development.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" disabled>Continue Setup</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('twoFASetupModal'));
    modal.show();
    
    // Clean up modal after hiding
    document.getElementById('twoFASetupModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function show2FADisableConfirmation() {
    // Create and show 2FA disable confirmation
    const modalHtml = `
        <div class="modal fade" id="twoFADisableModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Disable Two-Factor Authentication</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> Disabling 2FA will make your account less secure.
                        </div>
                        <p>Are you sure you want to disable Two-Factor Authentication?</p>
                        <p>You can re-enable it at any time from your security settings.</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" id="confirmDisable2FA">Disable 2FA</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('twoFADisableModal'));
    modal.show();
    
    // Handle confirmation
    document.getElementById('confirmDisable2FA').addEventListener('click', function() {
        // In a real implementation, this would make an API call
        showToast('Two-Factor Authentication has been disabled.', 'success');
        modal.hide();
    });
    
    // Clean up modal after hiding
    document.getElementById('twoFADisableModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
        // Reset the toggle if user cancelled
        const toggle = document.getElementById('two_fa_enabled');
        if (toggle) {
            toggle.checked = false;
        }
    });
}

function saveEmailPreference(preference, enabled) {
    // Auto-save email preference
    const formData = new FormData();
    formData.append('action', 'update_email_preference');
    formData.append('preference', preference);
    formData.append('enabled', enabled ? '1' : '0');
    
    fetch('/user/api/profile.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Email preference updated: ${preference.replace('email_', '').replace('_', ' ')}`, 'success');
        } else {
            showToast('Failed to update email preference', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while updating preference', 'error');
    });
}

function showToast(message, type) {
    const toastId = type === 'success' ? 'successToast' : 'errorToast';
    const toast = document.getElementById(toastId);
    
    if (toast) {
        const toastBody = toast.querySelector('.toast-body');
        toastBody.textContent = message;
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
}

// Handle responsive behavior
function handleResponsive() {
    const isMobile = window.innerWidth < 768;
    
    // Adjust settings layout for mobile
    const settingItems = document.querySelectorAll('.setting-item');
    settingItems.forEach(function(item) {
        const flexContainer = item.querySelector('.d-flex');
        if (flexContainer) {
            if (isMobile) {
                flexContainer.classList.remove('justify-content-between');
                flexContainer.classList.add('flex-column');
            } else {
                flexContainer.classList.add('justify-content-between');
                flexContainer.classList.remove('flex-column');
            }
        }
    });
}

// Listen for window resize
window.addEventListener('resize', handleResponsive);

// Initial responsive setup
handleResponsive();