/**
 * Financial Reports & Audit Trail JavaScript
 */

class FinancialReportsManager {
    constructor() {
        this.currentPage = 1;
        this.currentLimit = 50;
        this.currentFilters = {};
        this.charts = {};
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialData();
    }
    
    bindEvents() {
        // Tab switching
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                const target = e.target.getAttribute('data-bs-target');
                this.handleTabSwitch(target);
            });
        });
        
        // Filter form submission
        const filterForm = document.getElementById('reportFilters');
        if (filterForm) {
            filterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateReports();
            });
        }
        
        // Search functionality
        const auditSearch = document.getElementById('auditSearch');
        if (auditSearch) {
            let searchTimeout;
            auditSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchAuditLogs(e.target.value);
                }, 500);
            });
        }
    }
    
    loadInitialData() {
        this.loadSummaryData();
        this.loadCharts();
    }
    
    async loadSummaryData() {
        try {
            const dateFrom = document.getElementById('date_from')?.value || this.getDefaultDateFrom();
            const dateTo = document.getElementById('date_to')?.value || this.getDefaultDateTo();
            
            const response = await fetch(`/admin/reports/api/summary.php?date_from=${dateFrom}&date_to=${dateTo}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateSummaryDisplay(result.data);
                this.updateCharts(result.data);
            } else {
                this.showError('Failed to load summary data: ' + result.error);
            }
        } catch (error) {
            this.showError('Error loading summary data: ' + error.message);
        }
    }
    
    async loadTransactionDetails(page = 1, filters = {}) {
        try {
            this.showLoading('transactionDetailsTable');
            
            const params = new URLSearchParams({
                page: page,
                limit: this.currentLimit,
                date_from: document.getElementById('date_from')?.value || this.getDefaultDateFrom(),
                date_to: document.getElementById('date_to')?.value || this.getDefaultDateTo(),
                ...filters
            });
            
            const response = await fetch(`/admin/reports/api/transactions.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderTransactionTable(result.data);
                this.renderPagination(result.data, 'transactionPagination', (page) => this.loadTransactionDetails(page, filters));
            } else {
                this.showError('Failed to load transaction details: ' + result.error);
            }
        } catch (error) {
            this.showError('Error loading transaction details: ' + error.message);
        } finally {
            this.hideLoading('transactionDetailsTable');
        }
    }
    
    async loadAuditTrail(page = 1, filters = {}) {
        try {
            this.showLoading('auditTrailTable');
            
            const params = new URLSearchParams({
                page: page,
                limit: this.currentLimit,
                date_from: document.getElementById('date_from')?.value || this.getDefaultDateFrom(),
                date_to: document.getElementById('date_to')?.value || this.getDefaultDateTo(),
                ...filters
            });
            
            const response = await fetch(`/admin/reports/api/audit-trail.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderAuditTable(result.data);
                this.renderPagination(result.data, 'auditPagination', (page) => this.loadAuditTrail(page, filters));
            } else {
                this.showError('Failed to load audit trail: ' + result.error);
            }
        } catch (error) {
            this.showError('Error loading audit trail: ' + error.message);
        } finally {
            this.hideLoading('auditTrailTable');
        }
    }
    
    async loadAnalytics() {
        try {
            const dateFrom = document.getElementById('date_from')?.value || this.getDefaultDateFrom();
            const dateTo = document.getElementById('date_to')?.value || this.getDefaultDateTo();
            
            const response = await fetch(`/admin/reports/api/analytics.php?date_from=${dateFrom}&date_to=${dateTo}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderAnalytics(result.data);
            } else {
                this.showError('Failed to load analytics: ' + result.error);
            }
        } catch (error) {
            this.showError('Error loading analytics: ' + error.message);
        }
    }
    
    handleTabSwitch(target) {
        switch (target) {
            case '#summary':
                this.loadSummaryData();
                break;
            case '#transactions':
                this.loadTransactionDetails();
                break;
            case '#audit':
                this.loadAuditTrail();
                break;
            case '#analytics':
                this.loadAnalytics();
                break;
        }
    }
    
    updateReports() {
        const activeTab = document.querySelector('.nav-link.active')?.getAttribute('data-bs-target');
        this.handleTabSwitch(activeTab);
    }
    
    filterTransactions(type) {
        const filters = type === 'all' ? {} : { type: type };
        this.loadTransactionDetails(1, filters);
        
        // Update button states
        document.querySelectorAll('[onclick^="filterTransactions"]').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }
    
    searchAuditLogs(query = '') {
        const filters = query ? { search: query } : {};
        this.loadAuditTrail(1, filters);
    }
    
    updateSummaryDisplay(data) {
        // Update metric cards
        this.updateMetricCard('deposits', data.deposits);
        this.updateMetricCard('withdrawals', data.withdrawals);
        this.updateMetricCard('net_flow', { total: data.net_flow });
        this.updateMetricCard('pending_actions', { total: data.pending_actions });
    }
    
    updateMetricCard(type, data) {
        const card = document.querySelector(`[data-metric="${type}"]`);
        if (!card) return;
        
        const valueElement = card.querySelector('.metric-value');
        const subtitleElement = card.querySelector('.metric-subtitle');
        
        if (valueElement) {
            if (type === 'net_flow') {
                valueElement.textContent = '$' + this.formatNumber(data.total);
            } else if (type === 'pending_actions') {
                valueElement.textContent = data.total;
            } else {
                valueElement.textContent = '$' + this.formatNumber(data.total);
                if (subtitleElement) {
                    subtitleElement.textContent = data.count + ' transactions';
                }
            }
        }
    }
    
    updateCharts(data) {
        this.createDepositStatusChart(data.deposits);
        this.createWithdrawalStatusChart(data.withdrawals);
    }
    
    createDepositStatusChart(deposits) {
        const ctx = document.getElementById('depositStatusChart');
        if (!ctx) return;
        
        if (this.charts.depositStatus) {
            this.charts.depositStatus.destroy();
        }
        
        this.charts.depositStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Approved', 'Pending', 'Rejected'],
                datasets: [{
                    data: [
                        deposits.approved_amount,
                        deposits.pending_amount,
                        deposits.rejected_amount
                    ],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = '$' + this.formatNumber(context.parsed);
                                return label + ': ' + value;
                            }
                        }
                    }
                }
            }
        });
    }
    
    createWithdrawalStatusChart(withdrawals) {
        const ctx = document.getElementById('withdrawalStatusChart');
        if (!ctx) return;
        
        if (this.charts.withdrawalStatus) {
            this.charts.withdrawalStatus.destroy();
        }
        
        this.charts.withdrawalStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Approved', 'Pending', 'Rejected'],
                datasets: [{
                    data: [
                        withdrawals.approved_amount,
                        withdrawals.pending_amount,
                        withdrawals.rejected_amount
                    ],
                    backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                const label = context.label || '';
                                const value = '$' + this.formatNumber(context.parsed);
                                return label + ': ' + value;
                            }
                        }
                    }
                }
            }
        });
    }
    
    renderTransactionTable(data) {
        const tbody = document.querySelector('#transactionDetailsTable tbody');
        if (!tbody) return;
        
        if (!data.transactions || data.transactions.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">No transactions found</td></tr>';
            return;
        }
        
        tbody.innerHTML = data.transactions.map(transaction => `
            <tr>
                <td>${transaction.id}</td>
                <td>${this.formatDate(transaction.created_at)}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-2">
                            ${transaction.first_name ? transaction.first_name.charAt(0).toUpperCase() : 'U'}
                        </div>
                        <div>
                            <div class="fw-medium">${this.escapeHtml(transaction.username || 'Unknown')}</div>
                            <small class="text-muted">${this.escapeHtml(transaction.email || '')}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="transaction-type">
                        <i class="fas fa-${this.getTransactionIcon(transaction.type)}"></i>
                        ${this.formatTransactionType(transaction.type)}
                    </span>
                </td>
                <td class="${transaction.amount >= 0 ? 'amount-positive' : 'amount-negative'}">
                    $${this.formatNumber(Math.abs(transaction.amount))}
                </td>
                <td>$${this.formatNumber(transaction.balance_before)}</td>
                <td>$${this.formatNumber(transaction.balance_after)}</td>
                <td>
                    <span class="status-badge ${this.getStatusClass(transaction.status)}">
                        ${transaction.status}
                    </span>
                </td>
                <td>${this.escapeHtml(transaction.processed_by_username || 'System')}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary" 
                            onclick="viewTransactionDetails(${transaction.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    renderAuditTable(data) {
        const tbody = document.querySelector('#auditTrailTable tbody');
        if (!tbody) return;
        
        if (!data.audit_logs || data.audit_logs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">No audit logs found</td></tr>';
            return;
        }
        
        tbody.innerHTML = data.audit_logs.map(log => `
            <tr class="audit-entry severity-${log.severity}">
                <td>${this.formatDate(log.created_at)}</td>
                <td>${this.escapeHtml(log.user_display)}</td>
                <td>
                    <span class="audit-action">${log.action_display}</span>
                </td>
                <td>${log.entity_display}</td>
                <td>${log.entity_id}</td>
                <td>
                    <div class="audit-changes">${this.escapeHtml(log.formatted_changes)}</div>
                </td>
                <td>${log.ip_address}</td>
                <td>
                    <span title="${this.escapeHtml(log.user_agent)}">
                        ${this.escapeHtml(log.user_agent_short)}
                    </span>
                </td>
            </tr>
        `).join('');
    }
    
    renderAnalytics(data) {
        this.createVolumeTrendChart(data.volume_trends);
        this.createPaymentMethodChart(data.payment_methods);
        this.renderKeyMetrics(data.key_metrics);
        this.renderSuspiciousActivity(data.suspicious_activity);
    }
    
    createVolumeTrendChart(trends) {
        const ctx = document.getElementById('volumeTrendChart');
        if (!ctx || !trends) return;
        
        if (this.charts.volumeTrend) {
            this.charts.volumeTrend.destroy();
        }
        
        const labels = [...new Set(trends.map(t => t.period))];
        const depositData = labels.map(label => {
            const item = trends.find(t => t.period === label && t.type === 'deposit');
            return item ? item.total_volume : 0;
        });
        const withdrawalData = labels.map(label => {
            const item = trends.find(t => t.period === label && t.type === 'withdrawal');
            return item ? item.total_volume : 0;
        });
        
        this.charts.volumeTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Deposits',
                        data: depositData,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4
                    },
                    {
                        label: 'Withdrawals',
                        data: withdrawalData,
                        borderColor: '#dc3545',
                        backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => '$' + this.formatNumber(value)
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return context.dataset.label + ': $' + this.formatNumber(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    }
    
    createPaymentMethodChart(methods) {
        const ctx = document.getElementById('paymentMethodChart');
        if (!ctx || !methods) return;
        
        if (this.charts.paymentMethod) {
            this.charts.paymentMethod.destroy();
        }
        
        this.charts.paymentMethod = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: methods.map(m => m.name),
                datasets: [{
                    label: 'Usage Amount',
                    data: methods.map(m => m.total_amount),
                    backgroundColor: [
                        '#007bff', '#28a745', '#ffc107', '#dc3545', 
                        '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => '$' + this.formatNumber(value)
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: (context) => {
                                return 'Amount: $' + this.formatNumber(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    }
    
    renderKeyMetrics(metrics) {
        const container = document.getElementById('keyMetrics');
        if (!container || !metrics) return;
        
        container.innerHTML = Object.entries(metrics).map(([key, value]) => `
            <div class="metric-item">
                <span class="metric-name">${this.formatMetricName(key)}</span>
                <span class="metric-value">${this.formatMetricValue(key, value)}</span>
            </div>
        `).join('');
    }
    
    renderSuspiciousActivity(activities) {
        const container = document.getElementById('suspiciousActivity');
        if (!container || !activities || activities.length === 0) {
            if (container) container.innerHTML = '<p class="text-muted">No suspicious activity detected</p>';
            return;
        }
        
        container.innerHTML = activities.map(activity => `
            <div class="suspicious-activity ${activity.severity}">
                <div class="activity-alert ${activity.severity}">
                    <i class="fas fa-${activity.severity === 'high' ? 'exclamation-triangle' : 'exclamation-circle'}"></i>
                    <div>
                        <strong>${activity.type.replace('_', ' ').toUpperCase()}</strong>
                        <p class="mb-0">${activity.message}</p>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    renderPagination(data, containerId, callback) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const totalPages = data.total_pages || 1;
        const currentPage = data.page || 1;
        
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let pagination = '';
        
        // Previous button
        pagination += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            pagination += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                pagination += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            pagination += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                pagination += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            pagination += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(${totalPages})">${totalPages}</a>
                </li>
            `;
        }
        
        // Next button
        pagination += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="event.preventDefault(); ${callback.name}(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        container.innerHTML = pagination;
    }
    
    // Export functions
    async exportReport(format) {
        try {
            const dateFrom = document.getElementById('date_from')?.value || this.getDefaultDateFrom();
            const dateTo = document.getElementById('date_to')?.value || this.getDefaultDateTo();
            const activeTab = document.querySelector('.nav-link.active')?.getAttribute('data-bs-target');
            
            let reportType = 'summary';
            if (activeTab === '#transactions') reportType = 'transactions';
            else if (activeTab === '#audit') reportType = 'audit';
            
            const params = new URLSearchParams({
                type: reportType,
                format: format,
                date_from: dateFrom,
                date_to: dateTo
            });
            
            window.open(`/admin/reports/api/export.php?${params}`, '_blank');
            
        } catch (error) {
            this.showError('Export failed: ' + error.message);
        }
    }
    
    // Utility functions
    formatNumber(num) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(num);
    }
    
    formatDate(dateString) {
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    getTransactionIcon(type) {
        const icons = {
            'deposit': 'arrow-down',
            'withdrawal': 'arrow-up',
            'bonus': 'gift',
            'trade': 'exchange-alt',
            'fee': 'minus-circle'
        };
        return icons[type] || 'circle';
    }
    
    formatTransactionType(type) {
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
    
    getStatusClass(status) {
        const classes = {
            'completed': 'success',
            'approved': 'success',
            'pending': 'warning',
            'rejected': 'danger',
            'failed': 'danger'
        };
        return classes[status] || 'secondary';
    }
    
    formatMetricName(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
    
    formatMetricValue(key, value) {
        if (key.includes('amount') || key.includes('balance')) {
            return '$' + this.formatNumber(value);
        }
        if (key.includes('rate') || key.includes('percentage')) {
            return value + '%';
        }
        return value;
    }
    
    getDefaultDateFrom() {
        const date = new Date();
        date.setDate(date.getDate() - 30);
        return date.toISOString().split('T')[0];
    }
    
    getDefaultDateTo() {
        return new Date().toISOString().split('T')[0];
    }
    
    showLoading(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.innerHTML = `
                <div class="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading data...</p>
                </div>
            `;
        }
    }
    
    hideLoading(elementId) {
        // Loading will be hidden when content is rendered
    }
    
    showError(message) {
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Find a suitable container for the alert
        const container = document.querySelector('.card-body') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
    
    showSuccess(message) {
        const alertHtml = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const container = document.querySelector('.card-body') || document.body;
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
}

// Global functions for onclick handlers
function updateReports() {
    if (window.reportsManager) {
        window.reportsManager.updateReports();
    }
}

function filterTransactions(type) {
    if (window.reportsManager) {
        window.reportsManager.filterTransactions(type);
    }
}

function searchAuditLogs() {
    if (window.reportsManager) {
        const query = document.getElementById('auditSearch')?.value || '';
        window.reportsManager.searchAuditLogs(query);
    }
}

function exportReport(format) {
    if (window.reportsManager) {
        window.reportsManager.exportReport(format);
    }
}

function viewTransactionDetails(transactionId) {
    // Implementation for viewing transaction details
    console.log('View transaction details:', transactionId);
}

function viewUserDetails(userId) {
    // Implementation for viewing user details
    window.location.href = `/admin/users/index.php?user_id=${userId}`;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.reportsManager = new FinancialReportsManager();
});