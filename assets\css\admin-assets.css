/* Admin Assets Management Styles */

.admin-content {
    padding: 20px;
}

.content-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.content-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 24px;
}

.stat-content h3 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

/* Category Cards */
.category-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: white;
    font-size: 20px;
}

.category-card h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
}

.category-card p {
    color: #6c757d;
    font-size: 14px;
}

/* Asset Icon */
.asset-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

/* Table Styles */
.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Badges */
.badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 10px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Action buttons */
.btn-group-sm .btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Modal Styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: #dc3545;
}

/* Filter Dropdown */
.dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    transition: background-color 0.15s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #007bff;
    color: white;
}

/* Loading States */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Empty State */
.text-center.py-5 {
    padding: 60px 20px;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Price Change Colors */
.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
    font-size: 14px;
    padding: 12px 15px;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
}

/* Card Styles */
.card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
    padding: 15px 20px;
}

.card-header h5 {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-content {
        padding: 15px;
    }
    
    .content-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .content-header .d-flex {
        flex-direction: column;
        gap: 15px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .category-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 13px;
    }
    
    .btn-group-sm .btn {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    .modal-body {
        padding: 20px 15px;
    }
}

@media (max-width: 576px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .table th:nth-child(n+6),
    .table td:nth-child(n+6) {
        display: none;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 4px !important;
        margin-bottom: 2px;
    }
    
    .category-card {
        padding: 15px;
    }
    
    .category-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

/* Print Styles */
@media print {
    .btn, .dropdown, .modal {
        display: none !important;
    }
    
    .stat-card, .category-card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th, .table td {
        padding: 8px 4px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .stat-card, .category-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .stat-content h3, .category-card h6 {
        color: #e2e8f0;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .table td {
        border-color: #4a5568;
    }
    
    .table tbody tr:hover {
        background-color: #2d3748;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-header {
        background-color: #1a202c;
        border-color: #4a5568;
    }
    
    .form-control, .form-select {
        background-color: #1a202c;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #1a202c;
        border-color: #007bff;
        color: #e2e8f0;
    }
    
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .card-header {
        background-color: #1a202c;
        border-color: #4a5568;
    }
}