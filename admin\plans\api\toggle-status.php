<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $planId = intval($input['plan_id'] ?? 0);
    $newStatus = $input['status'] ?? '';
    
    if (!$planId) {
        echo json_encode(['success' => false, 'message' => 'Plan ID is required']);
        exit;
    }
    
    if (!in_array($newStatus, ['active', 'inactive'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit;
    }
    
    // Load the plan
    $plan = TradingPlan::find($planId);
    
    if (!$plan) {
        echo json_encode(['success' => false, 'message' => 'Trading plan not found']);
        exit;
    }
    
    $oldStatus = $plan->status;
    
    // Update status
    $plan->status = $newStatus;
    
    if ($plan->save()) {
        // Log the action
        AuditTrailService::log(
            'trading_plan_status_changed',
            'trading_plan',
            $plan->getId(),
            [
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'plan_name' => $plan->name
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => "Trading plan {$newStatus} successfully"
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update plan status'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Toggle trading plan status error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating plan status'
    ]);
}
?>