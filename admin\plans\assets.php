<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/views/AdminAssetsView.php';

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: ../../login.php');
    exit;
}

try {
    // Check if assets table exists, if not create it
    $checkTable = $pdo->query("SHOW TABLES LIKE 'trading_assets'");
    if ($checkTable->rowCount() == 0) {
        // Create trading assets table
        $createAssetsTable = "
        CREATE TABLE trading_assets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            symbol VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            category ENUM('crypto', 'forex', 'stocks', 'commodities', 'indices') NOT NULL,
            description TEXT,
            current_price DECIMAL(15,8),
            price_change_24h DECIMAL(10,4),
            status ENUM('active', 'inactive') DEFAULT 'active',
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_symbol (symbol),
            INDEX idx_category (category),
            INDEX idx_status (status),
            INDEX idx_sort_order (sort_order)
        )";
        
        $pdo->exec($createAssetsTable);
        
        // Insert some default assets
        $defaultAssets = [
            ['BTC/USD', 'Bitcoin', 'crypto', 'Bitcoin to US Dollar'],
            ['ETH/USD', 'Ethereum', 'crypto', 'Ethereum to US Dollar'],
            ['EUR/USD', 'Euro Dollar', 'forex', 'Euro to US Dollar'],
            ['GBP/USD', 'Pound Dollar', 'forex', 'British Pound to US Dollar'],
            ['AAPL', 'Apple Inc.', 'stocks', 'Apple Inc. Stock'],
            ['TSLA', 'Tesla Inc.', 'stocks', 'Tesla Inc. Stock'],
            ['GOLD', 'Gold', 'commodities', 'Gold Commodity'],
            ['OIL', 'Crude Oil', 'commodities', 'Crude Oil Commodity']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO trading_assets (symbol, name, category, description, sort_order) VALUES (?, ?, ?, ?, ?)");
        foreach ($defaultAssets as $index => $asset) {
            $stmt->execute([$asset[0], $asset[1], $asset[2], $asset[3], $index + 1]);
        }
    }
    
    // Get all assets
    $sql = "SELECT * FROM trading_assets ORDER BY sort_order ASC, created_at ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $assets = $stmt->fetchAll();
    
    // Get asset statistics
    $statsSql = "SELECT 
                    COUNT(*) as total_assets,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_assets,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_assets,
                    COUNT(DISTINCT category) as categories_count
                 FROM trading_assets";
    
    $statsStmt = $pdo->prepare($statsSql);
    $statsStmt->execute();
    $statistics = $statsStmt->fetch();
    
    // Get category breakdown
    $categorySql = "SELECT 
                        category,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count
                    FROM trading_assets 
                    GROUP BY category 
                    ORDER BY count DESC";
    
    $categoryStmt = $pdo->prepare($categorySql);
    $categoryStmt->execute();
    $categoryBreakdown = $categoryStmt->fetchAll();
    
    // Render the view
    $view = new AdminAssetsView();
    $view->render([
        'assets' => $assets,
        'statistics' => $statistics,
        'categoryBreakdown' => $categoryBreakdown,
        'user' => getCurrentUser()
    ]);
    
} catch (Exception $e) {
    error_log("Admin assets error: " . $e->getMessage());
    
    // Show error page
    $view = new AdminAssetsView();
    $view->render([
        'error' => 'Unable to load assets. Please try again.',
        'assets' => [],
        'statistics' => [],
        'categoryBreakdown' => [],
        'user' => getCurrentUser()
    ]);
}
?>