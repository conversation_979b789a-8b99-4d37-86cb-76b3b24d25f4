/* Admin Trading Plans Styles */

.admin-content {
    padding: 20px;
}

.content-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.content-header h1 {
    color: #2c3e50;
    font-weight: 600;
}

/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: white;
    font-size: 24px;
}

.stat-content h3 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

/* Plans Table */
.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Sortable functionality */
.sort-handle {
    cursor: grab;
    color: #6c757d;
    text-align: center;
}

.sort-handle:active {
    cursor: grabbing;
}

.sortable-ghost {
    opacity: 0.5;
    background-color: #e3f2fd;
}

.sortable-chosen {
    background-color: #f3e5f5;
}

.sortable-drag {
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transform: rotate(5deg);
}

/* Status badges */
.badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 10px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Action buttons */
.btn-group-sm .btn {
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 6px;
}

.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Modal Styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border: 1px solid #ced4da;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: #dc3545;
}

/* Features Management */
.feature-item {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.feature-item .input-group {
    margin-bottom: 8px;
}

.remove-feature-btn {
    border-left: none;
}

.remove-feature-btn:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

/* Calculated Values */
.alert {
    border: none;
    border-radius: 8px;
    font-size: 14px;
    padding: 12px 15px;
}

.alert-info {
    background-color: #e3f2fd;
    color: #1976d2;
}

.alert-success {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Loading States */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Empty State */
.text-center.py-5 {
    padding: 60px 20px;
}

.text-center.py-5 i {
    opacity: 0.5;
}

/* Filter Dropdown */
.dropdown-menu {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.dropdown-item {
    padding: 8px 16px;
    font-size: 14px;
    transition: background-color 0.15s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #007bff;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-content {
        padding: 15px;
    }
    
    .content-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .content-header .d-flex {
        flex-direction: column;
        gap: 15px;
    }
    
    .stat-card {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 13px;
    }
    
    .btn-group-sm .btn {
        padding: 4px 8px;
        font-size: 11px;
    }
    
    .modal-dialog {
        margin: 10px;
    }
    
    .modal-body {
        padding: 20px 15px;
    }
}

@media (max-width: 576px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .table th:nth-child(n+6),
    .table td:nth-child(n+6) {
        display: none;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 4px !important;
        margin-bottom: 2px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .stat-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .stat-content h3 {
        color: #e2e8f0;
    }
    
    .table th {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .table td {
        border-color: #4a5568;
    }
    
    .table tbody tr:hover {
        background-color: #2d3748;
    }
    
    .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-header {
        background-color: #1a202c;
        border-color: #4a5568;
    }
    
    .form-control, .form-select {
        background-color: #1a202c;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #1a202c;
        border-color: #007bff;
        color: #e2e8f0;
    }
}

/* Print Styles */
@media print {
    .btn, .dropdown, .modal {
        display: none !important;
    }
    
    .stat-card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .table {
        font-size: 12px;
    }
    
    .table th, .table td {
        padding: 8px 4px;
    }
}