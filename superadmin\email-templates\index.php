<?php
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/EmailTemplate.php';
require_once __DIR__ . '/../../classes/views/SuperAdminEmailTemplatesView.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Email Templates';
$view = new SuperAdminEmailTemplatesView();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        $currentUser = getCurrentUser();
        
        switch ($action) {
            case 'create_template':
                $templateData = [
                    'template_type' => $_POST['template_type'] ?? '',
                    'template_name' => $_POST['template_name'] ?? '',
                    'subject' => $_POST['subject'] ?? '',
                    'body_html' => $_POST['body_html'] ?? '',
                    'body_text' => $_POST['body_text'] ?? '',
                    'is_active' => isset($_POST['is_active']) ? 1 : 0,
                    'created_by' => $currentUser['id'],
                    'updated_by' => $currentUser['id']
                ];
                
                $template = new EmailTemplate();
                $errors = $template->validate($templateData);
                
                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }
                
                if ($template->create($templateData)) {
                    $response = ['success' => true, 'message' => 'Email template created successfully'];
                } else {
                    throw new Exception('Failed to create email template');
                }
                break;
                
            case 'update_template':
                $templateId = intval($_POST['template_id'] ?? 0);
                $templateData = [
                    'template_name' => $_POST['template_name'] ?? '',
                    'subject' => $_POST['subject'] ?? '',
                    'body_html' => $_POST['body_html'] ?? '',
                    'body_text' => $_POST['body_text'] ?? '',
                    'is_active' => isset($_POST['is_active']) ? 1 : 0,
                    'updated_by' => $currentUser['id']
                ];
                
                $template = EmailTemplate::find($templateId);
                if (!$template) {
                    throw new Exception('Template not found');
                }
                
                $errors = $template->validate($templateData);
                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }
                
                if ($template->update($templateData)) {
                    $response = ['success' => true, 'message' => 'Email template updated successfully'];
                } else {
                    throw new Exception('Failed to update email template');
                }
                break;
                
            case 'delete_template':
                $templateId = intval($_POST['template_id'] ?? 0);
                $template = EmailTemplate::find($templateId);
                
                if (!$template) {
                    throw new Exception('Template not found');
                }
                
                if ($template->delete()) {
                    $response = ['success' => true, 'message' => 'Email template deleted successfully'];
                } else {
                    throw new Exception('Failed to delete email template');
                }
                break;
                
            case 'toggle_status':
                $templateId = intval($_POST['template_id'] ?? 0);
                $template = EmailTemplate::find($templateId);
                
                if (!$template) {
                    throw new Exception('Template not found');
                }
                
                $newStatus = !$template->is_active;
                if ($template->update(['is_active' => $newStatus, 'updated_by' => $currentUser['id']])) {
                    $statusText = $newStatus ? 'activated' : 'deactivated';
                    $response = ['success' => true, 'message' => "Email template {$statusText} successfully"];
                } else {
                    throw new Exception('Failed to update template status');
                }
                break;
                
            case 'preview_template':
                $templateId = intval($_POST['template_id'] ?? 0);
                $template = EmailTemplate::find($templateId);
                
                if (!$template) {
                    throw new Exception('Template not found');
                }
                
                // Get sample data for preview
                $sampleData = $view->getSampleData($template->template_type);
                
                $previewHtml = $template->replacePlaceholders($template->body_html, $sampleData);
                $previewText = $template->replacePlaceholders($template->body_text, $sampleData);
                $previewSubject = $template->replacePlaceholders($template->subject, $sampleData);
                
                $response = [
                    'success' => true,
                    'preview' => [
                        'subject' => $previewSubject,
                        'html' => $previewHtml,
                        'text' => $previewText
                    ]
                ];
                break;
                
            case 'reset_defaults':
                EmailTemplate::createDefaults();
                $response = ['success' => true, 'message' => 'Default templates restored successfully'];
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for regular form submissions
    $_SESSION['form_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get templates for display
$templates = EmailTemplate::all();
$templateTypes = EmailTemplate::getTemplateTypes();

// Include layout
require_once __DIR__ . '/../../layouts/superadmin_layout.php';
?>