<?php
require_once '../../includes/functions.php';
require_once '../../classes/services/SessionManager.php';
require_once '../../classes/services/TransactionManager.php';
require_once '../../classes/views/WithdrawalStatusView.php';

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    header('Location: ../../login.php');
    exit;
}

// Get filter parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;
$status = $_GET['status'] ?? '';

// Get user's withdrawals
$withdrawals = Withdrawal::getByUser($user->getId(), $limit, $offset);

// Filter by status if specified
if ($status) {
    $withdrawals = array_filter($withdrawals, function($withdrawal) use ($status) {
        return $withdrawal->status === $status;
    });
}

// Get withdrawal statistics
$stats = [
    'total' => Withdrawal::count("user_id = ?", [$user->getId()]),
    'pending' => Withdrawal::count("user_id = ? AND status = 'pending'", [$user->getId()]),
    'approved' => Withdrawal::count("user_id = ? AND status = 'approved'", [$user->getId()]),
    'completed' => Withdrawal::count("user_id = ? AND status = 'completed'", [$user->getId()]),
    'rejected' => Withdrawal::count("user_id = ? AND status = 'rejected'", [$user->getId()]),
    'total_amount' => Withdrawal::getTotalAmountByStatus('completed') // User-specific would need custom query
];

// Calculate total pages for pagination
$totalWithdrawals = $stats['total'];
$totalPages = ceil($totalWithdrawals / $limit);

// Get recent transactions related to withdrawals
$recentTransactions = TransactionManager::getUserTransactionHistory($user->getId(), 5, 0, 'withdrawal');

// Render view
$view = new WithdrawalStatusView();
$view->render([
    'user' => $user,
    'withdrawals' => $withdrawals,
    'statistics' => $stats,
    'recent_transactions' => $recentTransactions,
    'filters' => [
        'status' => $status
    ],
    'pagination' => [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_records' => $totalWithdrawals,
        'limit' => $limit
    ]
]);
?>