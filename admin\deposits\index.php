<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/services/SessionManager.php';
require_once '../../classes/services/CSRFProtection.php';
require_once '../../classes/models/Deposit.php';
require_once '../../classes/models/User.php';
require_once '../../classes/models/TradingPlan.php';
require_once '../../classes/models/PaymentMethod.php';
require_once '../../classes/views/AdminDepositManagementView.php';

// Initialize session and check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: ../../login.php');
    exit();
}

// Get filter parameters
$status = $_GET['status'] ?? 'all';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Get deposits with details
$deposits = [];
$totalDeposits = 0;

try {
    if ($status === 'all') {
        $deposits = Deposit::getWithDetails($limit, $offset);
        $totalDeposits = Deposit::count();
    } else {
        $deposits = Deposit::getWithDetails($limit, $offset, $status);
        $totalDeposits = Deposit::count("status = ?", [$status]);
    }
} catch (Exception $e) {
    error_log("Error fetching deposits: " . $e->getMessage());
    $deposits = [];
}

// Get statistics
$stats = [
    'pending' => Deposit::getPendingCount(),
    'approved' => Deposit::count("status = 'approved'"),
    'rejected' => Deposit::count("status = 'rejected'"),
    'total_amount' => Deposit::getTotalAmountByStatus('approved')
];

// Calculate pagination
$totalPages = ceil($totalDeposits / $limit);

// Render view
$view = new AdminDepositManagementView();
$view->render([
    'deposits' => $deposits,
    'stats' => $stats,
    'currentStatus' => $status,
    'currentPage' => $page,
    'totalPages' => $totalPages,
    'totalDeposits' => $totalDeposits,
    'csrfToken' => CSRFProtection::generateToken()
]);
?>