<?php
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../classes/models/User.php';
require_once __DIR__ . '/../classes/models/Transaction.php';
require_once __DIR__ . '/../classes/models/SystemSetting.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Super Admin Dashboard';
$user = getCurrentUser();

// Get system statistics
try {
    $pdo = getDBConnection();
    
    // User statistics
    $userStats = $pdo->query("
        SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admin_users,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as new_today
        FROM users
    ")->fetch();
    
    // Transaction statistics
    $transactionStats = $pdo->query("
        SELECT 
            COUNT(*) as total_transactions,
            SUM(amount) as total_volume,
            SUM(CASE WHEN type = 'deposit' THEN amount ELSE 0 END) as total_deposits,
            SUM(CASE WHEN type = 'withdrawal' THEN amount ELSE 0 END) as total_withdrawals,
            SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_count
        FROM transactions
    ")->fetch();
    
    // System health checks
    $systemHealth = [
        'database' => 'online',
        'email' => defined('SMTP_HOST') && !empty(SMTP_HOST) ? 'configured' : 'not_configured',
        'maintenance' => SystemSetting::getValue('maintenance_mode', false) ? 'maintenance' : 'online',
        'debug' => SystemSetting::getValue('debug_mode', false) ? 'enabled' : 'disabled'
    ];
    
    // Recent activity
    $recentUsers = $pdo->query("
        SELECT id, first_name, last_name, email, created_at 
        FROM users 
        ORDER BY created_at DESC 
        LIMIT 5
    ")->fetchAll();
    
    $recentTransactions = $pdo->query("
        SELECT t.*, u.first_name, u.last_name 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        ORDER BY t.created_at DESC 
        LIMIT 5
    ")->fetchAll();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $userStats = $transactionStats = [];
    $systemHealth = ['database' => 'error'];
    $recentUsers = $recentTransactions = [];
}

// Include layout
require_once __DIR__ . '/../layouts/superadmin_layout.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 px-0">
            <div class="sidebar d-flex flex-column p-3">
                <div class="text-center mb-4">
                    <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="navbar-brand img-fluid" style="max-height: 50px;">
                    <div class="superadmin-badge mt-2">SUPER ADMIN</div>
                </div>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="/superadmin/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/settings/">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/admins/">
                            <i class="fas fa-user-shield me-2"></i>Admin Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/email-templates/">
                            <i class="fas fa-envelope-open-text me-2"></i>Email Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/audit/">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/superadmin/system/">
                            <i class="fas fa-server me-2"></i>System Health
                        </a>
                    </li>
                </ul>
                
                <div class="mt-auto">
                    <div class="dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($user['first_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/user/profile/"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Super Admin Dashboard</h1>
                    <div class="d-flex gap-2">
                        <span class="badge bg-success">System Online</span>
                        <span class="text-muted"><?php echo date('Y-m-d H:i:s'); ?></span>
                    </div>
                </div>
                
                <!-- System Health Status -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-heartbeat me-2"></i>System Health
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-database me-2 <?php echo $systemHealth['database'] === 'online' ? 'text-success' : 'text-danger'; ?>"></i>
                                            <span>Database: <strong><?php echo ucfirst($systemHealth['database']); ?></strong></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-envelope me-2 <?php echo $systemHealth['email'] === 'configured' ? 'text-success' : 'text-warning'; ?>"></i>
                                            <span>Email: <strong><?php echo ucfirst(str_replace('_', ' ', $systemHealth['email'])); ?></strong></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-tools me-2 <?php echo $systemHealth['maintenance'] === 'online' ? 'text-success' : 'text-warning'; ?>"></i>
                                            <span>Status: <strong><?php echo ucfirst($systemHealth['maintenance']); ?></strong></span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-bug me-2 <?php echo $systemHealth['debug'] === 'disabled' ? 'text-success' : 'text-warning'; ?>"></i>
                                            <span>Debug: <strong><?php echo ucfirst($systemHealth['debug']); ?></strong></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($userStats['total_users'] ?? 0); ?></h4>
                                        <p class="mb-0">Total Users</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small><?php echo $userStats['new_today'] ?? 0; ?> new today</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($userStats['active_users'] ?? 0); ?></h4>
                                        <p class="mb-0">Active Users</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small><?php echo $userStats['admin_users'] ?? 0; ?> admins</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0"><?php echo number_format($transactionStats['total_transactions'] ?? 0); ?></h4>
                                        <p class="mb-0">Total Transactions</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exchange-alt fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small><?php echo $transactionStats['today_count'] ?? 0; ?> today</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">$<?php echo number_format($transactionStats['total_volume'] ?? 0, 2); ?></h4>
                                        <p class="mb-0">Total Volume</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-dollar-sign fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small>All time</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user-plus me-2"></i>Recent Users
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentUsers)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recentUsers as $recentUser): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($recentUser['first_name'] . ' ' . $recentUser['last_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($recentUser['email']); ?></small>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($recentUser['created_at'])); ?>
                                                </small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="/admin/users/" class="btn btn-outline-primary btn-sm">View All Users</a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center">No recent users</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-exchange-alt me-2"></i>Recent Transactions
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentTransactions)): ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recentTransactions as $transaction): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo ucfirst($transaction['type']); ?> - 
                                                        <span class="<?php echo $transaction['type'] === 'deposit' ? 'text-success' : 'text-danger'; ?>">
                                                            $<?php echo number_format($transaction['amount'], 2); ?>
                                                        </span>
                                                    </small>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo date('M j, Y', strtotime($transaction['created_at'])); ?>
                                                </small>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="/admin/reports/" class="btn btn-outline-primary btn-sm">View All Transactions</a>
                                    </div>
                                <?php else: ?>
                                    <p class="text-muted text-center">No recent transactions</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt me-2"></i>Quick Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="/superadmin/settings/" class="btn btn-outline-primary w-100 mb-2">
                                            <i class="fas fa-cogs me-2"></i>System Settings
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="/admin/users/" class="btn btn-outline-success w-100 mb-2">
                                            <i class="fas fa-users me-2"></i>Manage Users
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="/admin/reports/" class="btn btn-outline-info w-100 mb-2">
                                            <i class="fas fa-chart-bar me-2"></i>View Reports
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="/superadmin/audit/" class="btn btn-outline-warning w-100 mb-2">
                                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.superadmin-badge {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: bold;
    text-align: center;
}

.sidebar {
    background: linear-gradient(180deg, #2c3e50, #34495e);
    min-height: 100vh;
    color: white;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 0.375rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
}
</style>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>