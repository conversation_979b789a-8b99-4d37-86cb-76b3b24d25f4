<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Checking database tables...\n";
    
    $stmt = $db->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "All tables:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    // Check for email-related tables
    $emailTables = array_filter($tables, function($table) {
        return strpos($table, 'email') !== false || strpos($table, 'template') !== false;
    });
    
    if (!empty($emailTables)) {
        echo "\nEmail-related tables:\n";
        foreach ($emailTables as $table) {
            echo "- $table\n";
            
            // Show structure
            $stmt = $db->query("DESCRIBE $table");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($columns as $column) {
                echo "  - {$column['Field']} ({$column['Type']})\n";
            }
        }
    } else {
        echo "\nNo email-related tables found.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>