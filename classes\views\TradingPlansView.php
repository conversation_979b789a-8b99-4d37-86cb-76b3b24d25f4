<?php
require_once __DIR__ . '/BaseView.php';

/**
 * Trading Plans View - Displays available trading plans and handles plan selection
 */
class TradingPlansView extends BaseView {
    private $tradingPlans;
    private $paymentMethods;
    private $user;
    
    public function __construct($tradingPlans, $paymentMethods, $user) {
        parent::__construct();
        $this->tradingPlans = $tradingPlans;
        $this->paymentMethods = $paymentMethods;
        $this->user = $user;
        $this->pageTitle = 'Trading Plans - Coinage Trading';
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/trading-plans.css">
        <?php
    }
    
    protected function renderBody() {
        ?>
        <div class="container-fluid">
            <div class="row">
                <!-- Page Header -->
                <div class="col-12">
                    <div class="page-header mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="h3 mb-0">Trading Plans</h1>
                                <p class="text-muted">Choose the perfect investment plan for your goals</p>
                            </div>
                            <div class="user-balance-card">
                                <div class="balance-info">
                                    <span class="balance-label">Available Balance:</span>
                                    <span class="balance-amount">$<?php echo number_format($this->user->balance, 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Trading Plans Grid -->
            <div class="row">
                <?php if (empty($this->tradingPlans)): ?>
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <h4>No Trading Plans Available</h4>
                            <p>There are currently no active trading plans. Please check back later.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($this->tradingPlans as $plan): ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="trading-plan-card" data-plan-id="<?php echo $plan->getId(); ?>">
                                <div class="plan-header">
                                    <h3 class="plan-name"><?php echo htmlspecialchars($plan->name); ?></h3>
                                    <div class="plan-return">
                                        <span class="return-rate"><?php echo $plan->getFormattedDailyReturn(); ?></span>
                                        <span class="return-period">Daily Return</span>
                                    </div>
                                </div>
                                
                                <div class="plan-body">
                                    <div class="plan-details">
                                        <div class="detail-item">
                                            <i class="fas fa-dollar-sign"></i>
                                            <div>
                                                <span class="detail-label">Investment Range</span>
                                                <span class="detail-value">$<?php echo $plan->getDepositRange(); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="detail-item">
                                            <i class="fas fa-calendar-alt"></i>
                                            <div>
                                                <span class="detail-label">Duration</span>
                                                <span class="detail-value"><?php echo $plan->duration_days; ?> Days</span>
                                            </div>
                                        </div>
                                        
                                        <div class="detail-item">
                                            <i class="fas fa-chart-line"></i>
                                            <div>
                                                <span class="detail-label">Total Return</span>
                                                <span class="detail-value"><?php echo $plan->getFormattedTotalReturn(); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <?php if ($plan->description): ?>
                                        <div class="plan-description">
                                            <p><?php echo htmlspecialchars($plan->description); ?></p>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php 
                                    $features = $plan->getFeaturesArray();
                                    if (!empty($features)): 
                                    ?>
                                        <div class="plan-features">
                                            <h5>Features:</h5>
                                            <ul>
                                                <?php foreach ($features as $feature): ?>
                                                    <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="plan-footer">
                                    <button type="button" class="btn btn-primary btn-select-plan" 
                                            data-plan-id="<?php echo $plan->getId(); ?>"
                                            data-plan-name="<?php echo htmlspecialchars($plan->name); ?>"
                                            data-min-deposit="<?php echo $plan->min_deposit; ?>"
                                            data-max-deposit="<?php echo $plan->max_deposit ?: ''; ?>">
                                        <i class="fas fa-rocket"></i> Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Deposit Modal -->
        <div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="depositModalLabel">
                            <i class="fas fa-plus-circle"></i> Make Deposit
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    
                    <form id="depositForm" method="POST" action="/user/deposit/process.php" enctype="multipart/form-data">
                        <div class="modal-body">
                            <!-- Plan Information -->
                            <div class="selected-plan-info mb-4">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Selected Plan: <span id="selectedPlanName"></span></h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small>Investment Range: $<span id="selectedPlanRange"></span></small>
                                        </div>
                                        <div class="col-md-6">
                                            <small>Daily Return: <span id="selectedPlanReturn"></span></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Deposit Amount -->
                            <div class="mb-3">
                                <label for="depositAmount" class="form-label">
                                    <i class="fas fa-dollar-sign"></i> Deposit Amount *
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="depositAmount" name="amount" 
                                           step="0.01" min="1" required>
                                </div>
                                <div class="form-text">
                                    <span id="amountValidation"></span>
                                </div>
                            </div>
                            
                            <!-- Payment Method -->
                            <div class="mb-3">
                                <label for="paymentMethod" class="form-label">
                                    <i class="fas fa-credit-card"></i> Payment Method *
                                </label>
                                <select class="form-select" id="paymentMethod" name="payment_method_id" required>
                                    <option value="">Select Payment Method</option>
                                    <?php foreach ($this->paymentMethods as $method): ?>
                                        <option value="<?php echo $method['id']; ?>" 
                                                data-type="<?php echo $method['type']; ?>"
                                                data-details="<?php echo htmlspecialchars($method['details']); ?>">
                                            <?php echo htmlspecialchars($method['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <!-- Payment Details -->
                            <div id="paymentDetails" class="mb-3" style="display: none;">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> Payment Instructions</h6>
                                    <div id="paymentInstructions"></div>
                                </div>
                            </div>
                            
                            <!-- Transaction ID -->
                            <div class="mb-3">
                                <label for="transactionId" class="form-label">
                                    <i class="fas fa-receipt"></i> Transaction ID
                                </label>
                                <input type="text" class="form-control" id="transactionId" name="transaction_id" 
                                       placeholder="Enter transaction/reference ID">
                                <div class="form-text">Optional: Enter the transaction ID from your payment</div>
                            </div>
                            
                            <!-- Proof of Payment -->
                            <div class="mb-3">
                                <label for="proofOfPayment" class="form-label">
                                    <i class="fas fa-file-upload"></i> Proof of Payment
                                </label>
                                <input type="file" class="form-control" id="proofOfPayment" name="proof_of_payment" 
                                       accept="image/*,.pdf">
                                <div class="form-text">Upload screenshot or receipt of your payment (optional)</div>
                            </div>
                            
                            <!-- Bonus Information -->
                            <div class="bonus-info">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-gift"></i> Bonus Information</h6>
                                    <p class="mb-0">You will receive a bonus based on your deposit amount. The exact bonus will be calculated and added to your account upon approval.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitDeposit">
                                <i class="fas fa-paper-plane"></i> Submit Deposit
                            </button>
                        </div>
                        
                        <!-- Hidden Fields -->
                        <input type="hidden" name="plan_id" id="selectedPlanId">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    </form>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/trading-plans.js"></script>
        <?php
    }
}
?>