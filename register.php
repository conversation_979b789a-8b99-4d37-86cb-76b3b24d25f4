<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'classes/controllers/AuthController.php';

// Process registration
$registerData = AuthController::register();
$errors = $registerData['errors'];
$message = $registerData['message'];
$success = $registerData['success'];
$csrfToken = $registerData['csrf_token'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <?php echo CSRFProtection::getTokenMeta(); ?>
    <style>
        body {
            background: linear-gradient(135deg, <?php echo PRIMARY_COLOR; ?> 0%, <?php echo SECONDARY_COLOR; ?> 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .register-header {
            background: <?php echo PRIMARY_COLOR; ?>;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .register-body {
            padding: 2rem;
        }
        .form-control:focus {
            border-color: <?php echo PRIMARY_COLOR; ?>;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .btn-primary {
            background-color: <?php echo PRIMARY_COLOR; ?>;
            border-color: <?php echo PRIMARY_COLOR; ?>;
        }
        .btn-primary:hover {
            background-color: <?php echo SECONDARY_COLOR; ?>;
            border-color: <?php echo SECONDARY_COLOR; ?>;
        }
        .alert {
            border-radius: 10px;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #dc3545; }
        .strength-medium { background-color: #ffc107; }
        .strength-strong { background-color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="register-container">
                    <div class="register-header">
                        <h2><i class="fas fa-coins"></i> <?php echo SITE_NAME; ?></h2>
                        <p class="mb-0">Create Your Account</p>
                    </div>
                    
                    <div class="register-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            <div class="text-center">
                                <a href="login.php" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Go to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <?php if ($message): ?>
                                <div class="alert alert-info alert-dismissible fade show" role="alert">
                                    <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($message); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($errors['general'])): ?>
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($errors['general']); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="register.php" id="registerForm">
                                <?php echo CSRFProtection::getTokenField(); ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="first_name" class="form-label">First Name *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                <input type="text" 
                                                       class="form-control <?php echo !empty($errors['first_name']) ? 'is-invalid' : ''; ?>" 
                                                       id="first_name" 
                                                       name="first_name" 
                                                       value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>"
                                                       required>
                                            </div>
                                            <?php if (!empty($errors['first_name'])): ?>
                                                <div class="invalid-feedback d-block">
                                                    <?php echo htmlspecialchars($errors['first_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="last_name" class="form-label">Last Name *</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                                <input type="text" 
                                                       class="form-control <?php echo !empty($errors['last_name']) ? 'is-invalid' : ''; ?>" 
                                                       id="last_name" 
                                                       name="last_name" 
                                                       value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>"
                                                       required>
                                            </div>
                                            <?php if (!empty($errors['last_name'])): ?>
                                                <div class="invalid-feedback d-block">
                                                    <?php echo htmlspecialchars($errors['last_name']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-at"></i></span>
                                        <input type="text" 
                                               class="form-control <?php echo !empty($errors['username']) ? 'is-invalid' : ''; ?>" 
                                               id="username" 
                                               name="username" 
                                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                               required 
                                               autocomplete="username">
                                    </div>
                                    <?php if (!empty($errors['username'])): ?>
                                        <div class="invalid-feedback d-block">
                                            <?php echo htmlspecialchars($errors['username']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="form-text">3-50 characters, letters, numbers, and underscores only</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" 
                                               class="form-control <?php echo !empty($errors['email']) ? 'is-invalid' : ''; ?>" 
                                               id="email" 
                                               name="email" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                               required 
                                               autocomplete="email">
                                    </div>
                                    <?php if (!empty($errors['email'])): ?>
                                        <div class="invalid-feedback d-block">
                                            <?php echo htmlspecialchars($errors['email']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                        <input type="tel" 
                                               class="form-control <?php echo !empty($errors['phone']) ? 'is-invalid' : ''; ?>" 
                                               id="phone" 
                                               name="phone" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                                    </div>
                                    <?php if (!empty($errors['phone'])): ?>
                                        <div class="invalid-feedback d-block">
                                            <?php echo htmlspecialchars($errors['phone']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="form-text">Optional - for account security</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" 
                                               class="form-control <?php echo !empty($errors['password']) ? 'is-invalid' : ''; ?>" 
                                               id="password" 
                                               name="password" 
                                               required 
                                               autocomplete="new-password">
                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <?php if (!empty($errors['password'])): ?>
                                        <div class="invalid-feedback d-block">
                                            <?php echo htmlspecialchars($errors['password']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="form-text">Minimum 6 characters with at least one letter and one number</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password *</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" 
                                               class="form-control <?php echo !empty($errors['confirm_password']) ? 'is-invalid' : ''; ?>" 
                                               id="confirm_password" 
                                               name="confirm_password" 
                                               required 
                                               autocomplete="new-password">
                                        <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <?php if (!empty($errors['confirm_password'])): ?>
                                        <div class="invalid-feedback d-block">
                                            <?php echo htmlspecialchars($errors['confirm_password']); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div id="passwordMatch" class="form-text"></div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="terms" required>
                                    <label class="form-check-label" for="terms">
                                        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a> *
                                    </label>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-user-plus"></i> Create Account
                                    </button>
                                </div>
                            </form>
                            
                            <hr class="my-4">
                            
                            <div class="text-center">
                                <p class="mb-0">
                                    Already have an account? 
                                    <a href="login.php" class="text-decoration-none fw-bold">
                                        <i class="fas fa-sign-in-alt"></i> Sign In
                                    </a>
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle password visibility
        function setupPasswordToggle(passwordId, toggleId) {
            document.getElementById(toggleId).addEventListener('click', function() {
                const password = document.getElementById(passwordId);
                const icon = this.querySelector('i');
                
                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
        }
        
        setupPasswordToggle('password', 'togglePassword');
        setupPasswordToggle('confirm_password', 'toggleConfirmPassword');
        
        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-zA-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            strengthBar.style.width = (strength * 25) + '%';
            
            if (strength <= 1) {
                strengthBar.className = 'password-strength strength-weak';
            } else if (strength <= 2) {
                strengthBar.className = 'password-strength strength-medium';
            } else {
                strengthBar.className = 'password-strength strength-strong';
            }
        });
        
        // Password match indicator
        function checkPasswordMatch() {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchIndicator = document.getElementById('passwordMatch');
            
            if (confirmPassword.length > 0) {
                if (password === confirmPassword) {
                    matchIndicator.innerHTML = '<span class="text-success"><i class="fas fa-check"></i> Passwords match</span>';
                } else {
                    matchIndicator.innerHTML = '<span class="text-danger"><i class="fas fa-times"></i> Passwords do not match</span>';
                }
            } else {
                matchIndicator.innerHTML = '';
            }
        }
        
        document.getElementById('password').addEventListener('input', checkPasswordMatch);
        document.getElementById('confirm_password').addEventListener('input', checkPasswordMatch);
        
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const terms = document.getElementById('terms').checked;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match.');
                return false;
            }
            
            if (!terms) {
                e.preventDefault();
                alert('Please accept the Terms of Service and Privacy Policy.');
                return false;
            }
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>