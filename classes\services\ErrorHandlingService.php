<?php
require_once __DIR__ . '/SecurityAuditService.php';
require_once __DIR__ . '/EmailNotificationService.php';
require_once __DIR__ . '/../models/SystemSetting.php';

/**
 * Centralized Error Handling Service
 * Comprehensive error management with logging, notifications, and recovery
 */
class ErrorHandlingService {
    private static $instance = null;
    private $auditService;
    private $emailService;
    private $errorLog = [];
    
    // Error severity levels
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';
    
    // Error categories
    const CATEGORY_DATABASE = 'database';
    const CATEGORY_AUTHENTICATION = 'authentication';
    const CATEGORY_FINANCIAL = 'financial';
    const CATEGORY_FILE_UPLOAD = 'file_upload';
    const CATEGORY_VALIDATION = 'validation';
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_SECURITY = 'security';
    const CATEGORY_API = 'api';
    
    private function __construct() {
        $this->auditService = SecurityAuditService::getInstance();
        $this->emailService = EmailNotificationService::getInstance();
        
        // Set custom error and exception handlers
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleFatalError']);
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line) {
        // Don't handle errors that are suppressed with @
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorData = [
            'type' => 'php_error',
            'severity' => $this->mapPhpSeverity($severity),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'category' => $this->categorizeError($message),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        
        // Don't execute PHP internal error handler
        return true;
    }
    
    /**
     * Handle uncaught exceptions
     */
    public function handleException($exception) {
        $errorData = [
            'type' => 'exception',
            'severity' => self::SEVERITY_HIGH,
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'category' => $this->categorizeError($exception->getMessage()),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        $this->displayErrorPage($errorData);
    }
    
    /**
     * Handle fatal errors
     */
    public function handleFatalError() {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorData = [
                'type' => 'fatal_error',
                'severity' => self::SEVERITY_CRITICAL,
                'message' => $error['message'],
                'file' => $error['file'],
                'line' => $error['line'],
                'category' => $this->categorizeError($error['message']),
                'context' => $this->getErrorContext()
            ];
            
            $this->logError($errorData);
            $this->notifyAdmins($errorData);
        }
    }
    
    /**
     * Log application-specific errors
     */
    public function logApplicationError($message, $category = self::CATEGORY_SYSTEM, $severity = self::SEVERITY_MEDIUM, $context = []) {
        $errorData = [
            'type' => 'application_error',
            'severity' => $severity,
            'message' => $message,
            'category' => $category,
            'context' => array_merge($this->getErrorContext(), $context),
            'file' => debug_backtrace()[0]['file'] ?? 'unknown',
            'line' => debug_backtrace()[0]['line'] ?? 0
        ];
        
        $this->logError($errorData);
        
        // Notify admins for high/critical errors
        if (in_array($severity, [self::SEVERITY_HIGH, self::SEVERITY_CRITICAL])) {
            $this->notifyAdmins($errorData);
        }
        
        return $this->generateErrorId($errorData);
    }
    
    /**
     * Handle database errors with transaction rollback
     */
    public function handleDatabaseError($exception, $query = null, $params = []) {
        // Attempt transaction rollback if in transaction
        try {
            $db = Database::getInstance()->getConnection();
            if ($db->inTransaction()) {
                $db->rollBack();
            }
        } catch (Exception $e) {
            // Ignore rollback errors
        }
        
        $errorData = [
            'type' => 'database_error',
            'severity' => self::SEVERITY_HIGH,
            'message' => $exception->getMessage(),
            'category' => self::CATEGORY_DATABASE,
            'query' => $query,
            'params' => $this->sanitizeParams($params),
            'context' => $this->getErrorContext(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine()
        ];
        
        $this->logError($errorData);
        
        return [
            'success' => false,
            'error' => 'Database operation failed. Please try again.',
            'error_id' => $this->generateErrorId($errorData)
        ];
    }
    
    /**
     * Handle authentication errors with attempt tracking
     */
    public function handleAuthenticationError($message, $userId = null, $email = null, $ip = null) {
        $errorData = [
            'type' => 'authentication_error',
            'severity' => self::SEVERITY_MEDIUM,
            'message' => $message,
            'category' => self::CATEGORY_AUTHENTICATION,
            'user_id' => $userId,
            'email' => $email,
            'ip' => $ip ?: ($_SERVER['REMOTE_ADDR'] ?? 'unknown'),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        
        // Track failed attempts
        $this->trackFailedAttempt($email ?: $userId, $ip);
        
        return [
            'success' => false,
            'error' => 'Authentication failed. Please check your credentials.',
            'error_id' => $this->generateErrorId($errorData)
        ];
    }
    
    /**
     * Handle file upload errors
     */
    public function handleFileUploadError($uploadError, $fileName = null, $fileSize = null) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File exceeds maximum allowed size',
            UPLOAD_ERR_FORM_SIZE => 'File exceeds form maximum size',
            UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
            UPLOAD_ERR_NO_FILE => 'No file was uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
            UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
            UPLOAD_ERR_EXTENSION => 'File upload stopped by extension'
        ];
        
        $message = $errorMessages[$uploadError] ?? 'Unknown upload error';
        
        $errorData = [
            'type' => 'file_upload_error',
            'severity' => self::SEVERITY_MEDIUM,
            'message' => $message,
            'category' => self::CATEGORY_FILE_UPLOAD,
            'upload_error_code' => $uploadError,
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        
        return [
            'success' => false,
            'error' => $message,
            'error_id' => $this->generateErrorId($errorData)
        ];
    }
    
    /**
     * Handle financial transaction errors
     */
    public function handleFinancialError($message, $transactionData = [], $severity = self::SEVERITY_HIGH) {
        $errorData = [
            'type' => 'financial_error',
            'severity' => $severity,
            'message' => $message,
            'category' => self::CATEGORY_FINANCIAL,
            'transaction_data' => $this->sanitizeFinancialData($transactionData),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        
        // Always notify admins for financial errors
        $this->notifyAdmins($errorData);
        
        return [
            'success' => false,
            'error' => 'Transaction failed. Please contact support if the problem persists.',
            'error_id' => $this->generateErrorId($errorData)
        ];
    }
    
    /**
     * Handle validation errors
     */
    public function handleValidationError($errors, $formData = []) {
        $errorData = [
            'type' => 'validation_error',
            'severity' => self::SEVERITY_LOW,
            'message' => 'Form validation failed',
            'category' => self::CATEGORY_VALIDATION,
            'validation_errors' => $errors,
            'form_data' => $this->sanitizeFormData($formData),
            'context' => $this->getErrorContext()
        ];
        
        $this->logError($errorData);
        
        return [
            'success' => false,
            'errors' => $errors,
            'error_id' => $this->generateErrorId($errorData)
        ];
    }
    
    /**
     * Log error to multiple destinations
     */
    private function logError($errorData) {
        $errorId = $this->generateErrorId($errorData);
        $errorData['error_id'] = $errorId;
        $errorData['timestamp'] = date('Y-m-d H:i:s');
        
        // Add to internal log
        $this->errorLog[] = $errorData;
        
        // Log to security audit system
        $this->auditService->logSystemEvent('error_occurred', $errorData);
        
        // Log to file
        $this->logToFile($errorData);
        
        // Log to database if possible
        $this->logToDatabase($errorData);
        
        // Send to external monitoring if configured
        $this->sendToExternalMonitoring($errorData);
    }
    
    /**
     * Log error to file
     */
    private function logToFile($errorData) {
        try {
            $logDir = __DIR__ . '/../../logs/errors';
            if (!file_exists($logDir)) {
                mkdir($logDir, 0755, true);
            }
            
            $logFile = $logDir . '/error_' . date('Y-m-d') . '.log';
            $logEntry = json_encode($errorData) . "\n";
            
            file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            // Fallback to PHP error log
            error_log("Error logging failed: " . $e->getMessage());
            error_log("Original error: " . json_encode($errorData));
        }
    }
    
    /**
     * Log error to database
     */
    private function logToDatabase($errorData) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                INSERT INTO error_logs (
                    error_id, type, severity, category, message, file, line, 
                    context, user_id, ip_address, user_agent, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $stmt->execute([
                $errorData['error_id'],
                $errorData['type'],
                $errorData['severity'],
                $errorData['category'],
                $errorData['message'],
                $errorData['file'] ?? null,
                $errorData['line'] ?? null,
                json_encode($errorData['context']),
                $errorData['context']['user_id'] ?? null,
                $errorData['context']['ip'] ?? null,
                $errorData['context']['user_agent'] ?? null
            ]);
        } catch (Exception $e) {
            // Ignore database logging errors to prevent infinite loops
            error_log("Database error logging failed: " . $e->getMessage());
        }
    }
    
    /**
     * Send error to external monitoring service
     */
    private function sendToExternalMonitoring($errorData) {
        // Implement integration with external monitoring services
        // (Sentry, Bugsnag, etc.) if configured
        
        $monitoringEnabled = SystemSetting::getValue('external_monitoring_enabled', '0') === '1';
        if (!$monitoringEnabled) {
            return;
        }
        
        // Example implementation for external service
        try {
            $monitoringUrl = SystemSetting::getValue('monitoring_webhook_url', '');
            if (!empty($monitoringUrl)) {
                $payload = json_encode([
                    'error_id' => $errorData['error_id'],
                    'severity' => $errorData['severity'],
                    'message' => $errorData['message'],
                    'category' => $errorData['category'],
                    'timestamp' => $errorData['timestamp'],
                    'context' => $errorData['context']
                ]);
                
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => 'Content-Type: application/json',
                        'content' => $payload,
                        'timeout' => 5
                    ]
                ]);
                
                file_get_contents($monitoringUrl, false, $context);
            }
        } catch (Exception $e) {
            // Ignore external monitoring errors
        }
    }
    
    /**
     * Notify administrators of critical errors
     */
    private function notifyAdmins($errorData) {
        try {
            // Only notify for high/critical errors
            if (!in_array($errorData['severity'], [self::SEVERITY_HIGH, self::SEVERITY_CRITICAL])) {
                return;
            }
            
            // Check if admin notifications are enabled
            $notificationsEnabled = SystemSetting::getValue('admin_error_notifications', '1') === '1';
            if (!$notificationsEnabled) {
                return;
            }
            
            // Get admin emails
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT email, first_name FROM users WHERE role IN ('admin', 'superadmin') AND status = 'active'");
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($admins as $admin) {
                $this->sendErrorNotificationEmail($admin, $errorData);
            }
        } catch (Exception $e) {
            // Ignore notification errors to prevent infinite loops
            error_log("Admin notification failed: " . $e->getMessage());
        }
    }
    
    /**
     * Send error notification email to admin
     */
    private function sendErrorNotificationEmail($admin, $errorData) {
        $subject = "System Error Alert - {$errorData['severity']} - " . SystemSetting::getValue('site_name', 'Coinage Trading');
        
        $body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <h2 style='color: #dc3545;'>System Error Alert</h2>
            <p>Dear {$admin['first_name']},</p>
            <p>A {$errorData['severity']} error has occurred in the system:</p>
            
            <div style='background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;'>
                <h3>Error Details:</h3>
                <p><strong>Error ID:</strong> {$errorData['error_id']}</p>
                <p><strong>Type:</strong> {$errorData['type']}</p>
                <p><strong>Category:</strong> {$errorData['category']}</p>
                <p><strong>Message:</strong> {$errorData['message']}</p>
                <p><strong>Time:</strong> {$errorData['timestamp']}</p>
                " . (isset($errorData['file']) ? "<p><strong>File:</strong> {$errorData['file']}:{$errorData['line']}</p>" : "") . "
            </div>
            
            <p>Please investigate this error and take appropriate action.</p>
            <p>System Administrator</p>
        </div>";
        
        // Use email service to send notification
        try {
            mail($admin['email'], $subject, $body, [
                'Content-Type' => 'text/html; charset=UTF-8',
                'From' => 'system@' . ($_SERVER['HTTP_HOST'] ?? 'localhost')
            ]);
        } catch (Exception $e) {
            error_log("Failed to send error notification email: " . $e->getMessage());
        }
    }
    
    /**
     * Display user-friendly error page
     */
    private function displayErrorPage($errorData) {
        // Don't display error page for AJAX requests
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'error_id' => $errorData['error_id']
            ]);
            exit();
        }
        
        // Display appropriate error page based on severity
        $this->renderErrorPage($errorData);
    }
    
    /**
     * Render error page HTML
     */
    private function renderErrorPage($errorData) {
        http_response_code(500);
        
        $errorId = $errorData['error_id'];
        $isProduction = SystemSetting::getValue('environment', 'development') === 'production';
        
        ?>
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>System Error - <?= SystemSetting::getValue('site_name', 'Coinage Trading') ?></title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        </head>
        <body class="bg-light">
            <div class="container">
                <div class="row justify-content-center align-items-center min-vh-100">
                    <div class="col-md-6">
                        <div class="card shadow">
                            <div class="card-body text-center p-5">
                                <i class="fas fa-exclamation-triangle text-danger fa-4x mb-4"></i>
                                <h2 class="text-danger mb-3">System Error</h2>
                                <p class="text-muted mb-4">
                                    We're sorry, but something went wrong. Our team has been notified and is working to fix the issue.
                                </p>
                                
                                <div class="alert alert-light">
                                    <small class="text-muted">
                                        <strong>Error ID:</strong> <?= htmlspecialchars($errorId) ?><br>
                                        <strong>Time:</strong> <?= date('Y-m-d H:i:s') ?>
                                    </small>
                                </div>
                                
                                <?php if (!$isProduction && isset($errorData['message'])): ?>
                                    <div class="alert alert-warning">
                                        <small><strong>Debug Info:</strong> <?= htmlspecialchars($errorData['message']) ?></small>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button class="btn btn-primary" onclick="history.back()">
                                        <i class="fas fa-arrow-left me-2"></i>Go Back
                                    </button>
                                    <a href="/" class="btn btn-outline-primary">
                                        <i class="fas fa-home me-2"></i>Home Page
                                    </a>
                                </div>
                                
                                <div class="mt-4">
                                    <small class="text-muted">
                                        If this problem persists, please contact our support team with the error ID above.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        <?php
        exit();
    }
    
    /**
     * Get error context information
     */
    private function getErrorContext() {
        return [
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? null,
            'session_id' => session_id(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }
    
    /**
     * Generate unique error ID
     */
    private function generateErrorId($errorData) {
        return 'ERR_' . date('Ymd') . '_' . substr(md5(json_encode($errorData) . microtime()), 0, 8);
    }
    
    /**
     * Map PHP error severity to our severity levels
     */
    private function mapPhpSeverity($severity) {
        switch ($severity) {
            case E_ERROR:
            case E_PARSE:
            case E_CORE_ERROR:
            case E_COMPILE_ERROR:
                return self::SEVERITY_CRITICAL;
            case E_WARNING:
            case E_CORE_WARNING:
            case E_COMPILE_WARNING:
            case E_USER_ERROR:
                return self::SEVERITY_HIGH;
            case E_NOTICE:
            case E_USER_WARNING:
                return self::SEVERITY_MEDIUM;
            default:
                return self::SEVERITY_LOW;
        }
    }
    
    /**
     * Categorize error based on message content
     */
    private function categorizeError($message) {
        $message = strtolower($message);
        
        if (strpos($message, 'database') !== false || strpos($message, 'sql') !== false) {
            return self::CATEGORY_DATABASE;
        }
        if (strpos($message, 'auth') !== false || strpos($message, 'login') !== false) {
            return self::CATEGORY_AUTHENTICATION;
        }
        if (strpos($message, 'upload') !== false || strpos($message, 'file') !== false) {
            return self::CATEGORY_FILE_UPLOAD;
        }
        if (strpos($message, 'validation') !== false || strpos($message, 'invalid') !== false) {
            return self::CATEGORY_VALIDATION;
        }
        if (strpos($message, 'security') !== false || strpos($message, 'csrf') !== false) {
            return self::CATEGORY_SECURITY;
        }
        
        return self::CATEGORY_SYSTEM;
    }
    
    /**
     * Sanitize parameters for logging
     */
    private function sanitizeParams($params) {
        if (!is_array($params)) {
            return $params;
        }
        
        $sanitized = [];
        foreach ($params as $key => $value) {
            if (in_array(strtolower($key), ['password', 'token', 'secret', 'key'])) {
                $sanitized[$key] = '[REDACTED]';
            } else {
                $sanitized[$key] = is_string($value) ? substr($value, 0, 100) : $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize financial data for logging
     */
    private function sanitizeFinancialData($data) {
        if (!is_array($data)) {
            return $data;
        }
        
        $sensitiveFields = ['account_number', 'routing_number', 'card_number', 'cvv', 'pin'];
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (in_array(strtolower($key), $sensitiveFields)) {
                $sanitized[$key] = '[REDACTED]';
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize form data for logging
     */
    private function sanitizeFormData($data) {
        return $this->sanitizeParams($data);
    }
    
    /**
     * Track failed authentication attempts
     */
    private function trackFailedAttempt($identifier, $ip) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                INSERT INTO failed_login_attempts (identifier, ip_address, attempt_time) 
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$identifier, $ip]);
            
            // Clean old attempts (older than 24 hours)
            $stmt = $db->prepare("
                DELETE FROM failed_login_attempts 
                WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            $stmt->execute();
        } catch (Exception $e) {
            // Ignore tracking errors
        }
    }
    
    /**
     * Get error statistics
     */
    public function getErrorStatistics($days = 7) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT 
                    category,
                    severity,
                    COUNT(*) as count,
                    DATE(created_at) as date
                FROM error_logs 
                WHERE created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY category, severity, DATE(created_at)
                ORDER BY created_at DESC
            ");
            $stmt->execute([$days]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Get recent errors
     */
    public function getRecentErrors($limit = 50) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT error_id, type, severity, category, message, created_at
                FROM error_logs 
                ORDER BY created_at DESC 
                LIMIT ?
            ");
            $stmt->execute([$limit]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Clear old error logs
     */
    public function clearOldLogs($days = 30) {
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                DELETE FROM error_logs 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
            ");
            $stmt->execute([$days]);
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            return 0;
        }
    }
}
?>