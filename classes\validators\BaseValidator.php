<?php
/**
 * BaseValidator - Abstract base class for all validators
 * Provides common validation functionality and error handling
 */
abstract class BaseValidator {
    protected $errors = [];
    protected $data = [];
    
    /**
     * Common validation patterns
     */
    const PATTERNS = [
        'email' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/',
        'phone' => '/^[\+]?[0-9\s\-\(\)]+$/',
        'username' => '/^[a-zA-Z0-9_]{3,50}$/',
        'password_strong' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/',
        'numeric' => '/^\d+(\.\d{1,2})?$/',
        'alpha' => '/^[a-zA-Z\s]+$/',
        'alphanumeric' => '/^[a-zA-Z0-9\s]+$/'
    ];
    
    /**
     * Add validation error
     */
    protected function addError($field, $message) {
        $this->errors[$field] = $message;
    }
    
    /**
     * Check if validation passed
     */
    public function isValid() {
        return empty($this->errors);
    }
    
    /**
     * Get all validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Clear all errors
     */
    public function clearErrors() {
        $this->errors = [];
    }
    
    /**
     * Validate required field
     */
    protected function validateRequired($field, $value, $fieldName = null) {
        $fieldName = $fieldName ?: ucfirst(str_replace('_', ' ', $field));
        
        if (empty($value) && $value !== '0' && $value !== 0) {
            $this->addError($field, "$fieldName is required");
            return false;
        }
        return true;
    }
    
    /**
     * Validate field length
     */
    protected function validateLength($field, $value, $min = null, $max = null, $fieldName = null) {
        $fieldName = $fieldName ?: ucfirst(str_replace('_', ' ', $field));
        $length = strlen($value);
        
        if ($min !== null && $length < $min) {
            $this->addError($field, "$fieldName must be at least $min characters");
            return false;
        }
        
        if ($max !== null && $length > $max) {
            $this->addError($field, "$fieldName must not exceed $max characters");
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate using regex pattern
     */
    protected function validatePattern($field, $value, $pattern, $message, $fieldName = null) {
        $fieldName = $fieldName ?: ucfirst(str_replace('_', ' ', $field));
        
        if (!preg_match($pattern, $value)) {
            $this->addError($field, $message ?: "$fieldName format is invalid");
            return false;
        }
        return true;
    }
    
    /**
     * Validate email format
     */
    protected function validateEmail($field, $value, $fieldName = null) {
        return $this->validatePattern(
            $field, 
            $value, 
            self::PATTERNS['email'], 
            'Please enter a valid email address',
            $fieldName
        );
    }
    
    /**
     * Validate numeric value
     */
    protected function validateNumeric($field, $value, $min = null, $max = null, $fieldName = null) {
        $fieldName = $fieldName ?: ucfirst(str_replace('_', ' ', $field));
        
        if (!is_numeric($value)) {
            $this->addError($field, "$fieldName must be a valid number");
            return false;
        }
        
        $numValue = (float) $value;
        
        if ($min !== null && $numValue < $min) {
            $this->addError($field, "$fieldName must be at least $min");
            return false;
        }
        
        if ($max !== null && $numValue > $max) {
            $this->addError($field, "$fieldName cannot exceed $max");
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate enum values
     */
    protected function validateEnum($field, $value, $allowedValues, $fieldName = null) {
        $fieldName = $fieldName ?: ucfirst(str_replace('_', ' ', $field));
        
        if (!in_array($value, $allowedValues, true)) {
            $this->addError($field, "$fieldName must be one of: " . implode(', ', $allowedValues));
            return false;
        }
        return true;
    }
    
    /**
     * Sanitize input data
     */
    protected function sanitizeInput($value) {
        if (is_array($value)) {
            return array_map([$this, 'sanitizeInput'], $value);
        }
        
        return htmlspecialchars(trim($value), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Abstract method for validation rules
     */
    abstract public function validate($data);
}
?>