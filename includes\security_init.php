<?php
/**
 * Security Initialization
 * Include this file at the beginning of your application to enable security features
 */

require_once __DIR__ . '/../classes/services/SecurityMiddleware.php';
require_once __DIR__ . '/../classes/services/InputSanitizer.php';
require_once __DIR__ . '/../classes/validators/SecurityValidator.php';

// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    // Configure secure session settings
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.use_strict_mode', 1);
    ini_set('session.gc_maxlifetime', 3600); // 1 hour
    
    session_start();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Initialize security middleware
$securityMiddleware = SecurityMiddleware::getInstance();

// Set security headers
$securityMiddleware->setSecurityHeaders();

// Process request security
try {
    $securityMiddleware->processRequest();
} catch (Exception $e) {
    error_log("Security middleware error: " . $e->getMessage());
    http_response_code(500);
    exit('Security error occurred');
}

// Define security helper functions
if (!function_exists('sanitizeInput')) {
    /**
     * Enhanced sanitize input function
     */
    function sanitizeInput($data, $context = 'general') {
        return InputSanitizer::sanitizeForm($data, $context);
    }
}

if (!function_exists('validateCSRF')) {
    /**
     * Validate CSRF token
     */
    function validateCSRF($token) {
        return SecurityMiddleware::getInstance()->validateCSRF($token);
    }
}

if (!function_exists('preventXSS')) {
    /**
     * Prevent XSS attacks
     */
    function preventXSS($data) {
        return ValidationHelper::preventXSS($data);
    }
}

if (!function_exists('sanitizeForOutput')) {
    /**
     * Sanitize data for safe output
     */
    function sanitizeForOutput($data) {
        if (is_array($data)) {
            return array_map('sanitizeForOutput', $data);
        }
        return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}

if (!function_exists('isSecureConnection')) {
    /**
     * Check if connection is secure (HTTPS)
     */
    function isSecureConnection() {
        return isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
    }
}

if (!function_exists('logSecurityEvent')) {
    /**
     * Log security events
     */
    function logSecurityEvent($event, $details = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'details' => $details,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'user_id' => $_SESSION['user_id'] ?? null
        ];
        
        $logEntry = json_encode($logData) . "\n";
        
        $logFile = __DIR__ . '/../logs/security.log';
        if (!file_exists(dirname($logFile))) {
            mkdir(dirname($logFile), 0755, true);
        }
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// Set error reporting for security
if (!defined('PRODUCTION')) {
    // Development mode - show errors but log security events
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    // Production mode - hide errors and log everything
    error_reporting(E_ALL);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

// Set custom error handler for security events
set_error_handler(function($severity, $message, $file, $line) {
    // Log potential security-related errors
    if (strpos($message, 'SQL') !== false || 
        strpos($message, 'script') !== false || 
        strpos($message, 'eval') !== false) {
        logSecurityEvent('potential_security_error', [
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'severity' => $severity
        ]);
    }
    
    // Continue with default error handling
    return false;
});

// Define security constants
if (!defined('SECURITY_INITIALIZED')) {
    define('SECURITY_INITIALIZED', true);
    define('MAX_LOGIN_ATTEMPTS', 5);
    define('LOGIN_LOCKOUT_TIME', 300); // 5 minutes
    define('SESSION_TIMEOUT', 3600); // 1 hour
    define('CSRF_TOKEN_LIFETIME', 3600); // 1 hour
    define('PASSWORD_MIN_LENGTH', 8);
    define('REQUIRE_STRONG_PASSWORDS', true);
}
?>