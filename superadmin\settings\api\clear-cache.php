<?php
require_once __DIR__ . '/../../../includes/functions.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

header('Content-Type: application/json');

try {
    $currentUser = getCurrentUser();
    $clearedItems = [];
    
    // Clear PHP OPcache if available
    if (function_exists('opcache_reset')) {
        opcache_reset();
        $clearedItems[] = 'PHP OPcache';
    }
    
    // Clear session files (optional - be careful with this)
    $sessionPath = session_save_path();
    if ($sessionPath && is_dir($sessionPath)) {
        $sessionFiles = glob($sessionPath . '/sess_*');
        $sessionCount = 0;
        foreach ($sessionFiles as $file) {
            // Only clear old session files (older than 1 hour)
            if (filemtime($file) < time() - 3600) {
                unlink($file);
                $sessionCount++;
            }
        }
        if ($sessionCount > 0) {
            $clearedItems[] = "$sessionCount old session files";
        }
    }
    
    // Clear temporary files
    $tempDir = sys_get_temp_dir();
    $tempFiles = glob($tempDir . '/coinage_*');
    $tempCount = 0;
    foreach ($tempFiles as $file) {
        if (is_file($file) && filemtime($file) < time() - 3600) {
            unlink($file);
            $tempCount++;
        }
    }
    if ($tempCount > 0) {
        $clearedItems[] = "$tempCount temporary files";
    }
    
    // Clear log files older than 30 days
    $logDir = __DIR__ . '/../../../logs/';
    if (is_dir($logDir)) {
        $logFiles = glob($logDir . '*.log');
        $logCount = 0;
        foreach ($logFiles as $file) {
            if (filemtime($file) < time() - (30 * 24 * 3600)) {
                unlink($file);
                $logCount++;
            }
        }
        if ($logCount > 0) {
            $clearedItems[] = "$logCount old log files";
        }
    }
    
    // Clear upload temporary files
    $uploadTempDir = __DIR__ . '/../../../assets/uploads/temp/';
    if (is_dir($uploadTempDir)) {
        $uploadFiles = glob($uploadTempDir . '*');
        $uploadCount = 0;
        foreach ($uploadFiles as $file) {
            if (is_file($file) && filemtime($file) < time() - 3600) {
                unlink($file);
                $uploadCount++;
            }
        }
        if ($uploadCount > 0) {
            $clearedItems[] = "$uploadCount temporary upload files";
        }
    }
    
    // Log the action
    logAuditTrail($currentUser['id'], 'system_maintenance', 'clear_cache', 'System cache cleared: ' . implode(', ', $clearedItems));
    
    $message = empty($clearedItems) ? 
        'Cache cleared successfully (no items needed clearing)' : 
        'Cache cleared successfully: ' . implode(', ', $clearedItems);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'cleared_items' => $clearedItems
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to clear cache: ' . $e->getMessage()
    ]);
}
?>