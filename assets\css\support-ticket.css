/* Support Ticket Detail Styles */

.ticket-detail-container {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Ticket Header */
.ticket-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    padding: 1.5rem;
}

.ticket-header h4 {
    margin: 0;
    font-weight: 600;
}

.ticket-header .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* Message Container */
.message-container {
    margin-bottom: 2rem;
    position: relative;
}

.message-container:last-child {
    margin-bottom: 0;
}

/* Avatar */
.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Message Content */
.message-content {
    margin-left: 55px;
}

.message-content .bg-light {
    border: 1px solid #e9ecef;
    position: relative;
}

.message-content .bg-light::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #f8f9fa;
}

/* Admin Reply Styling */
.admin-reply .message-content {
    margin-left: 55px;
}

.admin-reply .bg-success {
    position: relative;
}

.admin-reply .bg-success::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid rgba(25, 135, 84, 0.1);
}

/* Ticket Information Card */
.ticket-info-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.ticket-info-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0;
    padding: 1rem 1.5rem;
}

.ticket-info-card .card-body {
    padding: 1.5rem;
}

.ticket-info-card .row {
    margin-bottom: 0.75rem;
}

.ticket-info-card .row:last-child {
    margin-bottom: 0;
}

/* Status Alerts */
.status-alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    margin-top: 1.5rem;
}

.status-alert i {
    margin-right: 0.5rem;
}

/* Actions Card */
.actions-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.actions-card .btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.actions-card .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Help Card */
.help-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.help-card .alert {
    margin-bottom: 0;
    border: none;
    background-color: #e3f2fd;
    color: #0277bd;
}

.help-card ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.help-card li {
    margin-bottom: 0.25rem;
}

/* Priority and Status Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
}

.badge.bg-urgent {
    background-color: #dc3545 !important;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Timeline Effect */
.message-container::before {
    content: "";
    position: absolute;
    left: 20px;
    top: 50px;
    bottom: -20px;
    width: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.message-container:last-child::before {
    display: none;
}

.message-container .avatar {
    position: relative;
    z-index: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .ticket-detail-container {
        padding: 10px 0;
    }
    
    .ticket-header {
        padding: 1rem;
    }
    
    .ticket-header h4 {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }
    
    .message-content {
        margin-left: 0;
        margin-top: 1rem;
    }
    
    .message-content .bg-light::before,
    .admin-reply .bg-success::before {
        display: none;
    }
    
    .message-container::before {
        display: none;
    }
    
    .ticket-info-card .row {
        margin-bottom: 1rem;
    }
    
    .ticket-info-card .col-sm-4,
    .ticket-info-card .col-sm-8 {
        margin-bottom: 0.25rem;
    }
    
    .actions-card .btn {
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .ticket-detail-container {
        background-color: white;
        padding: 0;
    }
    
    .breadcrumb,
    .actions-card,
    .help-card,
    .btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 1rem !important;
    }
    
    .ticket-header {
        background: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
    }
    
    .message-container::before {
        display: none;
    }
    
    .message-content {
        margin-left: 0;
    }
    
    .message-content .bg-light::before,
    .admin-reply .bg-success::before {
        display: none;
    }
    
    .badge {
        border: 1px solid #333 !important;
        color: #333 !important;
        background: white !important;
    }
}

/* Auto-refresh Indicator */
.auto-refresh-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
}

.auto-refresh-indicator.show {
    opacity: 1;
}

.auto-refresh-indicator i {
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Message Timestamps */
.message-timestamp {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

/* Ticket Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.pending {
    background-color: #ffc107;
    animation: pulse 2s infinite;
}

.status-indicator.answered {
    background-color: #17a2b8;
}

.status-indicator.closed {
    background-color: #28a745;
}

/* Loading States */
.loading-message {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Smooth Transitions */
.card,
.message-container,
.badge,
.btn {
    transition: all 0.2s ease;
}

/* Focus States */
.btn:focus,
.card:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}