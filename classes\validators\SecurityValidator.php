<?php
require_once __DIR__ . '/ValidationHelper.php';

/**
 * Security Validator
 * Advanced security validation for sensitive operations
 */
class SecurityValidator {
    private $errors = [];
    private static $rateLimits = [];
    
    /**
     * Validate password change request
     */
    public function validatePasswordChange($data) {
        $this->errors = [];
        
        $this->validateCurrentPassword($data['current_password'] ?? '');
        $this->validateNewPassword($data['new_password'] ?? '');
        $this->validatePasswordConfirmation($data['new_password'] ?? '', $data['password_confirmation'] ?? '');
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate 2FA setup
     */
    public function validate2FASetup($data) {
        $this->errors = [];
        
        $this->validate2FACode($data['code'] ?? '');
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate KYC document upload
     */
    public function validateKYCUpload($files, $data) {
        $this->errors = [];
        
        $this->validateKYCDocuments($files);
        $this->validateKYCData($data);
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate rate limiting for sensitive operations
     */
    public function validateRateLimit($operation, $identifier, $maxAttempts = 5, $timeWindow = 300) {
        $key = $operation . '_' . $identifier;
        $now = time();
        
        if (!isset(self::$rateLimits[$key])) {
            self::$rateLimits[$key] = [];
        }
        
        // Clean old attempts
        self::$rateLimits[$key] = array_filter(self::$rateLimits[$key], function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });
        
        if (count(self::$rateLimits[$key]) >= $maxAttempts) {
            $this->errors['rate_limit'] = "Too many attempts. Please try again in " . ceil($timeWindow / 60) . " minutes.";
            return false;
        }
        
        // Record this attempt
        self::$rateLimits[$key][] = $now;
        return true;
    }
    
    /**
     * Validate IP address for suspicious activity
     */
    public function validateIPAddress($ip, $userId = null) {
        // Check if IP is in blacklist (you would implement this with a database table)
        if ($this->isBlacklistedIP($ip)) {
            $this->errors['ip_blocked'] = 'Access denied from this IP address.';
            return false;
        }
        
        // Check for unusual geographic location (if user has previous login history)
        if ($userId && $this->isUnusualLocation($ip, $userId)) {
            $this->errors['location_warning'] = 'Login from unusual location detected. Additional verification may be required.';
        }
        
        return empty($this->errors);
    }
    
    /**
     * Validate session security
     */
    public function validateSession() {
        // Check session timeout
        if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity']) > 3600) {
            $this->errors['session_expired'] = 'Session has expired. Please log in again.';
            return false;
        }
        
        // Check session fingerprint
        $currentFingerprint = $this->generateSessionFingerprint();
        if (isset($_SESSION['fingerprint']) && $_SESSION['fingerprint'] !== $currentFingerprint) {
            $this->errors['session_hijack'] = 'Session security violation detected.';
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        if (!isset($_SESSION['fingerprint'])) {
            $_SESSION['fingerprint'] = $currentFingerprint;
        }
        
        return true;
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : '';
    }
    
    /**
     * Validate current password
     */
    private function validateCurrentPassword($password) {
        if (empty($password)) {
            $this->errors['current_password'] = 'Current password is required.';
        }
    }
    
    /**
     * Validate new password with enhanced security
     */
    private function validateNewPassword($password) {
        $error = ValidationHelper::validatePassword($password);
        if ($error) {
            $this->errors['new_password'] = $error;
            return;
        }
        
        // Enhanced password validation
        if (strlen($password) < 8) {
            $this->errors['new_password'] = 'Password must be at least 8 characters long.';
            return;
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $this->errors['new_password'] = 'Password must contain at least one uppercase letter.';
            return;
        }
        
        if (!preg_match('/[a-z]/', $password)) {
            $this->errors['new_password'] = 'Password must contain at least one lowercase letter.';
            return;
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $this->errors['new_password'] = 'Password must contain at least one number.';
            return;
        }
        
        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $this->errors['new_password'] = 'Password must contain at least one special character.';
            return;
        }
        
        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $this->errors['new_password'] = 'Password is too common. Please choose a more secure password.';
        }
    }
    
    /**
     * Validate password confirmation
     */
    private function validatePasswordConfirmation($password, $confirmation) {
        if (empty($confirmation)) {
            $this->errors['password_confirmation'] = 'Password confirmation is required.';
            return;
        }
        
        if ($password !== $confirmation) {
            $this->errors['password_confirmation'] = 'Passwords do not match.';
        }
    }
    
    /**
     * Validate 2FA code
     */
    private function validate2FACode($code) {
        if (empty($code)) {
            $this->errors['code'] = '2FA code is required.';
            return;
        }
        
        if (!preg_match('/^[0-9]{6}$/', $code)) {
            $this->errors['code'] = '2FA code must be 6 digits.';
        }
    }
    
    /**
     * Validate KYC documents
     */
    private function validateKYCDocuments($files) {
        $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf'];
        $maxSize = 5242880; // 5MB
        
        if (empty($files['id_front'])) {
            $this->errors['id_front'] = 'Front of ID document is required.';
        } else {
            $error = ValidationHelper::validateFileUpload($files['id_front'], $allowedTypes, $maxSize);
            if ($error) {
                $this->errors['id_front'] = $error;
            }
        }
        
        if (empty($files['id_back'])) {
            $this->errors['id_back'] = 'Back of ID document is required.';
        } else {
            $error = ValidationHelper::validateFileUpload($files['id_back'], $allowedTypes, $maxSize);
            if ($error) {
                $this->errors['id_back'] = $error;
            }
        }
        
        if (!empty($files['proof_of_address'])) {
            $error = ValidationHelper::validateFileUpload($files['proof_of_address'], $allowedTypes, $maxSize);
            if ($error) {
                $this->errors['proof_of_address'] = $error;
            }
        }
    }
    
    /**
     * Validate KYC personal data
     */
    private function validateKYCData($data) {
        $error = ValidationHelper::validateName($data['full_name'] ?? '', 'Full name');
        if ($error) {
            $this->errors['full_name'] = $error;
        }
        
        $error = ValidationHelper::validateDate($data['date_of_birth'] ?? '');
        if ($error) {
            $this->errors['date_of_birth'] = $error;
        } else {
            // Check if user is at least 18 years old
            $dob = new DateTime($data['date_of_birth']);
            $now = new DateTime();
            $age = $now->diff($dob)->y;
            if ($age < 18) {
                $this->errors['date_of_birth'] = 'You must be at least 18 years old.';
            }
        }
        
        if (empty($data['address'])) {
            $this->errors['address'] = 'Address is required.';
        }
        
        if (empty($data['city'])) {
            $this->errors['city'] = 'City is required.';
        }
        
        if (empty($data['country'])) {
            $this->errors['country'] = 'Country is required.';
        }
        
        if (empty($data['postal_code'])) {
            $this->errors['postal_code'] = 'Postal code is required.';
        }
    }
    
    /**
     * Validate CSRF token
     */
    private function validateCSRFToken($token) {
        $error = ValidationHelper::validateCSRFToken($token);
        if ($error) {
            $this->errors['csrf_token'] = $error;
        }
    }
    
    /**
     * Check if password is in common passwords list
     */
    private function isCommonPassword($password) {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123', 
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'shadow', 'football', 'baseball'
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }
    
    /**
     * Check if IP is blacklisted
     */
    private function isBlacklistedIP($ip) {
        // This would typically check against a database table
        // For now, return false (no blacklist check)
        return false;
    }
    
    /**
     * Check if login location is unusual for user
     */
    private function isUnusualLocation($ip, $userId) {
        // This would typically check user's login history
        // For now, return false (no location check)
        return false;
    }
    
    /**
     * Generate session fingerprint
     */
    private function generateSessionFingerprint() {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
        $acceptEncoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '';
        
        return hash('sha256', $userAgent . $acceptLanguage . $acceptEncoding);
    }
    
    /**
     * Validate login attempt with enhanced security
     */
    public function validateLoginAttempt($email, $password, $ip, $userAgent) {
        $this->errors = [];
        
        // Basic validation
        if (empty($email)) {
            $this->errors['email'] = 'Email is required.';
        }
        
        if (empty($password)) {
            $this->errors['password'] = 'Password is required.';
        }
        
        // Rate limiting
        if (!$this->validateRateLimit('login_' . $email, $ip, 5, 300)) {
            return false;
        }
        
        // Check for suspicious patterns
        if ($this->detectSuspiciousActivity($email, $ip, $userAgent)) {
            $this->errors['security'] = 'Suspicious activity detected. Please try again later.';
            return false;
        }
        
        return empty($this->errors);
    }
    
    /**
     * Detect suspicious login activity
     */
    private function detectSuspiciousActivity($email, $ip, $userAgent) {
        // Check for rapid login attempts from different IPs
        if ($this->hasRapidIPChanges($email)) {
            $this->logSecurityEvent('rapid_ip_changes', ['email' => $email, 'ip' => $ip]);
            return true;
        }
        
        // Check for suspicious user agent patterns
        if ($this->isSuspiciousUserAgent($userAgent)) {
            $this->logSecurityEvent('suspicious_user_agent', ['email' => $email, 'user_agent' => $userAgent]);
            return true;
        }
        
        // Check for known malicious IPs (would integrate with threat intelligence)
        if ($this->isMaliciousIP($ip)) {
            $this->logSecurityEvent('malicious_ip', ['email' => $email, 'ip' => $ip]);
            return true;
        }
        
        return false;
    }
    
    /**
     * Check for rapid IP changes
     */
    private function hasRapidIPChanges($email) {
        // This would check login history in database
        // For now, return false (implement with actual database queries)
        return false;
    }
    
    /**
     * Check for suspicious user agent
     */
    private function isSuspiciousUserAgent($userAgent) {
        $suspiciousPatterns = [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
            '/curl/i',
            '/wget/i',
            '/python/i',
            '/script/i'
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP is malicious
     */
    private function isMaliciousIP($ip) {
        // This would integrate with threat intelligence feeds
        // For now, return false (implement with actual threat data)
        return false;
    }
    
    /**
     * Validate password strength with comprehensive rules
     */
    public function validatePasswordStrength($password, $userInfo = []) {
        $this->errors = [];
        
        // Length check
        if (strlen($password) < 8) {
            $this->errors['password'] = 'Password must be at least 8 characters long.';
            return false;
        }
        
        if (strlen($password) > 128) {
            $this->errors['password'] = 'Password must not exceed 128 characters.';
            return false;
        }
        
        // Character requirements
        $hasUpper = preg_match('/[A-Z]/', $password);
        $hasLower = preg_match('/[a-z]/', $password);
        $hasNumber = preg_match('/[0-9]/', $password);
        $hasSpecial = preg_match('/[^a-zA-Z0-9]/', $password);
        
        $requirements = [];
        if (!$hasUpper) $requirements[] = 'uppercase letter';
        if (!$hasLower) $requirements[] = 'lowercase letter';
        if (!$hasNumber) $requirements[] = 'number';
        if (!$hasSpecial) $requirements[] = 'special character';
        
        if (!empty($requirements)) {
            $this->errors['password'] = 'Password must contain at least one: ' . implode(', ', $requirements);
            return false;
        }
        
        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $this->errors['password'] = 'Password is too common. Please choose a more secure password.';
            return false;
        }
        
        // Check for personal information in password
        if (!empty($userInfo) && $this->containsPersonalInfo($password, $userInfo)) {
            $this->errors['password'] = 'Password should not contain personal information.';
            return false;
        }
        
        // Check for repeated characters
        if ($this->hasRepeatedCharacters($password)) {
            $this->errors['password'] = 'Password should not contain repeated character sequences.';
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if password contains personal information
     */
    private function containsPersonalInfo($password, $userInfo) {
        $password = strtolower($password);
        
        $personalData = [
            $userInfo['first_name'] ?? '',
            $userInfo['last_name'] ?? '',
            $userInfo['username'] ?? '',
            $userInfo['email'] ?? '',
            explode('@', $userInfo['email'] ?? '')[0] ?? ''
        ];
        
        foreach ($personalData as $data) {
            if (!empty($data) && strlen($data) > 2 && strpos($password, strtolower($data)) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for repeated characters
     */
    private function hasRepeatedCharacters($password) {
        // Check for 3 or more repeated characters
        return preg_match('/(.)\1{2,}/', $password);
    }
    
    /**
     * Validate file upload security
     */
    public function validateFileUploadSecurity($file, $allowedTypes = [], $maxSize = 5242880) {
        $this->errors = [];
        
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            $this->errors['file'] = 'Invalid file upload.';
            return false;
        }
        
        // Check file size
        if ($file['size'] > $maxSize) {
            $this->errors['file'] = 'File size exceeds maximum allowed size.';
            return false;
        }
        
        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        if (!empty($allowedTypes) && !in_array($mimeType, $allowedTypes)) {
            $this->errors['file'] = 'File type not allowed.';
            return false;
        }
        
        // Check for malicious content
        if ($this->containsMaliciousContent($file['tmp_name'])) {
            $this->errors['file'] = 'File contains potentially malicious content.';
            return false;
        }
        
        // Validate filename
        if ($this->isMaliciousFilename($file['name'])) {
            $this->errors['file'] = 'Invalid filename.';
            return false;
        }
        
        return true;
    }
    
    /**
     * Check for malicious content in uploaded files
     */
    private function containsMaliciousContent($filePath) {
        $content = file_get_contents($filePath, false, null, 0, 1024); // Read first 1KB
        
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/eval\(/i',
            '/exec\(/i',
            '/system\(/i',
            '/shell_exec/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for malicious filename patterns
     */
    private function isMaliciousFilename($filename) {
        $maliciousPatterns = [
            '/\.\./i',  // Directory traversal
            '/\.(php|phtml|php3|php4|php5|phar)$/i',  // PHP files
            '/\.(exe|bat|cmd|scr|com|pif)$/i',  // Executable files
            '/\.(js|vbs|jar)$/i',  // Script files
            '/^\.ht/i',  // Apache config files
            '/web\.config$/i',  // IIS config files
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $filename)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Log security events with enhanced details
     */
    private function logSecurityEvent($event, $details = []) {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'event' => $event,
                'details' => $details,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
                'http_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
                'user_id' => $_SESSION['user_id'] ?? null,
                'session_id' => session_id()
            ];
            
            $logEntry = json_encode($logData) . "\n";
            
            $logFile = __DIR__ . '/../../logs/security_events.log';
            if (!file_exists(dirname($logFile))) {
                mkdir(dirname($logFile), 0755, true);
            }
            
            file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            error_log("Error logging security event: " . $e->getMessage());
        }
    }
    
    /**
     * Sanitize security-sensitive data
     */
    public static function sanitizeSecurityData($data) {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            switch ($key) {
                case 'current_password':
                case 'new_password':
                case 'password_confirmation':
                case 'csrf_token':
                    $sanitized[$key] = $value; // Don't sanitize passwords and tokens
                    break;
                case 'code':
                    $sanitized[$key] = preg_replace('/[^0-9]/', '', $value);
                    break;
                case 'date_of_birth':
                    $sanitized[$key] = preg_replace('/[^0-9\-]/', '', $value);
                    break;
                default:
                    $sanitized[$key] = ValidationHelper::sanitize($value);
                    break;
            }
        }
        
        return $sanitized;
    }
}
?>