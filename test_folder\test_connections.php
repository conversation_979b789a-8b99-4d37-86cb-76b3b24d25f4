<?php
session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/email_functions.php';

echo "<h1>Crypto Trading Platform - System Tests</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>";

// Test Database Connection
echo "<h2>🗄️ Database Connection Test</h2>";
$db = Database::getInstance();
if ($db->testConnection()) {
    echo "<p style='color: green; font-weight: bold;'>✅ Database connection successful!</p>";
    echo "<p><strong>Host:</strong> " . DB_HOST . ":" . DB_PORT . "</p>";
    echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
    
    try {
        $db_conn = getDB();
        $stmt = $db_conn->query("SELECT VERSION() as version");
        $version = $stmt->fetch();
        echo "<p><strong>MySQL Version:</strong> " . $version['version'] . "</p>";
        
        // Test if core tables exist
        $coreTable = ['users', 'trading_plans', 'deposits', 'withdrawals', 'transactions', 'system_settings', 'payment_methods'];
        $allTablesExist = true;
        
        echo "<h3>📋 Table Status Check</h3>";
        foreach ($coreTable as $table) {
            try {
                $stmt = $db_conn->query("SELECT COUNT(*) FROM `$table`");
                $count = $stmt->fetchColumn();
                echo "<p style='color: green;'>✅ Table '<strong>$table</strong>' exists with <strong>$count</strong> records</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Table '<strong>$table</strong>' not found</p>";
                $allTablesExist = false;
            }
        }
        
        if (!$allTablesExist) {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<p style='color: #856404;'><strong>⚠️ Some tables are missing. Please run the database migration.</strong></p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database query error: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Database connection failed!</p>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<p style='color: #721c24;'><strong>Troubleshooting:</strong></p>";
    echo "<ul style='color: #721c24;'>";
    echo "<li>Check if MySQL server is running</li>";
    echo "<li>Verify database credentials in config.php</li>";
    echo "<li>Ensure database '" . DB_NAME . "' exists</li>";
    echo "<li>Check if user has proper permissions</li>";
    echo "</ul>";
    echo "</div>";
}

// Test SMTP Connection
echo "<h2>📧 SMTP Connection Test</h2>";
try {
    if (class_exists('EmailService')) {
        $emailService = new EmailService();
        if ($emailService->testConnection()) {
            echo "<p style='color: green; font-weight: bold;'>✅ SMTP connection successful!</p>";
            echo "<p><strong>Host:</strong> " . SMTP_HOST . ":" . SMTP_PORT . "</p>";
            echo "<p><strong>Username:</strong> " . SMTP_USERNAME . "</p>";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ SMTP connection failed!</p>";
        }
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠️ EmailService class not found</p>";
        echo "<p>PHPMailer may not be installed. Run: <code>composer require phpmailer/phpmailer</code></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ SMTP Error: " . $e->getMessage() . "</p>";
    echo "<p style='color: orange;'>Note: PHPMailer library may not be installed. Run: <code>composer require phpmailer/phpmailer</code></p>";
}

// Configuration Check
echo "<h2>⚙️ Configuration Summary</h2>";
echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Setting</th>";
echo "<th style='border: 1px solid #dee2e6; padding: 8px; text-align: left;'>Value</th>";
echo "</tr>";

$configItems = [
    'Site Name' => SITE_NAME,
    'Base URL' => BASE_URL,
    'Primary Color' => "<span style='background:" . PRIMARY_COLOR . "; padding:5px; color:white; border-radius:3px;'>" . PRIMARY_COLOR . "</span>",
    'Secondary Color' => "<span style='background:" . SECONDARY_COLOR . "; padding:5px; color:white; border-radius:3px;'>" . SECONDARY_COLOR . "</span>",
    'Default Currency' => DEFAULT_CURRENCY . " (" . CURRENCY_SYMBOL . ")",
    'Deposit Bonus' => DEPOSIT_BONUS_PERCENT . "%",
    '2FA Enabled' => ENABLE_2FA ? 'Yes' : 'No',
    'Email Verification' => ENABLE_EMAIL_VERIFICATION ? 'Yes' : 'No',
    'Registration Bonus' => CURRENCY_SYMBOL . REGISTRATION_BONUS
];

foreach ($configItems as $setting => $value) {
    echo "<tr>";
    echo "<td style='border: 1px solid #dee2e6; padding: 8px;'><strong>$setting</strong></td>";
    echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>$value</td>";
    echo "</tr>";
}
echo "</table>";

// Test Database Schema Creation
echo "<h2>🔧 Database Schema Test</h2>";
try {
    $db_conn = getDB();
    
    // Test if we can create a simple table
    $sql = "CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        test_field VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($db_conn->exec($sql) !== false) {
        echo "<p style='color: green;'>✅ Database schema creation test passed!</p>";
        
        // Test insert
        $insertSql = "INSERT INTO test_table (test_field) VALUES ('test_value')";
        if ($db_conn->exec($insertSql) !== false) {
            echo "<p style='color: green;'>✅ Database insert test passed!</p>";
        }
        
        // Clean up test table
        $db_conn->exec("DROP TABLE IF EXISTS test_table");
        echo "<p style='color: blue;'>🧹 Test table cleaned up.</p>";
    } else {
        echo "<p style='color: red;'>❌ Database schema creation failed!</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database Schema Error: " . $e->getMessage() . "</p>";
}

// Action Buttons
echo "<h2>🚀 Quick Actions</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='../database/migrate.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Run Database Migration</a>";
echo "<a href='create_database_schema.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📋 Create Schema Only</a>";
echo "<a href='simple_db_test.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Simple DB Test</a>";
echo "</div>";

// Next Steps
echo "<h2>📋 Next Steps</h2>";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<ol style='color: #383d41;'>";
echo "<li>Install PHPMailer: <code>composer require phpmailer/phpmailer</code></li>";
echo "<li>Run database migration to create all tables</li>";
echo "<li>Test the complete system setup</li>";
echo "<li>Create the layout system and authentication (Task 2)</li>";
echo "<li>Set up user registration/login system</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>