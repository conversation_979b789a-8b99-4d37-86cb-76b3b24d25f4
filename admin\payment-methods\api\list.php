<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/PaymentMethod.php';

header('Content-Type: application/json');

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

try {
    // Get payment methods with deposit statistics
    $paymentMethods = PaymentMethod::getWithDepositStats();
    
    // Generate HTML for table rows
    $html = '';
    
    foreach ($paymentMethods as $method) {
        $statusBadge = $method['status'] === 'active' 
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-secondary">Inactive</span>';
        
        $typeIcon = getTypeIcon($method['type']);
        
        $html .= '
        <tr data-method-id="' . $method['id'] . '">
            <td>
                <div class="sort-handle" style="cursor: move;">
                    <i class="fas fa-grip-vertical text-muted"></i>
                    <span class="ms-2">' . $method['sort_order'] . '</span>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="' . $typeIcon . ' me-2"></i>
                    <strong>' . htmlspecialchars($method['name']) . '</strong>
                </div>
            </td>
            <td>
                <span class="badge bg-light text-dark">' . ucfirst($method['type']) . '</span>
            </td>
            <td>' . $statusBadge . '</td>
            <td>
                <span class="badge bg-info">' . $method['deposits_count'] . '</span>
            </td>
            <td>
                <strong>$' . number_format($method['total_deposits'], 2) . '</strong>
            </td>
            <td>
                ' . ($method['pending_deposits'] > 0 
                    ? '<span class="badge bg-warning">' . $method['pending_deposits'] . '</span>'
                    : '<span class="badge bg-light text-muted">0</span>') . '
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewPaymentMethod(' . $method['id'] . ')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="editPaymentMethod(' . $method['id'] . ')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-' . ($method['status'] === 'active' ? 'warning' : 'success') . '" 
                            onclick="togglePaymentMethodStatus(' . $method['id'] . ', \'' . $method['status'] . '\')">
                        <i class="fas fa-' . ($method['status'] === 'active' ? 'pause' : 'play') . '"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deletePaymentMethod(' . $method['id'] . ')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>';
    }
    
    echo json_encode([
        'success' => true,
        'html' => $html
    ]);
    
} catch (Exception $e) {
    error_log("Payment methods list error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while loading payment methods'
    ]);
}

function getTypeIcon($type) {
    $icons = [
        'crypto' => 'fab fa-bitcoin text-warning',
        'bank' => 'fas fa-university text-primary',
        'paypal' => 'fab fa-paypal text-info',
        'other' => 'fas fa-credit-card text-secondary'
    ];
    
    return $icons[$type] ?? 'fas fa-credit-card text-secondary';
}
?>