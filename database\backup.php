<?php
/**
 * Database Backup Script
 * Creates a complete backup of the crypto trading platform database
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';

class DatabaseBackup {
    private $db;
    private $backupDir;
    
    public function __construct() {
        $this->db = getDB();
        $this->backupDir = __DIR__ . '/backups';
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * Create a complete database backup
     */
    public function createBackup($includeData = true) {
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "coinage_backup_{$timestamp}.sql";
        $filepath = $this->backupDir . '/' . $filename;
        
        echo "<h2>Creating Database Backup</h2>";
        echo "<p>Backup file: <strong>$filename</strong></p>";
        
        try {
            $backup = $this->generateBackupSQL($includeData);
            
            if (file_put_contents($filepath, $backup)) {
                $filesize = $this->formatBytes(filesize($filepath));
                echo "<p style='color: green;'>✅ Backup created successfully!</p>";
                echo "<p><strong>File size:</strong> $filesize</p>";
                echo "<p><strong>Location:</strong> $filepath</p>";
                
                // Create download link
                $downloadUrl = str_replace($_SERVER['DOCUMENT_ROOT'], '', $filepath);
                echo "<p><a href='$downloadUrl' download='$filename' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📥 Download Backup</a></p>";
                
                return $filepath;
            } else {
                throw new Exception("Could not write backup file");
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Backup failed: " . $e->getMessage() . "</p>";
            return false;
        }
    }
    
    /**
     * Generate SQL backup content
     */
    private function generateBackupSQL($includeData = true) {
        $sql = "-- Crypto Trading Platform Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Database: " . DB_NAME . "\n\n";
        
        $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
        $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
        $sql .= "SET time_zone = \"+00:00\";\n\n";
        
        // Get all tables
        $tables = $this->getTables();
        
        foreach ($tables as $table) {
            $sql .= $this->getTableStructure($table);
            
            if ($includeData) {
                $sql .= $this->getTableData($table);
            }
            
            $sql .= "\n";
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
        
        return $sql;
    }
    
    /**
     * Get all tables in the database
     */
    private function getTables() {
        $stmt = $this->db->query("SHOW TABLES");
        $tables = [];
        
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        return $tables;
    }
    
    /**
     * Get table structure (CREATE TABLE statement)
     */
    private function getTableStructure($table) {
        $sql = "\n-- Table structure for table `$table`\n";
        $sql .= "DROP TABLE IF EXISTS `$table`;\n";
        
        $stmt = $this->db->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch(PDO::FETCH_NUM);
        
        $sql .= $row[1] . ";\n\n";
        
        return $sql;
    }
    
    /**
     * Get table data (INSERT statements)
     */
    private function getTableData($table) {
        $sql = "-- Dumping data for table `$table`\n";
        
        $stmt = $this->db->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($rows)) {
            return $sql . "-- No data found\n\n";
        }
        
        $columns = array_keys($rows[0]);
        $columnList = '`' . implode('`, `', $columns) . '`';
        
        $sql .= "INSERT INTO `$table` ($columnList) VALUES\n";
        
        $values = [];
        foreach ($rows as $row) {
            $rowValues = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $rowValues[] = 'NULL';
                } else {
                    $rowValues[] = $this->db->quote($value);
                }
            }
            $values[] = '(' . implode(', ', $rowValues) . ')';
        }
        
        $sql .= implode(",\n", $values) . ";\n\n";
        
        return $sql;
    }
    
    /**
     * Format file size in human readable format
     */
    private function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
    
    /**
     * List all backup files
     */
    public function listBackups() {
        $backups = [];
        $files = glob($this->backupDir . '/coinage_backup_*.sql');
        
        foreach ($files as $file) {
            $backups[] = [
                'filename' => basename($file),
                'filepath' => $file,
                'size' => $this->formatBytes(filesize($file)),
                'created' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
        
        // Sort by creation time (newest first)
        usort($backups, function($a, $b) {
            return filemtime($b['filepath']) - filemtime($a['filepath']);
        });
        
        return $backups;
    }
    
    /**
     * Delete old backup files
     */
    public function cleanupOldBackups($keepDays = 30) {
        $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
        $files = glob($this->backupDir . '/coinage_backup_*.sql');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deleted++;
                }
            }
        }
        
        return $deleted;
    }
}

// Handle backup creation if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'backup.php') {
    echo "<h1>Database Backup Tool</h1>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>";
    
    $backup = new DatabaseBackup();
    
    // Handle actions
    $action = $_GET['action'] ?? 'list';
    
    switch ($action) {
        case 'create':
            $includeData = ($_GET['data'] ?? 'yes') === 'yes';
            $backup->createBackup($includeData);
            break;
            
        case 'cleanup':
            $keepDays = (int)($_GET['days'] ?? 30);
            $deleted = $backup->cleanupOldBackups($keepDays);
            echo "<p style='color: green;'>✅ Deleted $deleted old backup files</p>";
            break;
    }
    
    // List existing backups
    echo "<h2>📁 Existing Backups</h2>";
    $backups = $backup->listBackups();
    
    if (empty($backups)) {
        echo "<p>No backup files found.</p>";
    } else {
        echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>Filename</th>";
        echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>Size</th>";
        echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>Created</th>";
        echo "<th style='border: 1px solid #dee2e6; padding: 8px;'>Actions</th>";
        echo "</tr>";
        
        foreach ($backups as $backupFile) {
            echo "<tr>";
            echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>" . htmlspecialchars($backupFile['filename']) . "</td>";
            echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>" . $backupFile['size'] . "</td>";
            echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>" . $backupFile['created'] . "</td>";
            echo "<td style='border: 1px solid #dee2e6; padding: 8px;'>";
            
            $downloadUrl = str_replace($_SERVER['DOCUMENT_ROOT'], '', $backupFile['filepath']);
            echo "<a href='$downloadUrl' download='" . $backupFile['filename'] . "' style='color: #007bff; text-decoration: none;'>📥 Download</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Action buttons
    echo "<h2>🚀 Actions</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='?action=create&data=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>💾 Create Full Backup</a>";
    echo "<a href='?action=create&data=no' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🏗️ Structure Only</a>";
    echo "<a href='?action=cleanup&days=30' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧹 Cleanup Old</a>";
    echo "</div>";
    
    echo "</div>";
}
?>