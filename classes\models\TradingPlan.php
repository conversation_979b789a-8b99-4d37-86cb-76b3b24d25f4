<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * TradingPlan Model - Handles trading plan data and operations
 */
class TradingPlan extends BaseModel {
    protected $table = 'trading_plans';
    protected $fillable = [
        'name', 'min_deposit', 'max_deposit', 'daily_return', 'duration_days',
        'status', 'description', 'features', 'sort_order'
    ];
    
    // Trading plan statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // Name validation
        if (empty($this->name)) {
            $errors['name'] = 'Plan name is required';
        } elseif (strlen($this->name) > 100) {
            $errors['name'] = 'Plan name must not exceed 100 characters';
        } elseif ($this->isPlanNameTaken()) {
            $errors['name'] = 'Plan name is already taken';
        }
        
        // Minimum deposit validation
        if (!isset($this->min_deposit)) {
            $errors['min_deposit'] = 'Minimum deposit is required';
        } elseif (!is_numeric($this->min_deposit)) {
            $errors['min_deposit'] = 'Minimum deposit must be numeric';
        } elseif ($this->min_deposit <= 0) {
            $errors['min_deposit'] = 'Minimum deposit must be greater than zero';
        } elseif ($this->min_deposit > 999999999.99) {
            $errors['min_deposit'] = 'Minimum deposit is too large';
        }
        
        // Maximum deposit validation
        if (isset($this->max_deposit)) {
            if (!is_numeric($this->max_deposit)) {
                $errors['max_deposit'] = 'Maximum deposit must be numeric';
            } elseif ($this->max_deposit <= 0) {
                $errors['max_deposit'] = 'Maximum deposit must be greater than zero';
            } elseif ($this->max_deposit > 999999999.99) {
                $errors['max_deposit'] = 'Maximum deposit is too large';
            } elseif (isset($this->min_deposit) && $this->max_deposit < $this->min_deposit) {
                $errors['max_deposit'] = 'Maximum deposit must be greater than minimum deposit';
            }
        }
        
        // Daily return validation
        if (!isset($this->daily_return)) {
            $errors['daily_return'] = 'Daily return is required';
        } elseif (!is_numeric($this->daily_return)) {
            $errors['daily_return'] = 'Daily return must be numeric';
        } elseif ($this->daily_return <= 0) {
            $errors['daily_return'] = 'Daily return must be greater than zero';
        } elseif ($this->daily_return > 100) {
            $errors['daily_return'] = 'Daily return cannot exceed 100%';
        }
        
        // Duration validation
        if (!isset($this->duration_days)) {
            $errors['duration_days'] = 'Duration is required';
        } elseif (!is_numeric($this->duration_days)) {
            $errors['duration_days'] = 'Duration must be numeric';
        } elseif ($this->duration_days <= 0) {
            $errors['duration_days'] = 'Duration must be greater than zero';
        } elseif ($this->duration_days > 3650) { // 10 years max
            $errors['duration_days'] = 'Duration cannot exceed 3650 days';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [self::STATUS_ACTIVE, self::STATUS_INACTIVE])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Sort order validation
        if (isset($this->sort_order) && !is_numeric($this->sort_order)) {
            $errors['sort_order'] = 'Sort order must be numeric';
        }
        
        return $errors;
    }
    
    /**
     * Check if plan name is already taken
     */
    private function isPlanNameTaken() {
        $sql = "SELECT id FROM {$this->table} WHERE name = :name";
        if ($this->exists) {
            $sql .= " AND id != :id";
        }
        
        $stmt = $this->db->prepare($sql);
        $params = ['name' => $this->name];
        if ($this->exists) {
            $params['id'] = $this->getId();
        }
        
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Override save to handle JSON encoding of features
     */
    public function save() {
        if (is_array($this->features)) {
            $this->features = json_encode($this->features);
        }
        
        // Set default sort order if not provided
        if (!isset($this->sort_order)) {
            $this->sort_order = $this->getNextSortOrder();
        }
        
        // Set default status if not provided
        if (!isset($this->status)) {
            $this->status = self::STATUS_ACTIVE;
        }
        
        return parent::save();
    }
    
    /**
     * Get features as array
     */
    public function getFeaturesArray() {
        if (is_string($this->features)) {
            return json_decode($this->features, true) ?? [];
        }
        
        return $this->features ?? [];
    }
    
    /**
     * Get next sort order
     */
    private function getNextSortOrder() {
        $sql = "SELECT MAX(sort_order) as max_order FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        return ($result['max_order'] ?? 0) + 1;
    }
    
    /**
     * Check if plan is active
     */
    public function isActive() {
        return $this->status === self::STATUS_ACTIVE;
    }
    
    /**
     * Check if plan is inactive
     */
    public function isInactive() {
        return $this->status === self::STATUS_INACTIVE;
    }
    
    /**
     * Activate the plan
     */
    public function activate() {
        $this->status = self::STATUS_ACTIVE;
        return $this->save();
    }
    
    /**
     * Deactivate the plan
     */
    public function deactivate() {
        $this->status = self::STATUS_INACTIVE;
        return $this->save();
    }
    
    /**
     * Check if amount is within plan limits
     */
    public function isAmountValid($amount) {
        if ($amount < $this->min_deposit) {
            return false;
        }
        
        if ($this->max_deposit && $amount > $this->max_deposit) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get formatted deposit range
     */
    public function getDepositRange() {
        $min = '$' . number_format($this->min_deposit, 2);
        
        if ($this->max_deposit) {
            $max = '$' . number_format($this->max_deposit, 2);
            return $min . ' - ' . $max;
        }
        
        return $min . '+';
    }
    
    /**
     * Get formatted daily return
     */
    public function getFormattedDailyReturn() {
        return number_format($this->daily_return, 2) . '%';
    }
    
    /**
     * Calculate total return for the duration
     */
    public function getTotalReturn() {
        return $this->daily_return * $this->duration_days;
    }
    
    /**
     * Get formatted total return
     */
    public function getFormattedTotalReturn() {
        return number_format($this->getTotalReturn(), 2) . '%';
    }
    
    /**
     * Calculate profit for given amount
     */
    public function calculateProfit($amount) {
        return ($amount * $this->getTotalReturn()) / 100;
    }
    
    /**
     * Calculate daily profit for given amount
     */
    public function calculateDailyProfit($amount) {
        return ($amount * $this->daily_return) / 100;
    }
    
    /**
     * Get plan deposits
     */
    public function getDeposits($limit = null) {
        require_once __DIR__ . '/Deposit.php';
        
        $sql = "SELECT * FROM deposits WHERE plan_id = :plan_id ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['plan_id' => $this->getId()]);
        $results = $stmt->fetchAll();
        
        $deposits = [];
        foreach ($results as $data) {
            $deposits[] = new Deposit($data);
        }
        
        return $deposits;
    }
    
    /**
     * Get total deposits amount for this plan
     */
    public function getTotalDepositsAmount() {
        $sql = "SELECT COALESCE(SUM(amount), 0) as total FROM deposits 
                WHERE plan_id = :plan_id AND status = 'approved'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['plan_id' => $this->getId()]);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
    
    /**
     * Get deposits count for this plan
     */
    public function getDepositsCount() {
        $sql = "SELECT COUNT(*) as count FROM deposits 
                WHERE plan_id = :plan_id AND status = 'approved'";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['plan_id' => $this->getId()]);
        $result = $stmt->fetch();
        
        return (int) $result['count'];
    }
    
    /**
     * Get active plans
     */
    public static function getActivePlans() {
        return static::where('status', self::STATUS_ACTIVE);
    }
    
    /**
     * Get plans ordered by sort order
     */
    public static function getOrderedPlans($status = null) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table}";
        
        if ($status) {
            $sql .= " WHERE status = :status";
        }
        
        $sql .= " ORDER BY sort_order ASC, created_at ASC";
        
        $stmt = $instance->db->prepare($sql);
        $params = [];
        if ($status) {
            $params['status'] = $status;
        }
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        $plans = [];
        foreach ($results as $data) {
            $plans[] = new static($data);
        }
        
        return $plans;
    }
    
    /**
     * Update sort orders
     */
    public static function updateSortOrders($planIds) {
        $instance = new static();
        
        $instance->beginTransaction();
        
        try {
            foreach ($planIds as $index => $planId) {
                $sql = "UPDATE {$instance->table} SET sort_order = :sort_order WHERE id = :id";
                $stmt = $instance->db->prepare($sql);
                $stmt->execute([
                    'sort_order' => $index + 1,
                    'id' => $planId
                ]);
            }
            
            $instance->commit();
            return true;
            
        } catch (Exception $e) {
            $instance->rollback();
            error_log("Sort order update error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get plan statistics
     */
    public static function getStatistics() {
        $instance = new static();
        
        $sql = "SELECT 
                    COUNT(*) as total_plans,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_plans,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_plans,
                    AVG(daily_return) as avg_daily_return,
                    MIN(min_deposit) as min_deposit_amount,
                    MAX(COALESCE(max_deposit, min_deposit)) as max_deposit_amount
                FROM {$instance->table}";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetch();
    }
    
    /**
     * Get plans with deposit statistics
     */
    public static function getWithDepositStats() {
        $instance = new static();
        
        $sql = "SELECT 
                    tp.*,
                    COUNT(d.id) as deposits_count,
                    COALESCE(SUM(CASE WHEN d.status = 'approved' THEN d.amount ELSE 0 END), 0) as total_deposits,
                    COALESCE(SUM(CASE WHEN d.status = 'pending' THEN 1 ELSE 0 END), 0) as pending_deposits
                FROM {$instance->table} tp
                LEFT JOIN deposits d ON tp.id = d.plan_id
                GROUP BY tp.id
                ORDER BY tp.sort_order ASC, tp.created_at ASC";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
    
    /**
     * Find plan by name
     */
    public static function findByName($name) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE name = :name LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['name' => $name]);
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
    
    /**
     * Get most popular plan (by deposits count)
     */
    public static function getMostPopular() {
        $instance = new static();
        
        $sql = "SELECT tp.*, COUNT(d.id) as deposits_count
                FROM {$instance->table} tp
                LEFT JOIN deposits d ON tp.id = d.plan_id AND d.status = 'approved'
                WHERE tp.status = 'active'
                GROUP BY tp.id
                ORDER BY deposits_count DESC
                LIMIT 1";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute();
        $data = $stmt->fetch();
        
        if ($data) {
            return new static($data);
        }
        
        return null;
    }
}
?>