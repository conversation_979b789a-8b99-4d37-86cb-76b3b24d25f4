<?php
/**
 * Email Templates Test Suite
 * Tests email template functionality including CRUD operations and email sending
 */

require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/models/EmailTemplate.php';
require_once __DIR__ . '/../classes/services/EmailTemplateService.php';

class EmailTemplateTest {
    private $pdo;
    private $testResults = [];
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    public function runAllTests() {
        echo "<h2>Email Templates Test Suite</h2>\n";
        echo "<div style='font-family: monospace; background: #f5f5f5; padding: 20px; border-radius: 5px;'>\n";
        
        $this->testDatabaseConnection();
        $this->testEmailTemplateModel();
        $this->testTemplateCreation();
        $this->testPlaceholderReplacement();
        $this->testTemplateRetrieval();
        $this->testTemplateValidation();
        $this->testEmailTemplateService();
        $this->testDefaultTemplates();
        
        $this->displayResults();
        echo "</div>\n";
    }
    
    private function testDatabaseConnection() {
        try {
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'email_templates'");
            $tableExists = $stmt->rowCount() > 0;
            
            if ($tableExists) {
                $this->addResult('Database Connection', 'PASS', 'Email templates table exists');
            } else {
                $this->addResult('Database Connection', 'FAIL', 'Email templates table not found');
                return;
            }
            
            // Check table structure
            $stmt = $this->pdo->query("DESCRIBE email_templates");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredColumns = ['id', 'template_type', 'template_name', 'subject', 'body_html', 'body_text', 'is_active'];
            $missingColumns = array_diff($requiredColumns, $columns);
            
            if (empty($missingColumns)) {
                $this->addResult('Table Structure', 'PASS', 'All required columns present');
            } else {
                $this->addResult('Table Structure', 'FAIL', 'Missing columns: ' . implode(', ', $missingColumns));
            }
            
        } catch (Exception $e) {
            $this->addResult('Database Connection', 'FAIL', $e->getMessage());
        }
    }
    
    private function testEmailTemplateModel() {
        try {
            // Test model instantiation
            $template = new EmailTemplate();
            $this->addResult('Model Instantiation', 'PASS', 'EmailTemplate model created successfully');
            
            // Test template types
            $types = EmailTemplate::getTemplateTypes();
            if (is_array($types) && count($types) > 0) {
                $this->addResult('Template Types', 'PASS', count($types) . ' template types defined');
            } else {
                $this->addResult('Template Types', 'FAIL', 'No template types found');
            }
            
            // Test placeholders
            $placeholders = EmailTemplate::getAllPlaceholders();
            if (is_array($placeholders) && count($placeholders) > 0) {
                $this->addResult('Placeholders', 'PASS', count($placeholders) . ' placeholder sets defined');
            } else {
                $this->addResult('Placeholders', 'FAIL', 'No placeholders found');
            }
            
        } catch (Exception $e) {
            $this->addResult('Model Instantiation', 'FAIL', $e->getMessage());
        }
    }
    
    private function testTemplateCreation() {
        try {
            // Create test template
            $templateData = [
                'template_type' => 'test_template',
                'template_name' => 'Test Template',
                'subject' => 'Test Subject - {{user_name}}',
                'body_html' => '<p>Hello {{user_name}}, this is a test email from {{site_name}}.</p>',
                'body_text' => 'Hello {{user_name}}, this is a test email from {{site_name}}.',
                'is_active' => true
            ];
            
            $template = new EmailTemplate();
            $result = $template->create($templateData);
            
            if ($result) {
                $this->addResult('Template Creation', 'PASS', 'Test template created successfully');
                
                // Clean up
                $this->pdo->exec("DELETE FROM email_templates WHERE template_type = 'test_template'");
            } else {
                $this->addResult('Template Creation', 'FAIL', 'Failed to create test template');
            }
            
        } catch (Exception $e) {
            $this->addResult('Template Creation', 'FAIL', $e->getMessage());
        }
    }
    
    private function testPlaceholderReplacement() {
        try {
            $template = new EmailTemplate();
            
            $content = 'Hello {{user_name}}, your balance is {{currency_symbol}}{{amount}} at {{site_name}}.';
            $data = [
                '{{user_name}}' => 'John Doe',
                '{{currency_symbol}}' => '$',
                '{{amount}}' => '1,250.00',
                '{{site_name}}' => 'Test Site'
            ];
            
            $result = $template->replacePlaceholders($content, $data);
            $expected = 'Hello John Doe, your balance is $1,250.00 at Test Site.';
            
            if ($result === $expected) {
                $this->addResult('Placeholder Replacement', 'PASS', 'Placeholders replaced correctly');
            } else {
                $this->addResult('Placeholder Replacement', 'FAIL', "Expected: $expected, Got: $result");
            }
            
        } catch (Exception $e) {
            $this->addResult('Placeholder Replacement', 'FAIL', $e->getMessage());
        }
    }
    
    private function testTemplateRetrieval() {
        try {
            // Test getting template by type
            $template = EmailTemplate::getByType('welcome');
            
            if ($template) {
                $this->addResult('Template Retrieval', 'PASS', 'Welcome template retrieved successfully');
            } else {
                $this->addResult('Template Retrieval', 'FAIL', 'Welcome template not found');
            }
            
            // Test getting all templates
            $allTemplates = EmailTemplate::all();
            
            if (is_array($allTemplates) && count($allTemplates) > 0) {
                $this->addResult('All Templates Retrieval', 'PASS', count($allTemplates) . ' templates found');
            } else {
                $this->addResult('All Templates Retrieval', 'FAIL', 'No templates found');
            }
            
        } catch (Exception $e) {
            $this->addResult('Template Retrieval', 'FAIL', $e->getMessage());
        }
    }
    
    private function testTemplateValidation() {
        try {
            $template = new EmailTemplate();
            
            // Test valid data
            $validData = [
                'template_name' => 'Test Template',
                'template_type' => 'test',
                'subject' => 'Test Subject',
                'body_html' => '<p>Test content</p>',
                'body_text' => 'Test content'
            ];
            
            $errors = $template->validate($validData);
            if (empty($errors)) {
                $this->addResult('Validation - Valid Data', 'PASS', 'Valid data passed validation');
            } else {
                $this->addResult('Validation - Valid Data', 'FAIL', 'Valid data failed validation: ' . implode(', ', $errors));
            }
            
            // Test invalid data
            $invalidData = [
                'template_name' => '',
                'template_type' => '',
                'subject' => '',
                'body_html' => '',
                'body_text' => ''
            ];
            
            $errors = $template->validate($invalidData);
            if (!empty($errors)) {
                $this->addResult('Validation - Invalid Data', 'PASS', 'Invalid data correctly rejected');
            } else {
                $this->addResult('Validation - Invalid Data', 'FAIL', 'Invalid data incorrectly accepted');
            }
            
        } catch (Exception $e) {
            $this->addResult('Template Validation', 'FAIL', $e->getMessage());
        }
    }
    
    private function testEmailTemplateService() {
        try {
            // Test service instantiation
            $service = new EmailTemplateService();
            $this->addResult('Service Instantiation', 'PASS', 'EmailTemplateService created successfully');
            
            // Test sample data generation
            $sampleData = $service->getSampleDataForType('welcome');
            if (is_array($sampleData) && count($sampleData) > 0) {
                $this->addResult('Sample Data Generation', 'PASS', 'Sample data generated successfully');
            } else {
                $this->addResult('Sample Data Generation', 'FAIL', 'Failed to generate sample data');
            }
            
        } catch (Exception $e) {
            $this->addResult('Email Template Service', 'FAIL', $e->getMessage());
        }
    }
    
    private function testDefaultTemplates() {
        try {
            // Check if default templates exist
            $defaultTypes = ['welcome', 'deposit_confirmation', 'withdrawal_notification', 'password_reset'];
            $foundTemplates = 0;
            
            foreach ($defaultTypes as $type) {
                $template = EmailTemplate::getByType($type);
                if ($template) {
                    $foundTemplates++;
                }
            }
            
            if ($foundTemplates === count($defaultTypes)) {
                $this->addResult('Default Templates', 'PASS', 'All default templates found');
            } else {
                $this->addResult('Default Templates', 'PARTIAL', "$foundTemplates/" . count($defaultTypes) . " default templates found");
            }
            
            // Test creating defaults
            EmailTemplate::createDefaults();
            $this->addResult('Create Defaults', 'PASS', 'Default templates creation completed');
            
        } catch (Exception $e) {
            $this->addResult('Default Templates', 'FAIL', $e->getMessage());
        }
    }
    
    private function addResult($test, $status, $message) {
        $this->testResults[] = [
            'test' => $test,
            'status' => $status,
            'message' => $message
        ];
    }
    
    private function displayResults() {
        echo "<h3>Test Results:</h3>\n";
        
        $passed = 0;
        $failed = 0;
        $partial = 0;
        
        foreach ($this->testResults as $result) {
            $color = match($result['status']) {
                'PASS' => 'green',
                'FAIL' => 'red',
                'PARTIAL' => 'orange',
                default => 'black'
            };
            
            echo "<div style='margin: 5px 0; color: $color;'>";
            echo "<strong>[{$result['status']}]</strong> {$result['test']}: {$result['message']}";
            echo "</div>\n";
            
            match($result['status']) {
                'PASS' => $passed++,
                'FAIL' => $failed++,
                'PARTIAL' => $partial++,
                default => null
            };
        }
        
        echo "<hr>\n";
        echo "<div style='font-weight: bold; margin-top: 20px;'>";
        echo "<span style='color: green;'>Passed: $passed</span> | ";
        echo "<span style='color: orange;'>Partial: $partial</span> | ";
        echo "<span style='color: red;'>Failed: $failed</span>";
        echo "</div>\n";
        
        $total = $passed + $failed + $partial;
        $successRate = $total > 0 ? round(($passed / $total) * 100, 1) : 0;
        echo "<div style='margin-top: 10px;'>Success Rate: {$successRate}%</div>\n";
    }
}

// Run tests
$tester = new EmailTemplateTest();
$tester->runAllTests();
?>