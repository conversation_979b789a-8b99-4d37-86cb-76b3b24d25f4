<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';
require_once __DIR__ . '/traits/SecurityTrait.php';
require_once __DIR__ . '/../services/CSRFProtection.php';

/**
 * SettingsView - User settings and preferences interface
 * 
 * Handles user account settings including security preferences,
 * notification settings, and account management options.
 * 
 * @package Views
 * <AUTHOR> Trading Platform
 * @version 1.0.0
 */
class SettingsView extends BaseView {
    use StatusColorTrait, SecurityTrait;
    
    /** @var User User object containing account settings */
    private $user;
    
    public function __construct($user) {
        parent::__construct();
        $this->user = $user;
        $this->setTitle('Settings - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/settings.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h1 class="h3 mb-4">Account Settings</h1>
                </div>
            </div>
            
            <div class="row">
                <!-- Security Settings -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Security Settings</h5>
                        </div>
                        <div class="card-body">
                            <form id="securityForm">
                                <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                
                                <!-- Two-Factor Authentication -->
                                <div class="setting-item mb-4">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">Two-Factor Authentication</h6>
                                            <p class="text-muted small mb-2">
                                                Add an extra layer of security to your account by requiring a verification code.
                                            </p>
                                            <span class="badge bg-<?php echo $this->user->two_fa_enabled ? 'success' : 'secondary'; ?>">
                                                <?php echo $this->user->two_fa_enabled ? 'Enabled' : 'Disabled'; ?>
                                            </span>
                                        </div>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="two_fa_enabled" 
                                                   name="two_fa_enabled" value="1" 
                                                   <?php echo $this->user->two_fa_enabled ? 'checked' : ''; ?>>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <!-- Email Notifications -->
                                <div class="setting-item mb-4">
                                    <h6 class="mb-3">Email Notifications</h6>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="email_deposits" 
                                               name="email_deposits" value="1" checked>
                                        <label class="form-check-label" for="email_deposits">
                                            Deposit confirmations
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="email_withdrawals" 
                                               name="email_withdrawals" value="1" checked>
                                        <label class="form-check-label" for="email_withdrawals">
                                            Withdrawal notifications
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="email_security" 
                                               name="email_security" value="1" checked>
                                        <label class="form-check-label" for="email_security">
                                            Security alerts
                                        </label>
                                    </div>
                                    
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="email_marketing" 
                                               name="email_marketing" value="1">
                                        <label class="form-check-label" for="email_marketing">
                                            Marketing updates
                                        </label>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Account Information -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Account Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Account ID</label>
                                <div class="fw-bold">#<?php echo str_pad($this->user->getId(), 6, '0', STR_PAD_LEFT); ?></div>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Email Address</label>
                                <div class="d-flex align-items-center">
                                    <span class="fw-bold me-2"><?php echo htmlspecialchars($this->user->email); ?></span>
                                    <?php if ($this->user->email_verified): ?>
                                        <span class="badge bg-success">Verified</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Unverified</span>
                                        <button class="btn btn-sm btn-outline-primary ms-2">Verify Now</button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Member Since</label>
                                <div class="fw-bold"><?php echo date('F j, Y', strtotime($this->user->created_at)); ?></div>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">Account Status</label>
                                <div>
                                    <span class="badge bg-<?php echo $this->getStatusColor($this->user->status); ?>">
                                        <?php echo ucfirst($this->user->status); ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="info-item mb-3">
                                <label class="form-label text-muted">KYC Status</label>
                                <div>
                                    <span class="badge bg-<?php echo $this->getKycStatusColor($this->user->kyc_status); ?>">
                                        <?php echo ucfirst($this->user->kyc_status); ?>
                                    </span>
                                    <?php if ($this->user->kyc_status === 'unverified'): ?>
                                        <button class="btn btn-sm btn-outline-primary ms-2">Start Verification</button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="info-item">
                                <label class="form-label text-muted">Last Login</label>
                                <div class="fw-bold">
                                    <?php echo $this->user->last_login ? date('M j, Y g:i A', strtotime($this->user->last_login)) : 'Never'; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Danger Zone -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Danger Zone
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6>Account Deactivation</h6>
                                    <p class="text-muted mb-0">
                                        Temporarily deactivate your account. You can reactivate it anytime by logging in.
                                        Your data and balance will be preserved.
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                                        Deactivate Account
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deactivate Account Modal -->
        <div class="modal fade" id="deactivateModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Deactivate Account</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This will temporarily deactivate your account.
                        </div>
                        <p>Are you sure you want to deactivate your account? You can reactivate it anytime by logging in.</p>
                        <p><strong>What happens when you deactivate:</strong></p>
                        <ul>
                            <li>Your account will be temporarily suspended</li>
                            <li>You won't be able to make deposits or withdrawals</li>
                            <li>Your balance and data will be preserved</li>
                            <li>You can reactivate by contacting support</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-danger" id="confirmDeactivate">
                            Deactivate Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Success/Error Messages -->
        <div class="toast-container position-fixed bottom-0 end-0 p-3">
            <div id="successToast" class="toast" role="alert">
                <div class="toast-header bg-success text-white">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong class="me-auto">Success</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body"></div>
            </div>
            
            <div id="errorToast" class="toast" role="alert">
                <div class="toast-header bg-danger text-white">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong class="me-auto">Error</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body"></div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/settings.js"></script>
        <?php
    }
    
    private function getStatusColor($status) {
        $colors = [
            'active' => 'success',
            'suspended' => 'danger',
            'pending' => 'warning'
        ];
        
        return $colors[$status] ?? 'secondary';
    }
    
    private function getKycStatusColor($status) {
        $colors = [
            'verified' => 'success',
            'pending' => 'warning',
            'unverified' => 'secondary',
            'rejected' => 'danger'
        ];
        
        return $colors[$status] ?? 'secondary';
    }
    
    /**
     * Render 2FA setup page
     */
    public function render2FASetup() {
        $this->renderHead();
        ?>
        <body>
            <?php $this->renderNavigation(); ?>
            
            <div class="container mt-4">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h4><i class="fas fa-shield-alt me-2"></i>Two-Factor Authentication Setup</h4>
                            </div>
                            
                            <div class="card-body">
                                <?php if (!empty($this->data['errors'])): ?>
                                    <div class="alert alert-danger">
                                        <?php foreach ($this->data['errors'] as $error): ?>
                                            <div><?= htmlspecialchars($error) ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($this->data['message'])): ?>
                                    <div class="alert alert-info">
                                        <?= htmlspecialchars($this->data['message']) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (isset($this->data['already_enabled']) && $this->data['already_enabled']): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        2FA is already enabled for your account.
                                    </div>
                                    <div class="text-center">
                                        <a href="/user/security/2fa-disable.php" class="btn btn-warning">
                                            <i class="fas fa-times me-2"></i>Disable 2FA
                                        </a>
                                        <a href="/user/profile/" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                        </a>
                                    </div>
                                <?php elseif ($this->data['success'] && !empty($this->data['backup_codes'])): ?>
                                    <!-- Show backup codes after successful setup -->
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        2FA has been successfully enabled for your account!
                                    </div>
                                    
                                    <div class="alert alert-warning">
                                        <h5><i class="fas fa-key me-2"></i>Important: Save Your Backup Codes</h5>
                                        <p>These backup codes can be used to access your account if you lose your authenticator device. 
                                           Store them in a safe place - they won't be shown again.</p>
                                        
                                        <div class="backup-codes mt-3">
                                            <?php foreach ($this->data['backup_codes'] as $code): ?>
                                                <code class="d-block mb-1"><?= htmlspecialchars($code) ?></code>
                                            <?php endforeach; ?>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <button class="btn btn-primary" onclick="printBackupCodes()">
                                                <i class="fas fa-print me-2"></i>Print Codes
                                            </button>
                                            <button class="btn btn-secondary" onclick="downloadBackupCodes()">
                                                <i class="fas fa-download me-2"></i>Download Codes
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <a href="/user/profile/" class="btn btn-success">
                                            <i class="fas fa-check me-2"></i>Continue to Profile
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <!-- Setup form -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h5>Step 1: Scan QR Code</h5>
                                            <p>Use your authenticator app (Google Authenticator, Authy, etc.) to scan this QR code:</p>
                                            
                                            <?php if (!empty($this->data['qr_code_url'])): ?>
                                                <div class="text-center mb-3">
                                                    <img src="<?= htmlspecialchars($this->data['qr_code_url']) ?>" 
                                                         alt="2FA QR Code" 
                                                         class="img-fluid border rounded">
                                                </div>
                                            <?php endif; ?>
                                            
                                            <div class="alert alert-info">
                                                <strong>Manual Entry:</strong><br>
                                                If you can't scan the QR code, enter this secret manually:<br>
                                                <code><?= htmlspecialchars($this->data['secret'] ?? '') ?></code>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <h5>Step 2: Verify Setup</h5>
                                            <p>Enter the 6-digit code from your authenticator app to complete setup:</p>
                                            
                                            <form method="POST">
                                                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($this->data['csrf_token']) ?>">
                                                <input type="hidden" name="secret" value="<?= htmlspecialchars($this->data['secret'] ?? '') ?>">
                                                
                                                <div class="mb-3">
                                                    <label for="code" class="form-label">Verification Code</label>
                                                    <input type="text" 
                                                           class="form-control text-center" 
                                                           id="code" 
                                                           name="code" 
                                                           placeholder="000000"
                                                           maxlength="6"
                                                           pattern="[0-9]{6}"
                                                           required>
                                                </div>
                                                
                                                <div class="d-grid gap-2">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-check me-2"></i>Enable 2FA
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                // Format code input
                document.getElementById('code')?.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                // Print backup codes
                function printBackupCodes() {
                    const codes = <?= json_encode($this->data['backup_codes'] ?? []) ?>;
                    const printWindow = window.open('', '_blank');
                    printWindow.document.write(`
                        <html>
                        <head><title>2FA Backup Codes</title></head>
                        <body>
                            <h2>Two-Factor Authentication Backup Codes</h2>
                            <p>Account: <?= htmlspecialchars($_SESSION['user_email'] ?? 'Your Account') ?></p>
                            <p>Generated: <?= date('Y-m-d H:i:s') ?></p>
                            <ul>
                                ${codes.map(code => '<li><code>' + code + '</code></li>').join('')}
                            </ul>
                            <p><strong>Important:</strong> Store these codes in a safe place. They can be used to access your account if you lose your authenticator device.</p>
                        </body>
                        </html>
                    `);
                    printWindow.document.close();
                    printWindow.print();
                }
                
                // Download backup codes
                function downloadBackupCodes() {
                    const codes = <?= json_encode($this->data['backup_codes'] ?? []) ?>;
                    const content = `Two-Factor Authentication Backup Codes\n\nAccount: <?= htmlspecialchars($_SESSION['user_email'] ?? 'Your Account') ?>\nGenerated: <?= date('Y-m-d H:i:s') ?>\n\nBackup Codes:\n${codes.join('\n')}\n\nImportant: Store these codes in a safe place. They can be used to access your account if you lose your authenticator device.`;
                    
                    const blob = new Blob([content], { type: 'text/plain' });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '2fa-backup-codes.txt';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                }
            </script>
        </body>
        </html>
        <?php
    }
    
    /**
     * Render 2FA disable page
     */
    public function render2FADisable() {
        $this->renderHead();
        ?>
        <body>
            <?php $this->renderNavigation(); ?>
            
            <div class="container mt-4">
                <div class="row justify-content-center">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h4><i class="fas fa-shield-alt me-2"></i>Disable Two-Factor Authentication</h4>
                            </div>
                            
                            <div class="card-body">
                                <?php if (!empty($this->data['errors'])): ?>
                                    <div class="alert alert-danger">
                                        <?php foreach ($this->data['errors'] as $error): ?>
                                            <div><?= htmlspecialchars($error) ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($this->data['message'])): ?>
                                    <div class="alert alert-info">
                                        <?= htmlspecialchars($this->data['message']) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($this->data['success']): ?>
                                    <div class="alert alert-success">
                                        <i class="fas fa-check-circle me-2"></i>
                                        2FA has been successfully disabled for your account.
                                    </div>
                                    
                                    <div class="text-center">
                                        <a href="/user/profile/" class="btn btn-primary">
                                            <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Warning:</strong> Disabling 2FA will make your account less secure.
                                    </div>
                                    
                                    <p>To disable two-factor authentication, please provide your current password and a 2FA code:</p>
                                    
                                    <form method="POST">
                                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($this->data['csrf_token']) ?>">
                                        
                                        <div class="mb-3">
                                            <label for="current_password" class="form-label">Current Password</label>
                                            <input type="password" 
                                                   class="form-control" 
                                                   id="current_password" 
                                                   name="current_password" 
                                                   required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="code" class="form-label">2FA Code</label>
                                            <input type="text" 
                                                   class="form-control text-center" 
                                                   id="code" 
                                                   name="code" 
                                                   placeholder="000000"
                                                   maxlength="6"
                                                   pattern="[0-9]{6}"
                                                   required>
                                            <div class="form-text">Enter the current code from your authenticator app</div>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-times me-2"></i>Disable 2FA
                                            </button>
                                            <a href="/user/profile/" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-2"></i>Cancel
                                            </a>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                // Format code input
                document.getElementById('code')?.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
            </script>
        </body>
        </html>
        <?php
    }
}
?>