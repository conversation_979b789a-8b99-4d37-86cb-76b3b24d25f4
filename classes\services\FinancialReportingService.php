<?php
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Deposit.php';
require_once __DIR__ . '/../models/Withdrawal.php';
require_once __DIR__ . '/../models/PaymentMethod.php';
require_once __DIR__ . '/../models/TradingPlan.php';

/**
 * FinancialReportingService - Comprehensive financial reporting and analytics
 */
class FinancialReportingService {
    
    /**
     * Get comprehensive summary report
     */
    public function getSummaryReport($dateFrom, $dateTo) {
        $db = getDB();
        
        $report = [
            'period' => ['from' => $dateFrom, 'to' => $dateTo],
            'deposits' => $this->getDepositSummary($dateFrom, $dateTo),
            'withdrawals' => $this->getWithdrawalSummary($dateFrom, $dateTo),
            'transactions' => $this->getTransactionSummary($dateFrom, $dateTo),
            'users' => $this->getUserSummary($dateFrom, $dateTo),
            'top_users' => $this->getTopUsers($dateFrom, $dateTo),
            'payment_methods' => $this->getPaymentMethodUsage($dateFrom, $dateTo),
            'trading_plans' => $this->getTradingPlanUsage($dateFrom, $dateTo),
            'pending_actions' => $this->getPendingActions(),
            'net_flow' => 0
        ];
        
        // Calculate net flow
        $report['net_flow'] = $report['deposits']['approved_amount'] - $report['withdrawals']['approved_amount'];
        
        return $report;
    }
    
    /**
     * Get deposit summary statistics
     */
    private function getDepositSummary($dateFrom, $dateTo) {
        $db = getDB();
        
        $sql = "SELECT 
                    COUNT(*) as total_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END), 0) as approved_amount,
                    COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN status = 'rejected' THEN amount ELSE 0 END), 0) as rejected_amount,
                    COALESCE(SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END), 0) as approved_count,
                    COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_count,
                    COALESCE(SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END), 0) as rejected_count,
                    COALESCE(SUM(bonus_amount), 0) as total_bonus,
                    COALESCE(AVG(amount), 0) as average_amount
                FROM deposits 
                WHERE created_at >= :date_from AND created_at <= :date_to";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $result = $stmt->fetch();
        
        return [
            'count' => (int) $result['total_count'],
            'total' => (float) $result['total_amount'],
            'approved_amount' => (float) $result['approved_amount'],
            'pending_amount' => (float) $result['pending_amount'],
            'rejected_amount' => (float) $result['rejected_amount'],
            'approved_count' => (int) $result['approved_count'],
            'pending_count' => (int) $result['pending_count'],
            'rejected_count' => (int) $result['rejected_count'],
            'total_bonus' => (float) $result['total_bonus'],
            'average_amount' => (float) $result['average_amount']
        ];
    }
    
    /**
     * Get withdrawal summary statistics
     */
    private function getWithdrawalSummary($dateFrom, $dateTo) {
        $db = getDB();
        
        $sql = "SELECT 
                    COUNT(*) as total_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COALESCE(SUM(CASE WHEN status IN ('approved', 'completed') THEN amount ELSE 0 END), 0) as approved_amount,
                    COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN status = 'rejected' THEN amount ELSE 0 END), 0) as rejected_amount,
                    COALESCE(SUM(CASE WHEN status IN ('approved', 'completed') THEN 1 ELSE 0 END), 0) as approved_count,
                    COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_count,
                    COALESCE(SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END), 0) as rejected_count,
                    COALESCE(AVG(amount), 0) as average_amount
                FROM withdrawals 
                WHERE created_at >= :date_from AND created_at <= :date_to";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $result = $stmt->fetch();
        
        return [
            'count' => (int) $result['total_count'],
            'total' => (float) $result['total_amount'],
            'approved_amount' => (float) $result['approved_amount'],
            'pending_amount' => (float) $result['pending_amount'],
            'rejected_amount' => (float) $result['rejected_amount'],
            'approved_count' => (int) $result['approved_count'],
            'pending_count' => (int) $result['pending_count'],
            'rejected_count' => (int) $result['rejected_count'],
            'average_amount' => (float) $result['average_amount']
        ];
    }
    
    /**
     * Get transaction summary statistics
     */
    private function getTransactionSummary($dateFrom, $dateTo) {
        $db = getDB();
        
        $sql = "SELECT 
                    type,
                    COUNT(*) as count,
                    COALESCE(SUM(ABS(amount)), 0) as total_amount,
                    COALESCE(AVG(ABS(amount)), 0) as average_amount
                FROM transactions 
                WHERE created_at >= :date_from AND created_at <= :date_to AND status = 'completed'
                GROUP BY type
                ORDER BY total_amount DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $results = $stmt->fetchAll();
        
        $summary = [];
        foreach ($results as $row) {
            $summary[$row['type']] = [
                'count' => (int) $row['count'],
                'total_amount' => (float) $row['total_amount'],
                'average_amount' => (float) $row['average_amount']
            ];
        }
        
        return $summary;
    }
    
    /**
     * Get user activity summary
     */
    private function getUserSummary($dateFrom, $dateTo) {
        $db = getDB();
        
        // New users in period
        $sql = "SELECT COUNT(*) as new_users FROM users 
                WHERE created_at >= :date_from AND created_at <= :date_to AND role = 'user'";
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $newUsers = $stmt->fetchColumn();
        
        // Active users (users with transactions in period)
        $sql = "SELECT COUNT(DISTINCT user_id) as active_users 
                FROM transactions 
                WHERE created_at >= :date_from AND created_at <= :date_to";
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $activeUsers = $stmt->fetchColumn();
        
        // Total platform balance
        $sql = "SELECT COALESCE(SUM(balance), 0) as total_balance FROM users WHERE role = 'user'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $totalBalance = $stmt->fetchColumn();
        
        return [
            'new_users' => (int) $newUsers,
            'active_users' => (int) $activeUsers,
            'total_balance' => (float) $totalBalance
        ];
    }
    
    /**
     * Get top users by transaction volume
     */
    private function getTopUsers($dateFrom, $dateTo, $limit = 10) {
        $db = getDB();
        
        $sql = "SELECT 
                    u.id, u.username, u.first_name, u.last_name, u.email,
                    COALESCE(d.total_deposits, 0) as total_deposits,
                    COALESCE(w.total_withdrawals, 0) as total_withdrawals,
                    COALESCE(d.deposit_count, 0) + COALESCE(w.withdrawal_count, 0) as transaction_count
                FROM users u
                LEFT JOIN (
                    SELECT user_id, 
                           SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as total_deposits,
                           COUNT(*) as deposit_count
                    FROM deposits 
                    WHERE created_at >= ? AND created_at <= ?
                    GROUP BY user_id
                ) d ON u.id = d.user_id
                LEFT JOIN (
                    SELECT user_id, 
                           SUM(CASE WHEN status IN ('approved', 'completed') THEN amount ELSE 0 END) as total_withdrawals,
                           COUNT(*) as withdrawal_count
                    FROM withdrawals 
                    WHERE created_at >= ? AND created_at <= ?
                    GROUP BY user_id
                ) w ON u.id = w.user_id
                WHERE u.role = 'user' AND (d.user_id IS NOT NULL OR w.user_id IS NOT NULL)
                ORDER BY (COALESCE(d.total_deposits, 0) + COALESCE(w.total_withdrawals, 0)) DESC
                LIMIT ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$dateFrom, $dateTo, $dateFrom, $dateTo, $limit]);
        
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get payment method usage statistics
     */
    private function getPaymentMethodUsage($dateFrom, $dateTo) {
        $db = getDB();
        
        $sql = "SELECT 
                    pm.id, pm.name, pm.type,
                    COUNT(d.id) as usage_count,
                    COALESCE(SUM(CASE WHEN d.status = 'approved' THEN d.amount ELSE 0 END), 0) as total_amount,
                    COALESCE(AVG(CASE WHEN d.status = 'approved' THEN d.amount ELSE NULL END), 0) as average_amount
                FROM payment_methods pm
                LEFT JOIN deposits d ON pm.id = d.payment_method_id 
                    AND d.created_at >= :date_from AND d.created_at <= :date_to
                WHERE pm.status = 'active'
                GROUP BY pm.id, pm.name, pm.type
                ORDER BY total_amount DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get trading plan usage statistics
     */
    private function getTradingPlanUsage($dateFrom, $dateTo) {
        $db = getDB();
        
        $sql = "SELECT 
                    tp.id, tp.name, tp.min_deposit, tp.max_deposit,
                    COUNT(d.id) as usage_count,
                    COALESCE(SUM(CASE WHEN d.status = 'approved' THEN d.amount ELSE 0 END), 0) as total_amount,
                    COALESCE(AVG(CASE WHEN d.status = 'approved' THEN d.amount ELSE NULL END), 0) as average_amount
                FROM trading_plans tp
                LEFT JOIN deposits d ON tp.id = d.plan_id 
                    AND d.created_at >= :date_from AND d.created_at <= :date_to
                WHERE tp.status = 'active'
                GROUP BY tp.id, tp.name, tp.min_deposit, tp.max_deposit
                ORDER BY total_amount DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get pending actions requiring admin attention
     */
    private function getPendingActions() {
        $db = getDB();
        
        $sql = "SELECT 
                    (SELECT COUNT(*) FROM deposits WHERE status = 'pending') as pending_deposits,
                    (SELECT COUNT(*) FROM withdrawals WHERE status = 'pending') as pending_withdrawals,
                    (SELECT COUNT(*) FROM support_tickets WHERE status = 'pending') as pending_tickets";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        return (int) $result['pending_deposits'] + (int) $result['pending_withdrawals'] + (int) $result['pending_tickets'];
    }
    
    /**
     * Get detailed transaction report with pagination
     */
    public function getTransactionReport($dateFrom, $dateTo, $filters = [], $page = 1, $limit = 50) {
        $db = getDB();
        $offset = ($page - 1) * $limit;
        
        $whereConditions = ["t.created_at >= :date_from", "t.created_at <= :date_to"];
        $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
        
        // Apply filters
        if (!empty($filters['type'])) {
            $whereConditions[] = "t.type = :type";
            $params['type'] = $filters['type'];
        }
        
        if (!empty($filters['status'])) {
            $whereConditions[] = "t.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (!empty($filters['user_id'])) {
            $whereConditions[] = "t.user_id = :user_id";
            $params['user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['min_amount'])) {
            $whereConditions[] = "ABS(t.amount) >= :min_amount";
            $params['min_amount'] = $filters['min_amount'];
        }
        
        if (!empty($filters['max_amount'])) {
            $whereConditions[] = "ABS(t.amount) <= :max_amount";
            $params['max_amount'] = $filters['max_amount'];
        }
        
        $whereClause = implode(' AND ', $whereConditions);
        
        // Get total count
        $countSql = "SELECT COUNT(*) FROM transactions t 
                     LEFT JOIN users u ON t.user_id = u.id 
                     WHERE {$whereClause}";
        $stmt = $db->prepare($countSql);
        $stmt->execute($params);
        $totalCount = $stmt->fetchColumn();
        
        // Get paginated results
        $sql = "SELECT 
                    t.*, 
                    u.username, u.first_name, u.last_name, u.email,
                    admin.username as processed_by_username
                FROM transactions t
                LEFT JOIN users u ON t.user_id = u.id
                LEFT JOIN users admin ON t.processed_by = admin.id
                WHERE {$whereClause}
                ORDER BY t.created_at DESC
                LIMIT {$limit} OFFSET {$offset}";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $transactions = $stmt->fetchAll();
        
        return [
            'transactions' => $transactions,
            'total_count' => (int) $totalCount,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($totalCount / $limit)
        ];
    }
    
    /**
     * Get transaction volume trends over time
     */
    public function getVolumeTrends($dateFrom, $dateTo, $interval = 'day') {
        $db = getDB();
        
        $dateFormat = match($interval) {
            'hour' => '%Y-%m-%d %H:00:00',
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            default => '%Y-%m-%d'
        };
        
        $sql = "SELECT 
                    DATE_FORMAT(created_at, :date_format) as period,
                    type,
                    COUNT(*) as transaction_count,
                    COALESCE(SUM(ABS(amount)), 0) as total_volume
                FROM transactions
                WHERE created_at >= :date_from AND created_at <= :date_to AND status = 'completed'
                GROUP BY period, type
                ORDER BY period ASC, type";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            'date_format' => $dateFormat,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Export report data to CSV format
     */
    public function exportToCSV($reportType, $dateFrom, $dateTo, $filters = []) {
        switch ($reportType) {
            case 'transactions':
                return $this->exportTransactionsCSV($dateFrom, $dateTo, $filters);
            case 'deposits':
                return $this->exportDepositsCSV($dateFrom, $dateTo, $filters);
            case 'withdrawals':
                return $this->exportWithdrawalsCSV($dateFrom, $dateTo, $filters);
            case 'summary':
                return $this->exportSummaryCSV($dateFrom, $dateTo);
            default:
                throw new InvalidArgumentException('Invalid report type');
        }
    }
    
    /**
     * Export transactions to CSV
     */
    private function exportTransactionsCSV($dateFrom, $dateTo, $filters = []) {
        $report = $this->getTransactionReport($dateFrom, $dateTo, $filters, 1, 10000);
        
        $csv = "ID,Date,User,Email,Type,Amount,Balance Before,Balance After,Status,Description,Processed By,Reference\n";
        
        foreach ($report['transactions'] as $transaction) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%s,%.2f,%.2f,%.2f,%s,\"%s\",%s,%s\n",
                $transaction['id'],
                $transaction['created_at'],
                $transaction['username'] ?? '',
                $transaction['email'] ?? '',
                $transaction['type'],
                $transaction['amount'],
                $transaction['balance_before'],
                $transaction['balance_after'],
                $transaction['status'],
                str_replace('"', '""', $transaction['description']),
                $transaction['processed_by_username'] ?? '',
                $transaction['reference_id'] ?? ''
            );
        }
        
        return $csv;
    }
    
    /**
     * Export deposits to CSV
     */
    private function exportDepositsCSV($dateFrom, $dateTo, $filters = []) {
        $db = getDB();
        
        $sql = "SELECT 
                    d.*, 
                    u.username, u.first_name, u.last_name, u.email,
                    tp.name as plan_name,
                    pm.name as payment_method_name,
                    admin.username as processed_by_username
                FROM deposits d
                LEFT JOIN users u ON d.user_id = u.id
                LEFT JOIN trading_plans tp ON d.plan_id = tp.id
                LEFT JOIN payment_methods pm ON d.payment_method_id = pm.id
                LEFT JOIN users admin ON d.processed_by = admin.id
                WHERE d.created_at >= :date_from AND d.created_at <= :date_to
                ORDER BY d.created_at DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $deposits = $stmt->fetchAll();
        
        $csv = "ID,Date,User,Email,Amount,Bonus Amount,Plan,Payment Method,Status,Transaction ID,Processed By,Processed At\n";
        
        foreach ($deposits as $deposit) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%.2f,%.2f,%s,%s,%s,%s,%s,%s\n",
                $deposit['id'],
                $deposit['created_at'],
                $deposit['username'] ?? '',
                $deposit['email'] ?? '',
                $deposit['amount'],
                $deposit['bonus_amount'],
                $deposit['plan_name'] ?? '',
                $deposit['payment_method_name'] ?? '',
                $deposit['status'],
                $deposit['transaction_id'] ?? '',
                $deposit['processed_by_username'] ?? '',
                $deposit['processed_at'] ?? ''
            );
        }
        
        return $csv;
    }
    
    /**
     * Export withdrawals to CSV
     */
    private function exportWithdrawalsCSV($dateFrom, $dateTo, $filters = []) {
        $db = getDB();
        
        $sql = "SELECT 
                    w.*, 
                    u.username, u.first_name, u.last_name, u.email,
                    admin.username as processed_by_username
                FROM withdrawals w
                LEFT JOIN users u ON w.user_id = u.id
                LEFT JOIN users admin ON w.processed_by = admin.id
                WHERE w.created_at >= :date_from AND w.created_at <= :date_to
                ORDER BY w.created_at DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['date_from' => $dateFrom, 'date_to' => $dateTo]);
        $withdrawals = $stmt->fetchAll();
        
        $csv = "ID,Date,User,Email,Amount,Method,Account Details,Status,Processed By,Processed At,Rejection Reason\n";
        
        foreach ($withdrawals as $withdrawal) {
            $csv .= sprintf(
                "%d,%s,%s,%s,%.2f,%s,\"%s\",%s,%s,%s,\"%s\"\n",
                $withdrawal['id'],
                $withdrawal['created_at'],
                $withdrawal['username'] ?? '',
                $withdrawal['email'] ?? '',
                $withdrawal['amount'],
                $withdrawal['withdrawal_method'],
                str_replace('"', '""', $withdrawal['account_details']),
                $withdrawal['status'],
                $withdrawal['processed_by_username'] ?? '',
                $withdrawal['processed_at'] ?? '',
                str_replace('"', '""', $withdrawal['rejection_reason'] ?? '')
            );
        }
        
        return $csv;
    }
    
    /**
     * Export summary report to CSV
     */
    private function exportSummaryCSV($dateFrom, $dateTo) {
        $summary = $this->getSummaryReport($dateFrom, $dateTo);
        
        $csv = "Report Period: {$dateFrom} to {$dateTo}\n\n";
        $csv .= "DEPOSITS SUMMARY\n";
        $csv .= "Total Count,Total Amount,Approved Amount,Pending Amount,Rejected Amount,Average Amount\n";
        $csv .= sprintf(
            "%d,%.2f,%.2f,%.2f,%.2f,%.2f\n\n",
            $summary['deposits']['count'],
            $summary['deposits']['total'],
            $summary['deposits']['approved_amount'],
            $summary['deposits']['pending_amount'],
            $summary['deposits']['rejected_amount'],
            $summary['deposits']['average_amount']
        );
        
        $csv .= "WITHDRAWALS SUMMARY\n";
        $csv .= "Total Count,Total Amount,Approved Amount,Pending Amount,Rejected Amount,Average Amount\n";
        $csv .= sprintf(
            "%d,%.2f,%.2f,%.2f,%.2f,%.2f\n\n",
            $summary['withdrawals']['count'],
            $summary['withdrawals']['total'],
            $summary['withdrawals']['approved_amount'],
            $summary['withdrawals']['pending_amount'],
            $summary['withdrawals']['rejected_amount'],
            $summary['withdrawals']['average_amount']
        );
        
        $csv .= "NET FLOW\n";
        $csv .= sprintf("%.2f\n\n", $summary['net_flow']);
        
        $csv .= "TOP USERS\n";
        $csv .= "Username,Name,Email,Total Deposits,Total Withdrawals,Transaction Count\n";
        foreach ($summary['top_users'] as $user) {
            $csv .= sprintf(
                "%s,%s %s,%s,%.2f,%.2f,%d\n",
                $user['username'],
                $user['first_name'],
                $user['last_name'],
                $user['email'],
                $user['total_deposits'],
                $user['total_withdrawals'],
                $user['transaction_count']
            );
        }
        
        return $csv;
    }
}
?>