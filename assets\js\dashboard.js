// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
        refreshDashboardData();
    }, 300000); // 5 minutes
    
    // Handle quick action clicks
    document.querySelectorAll('.btn[href]').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            // Add loading state
            this.classList.add('loading');
        });
    });
    
    // Format numbers with animation
    animateNumbers();
    
    // Initialize charts if needed
    initializeCharts();
});

function refreshDashboardData() {
    // This would typically make an AJAX call to refresh dashboard data
    console.log('Refreshing dashboard data...');
    
    // Example implementation:
    /*
    fetch('/api/dashboard/refresh')
        .then(response => response.json())
        .then(data => {
            updateBalanceCards(data);
            updateStatistics(data);
            updateRecentTransactions(data);
        })
        .catch(error => {
            console.error('Error refreshing dashboard:', error);
        });
    */
}

function animateNumbers() {
    const numberElements = document.querySelectorAll('.card-title');
    
    numberElements.forEach(function(element) {
        const text = element.textContent;
        const match = text.match(/\$?([\d,]+\.?\d*)/);
        
        if (match) {
            const finalValue = parseFloat(match[1].replace(/,/g, ''));
            animateValue(element, 0, finalValue, 1000, text.includes('$'));
        }
    });
}

function animateValue(element, start, end, duration, isCurrency = false) {
    const startTime = performance.now();
    const originalText = element.textContent;
    
    function updateValue(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const currentValue = start + (end - start) * easeOutQuart(progress);
        const formattedValue = isCurrency ? 
            '$' + currentValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) :
            Math.floor(currentValue).toLocaleString();
        
        element.textContent = formattedValue;
        
        if (progress < 1) {
            requestAnimationFrame(updateValue);
        } else {
            element.textContent = originalText; // Restore original formatting
        }
    }
    
    requestAnimationFrame(updateValue);
}

function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

function initializeCharts() {
    // Initialize any charts here
    // This could include balance history, transaction trends, etc.
    
    // Example with Chart.js (if included):
    /*
    const ctx = document.getElementById('balanceChart');
    if (ctx) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Balance',
                    data: [12, 19, 3, 5, 2, 3],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }
    */
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create and show a toast notification
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    document.body.appendChild(container);
    return container;
}

// Handle responsive behavior
function handleResponsive() {
    const isMobile = window.innerWidth < 768;
    
    // Adjust card layouts for mobile
    const balanceCards = document.querySelectorAll('.balance-card');
    balanceCards.forEach(function(card) {
        if (isMobile) {
            card.classList.add('text-center');
        } else {
            card.classList.remove('text-center');
        }
    });
}

// Listen for window resize
window.addEventListener('resize', handleResponsive);

// Initial responsive setup
handleResponsive();