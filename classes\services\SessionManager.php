<?php
require_once __DIR__ . '/../models/User.php';

/**
 * SessionManager - Handles secure session management
 */
class SessionManager {
    
    private static $sessionStarted = false;
    private static $sessionTimeout = 3600; // 1 hour default
    
    /**
     * Start secure session
     */
    public static function startSession() {
        if (self::$sessionStarted) {
            return true;
        }
        
        // Only configure session settings if session hasn't started yet
        if (session_status() === PHP_SESSION_NONE) {
            // Configure session settings
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            // Set session timeout from system settings if available
            if (class_exists('SystemSetting')) {
                $timeout = SystemSetting::getValue('session_timeout_minutes', 60);
                self::$sessionTimeout = $timeout * 60;
            }
            
            ini_set('session.gc_maxlifetime', self::$sessionTimeout);
        }
        
        // Start session
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        self::$sessionStarted = true;
        
        // Validate existing session
        if (isset($_SESSION['user_id'])) {
            if (!self::validateSession()) {
                self::destroySession();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Set user session data
     */
    public static function setUserSession($user) {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        // Regenerate session ID for security
        session_regenerate_id(true);
        
        $_SESSION['user_id'] = $user->getId();
        $_SESSION['username'] = $user->username;
        $_SESSION['email'] = $user->email;
        $_SESSION['role'] = $user->role;
        $_SESSION['first_name'] = $user->first_name;
        $_SESSION['last_name'] = $user->last_name;
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'] ?? '';
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Generate CSRF token
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        
        // Store session in database
        self::storeSessionInDatabase();
    }
    
    /**
     * Validate current session
     */
    public static function validateSession() {
        if (!self::$sessionStarted) {
            return false;
        }
        
        // Check if session variables exist
        if (!isset($_SESSION['user_id']) || !isset($_SESSION['last_activity'])) {
            return false;
        }
        
        // Check session timeout
        if (time() - $_SESSION['last_activity'] > self::$sessionTimeout) {
            return false;
        }
        
        // Check IP address consistency (optional security measure)
        $currentIP = $_SERVER['REMOTE_ADDR'] ?? '';
        if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $currentIP) {
            // Log potential session hijacking attempt
            error_log("Session IP mismatch: Expected {$_SESSION['ip_address']}, got {$currentIP}");
            return false;
        }
        
        // Check user agent consistency (optional security measure)
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (isset($_SESSION['user_agent']) && $_SESSION['user_agent'] !== $currentUserAgent) {
            // Log potential session hijacking attempt
            error_log("Session User-Agent mismatch");
            return false;
        }
        
        // Verify user still exists and is active
        $user = User::find($_SESSION['user_id']);
        if (!$user || !$user->isActive()) {
            return false;
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        // Update session in database
        self::updateSessionInDatabase();
        
        return true;
    }
    
    /**
     * Destroy session
     */
    public static function destroySession() {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        // Remove session from database
        self::removeSessionFromDatabase();
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // Destroy session
        session_destroy();
        
        self::$sessionStarted = false;
    }
    
    /**
     * Check if user is logged in
     */
    public static function isLoggedIn() {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        return isset($_SESSION['user_id']) && self::validateSession();
    }
    
    /**
     * Get current user ID
     */
    public static function getUserId() {
        return self::isLoggedIn() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * Get current user
     */
    public static function getCurrentUser() {
        $userId = self::getUserId();
        return $userId ? User::find($userId) : null;
    }
    
    /**
     * Get user role
     */
    public static function getUserRole() {
        return self::isLoggedIn() ? $_SESSION['role'] : null;
    }
    
    /**
     * Get CSRF token
     */
    public static function getCSRFToken() {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateCSRFToken($token) {
        if (!self::$sessionStarted) {
            return false;
        }
        
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Regenerate session ID
     */
    public static function regenerateSessionId() {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        session_regenerate_id(true);
        
        // Update session in database with new ID
        self::updateSessionInDatabase();
    }
    
    /**
     * Set session message (flash message)
     */
    public static function setMessage($message, $type = 'info') {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        $_SESSION['flash_message'] = [
            'message' => $message,
            'type' => $type
        ];
    }
    
    /**
     * Get and clear session message
     */
    public static function getMessage() {
        if (!self::$sessionStarted) {
            self::startSession();
        }
        
        if (isset($_SESSION['flash_message'])) {
            $message = $_SESSION['flash_message'];
            unset($_SESSION['flash_message']);
            return $message;
        }
        
        return null;
    }
    
    /**
     * Store session in database
     */
    private static function storeSessionInDatabase() {
        if (!isset($_SESSION['user_id'])) {
            return;
        }
        
        try {
            $db = getDB();
            
            // Remove any existing sessions for this user (single session per user)
            $stmt = $db->prepare("DELETE FROM user_sessions WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            
            // Insert new session
            $stmt = $db->prepare("
                INSERT INTO user_sessions (id, user_id, ip_address, user_agent, last_activity, created_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                session_id(),
                $_SESSION['user_id'],
                $_SESSION['ip_address'],
                $_SESSION['user_agent']
            ]);
        } catch (Exception $e) {
            error_log("Session storage failed: " . $e->getMessage());
        }
    }
    
    /**
     * Update session in database
     */
    private static function updateSessionInDatabase() {
        if (!isset($_SESSION['user_id'])) {
            return;
        }
        
        try {
            $db = getDB();
            $stmt = $db->prepare("
                UPDATE user_sessions 
                SET last_activity = NOW() 
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([session_id(), $_SESSION['user_id']]);
        } catch (Exception $e) {
            error_log("Session update failed: " . $e->getMessage());
        }
    }
    
    /**
     * Remove session from database
     */
    private static function removeSessionFromDatabase() {
        if (!isset($_SESSION['user_id'])) {
            return;
        }
        
        try {
            $db = getDB();
            $stmt = $db->prepare("DELETE FROM user_sessions WHERE id = ? OR user_id = ?");
            $stmt->execute([session_id(), $_SESSION['user_id']]);
        } catch (Exception $e) {
            error_log("Session removal failed: " . $e->getMessage());
        }
    }
    
    /**
     * Clean up expired sessions
     */
    public static function cleanupExpiredSessions() {
        try {
            $db = getDB();
            $stmt = $db->prepare("
                DELETE FROM user_sessions 
                WHERE last_activity < DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            
            $stmt->execute([self::$sessionTimeout]);
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Session cleanup failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get active sessions count
     */
    public static function getActiveSessionsCount() {
        try {
            $db = getDB();
            $stmt = $db->prepare("
                SELECT COUNT(*) FROM user_sessions 
                WHERE last_activity > DATE_SUB(NOW(), INTERVAL ? SECOND)
            ");
            
            $stmt->execute([self::$sessionTimeout]);
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("Active sessions count failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get user's active sessions
     */
    public static function getUserSessions($userId) {
        try {
            $db = getDB();
            $stmt = $db->prepare("
                SELECT * FROM user_sessions 
                WHERE user_id = ? AND last_activity > DATE_SUB(NOW(), INTERVAL ? SECOND)
                ORDER BY last_activity DESC
            ");
            
            $stmt->execute([$userId, self::$sessionTimeout]);
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("User sessions retrieval failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Terminate user session by session ID
     */
    public static function terminateSession($sessionId) {
        try {
            $db = getDB();
            $stmt = $db->prepare("DELETE FROM user_sessions WHERE id = ?");
            $stmt->execute([$sessionId]);
            
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            error_log("Session termination failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Terminate all user sessions except current
     */
    public static function terminateOtherSessions($userId) {
        try {
            $db = getDB();
            $stmt = $db->prepare("DELETE FROM user_sessions WHERE user_id = ? AND id != ?");
            $stmt->execute([$userId, session_id()]);
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Other sessions termination failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get session timeout in seconds
     */
    public static function getSessionTimeout() {
        return self::$sessionTimeout;
    }
    
    /**
     * Set session timeout
     */
    public static function setSessionTimeout($timeout) {
        self::$sessionTimeout = $timeout;
        ini_set('session.gc_maxlifetime', $timeout);
    }
    
    /**
     * Check if session is about to expire
     */
    public static function isSessionExpiringSoon($warningTime = 300) { // 5 minutes default
        if (!self::isLoggedIn()) {
            return false;
        }
        
        $timeLeft = self::$sessionTimeout - (time() - $_SESSION['last_activity']);
        return $timeLeft <= $warningTime && $timeLeft > 0;
    }
    
    /**
     * Get time until session expires
     */
    public static function getTimeUntilExpiry() {
        if (!self::isLoggedIn()) {
            return 0;
        }
        
        return max(0, self::$sessionTimeout - (time() - $_SESSION['last_activity']));
    }
    
    /**
     * Extend session
     */
    public static function extendSession() {
        if (self::isLoggedIn()) {
            $_SESSION['last_activity'] = time();
            self::updateSessionInDatabase();
            return true;
        }
        
        return false;
    }
}
?>