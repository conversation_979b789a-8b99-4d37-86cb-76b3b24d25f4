<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/TradingPlan.php';
require_once '../../../classes/services/AuditTrailService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get form data
    $name = trim($_POST['name'] ?? '');
    $minDeposit = floatval($_POST['min_deposit'] ?? 0);
    $maxDeposit = !empty($_POST['max_deposit']) ? floatval($_POST['max_deposit']) : null;
    $dailyReturn = floatval($_POST['daily_return'] ?? 0);
    $durationDays = intval($_POST['duration_days'] ?? 0);
    $status = $_POST['status'] ?? 'active';
    $description = trim($_POST['description'] ?? '');
    $features = json_decode($_POST['features'] ?? '[]', true);
    
    // Create new trading plan
    $plan = new TradingPlan();
    $plan->name = $name;
    $plan->min_deposit = $minDeposit;
    $plan->max_deposit = $maxDeposit;
    $plan->daily_return = $dailyReturn;
    $plan->duration_days = $durationDays;
    $plan->status = $status;
    $plan->description = $description;
    $plan->features = $features;
    
    // Validate the plan
    $errors = $plan->validate();
    
    if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // Save the plan
    if ($plan->save()) {
        // Log the action
        AuditTrailService::log(
            'trading_plan_created',
            'trading_plan',
            $plan->getId(),
            [
                'name' => $name,
                'min_deposit' => $minDeposit,
                'max_deposit' => $maxDeposit,
                'daily_return' => $dailyReturn,
                'duration_days' => $durationDays,
                'status' => $status
            ]
        );
        
        echo json_encode([
            'success' => true,
            'message' => 'Trading plan created successfully',
            'plan_id' => $plan->getId()
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create trading plan'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Create trading plan error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while creating the trading plan'
    ]);
}
?>