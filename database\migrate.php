<?php
/**
 * Database Migration Script
 * Complete database setup with schema creation and data seeding
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/MigrationLogger.php';

// Start session for displaying results
session_start();

class DatabaseMigrator {
    private $db;
    private $logger;
    
    // Configuration constants
    private const SCHEMA_FILE = 'schema.sql';
    private const SEED_FILE = 'seed.sql';
    private const REQUIRED_TABLES = [
        'users', 'trading_plans', 'payment_methods', 'deposits', 
        'withdrawals', 'transactions', 'support_tickets', 
        'system_settings', 'audit_logs', 'email_templates',
        'user_sessions', 'notifications'
    ];
    
    public function __construct() {
        $this->logger = new MigrationLogger();
        
        try {
            $this->db = getDB();
            $this->validateConnection();
        } catch (Exception $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Run complete migration process
     */
    public function migrate() {
        echo "<h1>Crypto Trading Platform - Database Migration</h1>";
        echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>";
        
        // Step 1: Create schema
        $this->createSchema();
        
        // Step 2: Seed data
        $this->seedData();
        
        // Step 3: Create indexes and optimize
        $this->optimizeDatabase();
        
        // Step 4: Verify installation
        $this->verifyInstallation();
        
        // Display results
        $this->logger->displayResults();
        
        echo "</div>";
    }
    
    /**
     * Create database schema from SQL file
     */
    private function createSchema() {
        echo "<h2>🔧 Creating Database Schema...</h2>";
        
        try {
            $schemaSQL = file_get_contents(__DIR__ . '/' . self::SCHEMA_FILE);
            if (!$schemaSQL) {
                throw new Exception("Could not read schema.sql file");
            }
            
            // Begin transaction for schema creation
            $this->db->beginTransaction();
            
            try {
                // Split SQL into individual statements
                $statements = $this->splitSQL($schemaSQL);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (empty($statement) || strpos($statement, '--') === 0) {
                        continue;
                    }
                    
                    $this->db->exec($statement);
                }
                
                $this->db->commit();
                $this->logger->logSuccess("✅ Database schema created successfully");
                
            } catch (PDOException $e) {
                $this->db->rollback();
                throw new Exception("Schema creation failed: " . $e->getMessage());
            }
            
        } catch (Exception $e) {
            $this->logger->logError("Schema Creation Failed: " . $e->getMessage());
        }
    }
    
    /**
     * Seed database with default data
     */
    private function seedData() {
        echo "<h2>🌱 Seeding Database...</h2>";
        
        try {
            $seedSQL = file_get_contents(__DIR__ . '/' . self::SEED_FILE);
            if (!$seedSQL) {
                throw new Exception("Could not read seed.sql file");
            }
            
            // Split SQL into individual statements
            $statements = $this->splitSQL($seedSQL);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue;
                }
                
                try {
                    $this->db->exec($statement);
                } catch (PDOException $e) {
                    // Log error but continue with other statements
                    $this->logger->logError("Seed Error: " . $e->getMessage());
                }
            }
            
            $this->logger->logSuccess("✅ Database seeded with default data");
            
        } catch (Exception $e) {
            $this->logger->logError("Data Seeding Failed: " . $e->getMessage());
        }
    }
    
    /**
     * Optimize database with additional indexes
     */
    private function optimizeDatabase() {
        echo "<h2>⚡ Optimizing Database...</h2>";
        
        $optimizations = [
            "ANALYZE TABLE users, trading_plans, deposits, withdrawals, transactions, support_tickets",
            "OPTIMIZE TABLE users, trading_plans, deposits, withdrawals, transactions, support_tickets"
        ];
        
        foreach ($optimizations as $sql) {
            try {
                $this->db->exec($sql);
            } catch (PDOException $e) {
                $this->logger->logError("Optimization Warning: " . $e->getMessage());
            }
        }
        
        $this->logger->logSuccess("✅ Database optimized");
    }
    
    /**
     * Verify installation by checking tables and data
     */
    private function verifyInstallation() {
        echo "<h2>🔍 Verifying Installation...</h2>";
        
        $this->verifyTables();
        $this->verifyDefaultData();
    }
    
    /**
     * Verify all required tables exist
     */
    private function verifyTables() {
        $requiredTables = self::REQUIRED_TABLES;
        
        // Use INFORMATION_SCHEMA for efficient table checking
        $placeholders = str_repeat('?,', count($requiredTables) - 1) . '?';
        $sql = "SELECT TABLE_NAME, TABLE_ROWS 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ($placeholders)";
        
        try {
            $stmt = $this->db->prepare($sql);
            $params = array_merge([DB_NAME], $requiredTables);
            $stmt->execute($params);
            $existingTables = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            foreach ($requiredTables as $table) {
                if (isset($existingTables[$table])) {
                    $count = $existingTables[$table] ?? 0;
                    $this->logger->logSuccess("✅ Table '$table' exists with $count records");
                } else {
                    $this->logger->logError("❌ Table '$table' is missing or inaccessible");
                }
            }
        } catch (PDOException $e) {
            $this->logger->logError("❌ Could not verify tables: " . $e->getMessage());
        }
    }
    
    /**
     * Verify default data exists
     */
    private function verifyDefaultData() {
        $verifications = [
            [
                'query' => "SELECT COUNT(*) FROM users WHERE role = 'superadmin'",
                'success_msg' => "✅ Super admin user created successfully",
                'error_msg' => "❌ No super admin user found"
            ],
            [
                'query' => "SELECT COUNT(*) FROM trading_plans WHERE status = 'active'",
                'success_msg' => "✅ {count} active trading plans created",
                'error_msg' => "❌ No active trading plans found"
            ],
            [
                'query' => "SELECT COUNT(*) FROM payment_methods WHERE status = 'active'",
                'success_msg' => "✅ {count} payment methods configured",
                'error_msg' => "❌ No payment methods found"
            ]
        ];
        
        foreach ($verifications as $verification) {
            try {
                $stmt = $this->db->prepare($verification['query']);
                $stmt->execute();
                $count = $stmt->fetchColumn();
                
                if ($count > 0) {
                    $message = str_replace('{count}', $count, $verification['success_msg']);
                    $this->logger->logSuccess($message);
                } else {
                    $this->logger->logError($verification['error_msg']);
                }
            } catch (PDOException $e) {
                $this->logger->logError("❌ Verification error: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Split SQL file into individual statements
     */
    private function splitSQL($sql) {
        if (empty($sql)) {
            throw new InvalidArgumentException("SQL content cannot be empty");
        }
        
        // Remove comments and split by semicolon
        $sql = preg_replace('/--.*$/m', '', $sql);
        $statements = explode(';', $sql);
        
        return array_filter($statements, function($stmt) {
            return !empty(trim($stmt));
        });
    }
    
    /**
     * Validate database connection
     */
    private function validateConnection() {
        if (!$this->db) {
            throw new Exception("Database connection is not established");
        }
        
        try {
            $this->db->query("SELECT 1");
        } catch (PDOException $e) {
            throw new Exception("Database connection is not working: " . $e->getMessage());
        }
    }
    

}

// Run migration if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'migrate.php') {
    $migrator = new DatabaseMigrator();
    $migrator->migrate();
}
?>