<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/SessionManager.php';
require_once '../../../classes/services/CSRFProtection.php';
require_once '../../../classes/services/NotificationService.php';
require_once '../../../classes/models/Withdrawal.php';
require_once '../../../classes/models/User.php';

header('Content-Type: application/json');

// Check authentication
SessionManager::startSession();
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check CSRF token
if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

// Get form data
$withdrawalId = (int)($_POST['withdrawal_id'] ?? 0);
$adminNote = trim($_POST['admin_note'] ?? '');

if (!$withdrawalId) {
    echo json_encode(['success' => false, 'message' => 'Invalid withdrawal ID']);
    exit;
}

try {
    // Get withdrawal
    $withdrawal = Withdrawal::find($withdrawalId);
    
    if (!$withdrawal) {
        echo json_encode(['success' => false, 'message' => 'Withdrawal not found']);
        exit;
    }
    
    // Check if withdrawal is pending
    if (!$withdrawal->isPending()) {
        echo json_encode(['success' => false, 'message' => 'Only pending withdrawals can be approved']);
        exit;
    }
    
    // Get current admin
    $currentUser = SessionManager::getCurrentUser();
    
    // Add admin note if provided
    if ($adminNote) {
        $withdrawal->admin_note = $adminNote;
    }
    
    // Approve the withdrawal
    if ($withdrawal->approve($currentUser->getId())) {
        // Send notification to user
        try {
            $user = $withdrawal->getUser();
            if ($user) {
                NotificationService::sendWithdrawalApprovalNotification($user, $withdrawal);
            }
        } catch (Exception $e) {
            error_log("Failed to send withdrawal approval notification: " . $e->getMessage());
            // Don't fail the approval if notification fails
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Withdrawal approved successfully. User balance has been updated.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to approve withdrawal. Please check user balance and try again.'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Error approving withdrawal: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while approving the withdrawal']);
}
?>