<?php
require_once __DIR__ . '/BaseModel.php';

/**
 * Withdrawal Model - Handles withdrawal data and operations
 */
class Withdrawal extends BaseModel {
    protected $table = 'withdrawals';
    protected $fillable = [
        'user_id', 'amount', 'withdrawal_method', 'account_details',
        'status', 'admin_note', 'processed_by', 'processed_at'
    ];
    
    // Withdrawal statuses
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    
    /**
     * Validation rules
     */
    public function validate() {
        $errors = [];
        
        // User ID validation
        if (empty($this->user_id)) {
            $errors['user_id'] = 'User ID is required';
        } elseif (!is_numeric($this->user_id)) {
            $errors['user_id'] = 'User ID must be numeric';
        }
        
        // Amount validation
        if (empty($this->amount)) {
            $errors['amount'] = 'Amount is required';
        } elseif (!is_numeric($this->amount)) {
            $errors['amount'] = 'Amount must be numeric';
        } elseif ($this->amount <= 0) {
            $errors['amount'] = 'Amount must be greater than zero';
        } elseif ($this->amount > *********.99) {
            $errors['amount'] = 'Amount is too large';
        }
        
        // Withdrawal method validation
        if (empty($this->withdrawal_method)) {
            $errors['withdrawal_method'] = 'Withdrawal method is required';
        } elseif (strlen($this->withdrawal_method) > 50) {
            $errors['withdrawal_method'] = 'Withdrawal method must not exceed 50 characters';
        }
        
        // Account details validation
        if (empty($this->account_details)) {
            $errors['account_details'] = 'Account details are required';
        } elseif (!is_string($this->account_details) && !is_array($this->account_details)) {
            $errors['account_details'] = 'Account details must be valid';
        }
        
        // Status validation
        if (!empty($this->status) && !in_array($this->status, [
            self::STATUS_PENDING, self::STATUS_APPROVED, self::STATUS_REJECTED,
            self::STATUS_PROCESSING, self::STATUS_COMPLETED
        ])) {
            $errors['status'] = 'Invalid status';
        }
        
        // Balance validation (check if user has sufficient balance)
        if (!empty($this->user_id) && !empty($this->amount) && !$this->hasSufficientBalance()) {
            $errors['amount'] = 'Insufficient balance for withdrawal';
        }
        
        return $errors;
    }
    
    /**
     * Check if user has sufficient balance
     */
    private function hasSufficientBalance() {
        require_once __DIR__ . '/User.php';
        
        $user = User::find($this->user_id);
        if (!$user) {
            return false;
        }
        
        return $user->balance >= $this->amount;
    }
    
    /**
     * Override save to handle JSON encoding of account_details
     */
    public function save() {
        if (is_array($this->account_details)) {
            $this->account_details = json_encode($this->account_details);
        }
        
        return parent::save();
    }
    
    /**
     * Get account details as array
     */
    public function getAccountDetailsArray() {
        if (is_string($this->account_details)) {
            return json_decode($this->account_details, true) ?? [];
        }
        
        return $this->account_details ?? [];
    }
    
    /**
     * Get the user who made this withdrawal
     */
    public function getUser() {
        require_once __DIR__ . '/User.php';
        return User::find($this->user_id);
    }
    
    /**
     * Get the admin who processed this withdrawal
     */
    public function getProcessedBy() {
        if (!$this->processed_by) {
            return null;
        }
        
        require_once __DIR__ . '/User.php';
        return User::find($this->processed_by);
    }
    
    /**
     * Approve the withdrawal
     */
    public function approve($adminId) {
        $this->beginTransaction();
        
        try {
            // Check if user still has sufficient balance
            $user = $this->getUser();
            if (!$user) {
                throw new Exception('User not found');
            }
            
            if ($user->balance < $this->amount) {
                throw new Exception('Insufficient balance');
            }
            
            // Update withdrawal status
            $this->status = self::STATUS_APPROVED;
            $this->processed_by = $adminId;
            $this->processed_at = date('Y-m-d H:i:s');
            
            if (!$this->save()) {
                throw new Exception('Failed to update withdrawal');
            }
            
            // Deduct amount from user balance
            $user->balance -= $this->amount;
            $user->total_withdrawal += $this->amount;
            
            if (!$user->save()) {
                throw new Exception('Failed to update user balance');
            }
            
            // Create transaction record
            $this->createTransaction($user);
            
            $this->commit();
            return true;
            
        } catch (Exception $e) {
            $this->rollback();
            error_log("Withdrawal approval error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reject the withdrawal
     */
    public function reject($adminId, $reason = null) {
        $this->status = self::STATUS_REJECTED;
        $this->processed_by = $adminId;
        $this->processed_at = date('Y-m-d H:i:s');
        
        if ($reason) {
            $this->admin_note = $reason;
        }
        
        return $this->save();
    }
    
    /**
     * Mark withdrawal as processing
     */
    public function markAsProcessing($adminId) {
        $this->status = self::STATUS_PROCESSING;
        $this->processed_by = $adminId;
        $this->processed_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }
    
    /**
     * Mark withdrawal as completed
     */
    public function markAsCompleted($adminId) {
        $this->status = self::STATUS_COMPLETED;
        $this->processed_by = $adminId;
        $this->processed_at = date('Y-m-d H:i:s');
        
        return $this->save();
    }
    
    /**
     * Create transaction record
     */
    private function createTransaction($user) {
        require_once __DIR__ . '/Transaction.php';
        
        $transaction = new Transaction();
        $transaction->user_id = $user->getId();
        $transaction->type = 'withdrawal';
        $transaction->amount = $this->amount;
        $transaction->balance_before = $user->balance + $this->amount;
        $transaction->balance_after = $user->balance;
        $transaction->description = "Withdrawal approved - Amount: $" . number_format($this->amount, 2) . 
                                   " via " . $this->withdrawal_method;
        $transaction->reference_id = $this->getId();
        $transaction->reference_type = 'withdrawal';
        $transaction->status = 'completed';
        $transaction->processed_by = $this->processed_by;
        
        return $transaction->save();
    }
    
    /**
     * Check if withdrawal is pending
     */
    public function isPending() {
        return $this->status === self::STATUS_PENDING;
    }
    
    /**
     * Check if withdrawal is approved
     */
    public function isApproved() {
        return $this->status === self::STATUS_APPROVED;
    }
    
    /**
     * Check if withdrawal is rejected
     */
    public function isRejected() {
        return $this->status === self::STATUS_REJECTED;
    }
    
    /**
     * Check if withdrawal is processing
     */
    public function isProcessing() {
        return $this->status === self::STATUS_PROCESSING;
    }
    
    /**
     * Check if withdrawal is completed
     */
    public function isCompleted() {
        return $this->status === self::STATUS_COMPLETED;
    }
    
    /**
     * Get withdrawals by status
     */
    public static function getByStatus($status, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE status = :status ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $results = $stmt->fetchAll();
        
        $withdrawals = [];
        foreach ($results as $data) {
            $withdrawals[] = new static($data);
        }
        
        return $withdrawals;
    }
    
    /**
     * Get withdrawals by user
     */
    public static function getByUser($userId, $limit = null, $offset = 0) {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE user_id = :user_id ORDER BY created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['user_id' => $userId]);
        $results = $stmt->fetchAll();
        
        $withdrawals = [];
        foreach ($results as $data) {
            $withdrawals[] = new static($data);
        }
        
        return $withdrawals;
    }
    
    /**
     * Get pending withdrawals count
     */
    public static function getPendingCount() {
        return static::count("status = 'pending'");
    }
    
    /**
     * Get total withdrawals amount by status
     */
    public static function getTotalAmountByStatus($status) {
        $instance = new static();
        $sql = "SELECT COALESCE(SUM(amount), 0) as total FROM {$instance->table} WHERE status = :status";
        
        $stmt = $instance->db->prepare($sql);
        $stmt->execute(['status' => $status]);
        $result = $stmt->fetch();
        
        return (float) $result['total'];
    }
    
    /**
     * Get withdrawals with user information
     */
    public static function getWithDetails($limit = null, $offset = 0, $status = null) {
        $instance = new static();
        $sql = "SELECT w.*, u.username, u.first_name, u.last_name, u.email, u.balance
                FROM {$instance->table} w
                LEFT JOIN users u ON w.user_id = u.id";
        
        if ($status) {
            $sql .= " WHERE w.status = :status";
        }
        
        $sql .= " ORDER BY w.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }
        
        $stmt = $instance->db->prepare($sql);
        $params = [];
        if ($status) {
            $params['status'] = $status;
        }
        
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get minimum withdrawal amount from system settings
     */
    public static function getMinimumAmount() {
        $db = getDB();
        $sql = "SELECT setting_value FROM system_settings WHERE setting_key = 'minimum_withdrawal_amount'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        return $result ? (float) $result['setting_value'] : 10.00; // Default minimum
    }
    
    /**
     * Check if user can make withdrawal (daily/monthly limits, etc.)
     */
    public static function canUserWithdraw($userId, $amount) {
        require_once __DIR__ . '/User.php';
        
        $user = User::find($userId);
        if (!$user || !$user->isActive()) {
            return ['can_withdraw' => false, 'reason' => 'User account is not active'];
        }
        
        if ($user->balance < $amount) {
            return ['can_withdraw' => false, 'reason' => 'Insufficient balance'];
        }
        
        if ($amount < static::getMinimumAmount()) {
            return ['can_withdraw' => false, 'reason' => 'Amount below minimum withdrawal limit'];
        }
        
        // Check for pending withdrawals
        $pendingCount = static::count("user_id = ? AND status = 'pending'", [$userId]);
        if ($pendingCount > 0) {
            return ['can_withdraw' => false, 'reason' => 'You have pending withdrawal requests'];
        }
        
        // Additional checks can be added here (daily limits, KYC verification, etc.)
        
        return ['can_withdraw' => true, 'reason' => ''];
    }
}
?>