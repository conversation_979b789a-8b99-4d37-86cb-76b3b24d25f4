<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/models/PaymentMethod.php';
require_once '../../../classes/services/CSRFProtection.php';

header('Content-Type: application/json');

// Check admin authentication
if (!isLoggedIn() || !isAdmin()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Verify CSRF token
    if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        exit;
    }
    
    // Get payment method ID and new status
    $id = (int)($_POST['id'] ?? 0);
    $status = trim($_POST['status'] ?? '');
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid payment method ID']);
        exit;
    }
    
    if (!in_array($status, ['active', 'inactive'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        exit;
    }
    
    // Load payment method
    $paymentMethod = PaymentMethod::find($id);
    if (!$paymentMethod) {
        echo json_encode(['success' => false, 'message' => 'Payment method not found']);
        exit;
    }
    
    // Update status
    $paymentMethod->status = $status;
    
    if ($paymentMethod->save()) {
        $action = $status === 'active' ? 'activated' : 'deactivated';
        echo json_encode([
            'success' => true,
            'message' => "Payment method {$action} successfully",
            'data' => [
                'id' => $paymentMethod->getId(),
                'status' => $paymentMethod->status
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update payment method status'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Payment method status toggle error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while updating the status'
    ]);
}
?>