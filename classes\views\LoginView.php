<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/../services/CSRFProtection.php';

/**
 * Login View
 * Handles the presentation of the login page
 */
class LoginView extends BaseView {
    private $error;
    private $success;
    private $formData;
    
    public function __construct($error = '', $success = '', $formData = []) {
        $this->error = $error;
        $this->success = $success;
        $this->formData = $formData;
        parent::__construct('Login');
    }
    
    protected function renderCustomStyles() {
        ?>
        <style>
            body {
                background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
            }
            .login-container {
                background: white;
                border-radius: 15px;
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                overflow: hidden;
                max-width: 450px;
                width: 100%;
            }
            .login-header {
                background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
                color: white;
                padding: 2rem;
                text-align: center;
            }
            .login-form {
                padding: 2rem;
            }
            .form-control {
                border-radius: 10px;
                border: 2px solid #e9ecef;
                padding: 12px 15px;
                transition: all 0.3s ease;
            }
            .form-control:focus {
                border-color: var(--bs-primary);
                box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
            }
            .btn-login {
                background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-secondary) 100%);
                border: none;
                border-radius: 10px;
                padding: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                transition: all 0.3s ease;
            }
            .btn-login:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(var(--bs-primary-rgb), 0.4);
            }
            .divider {
                text-align: center;
                margin: 1.5rem 0;
                position: relative;
            }
            .divider::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 0;
                right: 0;
                height: 1px;
                background: #e9ecef;
            }
            .divider span {
                background: white;
                padding: 0 1rem;
                color: #6c757d;
            }
        </style>
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="login-container mx-auto">
                        <?php $this->renderHeader(); ?>
                        <?php $this->renderForm(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderHeader() {
        ?>
        <div class="login-header">
            <h2><i class="fas fa-chart-line me-2"></i><?php echo htmlspecialchars(SITE_NAME ?? 'Crypto Trading'); ?></h2>
            <p class="mb-0">Welcome back! Please sign in to your account</p>
        </div>
        <?php
    }
    
    private function renderForm() {
        ?>
        <div class="login-form">
            <?php $this->renderAlerts(); ?>
            
            <form method="POST" action="" id="loginForm">
                <?php echo CSRFProtection::getTokenField(); ?>
                
                <div class="mb-3">
                    <label for="identifier" class="form-label">
                        <i class="fas fa-user me-2"></i>Username or Email
                    </label>
                    <input type="text" class="form-control" id="identifier" name="identifier" 
                           value="<?php echo htmlspecialchars($this->formData['identifier'] ?? ''); ?>" 
                           required autocomplete="username" maxlength="100">
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Password
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="password" name="password" 
                               required autocomplete="current-password" maxlength="255">
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                Remember me
                            </label>
                        </div>
                    </div>
                    <div class="col-6 text-end">
                        <a href="<?php echo Routes::get('forgot_password'); ?>" class="text-decoration-none">
                            Forgot password?
                        </a>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                </button>
            </form>
            
            <div class="divider">
                <span>Don't have an account?</span>
            </div>
            
            <div class="text-center">
                <a href="<?php echo Routes::get('register'); ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </a>
            </div>
        </div>
        <?php
    }
    
    private function renderAlerts() {
        if ($this->error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($this->error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif;
        
        if ($this->success): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($this->success); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif;
    }
    
    protected function renderCustomScripts() {
        ?>
        <script>
            // Toggle password visibility
            document.getElementById('togglePassword').addEventListener('click', function() {
                const password = document.getElementById('password');
                const icon = this.querySelector('i');
                
                if (password.type === 'password') {
                    password.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    password.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            });
            
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
            
            // Form validation
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                const identifier = document.getElementById('identifier').value.trim();
                const password = document.getElementById('password').value;
                
                if (!identifier || !password) {
                    e.preventDefault();
                    alert('Please fill in all required fields.');
                    return false;
                }
            });
        </script>
        <?php
    }
    
    /**
     * Render 2FA verification page
     */
    public function render2FAVerification() {
        $this->renderHead();
        ?>
        <body>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6">
                        <div class="login-container">
                            <div class="login-header">
                                <h2><i class="fas fa-shield-alt me-2"></i>Two-Factor Authentication</h2>
                                <p class="mb-0">Enter your 6-digit authentication code</p>
                            </div>
                            
                            <div class="login-form">
                                <?php if (!empty($this->data['errors'])): ?>
                                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                        <?php foreach ($this->data['errors'] as $error): ?>
                                            <div><?= htmlspecialchars($error) ?></div>
                                        <?php endforeach; ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($this->data['message'])): ?>
                                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                                        <?= htmlspecialchars($this->data['message']) ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                <?php endif; ?>
                                
                                <form method="POST" id="twoFAForm">
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($this->data['csrf_token']) ?>">
                                    
                                    <div class="mb-3">
                                        <label for="code" class="form-label">Authentication Code</label>
                                        <input type="text" 
                                               class="form-control text-center" 
                                               id="code" 
                                               name="code" 
                                               placeholder="000000"
                                               maxlength="6"
                                               pattern="[0-9]{6}"
                                               autocomplete="one-time-code"
                                               required>
                                        <div class="form-text">Enter the 6-digit code from your authenticator app</div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="fas fa-check me-2"></i>Verify Code
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-link" id="useBackupCode">
                                        Use backup code instead
                                    </button>
                                </div>
                                
                                <!-- Backup code form (hidden by default) -->
                                <form method="POST" id="backupCodeForm" style="display: none;">
                                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($this->data['csrf_token']) ?>">
                                    <input type="hidden" name="use_backup_code" value="1">
                                    
                                    <div class="mb-3">
                                        <label for="backup_code" class="form-label">Backup Code</label>
                                        <input type="text" 
                                               class="form-control text-center" 
                                               id="backup_code" 
                                               name="code" 
                                               placeholder="Enter backup code"
                                               maxlength="8"
                                               style="text-transform: uppercase;">
                                        <div class="form-text">Enter one of your backup codes</div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-warning btn-lg">
                                            <i class="fas fa-key me-2"></i>Use Backup Code
                                        </button>
                                    </div>
                                </form>
                                
                                <div class="text-center mt-3">
                                    <a href="/login.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Login
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <script>
                // Auto-focus on code input
                document.getElementById('code').focus();
                
                // Format code input (numbers only)
                document.getElementById('code').addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                // Auto-submit when 6 digits entered
                document.getElementById('code').addEventListener('input', function(e) {
                    if (this.value.length === 6) {
                        document.getElementById('twoFAForm').submit();
                    }
                });
                
                // Toggle backup code form
                document.getElementById('useBackupCode').addEventListener('click', function() {
                    const normalForm = document.getElementById('twoFAForm');
                    const backupForm = document.getElementById('backupCodeForm');
                    
                    if (backupForm.style.display === 'none') {
                        normalForm.style.display = 'none';
                        backupForm.style.display = 'block';
                        document.getElementById('backup_code').focus();
                        this.textContent = 'Use authenticator code instead';
                    } else {
                        normalForm.style.display = 'block';
                        backupForm.style.display = 'none';
                        document.getElementById('code').focus();
                        this.textContent = 'Use backup code instead';
                    }
                });
                
                // Format backup code input
                document.getElementById('backup_code').addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
                });
                
                // Auto-dismiss alerts
                setTimeout(function() {
                    const alerts = document.querySelectorAll('.alert');
                    alerts.forEach(function(alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    });
                }, 5000);
            </script>
        </body>
        </html>
        <?php
    }
}
?>