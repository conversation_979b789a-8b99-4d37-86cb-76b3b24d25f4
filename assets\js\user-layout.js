/**
 * User Layout JavaScript
 * Handles sidebar toggle and UI interactions
 */
class UserLayout {
    constructor() {
        this.init();
    }

    init() {
        this.initSidebarToggle();
        this.initAutoHideAlerts();
        this.initThemeColors();
    }

    /**
     * Initialize mobile sidebar toggle functionality
     */
    initSidebarToggle() {
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        
        if (sidebarToggle && sidebar && sidebarOverlay) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                sidebarOverlay.classList.toggle('show');
            });
            
            sidebarOverlay.addEventListener('click', () => {
                sidebar.classList.remove('show');
                sidebarOverlay.classList.remove('show');
            });

            // Close sidebar on escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                }
            });
        }
    }

    /**
     * Auto-hide alerts after specified time
     */
    initAutoHideAlerts() {
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach((alert) => {
                if (window.bootstrap && window.bootstrap.Alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);
    }

    /**
     * Initialize dynamic theme colors
     */
    initThemeColors() {
        const themeColors = window.themeColors || {};
        const root = document.documentElement;
        
        Object.entries(themeColors).forEach(([key, value]) => {
            root.style.setProperty(`--${key.replace('_', '-')}-color`, value);
        });
    }

    /**
     * Update user balance display with animation
     */
    updateBalance(newBalance) {
        const balanceElement = document.querySelector('.balance-amount');
        if (balanceElement) {
            balanceElement.style.transition = 'all 0.3s ease';
            balanceElement.style.transform = 'scale(1.1)';
            
            setTimeout(() => {
                balanceElement.textContent = newBalance;
                balanceElement.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * Show notification toast
     */
    showNotification(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        const toast = this.createToast(message, type);
        
        toastContainer.appendChild(toast);
        
        if (window.bootstrap && window.bootstrap.Toast) {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
    }

    /**
     * Create toast container if it doesn't exist
     */
    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Create toast element
     */
    createToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        return toast;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.userLayout = new UserLayout();
});