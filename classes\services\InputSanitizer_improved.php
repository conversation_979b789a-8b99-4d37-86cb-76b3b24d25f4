<?php
require_once __DIR__ . '/../validators/ValidationHelper.php';

/**
 * Input Sanitizer Service
 * 
 * Provides context-aware input sanitization with security-first approach.
 * Uses strategy pattern for extensible sanitization rules.
 */
class InputSanitizer {
    
    private const MAX_INPUT_LENGTH = 10000;
    private const MAX_ARRAY_DEPTH = 5;
    private const SKIPPED_FIELDS = ['password', 'current_password', 'new_password', 'password_confirmation', 'csrf_token'];
    
    private static $strategies = [];
    private static $config = null;
    
    /**
     * Initialize sanitization strategies
     */
    private static function initStrategies() {
        if (empty(self::$strategies)) {
            self::$strategies = [
                'financial' => new FinancialSanitizationStrategy(),
                'user_profile' => new ProfileSanitizationStrategy(),
                'search' => new SearchSanitizationStrategy(),
                'admin' => new AdminSanitizationStrategy(),
                'general' => new GeneralSanitizationStrategy(),
            ];
        }
    }
    
    /**
     * Sanitize form data with context-specific rules
     */
    public static function sanitizeFormData(array $data, string $context = 'general'): array {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            $sanitized[$key] = self::sanitizeFieldValue($value, $key, $context);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize individual field value
     */
    public static function sanitizeFieldValue($value, string $fieldName, string $context = 'general') {
        if (self::shouldSkipSanitization($fieldName)) {
            return $value;
        }
        
        self::validateInput($value, $fieldName);
        
        if (is_array($value)) {
            return self::sanitizeArrayValue($value, $fieldName, $context);
        }
        
        return self::applySanitizationStrategy((string) $value, $fieldName, $context);
    }
    
    /**
     * Check if field should skip sanitization
     */
    private static function shouldSkipSanitization(string $fieldName): bool {
        return in_array($fieldName, self::SKIPPED_FIELDS);
    }
    
    /**
     * Validate input before sanitization
     */
    private static function validateInput($value, string $fieldName): void {
        if (is_string($value) && strlen($value) > self::MAX_INPUT_LENGTH) {
            throw new InvalidArgumentException("Input too large for field: {$fieldName}");
        }
        
        if (is_array($value) && self::getArrayDepth($value) > self::MAX_ARRAY_DEPTH) {
            throw new InvalidArgumentException("Array too deeply nested for field: {$fieldName}");
        }
    }
    
    /**
     * Sanitize array values recursively
     */
    private static function sanitizeArrayValue(array $value, string $fieldName, string $context): array {
        return array_map(function($item) use ($fieldName, $context) {
            return self::sanitizeFieldValue($item, $fieldName, $context);
        }, $value);
    }
    
    /**
     * Apply sanitization strategy based on context
     */
    private static function applySanitizationStrategy(string $value, string $fieldName, string $context): string {
        self::initStrategies();
        
        $strategy = self::$strategies[$context] ?? self::$strategies['general'];
        return $strategy->sanitize($value, $fieldName);
    }
    
    /**
     * Calculate array depth
     */
    private static function getArrayDepth(array $array): int {
        $maxDepth = 1;
        foreach ($array as $value) {
            if (is_array($value)) {
                $depth = self::getArrayDepth($value) + 1;
                if ($depth > $maxDepth) {
                    $maxDepth = $depth;
                }
            }
        }
        return $maxDepth;
    }
    
    /**
     * Deep sanitize with context awareness
     */
    public static function deepSanitize($data, string $context = 'general') {
        if (is_array($data)) {
            $sanitized = [];
            foreach ($data as $key => $value) {
                $sanitizedKey = ValidationHelper::sanitize($key);
                $sanitized[$sanitizedKey] = self::deepSanitize($value, $context);
            }
            return $sanitized;
        }
        
        return self::sanitizeFieldValue($data, 'unknown', $context);
    }
}

/**
 * Sanitization strategy interface
 */
interface SanitizationStrategy {
    public function sanitize(string $value, string $fieldName): string;
}

/**
 * Financial data sanitization strategy
 */
class FinancialSanitizationStrategy implements SanitizationStrategy {
    
    private const FIELD_PATTERNS = [
        'amount' => '/[^0-9.]/',
        'account_number' => '/[^0-9]/',
        'routing_number' => '/[^0-9]/',
        'crypto_address' => '/[^a-zA-Z0-9]/',
    ];
    
    public function sanitize(string $value, string $fieldName): string {
        if ($fieldName === 'paypal_email') {
            return filter_var($value, FILTER_SANITIZE_EMAIL);
        }
        
        if (isset(self::FIELD_PATTERNS[$fieldName])) {
            return preg_replace(self::FIELD_PATTERNS[$fieldName], '', $value);
        }
        
        return ValidationHelper::sanitize($value);
    }
}

/**
 * Profile data sanitization strategy
 */
class ProfileSanitizationStrategy implements SanitizationStrategy {
    
    public function sanitize(string $value, string $fieldName): string {
        switch ($fieldName) {
            case 'email':
                return strtolower(trim(filter_var($value, FILTER_SANITIZE_EMAIL)));
                
            case 'username':
                return preg_replace('/[^a-zA-Z0-9_]/', '', $value);
                
            case 'phone':
                return preg_replace('/[^0-9\s\-\(\)\+]/', '', $value);
                
            case 'first_name':
            case 'last_name':
            case 'full_name':
                $value = preg_replace('/[^a-zA-Z\s\-\'\.]/', '', $value);
                return ucwords(strtolower(trim($value)));
                
            case 'date_of_birth':
                return preg_replace('/[^0-9\-]/', '', $value);
                
            default:
                return ValidationHelper::sanitize($value);
        }
    }
}

/**
 * Search data sanitization strategy
 */
class SearchSanitizationStrategy implements SanitizationStrategy {
    
    public function sanitize(string $value, string $fieldName): string {
        $value = preg_replace('/[<>"\']/', '', $value);
        $value = ValidationHelper::preventXSS($value);
        return trim($value);
    }
}

/**
 * Admin data sanitization strategy
 */
class AdminSanitizationStrategy implements SanitizationStrategy {
    
    private const ALLOWED_HTML_TAGS = '<p><br><strong><em><u><ul><ol><li><a><h1><h2><h3><h4><h5><h6>';
    
    public function sanitize(string $value, string $fieldName): string {
        if (in_array($fieldName, ['content', 'description', 'message'])) {
            $value = strip_tags($value, self::ALLOWED_HTML_TAGS);
            return ValidationHelper::preventXSS($value);
        }
        
        return ValidationHelper::sanitize($value);
    }
}

/**
 * General sanitization strategy
 */
class GeneralSanitizationStrategy implements SanitizationStrategy {
    
    public function sanitize(string $value, string $fieldName): string {
        return ValidationHelper::sanitize($value);
    }
}
?>