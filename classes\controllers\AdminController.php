<?php

require_once __DIR__ . '/../services/AuthenticationManager.php';
require_once __DIR__ . '/../services/UserService.php';
require_once __DIR__ . '/../services/FinancialService.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/TradingPlan.php';
require_once __DIR__ . '/../models/Transaction.php';
require_once __DIR__ . '/../models/Deposit.php';
require_once __DIR__ . '/../models/Withdrawal.php';
require_once __DIR__ . '/../services/CSRFProtection.php';
require_once __DIR__ . '/../validators/ValidationHelper.php';

class AdminController {
    
    /**
     * Get dashboard statistics for admin overview
     */
    public static function getDashboardStats() {
        try {
            $userModel = new User();
            $depositModel = new Deposit();
            $withdrawalModel = new Withdrawal();
            $transactionModel = new Transaction();
            
            $stats = [
                'total_users' => $userModel->count(['role' => 'user']),
                'active_users' => $userModel->count(['role' => 'user', 'status' => 'active']),
                'pending_deposits' => $depositModel->count(['status' => 'pending']),
                'pending_withdrawals' => $withdrawalModel->count(['status' => 'pending']),
                'total_deposits' => $depositModel->sum('amount', ['status' => 'approved']),
                'total_withdrawals' => $withdrawalModel->sum('amount', ['status' => 'approved']),
                'recent_registrations' => $userModel->count([
                    'role' => 'user',
                    'created_at >=' => date('Y-m-d', strtotime('-7 days'))
                ])
            ];
            
            return $stats;
        } catch (Exception $e) {
            error_log("Error getting dashboard stats: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get recent platform activity
     */
    public static function getRecentActivity($limit = 10) {
        try {
            $transactionModel = new Transaction();
            return $transactionModel->findAll([
                'order' => 'created_at DESC',
                'limit' => $limit,
                'joins' => [
                    'users' => 'users.id = transactions.user_id'
                ],
                'select' => 'transactions.*, users.username, users.first_name, users.last_name'
            ]);
        } catch (Exception $e) {
            error_log("Error getting recent activity: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get all users with filtering and pagination
     */
    public static function getAllUsers($filters = [], $page = 1, $limit = 20) {
        try {
            $userModel = new User();
            $offset = ($page - 1) * $limit;
            
            $conditions = ['role' => 'user'];
            
            // Apply filters
            if (!empty($filters['status'])) {
                $conditions['status'] = $filters['status'];
            }
            
            if (!empty($filters['search'])) {
                $search = '%' . $filters['search'] . '%';
                $conditions['OR'] = [
                    'username LIKE' => $search,
                    'email LIKE' => $search,
                    'first_name LIKE' => $search,
                    'last_name LIKE' => $search
                ];
            }
            
            $users = $userModel->findAll([
                'conditions' => $conditions,
                'order' => 'created_at DESC',
                'limit' => $limit,
                'offset' => $offset
            ]);
            
            $total = $userModel->count($conditions);
            
            return [
                'users' => $users,
                'total' => $total,
                'pages' => ceil($total / $limit),
                'current_page' => $page
            ];
        } catch (Exception $e) {
            error_log("Error getting users: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get detailed user information
     */
    public static function getUserDetails($userId) {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return false;
            }
            
            // Get user's financial summary
            $depositModel = new Deposit();
            $withdrawalModel = new Withdrawal();
            $transactionModel = new Transaction();
            
            $user['total_deposits'] = $depositModel->sum('amount', [
                'user_id' => $userId,
                'status' => 'approved'
            ]) ?: 0;
            
            $user['total_withdrawals'] = $withdrawalModel->sum('amount', [
                'user_id' => $userId,
                'status' => 'approved'
            ]) ?: 0;
            
            $user['pending_deposits'] = $depositModel->count([
                'user_id' => $userId,
                'status' => 'pending'
            ]);
            
            $user['pending_withdrawals'] = $withdrawalModel->count([
                'user_id' => $userId,
                'status' => 'pending'
            ]);
            
            // Get recent transactions
            $user['recent_transactions'] = $transactionModel->findAll([
                'conditions' => ['user_id' => $userId],
                'order' => 'created_at DESC',
                'limit' => 10
            ]);
            
            return $user;
        } catch (Exception $e) {
            error_log("Error getting user details: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create new user account
     */
    public static function createUser($userData) {
        try {
            // Validate input
            $validator = new ValidationHelper();
            $rules = [
                'username' => 'required|min:3|max:50|unique:users',
                'email' => 'required|email|unique:users',
                'password' => 'required|min:6',
                'first_name' => 'required|max:50',
                'last_name' => 'required|max:50'
            ];
            
            $validation = $validator->validate($userData, $rules);
            if (!$validation['valid']) {
                return ['success' => false, 'errors' => $validation['errors']];
            }
            
            $userService = new UserService();
            $result = $userService->createUser([
                'username' => $userData['username'],
                'email' => $userData['email'],
                'password' => $userData['password'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'role' => 'user',
                'status' => 'active',
                'email_verified' => true // Admin created users are auto-verified
            ]);
            
            if ($result) {
                return ['success' => true, 'message' => 'User created successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to create user'];
            }
        } catch (Exception $e) {
            error_log("Error creating user: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while creating user'];
        }
    }
    
    /**
     * Update user information
     */
    public static function updateUser($userId, $userData) {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            // Validate input
            $validator = new ValidationHelper();
            $rules = [
                'username' => 'required|min:3|max:50',
                'email' => 'required|email',
                'first_name' => 'required|max:50',
                'last_name' => 'required|max:50'
            ];
            
            // Check for uniqueness excluding current user
            if ($userData['username'] !== $user['username']) {
                $existingUser = $userModel->findOne(['username' => $userData['username']]);
                if ($existingUser && $existingUser['id'] != $userId) {
                    return ['success' => false, 'errors' => ['username' => 'Username already exists']];
                }
            }
            
            if ($userData['email'] !== $user['email']) {
                $existingUser = $userModel->findOne(['email' => $userData['email']]);
                if ($existingUser && $existingUser['id'] != $userId) {
                    return ['success' => false, 'errors' => ['email' => 'Email already exists']];
                }
            }
            
            $validation = $validator->validate($userData, $rules);
            if (!$validation['valid']) {
                return ['success' => false, 'errors' => $validation['errors']];
            }
            
            $updateData = [
                'username' => $userData['username'],
                'email' => $userData['email'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name']
            ];
            
            // Update password if provided
            if (!empty($userData['password'])) {
                $updateData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
            }
            
            $result = $userModel->update($userId, $updateData);
            
            if ($result) {
                return ['success' => true, 'message' => 'User updated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to update user'];
            }
        } catch (Exception $e) {
            error_log("Error updating user: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while updating user'];
        }
    }
    
    /**
     * Suspend user account
     */
    public static function suspendUser($userId, $reason = '') {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            $result = $userModel->update($userId, [
                'status' => 'suspended',
                'suspension_reason' => $reason,
                'suspended_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                // Log the action
                self::logAdminAction('user_suspended', $userId, [
                    'reason' => $reason,
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                return ['success' => true, 'message' => 'User suspended successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to suspend user'];
            }
        } catch (Exception $e) {
            error_log("Error suspending user: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while suspending user'];
        }
    }
    
    /**
     * Activate user account
     */
    public static function activateUser($userId) {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            $result = $userModel->update($userId, [
                'status' => 'active',
                'suspension_reason' => null,
                'suspended_at' => null
            ]);
            
            if ($result) {
                // Log the action
                self::logAdminAction('user_activated', $userId, [
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                return ['success' => true, 'message' => 'User activated successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to activate user'];
            }
        } catch (Exception $e) {
            error_log("Error activating user: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while activating user'];
        }
    }
    
    /**
     * Credit user balance
     */
    public static function creditUserBalance($userId, $amount, $reason = '') {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            if ($amount <= 0) {
                return ['success' => false, 'message' => 'Amount must be greater than 0'];
            }
            
            $financialService = new FinancialService();
            $result = $financialService->creditUserBalance($userId, $amount, 'admin_credit', $reason);
            
            if ($result) {
                // Log the action
                self::logAdminAction('balance_credited', $userId, [
                    'amount' => $amount,
                    'reason' => $reason,
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                return ['success' => true, 'message' => 'Balance credited successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to credit balance'];
            }
        } catch (Exception $e) {
            error_log("Error crediting balance: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while crediting balance'];
        }
    }
    
    /**
     * Debit user balance
     */
    public static function debitUserBalance($userId, $amount, $reason = '') {
        try {
            $userModel = new User();
            $user = $userModel->findById($userId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            if ($amount <= 0) {
                return ['success' => false, 'message' => 'Amount must be greater than 0'];
            }
            
            if ($user['balance'] < $amount) {
                return ['success' => false, 'message' => 'Insufficient balance'];
            }
            
            $financialService = new FinancialService();
            $result = $financialService->debitUserBalance($userId, $amount, 'admin_debit', $reason);
            
            if ($result) {
                // Log the action
                self::logAdminAction('balance_debited', $userId, [
                    'amount' => $amount,
                    'reason' => $reason,
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                return ['success' => true, 'message' => 'Balance debited successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to debit balance'];
            }
        } catch (Exception $e) {
            error_log("Error debiting balance: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while debiting balance'];
        }
    }
    
    /**
     * Assign trading plan to user
     */
    public static function assignTradingPlan($userId, $planId) {
        try {
            $userModel = new User();
            $planModel = new TradingPlan();
            
            $user = $userModel->findById($userId);
            $plan = $planModel->findById($planId);
            
            if (!$user || $user['role'] !== 'user') {
                return ['success' => false, 'message' => 'User not found'];
            }
            
            if (!$plan || $plan['status'] !== 'active') {
                return ['success' => false, 'message' => 'Trading plan not found or inactive'];
            }
            
            $result = $userModel->update($userId, [
                'current_plan_id' => $planId,
                'plan_assigned_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                // Log the action
                self::logAdminAction('plan_assigned', $userId, [
                    'plan_id' => $planId,
                    'plan_name' => $plan['name'],
                    'admin_id' => $_SESSION['user_id']
                ]);
                
                return ['success' => true, 'message' => 'Trading plan assigned successfully'];
            } else {
                return ['success' => false, 'message' => 'Failed to assign trading plan'];
            }
        } catch (Exception $e) {
            error_log("Error assigning trading plan: " . $e->getMessage());
            return ['success' => false, 'message' => 'An error occurred while assigning trading plan'];
        }
    }
    
    /**
     * Get all active trading plans
     */
    public static function getActiveTradingPlans() {
        try {
            $planModel = new TradingPlan();
            return $planModel->findAll([
                'conditions' => ['status' => 'active'],
                'order' => 'name ASC'
            ]);
        } catch (Exception $e) {
            error_log("Error getting trading plans: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Log admin actions for audit trail
     */
    private static function logAdminAction($action, $targetUserId, $details = []) {
        try {
            $transactionModel = new Transaction();
            $transactionModel->create([
                'user_id' => $targetUserId,
                'type' => 'admin_action',
                'amount' => 0,
                'description' => json_encode([
                    'action' => $action,
                    'details' => $details,
                    'timestamp' => date('Y-m-d H:i:s')
                ]),
                'status' => 'completed'
            ]);
        } catch (Exception $e) {
            error_log("Error logging admin action: " . $e->getMessage());
        }
    }
}