<?php

class ThemeManager {
    private $config;
    private static $instance = null;
    
    private function __construct() {
        $this->config = ConfigManager::getInstance();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Get theme colors with defaults
     */
    public function getThemeColors() {
        return $this->config->get('theme_colors', [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'success' => '#28a745',
            'danger' => '#dc3545',
            'warning' => '#ffc107',
            'info' => '#17a2b8',
            'dark' => '#343a40',
            'light' => '#f8f9fa'
        ]);
    }
    
    /**
     * Update theme colors
     */
    public function updateThemeColors($colors) {
        $currentColors = $this->getThemeColors();
        $updatedColors = array_merge($currentColors, $colors);
        
        // Validate hex colors
        foreach ($updatedColors as $key => $color) {
            if (!$this->isValidHexColor($color)) {
                throw new InvalidArgumentException("Invalid hex color for {$key}: {$color}");
            }
        }
        
        return $this->config->set('theme_colors', $updatedColors);
    }
    
    /**
     * Generate CSS variables for theme colors
     */
    public function generateCSSVariables() {
        $colors = $this->getThemeColors();
        $css = ":root {\n";
        
        foreach ($colors as $name => $color) {
            $css .= "    --{$name}-color: {$color};\n";
        }
        
        $css .= "}\n";
        return $css;
    }
    
    /**
     * Get site branding settings
     */
    public function getBrandingSettings() {
        return [
            'site_name' => $this->config->get('site_name', 'Coinage Trading'),
            'site_logo' => $this->config->get('site_logo', '/assets/images/logo.png'),
            'favicon' => $this->config->get('favicon', '/assets/images/favicon.ico'),
            'footer_text' => $this->config->get('footer_text', '© 2024 Coinage Trading. All rights reserved.')
        ];
    }
    
    /**
     * Update branding settings
     */
    public function updateBrandingSettings($settings) {
        $allowedSettings = ['site_name', 'site_logo', 'favicon', 'footer_text'];
        
        foreach ($settings as $key => $value) {
            if (in_array($key, $allowedSettings)) {
                $this->config->set($key, $value);
            }
        }
        
        return true;
    }
    
    /**
     * Upload and set logo
     */
    public function uploadLogo($file) {
        $uploadDir = __DIR__ . '/../../assets/images/';
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
        
        if (!in_array($file['type'], $allowedTypes)) {
            throw new InvalidArgumentException('Invalid file type. Only JPEG, PNG, GIF, and SVG are allowed.');
        }
        
        if ($file['size'] > 2 * 1024 * 1024) { // 2MB limit
            throw new InvalidArgumentException('File size too large. Maximum 2MB allowed.');
        }
        
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = 'logo_' . time() . '.' . $extension;
        $filepath = $uploadDir . $filename;
        
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new RuntimeException('Failed to upload logo file.');
        }
        
        $logoUrl = '/assets/images/' . $filename;
        $this->config->set('site_logo', $logoUrl);
        
        return $logoUrl;
    }
    
    /**
     * Get layout configuration for specific role
     */
    public function getLayoutConfig($role) {
        $baseConfig = [
            'theme_colors' => $this->getThemeColors(),
            'branding' => $this->getBrandingSettings(),
            'features' => $this->getEnabledFeatures()
        ];
        
        switch ($role) {
            case 'user':
                return array_merge($baseConfig, [
                    'sidebar_color' => 'primary-dark',
                    'show_balance' => true,
                    'show_notifications' => true
                ]);
                
            case 'admin':
                return array_merge($baseConfig, [
                    'sidebar_color' => 'dark-secondary',
                    'show_pending_counts' => true,
                    'show_quick_actions' => true
                ]);
                
            case 'superadmin':
                return array_merge($baseConfig, [
                    'sidebar_color' => 'danger-dark',
                    'show_system_status' => true,
                    'show_danger_zone' => true
                ]);
                
            default:
                return $baseConfig;
        }
    }
    
    /**
     * Get enabled features
     */
    private function getEnabledFeatures() {
        return [
            'two_factor_auth' => $this->config->get('enable_2fa', false),
            'email_verification' => $this->config->get('enable_email_verification', true),
            'deposit_bonus' => $this->config->get('enable_deposit_bonus', true),
            'staking' => $this->config->get('enable_staking', false),
            'pool_investment' => $this->config->get('enable_pool_investment', false)
        ];
    }
    
    /**
     * Validate hex color format
     */
    private function isValidHexColor($color) {
        return preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color);
    }
    
    /**
     * Generate responsive breakpoints CSS
     */
    public function getResponsiveCSS() {
        return "
        /* Mobile First Responsive Design */
        @media (max-width: 575.98px) {
            .container-fluid { padding: 0 10px; }
            .card { margin-bottom: 15px; }
            .btn { font-size: 14px; padding: 8px 16px; }
        }
        
        @media (min-width: 576px) and (max-width: 767.98px) {
            .sidebar { width: 200px; }
        }
        
        @media (min-width: 768px) and (max-width: 991.98px) {
            .sidebar { width: 220px; }
        }
        
        @media (min-width: 992px) {
            .sidebar { width: 250px; }
        }
        ";
    }
}