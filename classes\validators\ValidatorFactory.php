<?php
/**
 * ValidatorFactory - Creates appropriate validators based on context
 */
class ValidatorFactory {
    private static $validators = [];
    
    /**
     * Register a validator class
     */
    public static function register($type, $className) {
        self::$validators[$type] = $className;
    }
    
    /**
     * Create validator instance
     */
    public static function create($type) {
        if (!isset(self::$validators[$type])) {
            throw new InvalidArgumentException("Validator type '$type' not registered");
        }
        
        $className = self::$validators[$type];
        
        if (!class_exists($className)) {
            throw new RuntimeException("Validator class '$className' not found");
        }
        
        return new $className();
    }
    
    /**
     * Initialize default validators
     */
    public static function initialize() {
        self::register('registration', 'RegistrationValidator');
        self::register('login', 'LoginValidator');
        self::register('financial', 'FinancialValidator');
        self::register('security', 'SecurityValidator');
    }
}

// Initialize default validators
ValidatorFactory::initialize();
?>