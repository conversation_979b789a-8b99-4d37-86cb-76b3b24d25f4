<?php
require_once __DIR__ . '/BaseView.php';
require_once __DIR__ . '/traits/StatusColorTrait.php';

/**
 * DashboardView - User dashboard interface
 * 
 * Displays user account overview including balance cards, statistics,
 * recent transactions, and quick action buttons.
 * 
 * @package Views
 * <AUTHOR> Trading Platform
 * @version 1.0.0
 */
class DashboardView extends BaseView {
    use StatusColorTrait;
    
    /** @var User User object containing account information */
    private $user;
    
    /** @var array Statistical data for dashboard display */
    private $stats;
    
    /** @var array User activity data */
    private $activity;
    
    /** @var array Recent transaction records */
    private $recentTransactions;
    
    /**
     * Constructor
     * 
     * @param User $user User object
     * @param array $stats Statistical data
     * @param array $activity User activity data
     * @param array $recentTransactions Recent transaction records
     */
    public function __construct($user, $stats, $activity, $recentTransactions) {
        parent::__construct();
        $this->user = $user;
        $this->stats = $stats;
        $this->activity = $activity;
        $this->recentTransactions = $recentTransactions;
        $this->setTitle('Dashboard - Coinage Trading');
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <link rel="stylesheet" href="/assets/css/dashboard.css">
        <?php
    }
    
    protected function renderContent() {
        ?>
        <div class="container-fluid">
            <?php 
            $this->renderWelcomeSection();
            $this->renderBalanceCards();
            $this->renderStatisticsRow();
            $this->renderRecentActivity();
            ?>
        </div>
        <?php
    }
    
    private function renderWelcomeSection() {
        ?>
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-section">
                    <h1 class="h3 mb-0">Welcome back, <?php echo htmlspecialchars($this->user->getFullName()); ?>!</h1>
                    <p class="text-muted mb-0">Here's your account overview</p>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderBalanceCards() {
        $balanceCards = [
            [
                'title' => 'Current Balance',
                'value' => $this->user->balance,
                'icon' => 'fas fa-wallet',
                'color' => 'primary'
            ],
            [
                'title' => 'Bonus Balance', 
                'value' => $this->user->bonus,
                'icon' => 'fas fa-gift',
                'color' => 'success'
            ],
            [
                'title' => 'Total Deposits',
                'value' => $this->user->total_deposit,
                'icon' => 'fas fa-chart-line', 
                'color' => 'info'
            ]
        ];
        ?>
        <!-- Balance Cards -->
        <div class="row mb-4">
            <?php foreach ($balanceCards as $card): ?>
                <div class="col-md-4 mb-3">
                    <div class="card balance-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="balance-icon bg-<?php echo $card['color']; ?>">
                                    <i class="<?php echo $card['icon']; ?>"></i>
                                </div>
                                <div class="ms-3">
                                    <h6 class="card-subtitle mb-1 text-muted"><?php echo $card['title']; ?></h6>
                                    <h4 class="card-title mb-0">$<?php echo number_format($card['value'], 2); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    private function renderStatisticsRow() {
        $statistics = [
            [
                'title' => 'Total Deposits',
                'value' => '$' . number_format($this->stats['total_deposits'], 2),
                'icon' => 'fas fa-arrow-down',
                'color' => 'success'
            ],
            [
                'title' => 'Total Withdrawals', 
                'value' => '$' . number_format($this->stats['total_withdrawals'], 2),
                'icon' => 'fas fa-arrow-up',
                'color' => 'warning'
            ],
            [
                'title' => 'Pending Deposits',
                'value' => $this->stats['pending_deposits'],
                'icon' => 'fas fa-clock',
                'color' => 'info'
            ],
            [
                'title' => 'Days Active',
                'value' => $this->stats['account_age_days'],
                'icon' => 'fas fa-calendar',
                'color' => 'primary'
            ]
        ];
        ?>
        <!-- Statistics Row -->
        <div class="row mb-4">
            <?php foreach ($statistics as $stat): ?>
                <div class="col-md-3 mb-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="<?php echo $stat['icon']; ?> text-<?php echo $stat['color']; ?> mb-2"></i>
                            <h5 class="card-title"><?php echo $stat['value']; ?></h5>
                            <p class="card-text text-muted"><?php echo $stat['title']; ?></p>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
    
    private function renderRecentActivity() {
        ?>
        <!-- Recent Activity -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <?php $this->renderTransactionsTable(); ?>
            </div>
            <div class="col-lg-4 mb-4">
                <?php $this->renderQuickActions(); ?>
                <?php $this->renderAccountStatus(); ?>
            </div>
        </div>
        <?php
    }
    
    private function renderTransactionsTable() {
        ?>
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Transactions</h5>
                <a href="/user/transactions/" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($this->recentTransactions)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No transactions yet</p>
                        <a href="/user/deposit/" class="btn btn-primary">Make Your First Deposit</a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($this->recentTransactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-<?php echo $this->getTransactionTypeColor($transaction->type); ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $transaction->type)); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $transaction->amount >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $transaction->amount >= 0 ? '+' : ''; ?>$<?php echo number_format($transaction->amount, 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $this->getStatusColor($transaction->status); ?>">
                                                <?php echo ucfirst($transaction->status); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($transaction->created_at)); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }
    
    private function renderQuickActions() {
        ?>
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/user/deposit/" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Make Deposit
                    </a>
                    <a href="/user/withdraw/" class="btn btn-warning">
                        <i class="fas fa-minus me-2"></i>Request Withdrawal
                    </a>
                    <a href="/user/transactions/" class="btn btn-info">
                        <i class="fas fa-history me-2"></i>View Transactions
                    </a>
                    <a href="/user/support/" class="btn btn-secondary">
                        <i class="fas fa-headset me-2"></i>Contact Support
                    </a>
                </div>
            </div>
        </div>
        <?php
    }
    
    private function renderAccountStatus() {
        ?>
        <!-- Account Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">Account Status</h5>
            </div>
            <div class="card-body">
                <div class="status-item mb-2">
                    <span class="status-label">Email:</span>
                    <span class="badge bg-<?php echo $this->user->email_verified ? 'success' : 'warning'; ?>">
                        <?php echo $this->user->email_verified ? 'Verified' : 'Unverified'; ?>
                    </span>
                </div>
                <div class="status-item mb-2">
                    <span class="status-label">KYC:</span>
                    <span class="badge bg-<?php echo $this->getKycStatusColor($this->user->kyc_status); ?>">
                        <?php echo ucfirst($this->user->kyc_status); ?>
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">2FA:</span>
                    <span class="badge bg-<?php echo $this->user->two_fa_enabled ? 'success' : 'secondary'; ?>">
                        <?php echo $this->user->two_fa_enabled ? 'Enabled' : 'Disabled'; ?>
                    </span>
                </div>
            </div>
        </div>
        <?php
    }
    
    protected function renderScripts() {
        parent::renderScripts();
        ?>
        <script src="/assets/js/dashboard.js"></script>
        <?php
    }
    

}
?>