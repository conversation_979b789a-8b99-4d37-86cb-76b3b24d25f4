<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/config/ConfigManager.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Check if user is logged in and is superadmin
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$config = ConfigManager::getInstance();
$message = '';
$errors = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid CSRF token';
    } else {
        try {
            // Theme Colors
            $themeColors = [
                'primary' => $_POST['primary_color'] ?? '#007bff',
                'secondary' => $_POST['secondary_color'] ?? '#6c757d',
                'success' => $_POST['success_color'] ?? '#28a745',
                'danger' => $_POST['danger_color'] ?? '#dc3545',
                'warning' => $_POST['warning_color'] ?? '#ffc107',
                'info' => $_POST['info_color'] ?? '#17a2b8',
                'dark' => $_POST['dark_color'] ?? '#343a40',
                'light' => $_POST['light_color'] ?? '#f8f9fa'
            ];
            
            // Site Branding
            $siteName = $_POST['site_name'] ?? 'Coinage Trading';
            $siteLogo = $_POST['site_logo'] ?? getBaseUrl() . 'assets/images/logo.png';
            $siteDescription = $_POST['site_description'] ?? 'Professional Trading Platform';
            
            // Layout Settings
            $sidebarStyle = $_POST['sidebar_style'] ?? 'modern';
            $layoutMode = $_POST['layout_mode'] ?? 'light';
            $compactMode = isset($_POST['compact_mode']);
            
            // Save settings
            $config->set('theme_colors', $themeColors);
            $config->set('site_name', $siteName);
            $config->set('site_logo', $siteLogo);
            $config->set('site_description', $siteDescription);
            $config->set('sidebar_style', $sidebarStyle);
            $config->set('layout_mode', $layoutMode);
            $config->set('compact_mode', $compactMode);
            
            $message = 'Appearance settings updated successfully!';
            
        } catch (Exception $e) {
            $errors[] = 'Error updating settings: ' . $e->getMessage();
        }
    }
}

// Get current settings
$currentThemeColors = $config->get('theme_colors', [
    'primary' => '#007bff',
    'secondary' => '#6c757d',
    'success' => '#28a745',
    'danger' => '#dc3545',
    'warning' => '#ffc107',
    'info' => '#17a2b8',
    'dark' => '#343a40',
    'light' => '#f8f9fa'
]);

$currentSiteName = $config->get('site_name', 'Coinage Trading');
$currentSiteLogo = $config->get('site_logo', getBaseUrl() . 'assets/images/logo.png');
$currentSiteDescription = $config->get('site_description', 'Professional Trading Platform');
$currentSidebarStyle = $config->get('sidebar_style', 'modern');
$currentLayoutMode = $config->get('layout_mode', 'light');
$currentCompactMode = $config->get('compact_mode', false);

$pageTitle = 'Appearance Settings';
$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $currentSiteName; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            <?php foreach ($currentThemeColors as $key => $value): ?>
            --<?php echo $key; ?>-color: <?php echo $value; ?>;
            <?php endforeach; ?>
        }
        
        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .color-preview:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .sidebar-preview {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            border: 2px solid #dee2e6;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .sidebar-preview:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,123,255,0.2);
        }
        
        .sidebar-preview.selected {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        
        .preview-sidebar {
            width: 60px;
            height: 100%;
            background: #ffffff;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 10px 5px;
        }
        
        .preview-content {
            flex: 1;
            background: #f8f9fa;
            padding: 10px;
        }
        
        .preview-nav-item {
            width: 30px;
            height: 6px;
            background: #dee2e6;
            border-radius: 3px;
            margin: 3px 0;
        }
        
        .preview-nav-item.active {
            background: var(--primary-color);
        }
        
        .settings-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border-radius: 15px;
        }
        
        .settings-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Include Sidebar -->
            <?php include __DIR__ . '/../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-palette me-2"></i>Appearance Settings
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-outline-secondary me-2" onclick="resetToDefaults()">
                            <i class="fas fa-undo me-2"></i>Reset to Defaults
                        </button>
                        <button type="button" class="btn btn-primary" onclick="previewChanges()">
                            <i class="fas fa-eye me-2"></i>Preview Changes
                        </button>
                    </div>
                </div>
                
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php foreach ($errors as $error): ?>
                            <div><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                    
                    <div class="row">
                        <!-- Theme Colors -->
                        <div class="col-lg-6 mb-4">
                            <div class="card settings-card">
                                <div class="card-header settings-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-palette me-2"></i>Theme Colors
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <?php foreach ($currentThemeColors as $colorName => $colorValue): ?>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-capitalize"><?php echo str_replace('_', ' ', $colorName); ?></label>
                                            <div class="input-group">
                                                <input type="color" 
                                                       class="form-control form-control-color" 
                                                       name="<?php echo $colorName; ?>_color" 
                                                       value="<?php echo $colorValue; ?>"
                                                       title="Choose <?php echo $colorName; ?> color">
                                                <input type="text" 
                                                       class="form-control" 
                                                       value="<?php echo $colorValue; ?>"
                                                       readonly>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Site Branding -->
                        <div class="col-lg-6 mb-4">
                            <div class="card settings-card">
                                <div class="card-header settings-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-building me-2"></i>Site Branding
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Site Name</label>
                                        <input type="text" class="form-control" name="site_name" 
                                               value="<?php echo htmlspecialchars($currentSiteName); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Site Logo URL</label>
                                        <input type="text" class="form-control" name="site_logo" 
                                               value="<?php echo htmlspecialchars($currentSiteLogo); ?>">
                                        <div class="form-text">Enter the URL or path to your logo image</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">Site Description</label>
                                        <textarea class="form-control" name="site_description" rows="3"><?php echo htmlspecialchars($currentSiteDescription); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Layout Settings -->
                        <div class="col-lg-8 mb-4">
                            <div class="card settings-card">
                                <div class="card-header settings-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-layout me-2"></i>Layout Settings
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Sidebar Style</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="sidebar-preview <?php echo $currentSidebarStyle === 'modern' ? 'selected' : ''; ?>" 
                                                         onclick="selectSidebarStyle('modern')">
                                                        <div class="d-flex h-100">
                                                            <div class="preview-sidebar">
                                                                <div class="preview-nav-item active"></div>
                                                                <div class="preview-nav-item"></div>
                                                                <div class="preview-nav-item"></div>
                                                            </div>
                                                            <div class="preview-content"></div>
                                                        </div>
                                                    </div>
                                                    <div class="text-center mt-2">
                                                        <input type="radio" name="sidebar_style" value="modern" 
                                                               <?php echo $currentSidebarStyle === 'modern' ? 'checked' : ''; ?>>
                                                        <label class="ms-1">Modern</label>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="sidebar-preview <?php echo $currentSidebarStyle === 'classic' ? 'selected' : ''; ?>" 
                                                         onclick="selectSidebarStyle('classic')">
                                                        <div class="d-flex h-100">
                                                            <div class="preview-sidebar" style="background: #343a40;">
                                                                <div class="preview-nav-item active" style="background: white;"></div>
                                                                <div class="preview-nav-item" style="background: rgba(255,255,255,0.5);"></div>
                                                                <div class="preview-nav-item" style="background: rgba(255,255,255,0.5);"></div>
                                                            </div>
                                                            <div class="preview-content"></div>
                                                        </div>
                                                    </div>
                                                    <div class="text-center mt-2">
                                                        <input type="radio" name="sidebar_style" value="classic" 
                                                               <?php echo $currentSidebarStyle === 'classic' ? 'checked' : ''; ?>>
                                                        <label class="ms-1">Classic</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label">Layout Mode</label>
                                            <select class="form-select" name="layout_mode">
                                                <option value="light" <?php echo $currentLayoutMode === 'light' ? 'selected' : ''; ?>>Light Mode</option>
                                                <option value="dark" <?php echo $currentLayoutMode === 'dark' ? 'selected' : ''; ?>>Dark Mode</option>
                                                <option value="auto" <?php echo $currentLayoutMode === 'auto' ? 'selected' : ''; ?>>Auto (System)</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="compact_mode" 
                                               <?php echo $currentCompactMode ? 'checked' : ''; ?>>
                                        <label class="form-check-label">
                                            Enable Compact Mode
                                        </label>
                                        <div class="form-text">Reduces spacing and makes the interface more compact</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Preview -->
                        <div class="col-lg-4 mb-4">
                            <div class="card settings-card">
                                <div class="card-header settings-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-eye me-2"></i>Live Preview
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="livePreview" class="border rounded p-3" style="height: 300px; background: #f8f9fa;">
                                        <div class="d-flex h-100">
                                            <div class="bg-white border-end" style="width: 80px; padding: 10px;">
                                                <div class="text-center mb-3">
                                                    <div style="width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; margin: 0 auto;"></div>
                                                </div>
                                                <div style="height: 8px; background: var(--primary-color); border-radius: 4px; margin: 5px 0;"></div>
                                                <div style="height: 8px; background: #dee2e6; border-radius: 4px; margin: 5px 0;"></div>
                                                <div style="height: 8px; background: #dee2e6; border-radius: 4px; margin: 5px 0;"></div>
                                            </div>
                                            <div class="flex-fill p-3">
                                                <div style="height: 20px; background: var(--primary-color); border-radius: 4px; width: 60%; margin-bottom: 15px;"></div>
                                                <div style="height: 12px; background: #dee2e6; border-radius: 4px; width: 80%; margin-bottom: 8px;"></div>
                                                <div style="height: 12px; background: #dee2e6; border-radius: 4px; width: 60%; margin-bottom: 8px;"></div>
                                                <div style="height: 12px; background: #dee2e6; border-radius: 4px; width: 40%;"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-center">
                                        <small class="text-muted">Preview updates as you change settings</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update color input text when color picker changes
        document.querySelectorAll('input[type="color"]').forEach(colorInput => {
            colorInput.addEventListener('change', function() {
                const textInput = this.parentElement.querySelector('input[type="text"]');
                textInput.value = this.value;
                updateLivePreview();
            });
        });
        
        // Update live preview
        function updateLivePreview() {
            const primaryColor = document.querySelector('input[name="primary_color"]').value;
            document.documentElement.style.setProperty('--primary-color', primaryColor);
        }
        
        // Select sidebar style
        function selectSidebarStyle(style) {
            document.querySelectorAll('.sidebar-preview').forEach(preview => {
                preview.classList.remove('selected');
            });
            event.target.closest('.sidebar-preview').classList.add('selected');
            document.querySelector(`input[name="sidebar_style"][value="${style}"]`).checked = true;
        }
        
        // Reset to defaults
        function resetToDefaults() {
            if (confirm('Are you sure you want to reset all appearance settings to defaults? This cannot be undone.')) {
                const defaults = {
                    'primary_color': '#007bff',
                    'secondary_color': '#6c757d',
                    'success_color': '#28a745',
                    'danger_color': '#dc3545',
                    'warning_color': '#ffc107',
                    'info_color': '#17a2b8',
                    'dark_color': '#343a40',
                    'light_color': '#f8f9fa'
                };
                
                Object.keys(defaults).forEach(key => {
                    const colorInput = document.querySelector(`input[name="${key}"]`);
                    const textInput = colorInput.parentElement.querySelector('input[type="text"]');
                    colorInput.value = defaults[key];
                    textInput.value = defaults[key];
                });
                
                document.querySelector('input[name="site_name"]').value = 'Coinage Trading';
                document.querySelector('input[name="site_logo"]').value = '<?php echo getBaseUrl(); ?>assets/images/logo.png';
                document.querySelector('textarea[name="site_description"]').value = 'Professional Trading Platform';
                document.querySelector('select[name="layout_mode"]').value = 'light';
                document.querySelector('input[name="sidebar_style"][value="modern"]').checked = true;
                document.querySelector('input[name="compact_mode"]').checked = false;
                
                updateLivePreview();
            }
        }
        
        // Preview changes
        function previewChanges() {
            alert('Preview functionality will open the site in a new tab with temporary settings applied.');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateLivePreview();
        });
    </script>
</body>
</html>