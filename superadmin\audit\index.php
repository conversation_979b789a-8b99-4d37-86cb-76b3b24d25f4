<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Audit Logs';
$user = getCurrentUser();

// Pagination settings
$page = (int)($_GET['page'] ?? 1);
$limit = 50;
$offset = ($page - 1) * $limit;

// Filter settings
$action_filter = $_GET['action'] ?? '';
$user_filter = $_GET['user'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build query
$db = getDB();
$where_conditions = [];
$params = [];

if ($action_filter) {
    $where_conditions[] = "action = ?";
    $params[] = $action_filter;
}

if ($user_filter) {
    $where_conditions[] = "user_id = ?";
    $params[] = $user_filter;
}

if ($date_from) {
    $where_conditions[] = "created_at >= ?";
    $params[] = $date_from . ' 00:00:00';
}

if ($date_to) {
    $where_conditions[] = "created_at <= ?";
    $params[] = $date_to . ' 23:59:59';
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count
$count_query = "SELECT COUNT(*) as total FROM audit_logs $where_clause";
$count_stmt = $db->prepare($count_query);
$count_stmt->execute($params);
$total_records = $count_stmt->fetch()['total'];
$total_pages = ceil($total_records / $limit);

// Get audit logs
$query = "SELECT al.*, u.first_name, u.last_name, u.email 
          FROM audit_logs al 
          LEFT JOIN users u ON al.user_id = u.id 
          $where_clause 
          ORDER BY al.created_at DESC 
          LIMIT $limit OFFSET $offset";

$stmt = $db->prepare($query);
$stmt->execute($params);
$audit_logs = $stmt->fetchAll();

// Get available actions for filter
$actions_query = "SELECT DISTINCT action FROM audit_logs ORDER BY action";
$actions_stmt = $db->query($actions_query);
$available_actions = $actions_stmt->fetchAll(PDO::FETCH_COLUMN);

// Get available users for filter
$users_query = "SELECT DISTINCT u.id, u.first_name, u.last_name 
                FROM audit_logs al 
                JOIN users u ON al.user_id = u.id 
                ORDER BY u.first_name, u.last_name";
$users_stmt = $db->query($users_query);
$available_users = $users_stmt->fetchAll();

$siteName = SystemSetting::getValue('site_name', 'Coinage Trading');
$siteLogo = SystemSetting::getValue('site_logo', '/assets/images/logo.png');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $siteName; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo SystemSetting::getValue('primary_color', '#007bff'); ?>;
            --sidebar-bg: <?php echo SystemSetting::getValue('sidebar_bg_color', '#343a40'); ?>;
            --sidebar-text: <?php echo SystemSetting::getValue('sidebar_text_color', '#ffffff'); ?>;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            min-height: 100vh;
            color: var(--sidebar-text);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: var(--sidebar-text);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: #fff;
            transform: translateX(5px);
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 20px;
        }
        
        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .audit-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .badge-action {
            font-size: 0.75rem;
            padding: 4px 8px;
        }
        
        .btn-custom {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            padding: 10px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .superadmin-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: 1px solid #dee2e6;
        }
        
        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 px-0">
            <div class="sidebar d-flex flex-column p-3">
                <div class="text-center mb-4">
                    <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="navbar-brand img-fluid" style="max-height: 50px;">
                    <h5 class="mt-2"><?php echo $siteName; ?></h5>
                    <div class="superadmin-badge mt-2">SUPER ADMIN</div>
                </div>
                
                <div class="user-info mb-4 text-center">
                    <div class="avatar bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="mt-2">
                        <small>Super Admin:</small><br>
                        <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                    </div>
                </div>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../settings/">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../appearance/">
                            <i class="fas fa-palette me-2"></i>Appearance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../email-templates/">
                            <i class="fas fa-envelope-open-text me-2"></i>Email Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../audit/">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../backup/">
                            <i class="fas fa-database me-2"></i>Database Backup
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../security/">
                            <i class="fas fa-shield-alt me-2"></i>Security Center
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><i class="fas fa-clipboard-list me-2"></i>Audit Logs</h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-custom" onclick="exportLogs()">
                            <i class="fas fa-download me-1"></i>Export Logs
                        </button>
                        <button class="btn btn-outline-danger btn-custom" onclick="clearOldLogs()">
                            <i class="fas fa-trash me-1"></i>Clear Old Logs
                        </button>
                    </div>
                </div>

                <!-- Filters -->
                <div class="filter-card">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filter Logs</h5>

                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Action</label>
                            <select class="form-select" name="action">
                                <option value="">All Actions</option>
                                <?php foreach ($available_actions as $action): ?>
                                    <option value="<?php echo htmlspecialchars($action); ?>" <?php echo $action_filter === $action ? 'selected' : ''; ?>>
                                        <?php echo ucwords(str_replace('_', ' ', $action)); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">User</label>
                            <select class="form-select" name="user">
                                <option value="">All Users</option>
                                <?php foreach ($available_users as $user_option): ?>
                                    <option value="<?php echo $user_option['id']; ?>" <?php echo $user_filter == $user_option['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user_option['first_name'] . ' ' . $user_option['last_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>

                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary btn-custom me-2">
                                <i class="fas fa-search me-1"></i>Filter
                            </button>
                            <a href="?" class="btn btn-outline-secondary btn-custom">
                                <i class="fas fa-times me-1"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Results Summary -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="text-muted">
                            Showing <?php echo number_format($total_records); ?> total records
                            <?php if ($page > 1 || $total_pages > 1): ?>
                                (Page <?php echo $page; ?> of <?php echo $total_pages; ?>)
                            <?php endif; ?>
                        </span>
                    </div>
                    <div>
                        <small class="text-muted">
                            Records per page: <?php echo $limit; ?>
                        </small>
                    </div>
                </div>

                <!-- Audit Logs Table -->
                <div class="audit-table">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Description</th>
                                    <th>IP Address</th>
                                    <th>User Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($audit_logs)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                            <div class="text-muted">No audit logs found</div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($audit_logs as $log): ?>
                                        <tr>
                                            <td>
                                                <div class="fw-medium"><?php echo date('M j, Y', strtotime($log['created_at'])); ?></div>
                                                <small class="text-muted"><?php echo date('H:i:s', strtotime($log['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($log['first_name'] && $log['last_name']): ?>
                                                    <div class="fw-medium"><?php echo htmlspecialchars($log['first_name'] . ' ' . $log['last_name']); ?></div>
                                                    <small class="text-muted"><?php echo htmlspecialchars($log['email']); ?></small>
                                                <?php else: ?>
                                                    <span class="text-muted">System</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $badgeClass = 'bg-secondary';
                                                switch ($log['action']) {
                                                    case 'login':
                                                        $badgeClass = 'bg-success';
                                                        break;
                                                    case 'login_failed':
                                                        $badgeClass = 'bg-danger';
                                                        break;
                                                    case 'logout':
                                                        $badgeClass = 'bg-warning';
                                                        break;
                                                    case 'admin_created':
                                                    case 'user_created':
                                                        $badgeClass = 'bg-info';
                                                        break;
                                                    case 'admin_deleted':
                                                    case 'user_deleted':
                                                        $badgeClass = 'bg-danger';
                                                        break;
                                                    case 'settings_updated':
                                                        $badgeClass = 'bg-primary';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $badgeClass; ?> badge-action">
                                                    <?php echo ucwords(str_replace('_', ' ', $log['action'])); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 300px;" title="<?php echo htmlspecialchars($log['description'] ?? ''); ?>">
                                                    <?php echo htmlspecialchars($log['description'] ?? 'No description'); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <code class="small"><?php echo htmlspecialchars($log['ip_address'] ?? 'N/A'); ?></code>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($log['user_agent'] ?? ''); ?>">
                                                    <small class="text-muted"><?php echo htmlspecialchars($log['user_agent'] ?? 'N/A'); ?></small>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="d-flex justify-content-center mt-4">
                        <nav aria-label="Audit logs pagination">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                for ($i = $start_page; $i <= $end_page; $i++):
                                ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function exportLogs() {
    // Implementation for exporting logs
    alert('Export logs functionality will be implemented');
}

function clearOldLogs() {
    if (confirm('Are you sure you want to clear old audit logs? This action cannot be undone.')) {
        // Implementation for clearing old logs
        alert('Clear old logs functionality will be implemented');
    }
}
</script>

</body>
</html>
