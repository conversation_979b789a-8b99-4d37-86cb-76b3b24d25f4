<?php
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../classes/services/AuditTrailService.php';
require_once __DIR__ . '/../classes/services/FinancialReportingService.php';

echo "Testing Financial Reporting and Audit Trail System\n";
echo "================================================\n\n";

try {
    // Test 1: Create some sample audit log entries
    echo "1. Creating sample audit log entries...\n";
    
    // Sample login events
    AuditTrailService::logAuthEvent('login_success', 1, true, ['test' => 'data']);
    AuditTrailService::logAuthEvent('login_failed', 2, false, ['test' => 'data']);
    
    // Sample financial events
    AuditTrailService::logFinancialEvent('deposit_approved', 'deposit', 1, 
        ['status' => 'pending', 'amount' => 100], 
        ['status' => 'approved', 'amount' => 100], 
        1
    );
    
    AuditTrailService::logAdminAction('user_suspended', 'user', 2, 
        ['status' => 'active'], 1, 2
    );
    
    echo "✓ Sample audit entries created\n\n";
    
    // Test 2: Test FinancialReportingService
    echo "2. Testing Financial Reporting Service...\n";
    
    $reportingService = new FinancialReportingService();
    $dateFrom = date('Y-m-d', strtotime('-30 days'));
    $dateTo = date('Y-m-d');
    
    $summary = $reportingService->getSummaryReport($dateFrom, $dateTo);
    echo "✓ Summary report generated\n";
    echo "  - Total deposits: $" . number_format($summary['deposits']['total'], 2) . "\n";
    echo "  - Total withdrawals: $" . number_format($summary['withdrawals']['total'], 2) . "\n";
    echo "  - Net flow: $" . number_format($summary['net_flow'], 2) . "\n";
    echo "  - Pending actions: " . $summary['pending_actions'] . "\n\n";
    
    // Test 3: Test AuditTrailService
    echo "3. Testing Audit Trail Service...\n";
    
    $auditService = new AuditTrailService();
    $auditData = $auditService->getAuditTrail([], 1, 10);
    echo "✓ Audit trail retrieved\n";
    echo "  - Total audit logs: " . $auditData['total_count'] . "\n";
    echo "  - Logs on current page: " . count($auditData['audit_logs']) . "\n\n";
    
    // Test 4: Test suspicious activity detection
    echo "4. Testing Suspicious Activity Detection...\n";
    
    $suspiciousActivity = $auditService->getSuspiciousActivity(24);
    echo "✓ Suspicious activity check completed\n";
    echo "  - Alerts found: " . count($suspiciousActivity) . "\n";
    
    if (!empty($suspiciousActivity)) {
        foreach ($suspiciousActivity as $alert) {
            echo "  - " . $alert['type'] . " (" . $alert['severity'] . "): " . $alert['message'] . "\n";
        }
    }
    echo "\n";
    
    // Test 5: Test export functionality
    echo "5. Testing Export Functionality...\n";
    
    $csvData = $reportingService->exportToCSV('summary', $dateFrom, $dateTo);
    echo "✓ CSV export generated (" . strlen($csvData) . " bytes)\n";
    
    $auditCsv = $auditService->exportAuditTrail();
    echo "✓ Audit trail CSV export generated (" . strlen($auditCsv) . " bytes)\n\n";
    
    // Test 6: Test volume trends
    echo "6. Testing Volume Trends...\n";
    
    $trends = $reportingService->getVolumeTrends($dateFrom, $dateTo, 'day');
    echo "✓ Volume trends retrieved\n";
    echo "  - Data points: " . count($trends) . "\n\n";
    
    echo "All tests completed successfully! ✓\n";
    echo "\nThe financial reporting and audit trail system is working correctly.\n";
    echo "You can now access the reports at: /admin/reports/\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>