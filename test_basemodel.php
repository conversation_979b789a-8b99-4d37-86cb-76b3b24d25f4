<?php
/**
 * Test BaseModel SQL Syntax Fixes
 */

// Include necessary files
require_once 'config.php';
require_once 'includes/functions.php';
require_once 'includes/db_connect.php';
require_once 'classes/models/BaseModel.php';
require_once 'classes/models/User.php';

echo "<h1>BaseModel SQL Syntax Test</h1>";

// Test 1: Test count method with reserved keywords
echo "<h2>Test 1: Count Method with Reserved Keywords</h2>";
try {
    // This should have previously caused the SQL syntax error
    $testConditions = [
        'status' => 'active',
        'order' => 'created_at DESC',  // This should be filtered out
        'limit' => 10,                 // This should be filtered out
        'joins' => 'users',            // This should be filtered out
        'select' => '*'                // This should be filtered out
    ];
    
    $count = User::count($testConditions);
    echo "Count with reserved keywords: " . $count . "<br>";
    echo "<span style='color: green;'>✓ Count method handles reserved keywords correctly</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Count method failed: " . $e->getMessage() . "</span><br>";
}

// Test 2: Test findAll method with reserved keywords
echo "<h2>Test 2: FindAll Method with Reserved Keywords</h2>";
try {
    $testConditions = [
        'status' => 'active',
        'order' => 'created_at DESC',  // This should be filtered out
        'limit' => 5,                  // This should be filtered out
        'offset' => 0,                 // This should be filtered out
        'select' => 'id, username'     // This should be filtered out
    ];
    
    $users = User::findAll($testConditions, 5, 'created_at DESC');
    echo "FindAll with reserved keywords returned " . count($users) . " users<br>";
    echo "<span style='color: green;'>✓ FindAll method handles reserved keywords correctly</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ FindAll method failed: " . $e->getMessage() . "</span><br>";
}

// Test 3: Test normal conditions still work
echo "<h2>Test 3: Normal Conditions Still Work</h2>";
try {
    $normalConditions = [
        'status' => 'active'
    ];
    
    $count = User::count($normalConditions);
    echo "Normal count: " . $count . "<br>";
    
    $users = User::findAll($normalConditions, 3);
    echo "Normal findAll returned " . count($users) . " users<br>";
    echo "<span style='color: green;'>✓ Normal conditions work correctly</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Normal conditions failed: " . $e->getMessage() . "</span><br>";
}

// Test 4: Test string conditions
echo "<h2>Test 4: String Conditions</h2>";
try {
    $stringCondition = "status = 'active'";
    $count = User::count($stringCondition);
    echo "String condition count: " . $count . "<br>";
    echo "<span style='color: green;'>✓ String conditions work correctly</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ String conditions failed: " . $e->getMessage() . "</span><br>";
}

// Test 5: Test empty conditions
echo "<h2>Test 5: Empty Conditions</h2>";
try {
    $count = User::count([]);
    echo "Empty conditions count: " . $count . "<br>";
    
    $users = User::findAll([]);
    echo "Empty conditions findAll returned " . count($users) . " users<br>";
    echo "<span style='color: green;'>✓ Empty conditions work correctly</span><br>";
} catch (Exception $e) {
    echo "<span style='color: red;'>✗ Empty conditions failed: " . $e->getMessage() . "</span><br>";
}

echo "<h2>Summary</h2>";
echo "BaseModel SQL syntax test completed. The fixes should prevent 'order = ? AND limit = ?' type errors.";
?>
