<?php
require_once __DIR__ . '/../../../includes/functions.php';
require_once __DIR__ . '/../../../classes/models/EmailTemplate.php';

// Set JSON header
header('Content-Type: application/json');

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit;
}

try {
    $templateId = intval($_GET['id'] ?? 0);
    
    if (!$templateId) {
        throw new Exception('Template ID is required');
    }
    
    $template = EmailTemplate::find($templateId);
    
    if (!$template) {
        throw new Exception('Template not found');
    }
    
    echo json_encode([
        'success' => true,
        'template' => $template
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}