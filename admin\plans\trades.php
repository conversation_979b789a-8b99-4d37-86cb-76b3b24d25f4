<?php
require_once '../../includes/db_connect.php';
require_once '../../includes/functions.php';
require_once '../../classes/models/User.php';
require_once '../../classes/models/TradingPlan.php';
require_once '../../classes/views/AdminTradeHistoryView.php';

// Check authentication and admin role
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: ../../login.php');
    exit;
}

try {
    // Get filter parameters
    $userId = intval($_GET['user_id'] ?? 0);
    $planId = intval($_GET['plan_id'] ?? 0);
    $status = $_GET['status'] ?? '';
    $dateFrom = $_GET['date_from'] ?? '';
    $dateTo = $_GET['date_to'] ?? '';
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if ($userId) {
        $conditions[] = "t.user_id = :user_id";
        $params['user_id'] = $userId;
    }
    
    if ($planId) {
        $conditions[] = "t.plan_id = :plan_id";
        $params['plan_id'] = $planId;
    }
    
    if ($status) {
        $conditions[] = "t.status = :status";
        $params['status'] = $status;
    }
    
    if ($dateFrom) {
        $conditions[] = "DATE(t.created_at) >= :date_from";
        $params['date_from'] = $dateFrom;
    }
    
    if ($dateTo) {
        $conditions[] = "DATE(t.created_at) <= :date_to";
        $params['date_to'] = $dateTo;
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    // Check if trades table exists, if not create it
    $checkTable = $pdo->query("SHOW TABLES LIKE 'trades'");
    if ($checkTable->rowCount() == 0) {
        // Create trades table
        $createTradesTable = "
        CREATE TABLE trades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            plan_id INT,
            asset VARCHAR(50) NOT NULL,
            trade_type ENUM('buy', 'sell') NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            entry_price DECIMAL(15,8) NOT NULL,
            exit_price DECIMAL(15,8),
            profit_loss DECIMAL(15,2) DEFAULT 0.00,
            status ENUM('open', 'closed', 'cancelled') DEFAULT 'open',
            opened_at DATETIME NOT NULL,
            closed_at DATETIME,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (plan_id) REFERENCES trading_plans(id) ON DELETE SET NULL,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            
            INDEX idx_user_id (user_id),
            INDEX idx_plan_id (plan_id),
            INDEX idx_status (status),
            INDEX idx_opened_at (opened_at),
            INDEX idx_closed_at (closed_at),
            INDEX idx_asset (asset)
        )";
        
        $pdo->exec($createTradesTable);
    }
    
    // Get trades with user and plan information
    $sql = "SELECT 
                t.*,
                u.username,
                u.first_name,
                u.last_name,
                tp.name as plan_name,
                creator.username as created_by_username
            FROM trades t
            LEFT JOIN users u ON t.user_id = u.id
            LEFT JOIN trading_plans tp ON t.plan_id = tp.id
            LEFT JOIN users creator ON t.created_by = creator.id
            {$whereClause}
            ORDER BY t.created_at DESC
            LIMIT {$limit} OFFSET {$offset}";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $trades = $stmt->fetchAll();
    
    // Get total count for pagination
    $countSql = "SELECT COUNT(*) as total FROM trades t {$whereClause}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalTrades = $countStmt->fetch()['total'];
    
    // Get users for filter dropdown
    $users = User::where('role', 'user');
    
    // Get trading plans for filter dropdown
    $plans = TradingPlan::getActivePlans();
    
    // Calculate pagination
    $totalPages = ceil($totalTrades / $limit);
    
    // Get trade statistics
    $statsSql = "SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_trades,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_trades,
                    SUM(CASE WHEN status = 'closed' AND profit_loss > 0 THEN 1 ELSE 0 END) as profitable_trades,
                    SUM(CASE WHEN status = 'closed' THEN profit_loss ELSE 0 END) as total_profit_loss,
                    AVG(CASE WHEN status = 'closed' THEN profit_loss ELSE NULL END) as avg_profit_loss
                 FROM trades t {$whereClause}";
    
    $statsStmt = $pdo->prepare($statsSql);
    $statsStmt->execute($params);
    $statistics = $statsStmt->fetch();
    
    // Render the view
    $view = new AdminTradeHistoryView();
    $view->render([
        'trades' => $trades,
        'users' => $users,
        'plans' => $plans,
        'statistics' => $statistics,
        'filters' => [
            'user_id' => $userId,
            'plan_id' => $planId,
            'status' => $status,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ],
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalTrades,
            'limit' => $limit
        ],
        'user' => getCurrentUser()
    ]);
    
} catch (Exception $e) {
    error_log("Admin trade history error: " . $e->getMessage());
    
    // Show error page
    $view = new AdminTradeHistoryView();
    $view->render([
        'error' => 'Unable to load trade history. Please try again.',
        'trades' => [],
        'users' => [],
        'plans' => [],
        'statistics' => [],
        'filters' => [],
        'pagination' => [],
        'user' => getCurrentUser()
    ]);
}
?>