<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/TradingPlan.php';
require_once __DIR__ . '/../../classes/models/PaymentMethod.php';
require_once __DIR__ . '/../../classes/views/TradingPlansView.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: /login.php');
    exit();
}

// Ensure user role is 'user'
if (!hasRole('user')) {
    header('Location: /login.php?error=access_denied');
    exit();
}

try {
    // Get active trading plans
    $tradingPlans = TradingPlan::getActivePlans();
    
    // Get active payment methods
    $paymentMethods = PaymentMethod::getActive();
    
    // Get current user
    $user = getCurrentUser();
    
    // Create and render the trading plans view
    $view = new TradingPlansView($tradingPlans, $paymentMethods, $user);
    $view->render();
    
} catch (Exception $e) {
    error_log("Trading plans error: " . $e->getMessage());
    header('Location: /user/dashboard/?error=system_error');
    exit();
}
?>