<?php
require_once '../../includes/functions.php';
require_once '../../classes/services/SessionManager.php';
require_once '../../classes/services/TransactionManager.php';
require_once '../../classes/views/TransactionHistoryView.php';

// Check authentication
SessionManager::requireLogin();
$user = SessionManager::getCurrentUser();

if (!$user || $user->role !== 'user') {
    header('Location: ../../login.php');
    exit;
}

// Get filter parameters
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;
$type = $_GET['type'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// Build filters
$filters = ['user_id' => $user->getId()];
if ($type) $filters['type'] = $type;
if ($dateFrom) $filters['date_from'] = $dateFrom . ' 00:00:00';
if ($dateTo) $filters['date_to'] = $dateTo . ' 23:59:59';

// Get transactions
$transactions = TransactionManager::getUserTransactionHistory($user->getId(), $limit, $offset, $type ?: null);

// Get transaction statistics
$statistics = TransactionManager::getTransactionStatistics($user->getId(), $dateFrom ?: null, $dateTo ?: null);

// Get user transaction summary
$summary = TransactionManager::getUserTransactionSummary($user->getId());

// Calculate total pages for pagination
$totalTransactions = Transaction::count("user_id = ?", [$user->getId()]);
$totalPages = ceil($totalTransactions / $limit);

// Render view
$view = new TransactionHistoryView();
$view->render([
    'user' => $user,
    'transactions' => $transactions,
    'statistics' => $statistics,
    'summary' => $summary,
    'filters' => [
        'type' => $type,
        'date_from' => $dateFrom,
        'date_to' => $dateTo
    ],
    'pagination' => [
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_records' => $totalTransactions,
        'limit' => $limit
    ]
]);
?>