<?php
session_start();

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/controllers/AdminController.php';
require_once __DIR__ . '/../../classes/views/AdminDashboardView.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !hasRole(['admin', 'superadmin'])) {
    header('Location: /login.php');
    exit();
}

try {
    // Get dashboard statistics
    $stats = AdminController::getDashboardStats();
    if ($stats === false) {
        throw new Exception('Failed to load dashboard statistics');
    }
    
    // Get recent activity
    $recentActivity = AdminController::getRecentActivity(10);
    
    // Set page title and content
    $pageTitle = 'Dashboard';
    
    // Start output buffering to capture the dashboard content
    ob_start();
    
    // Render the dashboard content
    $view = new AdminDashboardView($stats, $recentActivity);
    echo $view->renderContent();
    
    // Get the content
    $content = ob_get_clean();
    
    // Include the admin layout
    include __DIR__ . '/../../layouts/admin_layout.php';
    
} catch (Exception $e) {
    error_log("Admin dashboard error: " . $e->getMessage());
    
    // Show error page
    $view = new AdminDashboardView([], []);
    $view->render('admin');
}
?>