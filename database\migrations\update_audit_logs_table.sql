-- Update audit_logs table to match AuditTrailService requirements
-- This migration updates the existing audit_logs table structure

-- Add new columns if they don't exist
ALTER TABLE audit_logs 
ADD COLUMN IF NOT EXISTS entity_type VARCHAR(50) AFTER action,
ADD COLUMN IF NOT EXISTS entity_id VARCHAR(100) AFTER entity_type,
ADD COLUMN IF NOT EXISTS changes JSON AFTER entity_id,
ADD COLUMN IF NOT EXISTS additional_data JSON AFTER user_agent;

-- Update existing columns to match new structure
-- Note: This will preserve existing data while adding new functionality

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_entity_type ON audit_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_entity_id ON audit_logs(entity_id);

-- Update any existing records to use new structure (optional migration)
-- This can be run to migrate existing audit logs to new format
UPDATE audit_logs 
SET 
    entity_type = COALESCE(table_name, 'unknown'),
    entity_id = COALESCE(record_id, '0'),
    changes = JSON_OBJECT('old_values', old_values, 'new_values', new_values)
WHERE entity_type IS NULL OR entity_id IS NULL;