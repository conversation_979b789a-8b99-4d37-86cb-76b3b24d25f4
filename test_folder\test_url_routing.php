<?php
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/functions.php';

echo "Running URL Routing Tests...\n";

// Test Case 1: Basic URL Generation
$expected = BASE_URL . 'dashboard';
$actual = url('dashboard');
assert($actual === $expected, 'Test Case 1 Failed: Basic URL generation');

// Test Case 2: URL with leading slash
$expected = BASE_URL . 'admin/dashboard';
$actual = url('/admin/dashboard');
assert($actual === $expected, 'Test Case 2 Failed: URL with leading slash');

// Test Case 3: Empty path
$expected = BASE_URL;
$actual = url('');
assert($actual === $expected, 'Test Case 3 Failed: Empty path');

// Test Case 4: URL with multiple segments
$expected = BASE_URL . 'superadmin/settings/system';
$actual = url('superadmin/settings/system');
assert($actual === $expected, 'Test Case 4 Failed: URL with multiple segments');

echo "All URL routing tests passed!\n";
?>