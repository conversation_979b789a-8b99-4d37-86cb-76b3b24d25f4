:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #f8f9fa;
}

.sidebar {
    min-height: 100vh;
    background: #ffffff;
    box-shadow: 2px 0 15px rgba(0,0,0,0.08);
    border-right: 1px solid #e9ecef;
}

.sidebar .nav-link {
    color: #495057;
    padding: 12px 20px;
    margin: 2px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background: rgba(0, 123, 255, 0.08);
    border-color: rgba(0, 123, 255, 0.2);
    transform: translateX(3px);
}

.sidebar .nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.main-content {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.navbar-brand img {
    height: 40px;
    width: auto;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.balance-card {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stats-card {
    border-radius: 15px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        z-index: 1050;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}