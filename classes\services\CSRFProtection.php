<?php
require_once __DIR__ . '/SessionManager.php';

/**
 * CSRFProtection - Handles CSRF token generation and validation
 */
class CSRFProtection {
    
    /**
     * Generate CSRF token
     */
    public static function generateToken() {
        return SessionManager::getCSRFToken();
    }
    
    /**
     * Validate CSRF token
     */
    public static function validateToken($token) {
        return SessionManager::validateCSRFToken($token);
    }
    
    /**
     * Get CSRF token input field HTML
     */
    public static function getTokenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Get CSRF token meta tag HTML
     */
    public static function getTokenMeta() {
        $token = self::generateToken();
        return '<meta name="csrf-token" content="' . htmlspecialchars($token) . '">';
    }
    
    /**
     * Validate CSRF token from request
     */
    public static function validateRequest($method = 'POST') {
        $token = null;
        
        // Get token from different sources
        if ($method === 'POST' && isset($_POST['csrf_token'])) {
            $token = $_POST['csrf_token'];
        } elseif (isset($_GET['csrf_token'])) {
            $token = $_GET['csrf_token'];
        } elseif (isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
            $token = $_SERVER['HTTP_X_CSRF_TOKEN'];
        } elseif (isset($_SERVER['HTTP_X_XSRF_TOKEN'])) {
            $token = $_SERVER['HTTP_X_XSRF_TOKEN'];
        }
        
        if (!$token) {
            return [
                'valid' => false,
                'error' => 'CSRF token not found in request'
            ];
        }
        
        if (!self::validateToken($token)) {
            return [
                'valid' => false,
                'error' => 'Invalid CSRF token'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * Require valid CSRF token or die
     */
    public static function requireValidToken($method = 'POST') {
        $result = self::validateRequest($method);
        
        if (!$result['valid']) {
            http_response_code(403);
            die(json_encode([
                'success' => false,
                'error' => $result['error']
            ]));
        }
    }
    
    /**
     * Middleware function to check CSRF token
     */
    public static function middleware($method = 'POST') {
        return function() use ($method) {
            self::requireValidToken($method);
        };
    }
    
    /**
     * Generate token for AJAX requests
     */
    public static function getAjaxToken() {
        return [
            'token' => self::generateToken(),
            'header' => 'X-CSRF-TOKEN'
        ];
    }
    
    /**
     * Validate token from AJAX request
     */
    public static function validateAjaxRequest() {
        $token = $_SERVER['HTTP_X_CSRF_TOKEN'] ?? $_POST['csrf_token'] ?? null;
        
        if (!$token) {
            http_response_code(403);
            echo json_encode([
                'success' => false,
                'error' => 'CSRF token required for AJAX requests'
            ]);
            exit();
        }
        
        if (!self::validateToken($token)) {
            http_response_code(403);
            echo json_encode([
                'success' => false,
                'error' => 'Invalid CSRF token'
            ]);
            exit();
        }
        
        return true;
    }
}
?>