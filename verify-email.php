<?php
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'classes/controllers/AuthController.php';

// Process email verification
$verifyData = AuthController::verifyEmail();
$errors = $verifyData['errors'];
$message = $verifyData['message'];
$success = $verifyData['success'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, <?php echo PRIMARY_COLOR; ?> 0%, <?php echo SECONDARY_COLOR; ?> 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .verify-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .verify-header {
            background: <?php echo PRIMARY_COLOR; ?>;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .verify-body {
            padding: 2rem;
            text-align: center;
        }
        .btn-primary {
            background-color: <?php echo PRIMARY_COLOR; ?>;
            border-color: <?php echo PRIMARY_COLOR; ?>;
        }
        .btn-primary:hover {
            background-color: <?php echo SECONDARY_COLOR; ?>;
            border-color: <?php echo SECONDARY_COLOR; ?>;
        }
        .alert {
            border-radius: 10px;
        }
        .icon-large {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="verify-container">
                    <div class="verify-header">
                        <h2><i class="fas fa-envelope-open"></i> <?php echo SITE_NAME; ?></h2>
                        <p class="mb-0">Email Verification</p>
                    </div>
                    
                    <div class="verify-body">
                        <?php if ($success): ?>
                            <div class="icon-large text-success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <h3 class="text-success mb-3">Email Verified Successfully!</h3>
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                            </div>
                            <p class="text-muted mb-4">
                                Your email address has been verified and your account is now active. 
                                You can now log in and start using our platform.
                            </p>
                            <a href="login.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt"></i> Go to Login
                            </a>
                        <?php else: ?>
                            <div class="icon-large text-danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <h3 class="text-danger mb-3">Verification Failed</h3>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle"></i> 
                                <?php echo htmlspecialchars($errors['token'] ?? 'Email verification failed'); ?>
                            </div>
                            <p class="text-muted mb-4">
                                The verification link may have expired or is invalid. 
                                Please request a new verification email.
                            </p>
                            <div class="d-grid gap-2">
                                <a href="resend-verification.php" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i> Resend Verification Email
                                </a>
                                <a href="register.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-user-plus"></i> Register New Account
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <hr class="my-4">
                        
                        <div class="text-center">
                            <p class="mb-0">
                                <a href="login.php" class="text-decoration-none">
                                    <i class="fas fa-arrow-left"></i> Back to Login
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>