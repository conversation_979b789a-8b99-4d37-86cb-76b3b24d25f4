<?php
/**
 * ConfigManager - Centralized configuration management
 */
class ConfigManager {
    private static $instance = null;
    private $config = [];
    private $cached = false;
    
    private function __construct() {
        $this->loadConfiguration();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load configuration from multiple sources
     */
    private function loadConfiguration() {
        // Load from config.php
        $this->config = [
            'database' => [
                'host' => DB_HOST,
                'port' => DB_PORT,
                'name' => DB_NAME,
                'user' => DB_USER,
                'password' => DB_PASS
            ],
            'smtp' => [
                'host' => SMTP_HOST,
                'port' => SMTP_PORT,
                'username' => SMTP_USERNAME,
                'password' => SMTP_PASSWORD,
                'from_email' => SMTP_FROM_EMAIL,
                'from_name' => SMTP_FROM_NAME
            ],
            'app' => [
                'name' => SITE_NAME,
                'url' => BASE_URL,
                'currency' => DEFAULT_CURRENCY,
                'currency_symbol' => CURRENCY_SYMBOL,
                'debug' => DEBUG_MODE ?? false
            ],
            'features' => [
                '2fa' => ENABLE_2FA,
                'email_verification' => ENABLE_EMAIL_VERIFICATION,
                'kyc_required' => KYC_REQUIRED ?? false
            ],
            'financial' => [
                'registration_bonus' => REGISTRATION_BONUS,
                'deposit_bonus_percent' => DEPOSIT_BONUS_PERCENT,
                'min_withdrawal' => MIN_WITHDRAWAL_AMOUNT ?? 10.00,
                'max_withdrawal' => MAX_WITHDRAWAL_AMOUNT ?? 10000.00
            ]
        ];
        
        // Load from database settings
        $this->loadDatabaseSettings();
        $this->cached = true;
    }
    
    /**
     * Load settings from database
     */
    private function loadDatabaseSettings() {
        try {
            require_once __DIR__ . '/../models/SystemSetting.php';
            $settings = SystemSetting::getAllAsArray();
            
            if (!empty($settings)) {
                $this->config['system'] = $settings;
            }
        } catch (Exception $e) {
            // Database not available or settings table doesn't exist
            error_log("Could not load database settings: " . $e->getMessage());
        }
    }
    
    /**
     * Get configuration value
     */
    public function get($key, $default = null) {
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }
    
    /**
     * Set configuration value (runtime only)
     */
    public function set($key, $value) {
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }
    
    /**
     * Check if configuration key exists
     */
    public function has($key) {
        return $this->get($key) !== null;
    }
    
    /**
     * Get all configuration
     */
    public function all() {
        return $this->config;
    }
    
    /**
     * Refresh configuration from sources
     */
    public function refresh() {
        $this->cached = false;
        $this->loadConfiguration();
    }
    
    /**
     * Get environment-specific configuration
     */
    public function getEnvironment() {
        return $this->get('app.debug') ? 'development' : 'production';
    }
    
    /**
     * Check if feature is enabled
     */
    public function isFeatureEnabled($feature) {
        return (bool) $this->get("features.$feature", false);
    }
}

// Global helper function
function config($key, $default = null) {
    return ConfigManager::getInstance()->get($key, $default);
}
?>