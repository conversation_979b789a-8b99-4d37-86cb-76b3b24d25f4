<?php
require_once __DIR__ . '/ValidationHelper.php';
require_once __DIR__ . '/../models/User.php';

/**
 * Registration Form Validator
 * 
 * Handles validation of user registration form inputs using configurable rules.
 * Provides comprehensive validation including uniqueness checks, security validation,
 * and customizable error handling.
 * 
 * @package Validators
 * <AUTHOR> Trading Platform
 * @version 1.0.0
 */
class RegistrationValidator {
    /** @var array<string, string> Validation errors indexed by field name */
    private array $errors = [];
    
    /** @var array<string, array> Validation rules configuration */
    private array $validationRules = [];
    
    public function __construct() {
        $this->initializeValidationRules();
    }
    
    /**
     * Initialize validation rules configuration
     */
    private function initializeValidationRules() {
        $this->validationRules = [
            'first_name' => [
                'validator' => 'ValidationHelper::validateName',
                'params' => ['First name'],
                'required' => true
            ],
            'last_name' => [
                'validator' => 'ValidationHelper::validateName', 
                'params' => ['Last name'],
                'required' => true
            ],
            'username' => [
                'validator' => 'ValidationHelper::validateUsername',
                'params' => [],
                'required' => true,
                'unique_check' => 'User::findByUsername'
            ],
            'email' => [
                'validator' => 'ValidationHelper::validateEmail',
                'params' => [],
                'required' => true,
                'unique_check' => 'User::findByEmail'
            ],
            'password' => [
                'validator' => 'ValidationHelper::validatePassword',
                'params' => [],
                'required' => true
            ],
            'phone' => [
                'validator' => 'ValidationHelper::validatePhone',
                'params' => [],
                'required' => false
            ]
        ];
    }
    
    /**
     * Validate registration form data using configured rules
     */
    public function validate($data) {
        $this->errors = [];
        
        // Validate data structure first
        if (!$this->validateDataStructure($data)) {
            return false;
        }
        
        // Validate using rules configuration
        foreach ($this->validationRules as $field => $rule) {
            $this->validateField($field, $data[$field] ?? '', $rule);
        }
        
        // Special validations that don't fit the standard pattern
        $this->validatePasswordConfirmation($data['password'] ?? '', $data['password_confirmation'] ?? '');
        $this->validateTermsAcceptance($data['terms_accepted'] ?? false);
        $this->validateCSRFToken($data['csrf_token'] ?? '');
        
        return empty($this->errors);
    }
    
    /**
     * Validate that the input data has the expected structure
     */
    private function validateDataStructure($data): bool {
        if (!is_array($data)) {
            $this->addError('general', 'Invalid data format provided.');
            return false;
        }
        
        // Check for required fields
        $requiredFields = ['first_name', 'last_name', 'username', 'email', 'password', 'password_confirmation'];
        $missingFields = [];
        
        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $data)) {
                $missingFields[] = $field;
            }
        }
        
        if (!empty($missingFields)) {
            $this->addError('general', 'Missing required fields: ' . implode(', ', $missingFields));
            return false;
        }
        
        return true;
    }
    
    /**
     * Validate a single field using its rule configuration
     */
    private function validateField($fieldName, $value, $rule) {
        // Skip validation for optional empty fields
        if (!$rule['required'] && empty($value)) {
            return;
        }
        
        // Run the validator function
        $validator = $rule['validator'];
        $params = array_merge([$value], $rule['params']);
        $error = call_user_func_array($validator, $params);
        
        if ($error) {
            $this->errors[$fieldName] = $error;
            return;
        }
        
        // Check uniqueness if required (only if basic validation passed)
        if (isset($rule['unique_check']) && !empty($value) && !isset($this->errors[$fieldName])) {
            $uniqueChecker = $rule['unique_check'];
            try {
                if (call_user_func($uniqueChecker, $value)) {
                    $this->errors[$fieldName] = $this->getUniqueErrorMessage($fieldName);
                }
            } catch (Exception $e) {
                error_log("Uniqueness check failed for {$fieldName}: " . $e->getMessage());
                $this->errors[$fieldName] = 'Unable to verify ' . $fieldName . ' availability.';
            }
        }
    }
    
    /**
     * Get appropriate error message for uniqueness validation
     */
    private function getUniqueErrorMessage($fieldName) {
        $messages = [
            'username' => 'Username is already taken.',
            'email' => 'Email address is already registered.'
        ];
        
        return $messages[$fieldName] ?? ucfirst($fieldName) . ' is already in use.';
    }
    
    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : '';
    }
    
    /**
     * Check if validation passed
     */
    public function isValid() {
        return empty($this->errors);
    }
    
    /**
     * Get errors for a specific field
     */
    public function getFieldError($fieldName) {
        return $this->errors[$fieldName] ?? null;
    }
    
    /**
     * Check if a specific field has errors
     */
    public function hasFieldError($fieldName) {
        return isset($this->errors[$fieldName]);
    }
    
    /**
     * Get formatted error messages for display
     */
    public function getFormattedErrors() {
        $formatted = [];
        foreach ($this->errors as $field => $message) {
            $formatted[] = ucfirst(str_replace('_', ' ', $field)) . ': ' . $message;
        }
        return $formatted;
    }
    

    
    /**
     * Validate password confirmation matches password
     */
    private function validatePasswordConfirmation($password, $passwordConfirmation) {
        if (empty($passwordConfirmation)) {
            $this->addError('password_confirmation', 'Password confirmation is required.');
            return;
        }
        
        if (!hash_equals($password, $passwordConfirmation)) {
            $this->addError('password_confirmation', 'Passwords do not match.');
        }
    }
    
    /**
     * Validate terms and conditions acceptance
     */
    private function validateTermsAcceptance($termsAccepted) {
        if (!$termsAccepted) {
            $this->addError('terms_accepted', 'You must accept the terms and conditions to register.');
        }
    }
    
    /**
     * Validate CSRF token for security
     */
    private function validateCSRFToken($token) {
        $error = ValidationHelper::validateCSRFToken($token);
        if ($error) {
            $this->addError('csrf_token', $error);
        }
    }
    
    /**
     * Add an error to the errors collection
     */
    private function addError($field, $message) {
        $this->errors[$field] = $message;
    }
    
    /**
     * Sanitize and normalize input data
     */
    public static function sanitize(array $data): array {
        $sanitized = [];
        
        // Define sanitization rules for each field
        $sanitizationRules = [
            'first_name' => 'name',
            'last_name' => 'name', 
            'username' => 'username',
            'email' => 'email',
            'password' => 'password',
            'password_confirmation' => 'password',
            'phone' => 'phone',
            'terms_accepted' => 'boolean',
            'csrf_token' => 'token'
        ];
        
        foreach ($sanitizationRules as $field => $type) {
            $sanitized[$field] = self::sanitizeField($data[$field] ?? null, $type);
        }
        
        return $sanitized;
    }
    
    /**
     * Sanitize individual field based on its type
     */
    private static function sanitizeField($value, string $type) {
        switch ($type) {
            case 'name':
                return ValidationHelper::sanitize($value ?? '');
                
            case 'username':
                return trim($value ?? '');
                
            case 'email':
                return trim(strtolower($value ?? ''));
                
            case 'password':
                // Don't sanitize passwords - preserve original input
                return $value ?? '';
                
            case 'phone':
                return ValidationHelper::sanitize($value ?? '');
                
            case 'boolean':
                return !empty($value);
                
            case 'token':
                return trim($value ?? '');
                
            default:
                return ValidationHelper::sanitize($value ?? '');
        }
    }
    
    /**
     * Get validation summary for debugging
     */
    public function getValidationSummary(): array {
        return [
            'total_errors' => count($this->errors),
            'fields_with_errors' => array_keys($this->errors),
            'is_valid' => $this->isValid(),
            'validation_rules_count' => count($this->validationRules)
        ];
    }
    
    /**
     * Get validation rules for frontend validation
     * 
     * @return array<string, array> Simplified rules for client-side use
     */
    public function getClientValidationRules(): array {
        $clientRules = [];
        
        foreach ($this->validationRules as $field => $rule) {
            $clientRules[$field] = [
                'required' => $rule['required'],
                'type' => $this->getFieldType($field)
            ];
        }
        
        return $clientRules;
    }
    
    /**
     * Get field type for client-side validation
     */
    private function getFieldType(string $field): string {
        $typeMap = [
            'email' => 'email',
            'password' => 'password',
            'phone' => 'tel',
            'username' => 'text'
        ];
        
        return $typeMap[$field] ?? 'text';
    }
    
    /**
     * Reset validator state for reuse
     */
    public function reset(): void {
        $this->errors = [];
    }
}
?>