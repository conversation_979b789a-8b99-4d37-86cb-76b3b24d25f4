<?php
// Simple debug script to test login functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Login Debug Test</h2>";

try {
    require_once 'config.php';
    echo "✅ Config loaded<br>";
    
    require_once 'includes/functions.php';
    echo "✅ Functions loaded<br>";
    
    require_once 'includes/db_connect.php';
    echo "✅ DB connect loaded<br>";
    
    // Test database connection
    $db = getDB();
    echo "✅ Database connection successful<br>";
    
    // Test URL functions
    echo "Base URL: " . getBaseUrl() . "<br>";
    echo "URL function: " . url('test') . "<br>";
    echo "Asset function: " . asset('css/test.css') . "<br>";
    
    // Test user functions
    echo "Is logged in: " . (isLoggedIn() ? 'Yes' : 'No') . "<br>";
    echo "User role: " . (getUserRole() ?? 'None') . "<br>";
    
    // Test hasRole function
    echo "Has user role: " . (hasRole('user') ? 'Yes' : 'No') . "<br>";
    echo "Has admin role: " . (hasRole(['admin', 'superadmin']) ? 'Yes' : 'No') . "<br>";
    
    echo "<h3>Test Links:</h3>";
    echo '<a href="' . url('user/login.php') . '">User Login</a><br>';
    echo '<a href="' . url('admin/login.php') . '">Admin Login</a><br>';
    echo '<a href="' . url('superadmin/login.php') . '">Super Admin Login</a><br>';
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}
?>