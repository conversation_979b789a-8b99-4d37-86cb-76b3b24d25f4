<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Centralized error handling and logging
 */
class ErrorHandler {
    private static $instance = null;
    private $logFile;
    private $debugMode;
    
    private function __construct() {
        $this->logFile = __DIR__ . '/../../logs/error.log';
        $this->debugMode = defined('DEBUG_MODE') ? DEBUG_MODE : false;
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Handle exceptions with proper logging and user-friendly messages
     */
    public function handleException(Throwable $exception, $context = []) {
        $errorId = $this->generateErrorId();
        
        $logData = [
            'error_id' => $errorId,
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'context' => $context,
            'user_id' => $_SESSION['user_id'] ?? null,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        $this->logError($logData);
        
        if ($this->debugMode) {
            return [
                'error' => true,
                'message' => $exception->getMessage(),
                'error_id' => $errorId,
                'debug' => [
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine(),
                    'trace' => $exception->getTrace()
                ]
            ];
        }
        
        return [
            'error' => true,
            'message' => 'An error occurred. Please try again later.',
            'error_id' => $errorId
        ];
    }
    
    /**
     * Handle validation errors
     */
    public function handleValidationErrors(array $errors) {
        return [
            'error' => true,
            'type' => 'validation',
            'message' => 'Please correct the errors below.',
            'errors' => $errors
        ];
    }
    
    /**
     * Log error to file
     */
    private function logError(array $logData) {
        $logEntry = json_encode($logData, JSON_PRETTY_PRINT) . "\n" . str_repeat('-', 80) . "\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Generate unique error ID for tracking
     */
    private function generateErrorId() {
        return 'ERR_' . date('Ymd') . '_' . substr(uniqid(), -6);
    }
    
    /**
     * Set up global exception handler
     */
    public function setupGlobalHandler() {
        set_exception_handler([$this, 'handleException']);
        set_error_handler([$this, 'handleError']);
    }
    
    /**
     * Handle PHP errors
     */
    public function handleError($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        throw new ErrorException($message, 0, $severity, $file, $line);
    }
}
?>