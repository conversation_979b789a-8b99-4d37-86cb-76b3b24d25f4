<?php
/**
 * Model Testing Script
 * Tests all core PHP classes and data models
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../includes/db_connect.php';
require_once __DIR__ . '/../includes/functions.php';

// Include all model classes
require_once __DIR__ . '/../classes/models/BaseModel.php';
require_once __DIR__ . '/../classes/models/User.php';
require_once __DIR__ . '/../classes/models/TradingPlan.php';
require_once __DIR__ . '/../classes/models/PaymentMethod.php';
require_once __DIR__ . '/../classes/models/Deposit.php';
require_once __DIR__ . '/../classes/models/Withdrawal.php';
require_once __DIR__ . '/../classes/models/Transaction.php';
require_once __DIR__ . '/../classes/models/SupportTicket.php';
require_once __DIR__ . '/../classes/models/SystemSetting.php';

echo "<h1>🧪 Core PHP Classes and Data Models Test</h1>";
echo "<div style='font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto;'>";

$errors = [];
$success = [];
$warnings = [];

// Test 1: BaseModel functionality
echo "<h2>1️⃣ BaseModel Functionality Test</h2>";

try {
    // Test User model (extends BaseModel)
    $user = new User();
    $user->username = 'testuser';
    $user->email = '<EMAIL>';
    $user->password = 'password123';
    $user->first_name = 'Test';
    $user->last_name = 'User';
    $user->role = User::ROLE_USER;
    $user->status = User::STATUS_ACTIVE;
    
    // Test validation
    $validationErrors = $user->validate();
    if (empty($validationErrors)) {
        $success[] = "✅ BaseModel validation working correctly";
    } else {
        $warnings[] = "⚠️ BaseModel validation found issues: " . implode(', ', $validationErrors);
    }
    
    // Test toArray method
    $userArray = $user->toArray();
    if (is_array($userArray) && isset($userArray['username'])) {
        $success[] = "✅ BaseModel toArray() method working";
    } else {
        $errors[] = "❌ BaseModel toArray() method failed";
    }
    
    // Test magic methods
    if ($user->username === 'testuser' && isset($user->email)) {
        $success[] = "✅ BaseModel magic methods (__get, __set, __isset) working";
    } else {
        $errors[] = "❌ BaseModel magic methods failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ BaseModel test failed: " . $e->getMessage();
}

// Test 2: User Model
echo "<h2>2️⃣ User Model Test</h2>";

try {
    // Test User constants
    if (defined('User::ROLE_USER') && defined('User::STATUS_ACTIVE')) {
        $success[] = "✅ User model constants defined correctly";
    } else {
        $errors[] = "❌ User model constants missing";
    }
    
    // Test User methods
    $user = new User();
    if (method_exists($user, 'verifyPassword') && method_exists($user, 'updateBalance')) {
        $success[] = "✅ User model methods exist";
    } else {
        $errors[] = "❌ User model methods missing";
    }
    
    // Test User relationships
    if (method_exists($user, 'getDeposits') && method_exists($user, 'getWithdrawals')) {
        $success[] = "✅ User model relationships methods exist";
    } else {
        $errors[] = "❌ User model relationship methods missing";
    }
    
    // Test static methods
    if (method_exists('User', 'findByEmail') && method_exists('User', 'findByUsername')) {
        $success[] = "✅ User model static methods exist";
    } else {
        $errors[] = "❌ User model static methods missing";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ User model test failed: " . $e->getMessage();
}

// Test 3: TradingPlan Model
echo "<h2>3️⃣ TradingPlan Model Test</h2>";

try {
    $plan = new TradingPlan();
    $plan->name = 'Test Plan';
    $plan->min_deposit = 100;
    $plan->max_deposit = 1000;
    $plan->daily_return = 2.5;
    $plan->duration_days = 30;
    
    // Test validation
    $validationErrors = $plan->validate();
    if (empty($validationErrors)) {
        $success[] = "✅ TradingPlan validation working";
    } else {
        $warnings[] = "⚠️ TradingPlan validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test calculation methods
    if (method_exists($plan, 'calculateProfit') && method_exists($plan, 'getTotalReturn')) {
        $totalReturn = $plan->getTotalReturn();
        $profit = $plan->calculateProfit(1000);
        
        if ($totalReturn == 75 && $profit == 750) { // 2.5% * 30 days = 75%, 1000 * 75% = 750
            $success[] = "✅ TradingPlan calculation methods working correctly";
        } else {
            $warnings[] = "⚠️ TradingPlan calculations may be incorrect (Total: $totalReturn, Profit: $profit)";
        }
    } else {
        $errors[] = "❌ TradingPlan calculation methods missing";
    }
    
    // Test static methods
    if (method_exists('TradingPlan', 'getActivePlans') && method_exists('TradingPlan', 'getOrderedPlans')) {
        $success[] = "✅ TradingPlan static methods exist";
    } else {
        $errors[] = "❌ TradingPlan static methods missing";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ TradingPlan model test failed: " . $e->getMessage();
}

// Test 4: Deposit Model
echo "<h2>4️⃣ Deposit Model Test</h2>";

try {
    $deposit = new Deposit();
    $deposit->user_id = 1;
    $deposit->amount = 500;
    $deposit->status = Deposit::STATUS_PENDING;
    
    // Test validation
    $validationErrors = $deposit->validate();
    if (count($validationErrors) <= 2) { // May have user_id and plan_id validation errors
        $success[] = "✅ Deposit validation working";
    } else {
        $warnings[] = "⚠️ Deposit validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test status methods
    if (method_exists($deposit, 'isPending') && method_exists($deposit, 'isApproved')) {
        if ($deposit->isPending() && !$deposit->isApproved()) {
            $success[] = "✅ Deposit status methods working";
        } else {
            $warnings[] = "⚠️ Deposit status methods may be incorrect";
        }
    } else {
        $errors[] = "❌ Deposit status methods missing";
    }
    
    // Test business logic methods
    if (method_exists($deposit, 'approve') && method_exists($deposit, 'calculateBonus')) {
        $success[] = "✅ Deposit business logic methods exist";
    } else {
        $errors[] = "❌ Deposit business logic methods missing";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Deposit model test failed: " . $e->getMessage();
}

// Test 5: Withdrawal Model
echo "<h2>5️⃣ Withdrawal Model Test</h2>";

try {
    $withdrawal = new Withdrawal();
    $withdrawal->user_id = 1;
    $withdrawal->amount = 200;
    $withdrawal->withdrawal_method = 'Bank Transfer';
    $withdrawal->account_details = ['account' => '*********'];
    $withdrawal->status = Withdrawal::STATUS_PENDING;
    
    // Test validation
    $validationErrors = $withdrawal->validate();
    if (count($validationErrors) <= 2) { // May have user balance validation errors
        $success[] = "✅ Withdrawal validation working";
    } else {
        $warnings[] = "⚠️ Withdrawal validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test JSON handling
    $accountDetails = $withdrawal->getAccountDetailsArray();
    if (is_array($accountDetails) && isset($accountDetails['account'])) {
        $success[] = "✅ Withdrawal JSON handling working";
    } else {
        $errors[] = "❌ Withdrawal JSON handling failed";
    }
    
    // Test status methods
    if (method_exists($withdrawal, 'isPending') && method_exists($withdrawal, 'approve')) {
        $success[] = "✅ Withdrawal status and business methods exist";
    } else {
        $errors[] = "❌ Withdrawal methods missing";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Withdrawal model test failed: " . $e->getMessage();
}

// Test 6: Transaction Model
echo "<h2>6️⃣ Transaction Model Test</h2>";

try {
    $transaction = new Transaction();
    $transaction->user_id = 1;
    $transaction->type = Transaction::TYPE_DEPOSIT;
    $transaction->amount = 500;
    $transaction->balance_before = 1000;
    $transaction->balance_after = 1500;
    $transaction->description = 'Test deposit';
    
    // Test validation
    $validationErrors = $transaction->validate();
    if (empty($validationErrors)) {
        $success[] = "✅ Transaction validation working";
    } else {
        $warnings[] = "⚠️ Transaction validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test helper methods
    if ($transaction->isCredit() && !$transaction->isDebit()) {
        $success[] = "✅ Transaction credit/debit detection working";
    } else {
        $errors[] = "❌ Transaction credit/debit detection failed";
    }
    
    // Test formatting methods
    $formattedAmount = $transaction->getFormattedAmount();
    $displayName = $transaction->getTypeDisplayName();
    
    if (strpos($formattedAmount, '+') !== false && $displayName === 'Deposit') {
        $success[] = "✅ Transaction formatting methods working";
    } else {
        $warnings[] = "⚠️ Transaction formatting may be incorrect";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Transaction model test failed: " . $e->getMessage();
}

// Test 7: SupportTicket Model
echo "<h2>7️⃣ SupportTicket Model Test</h2>";

try {
    $ticket = new SupportTicket();
    $ticket->user_id = 1;
    $ticket->subject = 'Test Support Ticket';
    $ticket->message = 'This is a test support ticket message for testing purposes.';
    $ticket->priority = SupportTicket::PRIORITY_MEDIUM;
    $ticket->category = SupportTicket::CATEGORY_GENERAL;
    
    // Test validation
    $validationErrors = $ticket->validate();
    if (empty($validationErrors)) {
        $success[] = "✅ SupportTicket validation working";
    } else {
        $warnings[] = "⚠️ SupportTicket validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test status methods
    if (method_exists($ticket, 'isPending') && method_exists($ticket, 'reply')) {
        $success[] = "✅ SupportTicket status and business methods exist";
    } else {
        $errors[] = "❌ SupportTicket methods missing";
    }
    
    // Test display methods
    $priorityName = $ticket->getPriorityDisplayName();
    $statusClass = $ticket->getStatusClass();
    
    if ($priorityName === 'Medium' && !empty($statusClass)) {
        $success[] = "✅ SupportTicket display methods working";
    } else {
        $warnings[] = "⚠️ SupportTicket display methods may be incorrect";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ SupportTicket model test failed: " . $e->getMessage();
}

// Test 8: SystemSetting Model
echo "<h2>8️⃣ SystemSetting Model Test</h2>";

try {
    $setting = new SystemSetting();
    $setting->setting_key = 'test_setting';
    $setting->setting_value = 'test_value';
    $setting->setting_type = SystemSetting::TYPE_STRING;
    $setting->category = SystemSetting::CATEGORY_GENERAL;
    
    // Test validation
    $validationErrors = $setting->validate();
    if (count($validationErrors) <= 1) { // May have unique key validation error
        $success[] = "✅ SystemSetting validation working";
    } else {
        $warnings[] = "⚠️ SystemSetting validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test typed value methods
    $setting->setting_type = SystemSetting::TYPE_BOOLEAN;
    $setting->setting_value = 'true';
    $typedValue = $setting->getTypedValue();
    
    if ($typedValue === true) {
        $success[] = "✅ SystemSetting typed value conversion working";
    } else {
        $errors[] = "❌ SystemSetting typed value conversion failed";
    }
    
    // Test static methods
    if (method_exists('SystemSetting', 'getValue') && method_exists('SystemSetting', 'setValue')) {
        $success[] = "✅ SystemSetting static methods exist";
    } else {
        $errors[] = "❌ SystemSetting static methods missing";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ SystemSetting model test failed: " . $e->getMessage();
}

// Test 9: PaymentMethod Model
echo "<h2>9️⃣ PaymentMethod Model Test</h2>";

try {
    $paymentMethod = new PaymentMethod();
    $paymentMethod->name = 'Test Bitcoin';
    $paymentMethod->type = PaymentMethod::TYPE_CRYPTO;
    $paymentMethod->details = [
        'address' => '**********************************',
        'network' => 'Bitcoin',
        'confirmations' => 3
    ];
    
    // Test validation
    $validationErrors = $paymentMethod->validate();
    if (count($validationErrors) <= 1) { // May have unique name validation error
        $success[] = "✅ PaymentMethod validation working";
    } else {
        $warnings[] = "⚠️ PaymentMethod validation issues: " . implode(', ', $validationErrors);
    }
    
    // Test JSON handling
    $detailsArray = $paymentMethod->getDetailsArray();
    if (is_array($detailsArray) && isset($detailsArray['address'])) {
        $success[] = "✅ PaymentMethod JSON handling working";
    } else {
        $errors[] = "❌ PaymentMethod JSON handling failed";
    }
    
    // Test formatting methods
    $formattedDetails = $paymentMethod->getFormattedDetails();
    $typeDisplay = $paymentMethod->getTypeDisplayName();
    
    if (is_array($formattedDetails) && $typeDisplay === 'Cryptocurrency') {
        $success[] = "✅ PaymentMethod formatting methods working";
    } else {
        $warnings[] = "⚠️ PaymentMethod formatting may be incorrect";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ PaymentMethod model test failed: " . $e->getMessage();
}

// Test 10: Database Integration
echo "<h2>🔟 Database Integration Test</h2>";

try {
    // Test database connection through models
    $userCount = User::count();
    $planCount = TradingPlan::count();
    
    if (is_numeric($userCount) && is_numeric($planCount)) {
        $success[] = "✅ Database integration working (Users: $userCount, Plans: $planCount)";
    } else {
        $errors[] = "❌ Database integration failed";
    }
    
    // Test find operations
    $firstUser = User::find(1);
    if ($firstUser && $firstUser->getId() == 1) {
        $success[] = "✅ Database find operations working";
    } else {
        $warnings[] = "⚠️ Database find operations may have issues";
    }
    
    // Test where operations
    $activeUsers = User::where('status', User::STATUS_ACTIVE);
    if (is_array($activeUsers)) {
        $success[] = "✅ Database where operations working";
    } else {
        $errors[] = "❌ Database where operations failed";
    }
    
} catch (Exception $e) {
    $errors[] = "❌ Database integration test failed: " . $e->getMessage();
}

// Test 11: Model Relationships
echo "<h2>1️⃣1️⃣ Model Relationships Test</h2>";

try {
    // Test User -> Deposits relationship
    if (method_exists('User', 'find')) {
        $user = User::find(1);
        if ($user && method_exists($user, 'getDeposits')) {
            $deposits = $user->getDeposits(5);
            if (is_array($deposits)) {
                $success[] = "✅ User -> Deposits relationship working";
            } else {
                $warnings[] = "⚠️ User -> Deposits relationship may have issues";
            }
        }
    }
    
    // Test Deposit -> User relationship
    $deposits = Deposit::all(1);
    if (!empty($deposits) && method_exists($deposits[0], 'getUser')) {
        $depositUser = $deposits[0]->getUser();
        if ($depositUser instanceof User) {
            $success[] = "✅ Deposit -> User relationship working";
        } else {
            $warnings[] = "⚠️ Deposit -> User relationship may have issues";
        }
    } else {
        $warnings[] = "⚠️ No deposits found to test relationships";
    }
    
} catch (Exception $e) {
    $warnings[] = "⚠️ Model relationships test had issues: " . $e->getMessage();
}

// Display Results
echo "<h2>📊 Test Results Summary</h2>";

// Success messages
if (!empty($success)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Successful Tests (" . count($success) . " items)</h3>";
    foreach ($success as $message) {
        echo "<p style='color: #155724; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Warnings
if (!empty($warnings)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ Warnings (" . count($warnings) . " items)</h3>";
    foreach ($warnings as $message) {
        echo "<p style='color: #856404; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Errors
if (!empty($errors)) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors (" . count($errors) . " items)</h3>";
    foreach ($errors as $message) {
        echo "<p style='color: #721c24; margin: 3px 0; font-size: 14px;'>$message</p>";
    }
    echo "</div>";
}

// Final Status
echo "<h2>🎯 Task 2 Completion Status</h2>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 TASK 2 COMPLETED SUCCESSFULLY!</h3>";
    echo "<p style='color: #155724; margin: 10px 0;'>Core PHP classes and data models have been implemented and tested.</p>";
    echo "<p style='color: #155724; margin: 10px 0;'><strong>Ready to proceed to Task 3: Build authentication and session management system</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 10px 0; border-radius: 5px; text-align: center;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>❌ TASK 2 HAS ISSUES</h3>";
    echo "<p style='color: #721c24; margin: 10px 0;'>Please resolve the errors above before proceeding to the next task.</p>";
    echo "</div>";
}

// Model Summary
echo "<h2>📋 Implemented Models Summary</h2>";
echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
echo "<ol style='color: #383d41;'>";
echo "<li><strong>BaseModel</strong> - Abstract base class with CRUD operations</li>";
echo "<li><strong>User</strong> - User management with authentication and balance tracking</li>";
echo "<li><strong>TradingPlan</strong> - Trading plan management with profit calculations</li>";
echo "<li><strong>Deposit</strong> - Deposit processing with approval workflow</li>";
echo "<li><strong>Withdrawal</strong> - Withdrawal management with balance validation</li>";
echo "<li><strong>Transaction</strong> - Complete transaction history and audit trail</li>";
echo "<li><strong>SupportTicket</strong> - Support ticket system with priority management</li>";
echo "<li><strong>SystemSetting</strong> - System configuration with typed values</li>";
echo "<li><strong>PaymentMethod</strong> - Payment method management with JSON details</li>";
echo "</ol>";
echo "</div>";

echo "</div>";
?>