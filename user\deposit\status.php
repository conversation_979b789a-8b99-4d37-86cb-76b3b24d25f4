<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/Deposit.php';
require_once __DIR__ . '/../../classes/views/DepositStatusView.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: /login.php');
    exit();
}

// Ensure user role is 'user'
if (!hasRole('user')) {
    header('Location: /login.php?error=access_denied');
    exit();
}

try {
    // Get current user
    $user = getCurrentUser();
    
    // Get user's deposits with pagination
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = 10;
    $offset = ($page - 1) * $limit;
    
    // Get deposits with details
    $deposits = Deposit::getByUser($user->getId(), $limit, $offset);
    
    // Get total count for pagination
    $totalDeposits = Deposit::count("user_id = {$user->getId()}");
    $totalPages = ceil($totalDeposits / $limit);
    
    // Get deposit statistics
    $stats = [
        'total_deposits' => Deposit::count("user_id = {$user->getId()}"),
        'pending_deposits' => Deposit::count("user_id = {$user->getId()} AND status = 'pending'"),
        'approved_deposits' => Deposit::count("user_id = {$user->getId()} AND status = 'approved'"),
        'rejected_deposits' => Deposit::count("user_id = {$user->getId()} AND status = 'rejected'"),
        'total_amount' => Deposit::getTotalAmountByUser($user->getId(), 'approved'),
        'pending_amount' => Deposit::getTotalAmountByUser($user->getId(), 'pending')
    ];
    
    // Create and render the deposit status view
    $view = new DepositStatusView($deposits, $stats, $page, $totalPages, $user);
    $view->render();
    
} catch (Exception $e) {
    error_log("Deposit status error: " . $e->getMessage());
    header('Location: /user/dashboard/?error=system_error');
    exit();
}
?>