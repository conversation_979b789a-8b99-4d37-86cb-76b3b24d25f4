<?php
require_once __DIR__ . '/../includes/db_connect.php';

try {
    $db = getDB();
    
    echo "Current audit_logs table structure:\n";
    $result = $db->query('DESCRIBE audit_logs');
    while ($row = $result->fetch()) {
        echo $row['Field'] . ' - ' . $row['Type'] . "\n";
    }
    
    echo "\nCurrent indexes:\n";
    $result = $db->query('SHOW INDEX FROM audit_logs');
    while ($row = $result->fetch()) {
        echo $row['Key_name'] . " on " . $row['Column_name'] . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>