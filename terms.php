<?php
require_once 'includes/db_connect.php';
require_once 'classes/models/SystemSetting.php';
require_once 'classes/views/BaseView.php';

/**
 * Terms of Service Page
 */
class TermsPageView extends BaseView {
    private $settings;
    
    public function __construct() {
        parent::__construct();
        $this->settings = SystemSetting::getAllAsArray();
        $this->setTitle('Terms of Service - ' . ($this->settings['site_name'] ?? 'Coinage Trading'));
    }
    
    protected function renderHead() {
        parent::renderHead();
        ?>
        <meta name="description" content="Terms of Service for <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?> - Read our terms and conditions for using our cryptocurrency trading platform.">
        <link rel="stylesheet" href="/assets/css/landing-page.css">
        <style>
            .terms-content {
                line-height: 1.8;
            }
            .terms-content h3 {
                color: #2c3e50;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }
            .terms-content h4 {
                color: #34495e;
                margin-top: 1.5rem;
                margin-bottom: 0.75rem;
            }
            .terms-content p {
                margin-bottom: 1rem;
            }
            .terms-content ul {
                margin-bottom: 1rem;
            }
            .terms-content li {
                margin-bottom: 0.5rem;
            }
        </style>
        <?php
    }
    
    protected function renderContent() {
        ?>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-coins me-2"></i>
                    <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/about.php">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/contact.php">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/login.php">Login</a>
                        </li>
                        <li class="nav-item">
                            <a class="btn btn-primary ms-2" href="/register.php">Sign Up</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Content Section -->
        <section class="py-5" style="margin-top: 80px;">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow">
                            <div class="card-body p-5">
                                <h1 class="mb-4">Terms of Service</h1>
                                <p class="text-muted mb-4">
                                    <strong>Last Updated:</strong> <?= date('F j, Y') ?>
                                </p>
                                
                                <div class="terms-content">
                                    <h3>1. Acceptance of Terms</h3>
                                    <p>
                                        By accessing and using <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?> 
                                        ("the Platform", "we", "us", or "our"), you accept and agree to be bound by the terms 
                                        and provision of this agreement. If you do not agree to abide by the above, please do 
                                        not use this service.
                                    </p>

                                    <h3>2. Description of Service</h3>
                                    <p>
                                        <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?> provides 
                                        a cryptocurrency trading platform that allows users to:
                                    </p>
                                    <ul>
                                        <li>Trade various cryptocurrencies</li>
                                        <li>Deposit and withdraw funds</li>
                                        <li>Access trading tools and analytics</li>
                                        <li>Participate in trading plans and investment opportunities</li>
                                    </ul>

                                    <h3>3. User Accounts</h3>
                                    <h4>3.1 Account Registration</h4>
                                    <p>
                                        To use our services, you must create an account by providing accurate, current, 
                                        and complete information. You are responsible for maintaining the confidentiality 
                                        of your account credentials.
                                    </p>
                                    
                                    <h4>3.2 Account Verification</h4>
                                    <p>
                                        We may require identity verification (KYC) before you can access certain features. 
                                        You agree to provide accurate documentation when requested.
                                    </p>
                                    
                                    <h4>3.3 Account Security</h4>
                                    <p>
                                        You are responsible for all activities that occur under your account. You must 
                                        immediately notify us of any unauthorized use of your account.
                                    </p>

                                    <h3>4. Trading and Investment</h3>
                                    <h4>4.1 Risk Disclosure</h4>
                                    <p>
                                        Cryptocurrency trading involves substantial risk of loss. Past performance does not 
                                        guarantee future results. You should only invest what you can afford to lose.
                                    </p>
                                    
                                    <h4>4.2 Trading Plans</h4>
                                    <p>
                                        Our trading plans are investment opportunities with varying risk levels and potential 
                                        returns. All investments are subject to market conditions and may result in losses.
                                    </p>
                                    
                                    <h4>4.3 No Investment Advice</h4>
                                    <p>
                                        We do not provide investment advice. All trading decisions are your own responsibility. 
                                        You should consult with financial advisors before making investment decisions.
                                    </p>

                                    <h3>5. Fees and Payments</h3>
                                    <h4>5.1 Trading Fees</h4>
                                    <p>
                                        We charge fees for trading activities as disclosed on our platform. Fees may change 
                                        with prior notice.
                                    </p>
                                    
                                    <h4>5.2 Deposits and Withdrawals</h4>
                                    <p>
                                        Deposit and withdrawal processing times vary by payment method. We reserve the right 
                                        to verify transactions and may delay processing for security reasons.
                                    </p>

                                    <h3>6. Prohibited Activities</h3>
                                    <p>You agree not to:</p>
                                    <ul>
                                        <li>Use the platform for illegal activities</li>
                                        <li>Attempt to manipulate markets or prices</li>
                                        <li>Create multiple accounts to circumvent limits</li>
                                        <li>Use automated trading systems without permission</li>
                                        <li>Engage in money laundering or terrorist financing</li>
                                        <li>Violate any applicable laws or regulations</li>
                                    </ul>

                                    <h3>7. Privacy and Data Protection</h3>
                                    <p>
                                        Your privacy is important to us. Please review our Privacy Policy to understand 
                                        how we collect, use, and protect your information.
                                    </p>

                                    <h3>8. Intellectual Property</h3>
                                    <p>
                                        All content on the platform, including text, graphics, logos, and software, is 
                                        owned by us or our licensors and is protected by copyright and other intellectual 
                                        property laws.
                                    </p>

                                    <h3>9. Disclaimers and Limitation of Liability</h3>
                                    <h4>9.1 Service Availability</h4>
                                    <p>
                                        We strive to maintain platform availability but do not guarantee uninterrupted 
                                        service. We may suspend service for maintenance or security reasons.
                                    </p>
                                    
                                    <h4>9.2 Limitation of Liability</h4>
                                    <p>
                                        To the maximum extent permitted by law, we shall not be liable for any indirect, 
                                        incidental, special, or consequential damages arising from your use of the platform.
                                    </p>

                                    <h3>10. Termination</h3>
                                    <p>
                                        We may terminate or suspend your account at any time for violation of these terms 
                                        or for any other reason. You may close your account at any time by contacting support.
                                    </p>

                                    <h3>11. Governing Law</h3>
                                    <p>
                                        These terms are governed by the laws of the jurisdiction where our company is 
                                        incorporated. Any disputes will be resolved through binding arbitration.
                                    </p>

                                    <h3>12. Changes to Terms</h3>
                                    <p>
                                        We may update these terms at any time. Continued use of the platform after changes 
                                        constitutes acceptance of the new terms. We will notify users of significant changes.
                                    </p>

                                    <h3>13. Contact Information</h3>
                                    <p>
                                        If you have questions about these terms, please contact us at:
                                    </p>
                                    <ul>
                                        <li>Email: <EMAIL></li>
                                        <li>Phone: +****************</li>
                                        <li>Address: 123 Trading Street, Crypto City, CC 12345</li>
                                    </ul>
                                </div>
                                
                                <div class="mt-5 pt-4 border-top">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <a href="/privacy.php" class="btn btn-outline-primary">
                                                <i class="fas fa-shield-alt me-2"></i>Privacy Policy
                                            </a>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <a href="/contact.php" class="btn btn-primary">
                                                <i class="fas fa-envelope me-2"></i>Contact Us
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer-section bg-dark text-white py-5">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <h5><?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?></h5>
                        <p>Professional cryptocurrency trading platform with advanced security and cutting-edge technology.</p>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6>Legal</h6>
                        <ul class="footer-links">
                            <li><a href="/terms.php">Terms of Service</a></li>
                            <li><a href="/privacy.php">Privacy Policy</a></li>
                            <li><a href="/security.php">Security</a></li>
                            <li><a href="/compliance.php">Compliance</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-2 col-md-6 mb-4">
                        <h6>Support</h6>
                        <ul class="footer-links">
                            <li><a href="/user/support/index.php">Help Center</a></li>
                            <li><a href="/contact.php">Contact Us</a></li>
                            <li><a href="/about.php">About Us</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <h6>Contact Info</h6>
                        <p><i class="fas fa-envelope me-2"></i><EMAIL></p>
                        <p><i class="fas fa-phone me-2"></i>+****************</p>
                    </div>
                </div>
                <hr class="my-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="mb-0">&copy; <?= date('Y') ?> <?= htmlspecialchars($this->settings['site_name'] ?? 'Coinage Trading') ?>. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-0">
                            <i class="fas fa-shield-alt me-1"></i>
                            Secured by SSL encryption
                        </p>
                    </div>
                </div>
            </div>
        </footer>
        <?php
    }
}

$view = new TermsPageView();
$view->render();
?>