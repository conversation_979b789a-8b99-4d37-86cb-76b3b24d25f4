<?php
session_start();
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/Deposit.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to continue']);
    exit();
}

// Ensure user role is 'user'
if (!hasRole('user')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

// Check if request is GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Get deposit ID
    $depositId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if (!$depositId) {
        throw new Exception('Invalid deposit ID');
    }
    
    // Get current user
    $user = getCurrentUser();
    if (!$user) {
        throw new Exception('User session expired');
    }
    
    // Get deposit with details
    $sql = "SELECT d.*, 
                   tp.name as plan_name,
                   pm.name as payment_method_name,
                   u_admin.first_name as approved_by_first_name,
                   u_admin.last_name as approved_by_last_name
            FROM deposits d
            LEFT JOIN trading_plans tp ON d.plan_id = tp.id
            LEFT JOIN payment_methods pm ON d.payment_method_id = pm.id
            LEFT JOIN users u_admin ON d.approved_by = u_admin.id
            WHERE d.id = :deposit_id AND d.user_id = :user_id";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        'deposit_id' => $depositId,
        'user_id' => $user->getId()
    ]);
    
    $deposit = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$deposit) {
        throw new Exception('Deposit not found');
    }
    
    // Format approved by name
    if ($deposit['approved_by_first_name'] && $deposit['approved_by_last_name']) {
        $deposit['approved_by_name'] = $deposit['approved_by_first_name'] . ' ' . $deposit['approved_by_last_name'];
    } else {
        $deposit['approved_by_name'] = null;
    }
    
    // Remove sensitive fields
    unset($deposit['approved_by_first_name']);
    unset($deposit['approved_by_last_name']);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'deposit' => $deposit
    ]);
    
} catch (Exception $e) {
    error_log("Deposit details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>