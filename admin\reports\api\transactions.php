<?php
require_once '../../../includes/db_connect.php';
require_once '../../../includes/functions.php';
require_once '../../../classes/services/AuthenticationManager.php';
require_once '../../../classes/services/FinancialReportingService.php';

header('Content-Type: application/json');

// Check authentication and admin role
if (!AuthenticationManager::isLoggedIn() || !AuthenticationManager::hasRole(['admin', 'super_admin'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

try {
    $dateFrom = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
    $dateTo = $_GET['date_to'] ?? date('Y-m-d');
    $page = max(1, (int) ($_GET['page'] ?? 1));
    $limit = min(100, max(10, (int) ($_GET['limit'] ?? 50)));
    
    // Build filters
    $filters = [];
    if (!empty($_GET['type'])) {
        $filters['type'] = $_GET['type'];
    }
    if (!empty($_GET['status'])) {
        $filters['status'] = $_GET['status'];
    }
    if (!empty($_GET['user_id'])) {
        $filters['user_id'] = (int) $_GET['user_id'];
    }
    if (!empty($_GET['min_amount'])) {
        $filters['min_amount'] = (float) $_GET['min_amount'];
    }
    if (!empty($_GET['max_amount'])) {
        $filters['max_amount'] = (float) $_GET['max_amount'];
    }
    
    // Validate date format
    if (!DateTime::createFromFormat('Y-m-d', $dateFrom) || !DateTime::createFromFormat('Y-m-d', $dateTo)) {
        throw new InvalidArgumentException('Invalid date format');
    }
    
    $reportingService = new FinancialReportingService();
    $report = $reportingService->getTransactionReport($dateFrom, $dateTo, $filters, $page, $limit);
    
    // Format transactions for display
    foreach ($report['transactions'] as &$transaction) {
        $transaction['formatted_amount'] = number_format(abs($transaction['amount']), 2);
        $transaction['amount_class'] = $transaction['amount'] >= 0 ? 'text-success' : 'text-danger';
        $transaction['status_class'] = match($transaction['status']) {
            'completed' => 'success',
            'pending' => 'warning',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            default => 'secondary'
        };
        $transaction['type_display'] = ucwords(str_replace('_', ' ', $transaction['type']));
        $transaction['user_display'] = $transaction['first_name'] . ' ' . $transaction['last_name'];
    }
    
    echo json_encode([
        'success' => true,
        'data' => $report
    ]);
    
} catch (Exception $e) {
    error_log("Transaction report API error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>