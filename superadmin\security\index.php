<?php
session_start();
require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../../includes/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';
require_once __DIR__ . '/../../classes/models/User.php';
require_once __DIR__ . '/../../classes/models/SystemSetting.php';
require_once __DIR__ . '/../../classes/services/CSRFProtection.php';

// Ensure user is authenticated and has superadmin role
if (!isLoggedIn() || !hasRole('superadmin')) {
    redirectTo('superadmin/login.php');
}

$pageTitle = 'Security Center';
$user = getCurrentUser();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $response = ['success' => false, 'message' => ''];
    
    try {
        if (!CSRFProtection::validateToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'update_security_settings':
                $settings = [
                    'session_timeout' => (int)($_POST['session_timeout'] ?? 30),
                    'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
                    'lockout_duration' => (int)($_POST['lockout_duration'] ?? 15),
                    'password_min_length' => (int)($_POST['password_min_length'] ?? 8),
                    'require_2fa' => isset($_POST['require_2fa']) ? 1 : 0,
                    'enable_audit_logs' => isset($_POST['enable_audit_logs']) ? 1 : 0,
                ];
                
                foreach ($settings as $key => $value) {
                    SystemSetting::setValue($key, $value);
                }
                
                $response = ['success' => true, 'message' => 'Security settings updated successfully'];
                break;
                
            case 'clear_audit_logs':
                $db = getDB();
                $stmt = $db->prepare("DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)");
                $days = (int)($_POST['days'] ?? 30);
                $stmt->execute([$days]);
                
                $response = ['success' => true, 'message' => "Audit logs older than {$days} days cleared"];
                break;
                
            default:
                throw new Exception('Invalid action');
        }
        
    } catch (Exception $e) {
        $response = ['success' => false, 'message' => $e->getMessage()];
    }
    
    // Return JSON response for AJAX requests
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // Store response in session for page reload
    $_SESSION['security_response'] = $response;
    header('Location: ' . $_SERVER['REQUEST_URI']);
    exit;
}

// Get response from session if available
$response = $_SESSION['security_response'] ?? null;
unset($_SESSION['security_response']);

// Get current security settings
$sessionTimeout = SystemSetting::getValue('session_timeout', 30);
$maxLoginAttempts = SystemSetting::getValue('max_login_attempts', 5);
$lockoutDuration = SystemSetting::getValue('lockout_duration', 15);
$passwordMinLength = SystemSetting::getValue('password_min_length', 8);
$require2FA = SystemSetting::getValue('require_2fa', 0);
$enableAuditLogs = SystemSetting::getValue('enable_audit_logs', 1);

// Get security statistics
$db = getDB();

// Failed login attempts in last 24 hours
$stmt = $db->query("SELECT COUNT(*) as failed_logins FROM audit_logs WHERE action = 'login_failed' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)");
$failedLogins = $stmt->fetch()['failed_logins'] ?? 0;

// Active sessions - check if table and column exist first
try {
    $stmt = $db->query("SHOW TABLES LIKE 'user_sessions'");
    $sessionTableExists = $stmt->rowCount() > 0;

    if ($sessionTableExists) {
        $stmt = $db->query("SHOW COLUMNS FROM user_sessions LIKE 'expires_at'");
        $expiresColumnExists = $stmt->rowCount() > 0;

        if ($expiresColumnExists) {
            $stmt = $db->query("SELECT COUNT(DISTINCT user_id) as active_sessions FROM user_sessions WHERE expires_at > NOW()");
            $activeSessions = $stmt->fetch()['active_sessions'] ?? 0;
        } else {
            // Fallback: count recent login sessions from audit logs
            $stmt = $db->query("SELECT COUNT(DISTINCT user_id) as active_sessions FROM audit_logs WHERE action = 'Login Success' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
            $activeSessions = $stmt->fetch()['active_sessions'] ?? 0;
        }
    } else {
        // Fallback: count recent login sessions from audit logs
        $stmt = $db->query("SELECT COUNT(DISTINCT user_id) as active_sessions FROM audit_logs WHERE action = 'Login Success' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $activeSessions = $stmt->fetch()['active_sessions'] ?? 0;
    }
} catch (Exception $e) {
    // Fallback: count recent login sessions from audit logs
    $stmt = $db->query("SELECT COUNT(DISTINCT user_id) as active_sessions FROM audit_logs WHERE action = 'Login Success' AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $activeSessions = $stmt->fetch()['active_sessions'] ?? 0;
}

// Recent security events
$stmt = $db->prepare("SELECT * FROM audit_logs WHERE action IN ('login_failed', 'password_changed', 'admin_created', 'admin_deleted') ORDER BY created_at DESC LIMIT 10");
$stmt->execute();
$recentEvents = $stmt->fetchAll();

// Get admin users count
$stmt = $db->query("SELECT COUNT(*) as admin_count FROM users WHERE role IN ('admin', 'superadmin')");
$adminCount = $stmt->fetch()['admin_count'] ?? 0;

$siteName = SystemSetting::getValue('site_name', 'Coinage Trading');
$siteLogo = SystemSetting::getValue('site_logo', '/assets/images/logo.png');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - Super Admin - <?php echo $siteName; ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo SystemSetting::getValue('primary_color', '#007bff'); ?>;
            --sidebar-bg: <?php echo SystemSetting::getValue('sidebar_bg_color', '#343a40'); ?>;
            --sidebar-text: <?php echo SystemSetting::getValue('sidebar_text_color', '#ffffff'); ?>;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--sidebar-bg) 0%, #2c3e50 100%);
            min-height: 100vh;
            color: var(--sidebar-text);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: var(--sidebar-text);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: #fff;
            transform: translateX(5px);
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 20px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .security-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }
        
        .btn-custom {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            padding: 12px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
        
        .superadmin-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 px-0">
            <div class="sidebar d-flex flex-column p-3">
                <div class="text-center mb-4">
                    <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?>" class="navbar-brand img-fluid" style="max-height: 50px;">
                    <h5 class="mt-2"><?php echo $siteName; ?></h5>
                    <div class="superadmin-badge mt-2">SUPER ADMIN</div>
                </div>
                
                <div class="user-info mb-4 text-center">
                    <div class="avatar bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="mt-2">
                        <small>Super Admin:</small><br>
                        <strong><?php echo htmlspecialchars($user['first_name']); ?></strong>
                    </div>
                </div>
                
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard/">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../settings/">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../appearance/">
                            <i class="fas fa-palette me-2"></i>Appearance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../email-templates/">
                            <i class="fas fa-envelope-open-text me-2"></i>Email Templates
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../audit/">
                            <i class="fas fa-clipboard-list me-2"></i>Audit Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../backup/">
                            <i class="fas fa-database me-2"></i>Database Backup
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="../security/">
                            <i class="fas fa-shield-alt me-2"></i>Security Center
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../../logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="main-content p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0"><i class="fas fa-shield-alt me-2"></i>Security Center</h1>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-custom" onclick="exportSecurityReport()">
                            <i class="fas fa-download me-1"></i>Export Report
                        </button>
                        <button class="btn btn-outline-danger btn-custom" onclick="emergencyLockdown()">
                            <i class="fas fa-lock me-1"></i>Emergency Lockdown
                        </button>
                    </div>
                </div>
                
                <?php if ($response): ?>
                    <div class="alert alert-<?php echo $response['success'] ? 'success' : 'danger'; ?> alert-dismissible fade show alert-custom">
                        <?php echo htmlspecialchars($response['message']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Security Overview -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Failed Logins (24h)</h6>
                                    <h3 class="mb-0"><?php echo $failedLogins; ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Active Sessions</h6>
                                    <h3 class="mb-0"><?php echo $activeSessions; ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Admin Users</h6>
                                    <h3 class="mb-0"><?php echo $adminCount; ?></h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-user-shield fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="d-flex align-items-center">
                                <div class="flex-grow-1">
                                    <h6 class="mb-0">Security Level</h6>
                                    <h3 class="mb-0">High</h3>
                                </div>
                                <div class="ms-3">
                                    <i class="fas fa-shield-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="security-card">
                            <h5 class="mb-4"><i class="fas fa-cog me-2"></i>Security Settings</h5>

                            <form method="POST" id="securitySettingsForm">
                                <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                <input type="hidden" name="action" value="update_security_settings">

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Session Timeout (minutes)</label>
                                        <input type="number" class="form-control" name="session_timeout" value="<?php echo $sessionTimeout; ?>" min="5" max="1440">
                                        <small class="text-muted">How long users stay logged in when inactive</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Max Login Attempts</label>
                                        <input type="number" class="form-control" name="max_login_attempts" value="<?php echo $maxLoginAttempts; ?>" min="3" max="10">
                                        <small class="text-muted">Failed attempts before account lockout</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Lockout Duration (minutes)</label>
                                        <input type="number" class="form-control" name="lockout_duration" value="<?php echo $lockoutDuration; ?>" min="5" max="60">
                                        <small class="text-muted">How long accounts remain locked</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Minimum Password Length</label>
                                        <input type="number" class="form-control" name="password_min_length" value="<?php echo $passwordMinLength; ?>" min="6" max="20">
                                        <small class="text-muted">Required password character count</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="require_2fa" id="require2fa" <?php echo $require2FA ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="require2fa">
                                                Require Two-Factor Authentication
                                            </label>
                                        </div>
                                        <small class="text-muted">Force all users to enable 2FA</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="enable_audit_logs" id="enableAudit" <?php echo $enableAuditLogs ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enableAudit">
                                                Enable Audit Logging
                                            </label>
                                        </div>
                                        <small class="text-muted">Log all user actions and system events</small>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-custom">
                                    <i class="fas fa-save me-1"></i>Save Security Settings
                                </button>
                            </form>
                        </div>

                        <!-- Admin User Management -->
                        <div class="security-card">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>Admin User Management</h5>
                                <a href="../admins/" class="btn btn-outline-primary btn-custom">
                                    <i class="fas fa-users-cog me-1"></i>Manage Admins
                                </a>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center p-3 bg-light rounded">
                                        <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                        <h6>Total Admins</h6>
                                        <h4 class="text-primary"><?php echo $adminCount; ?></h4>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <p class="mb-2"><strong>Quick Actions:</strong></p>
                                    <div class="d-flex gap-2 flex-wrap">
                                        <a href="../admins/?action=create" class="btn btn-sm btn-success">
                                            <i class="fas fa-plus me-1"></i>Add Admin
                                        </a>
                                        <button class="btn btn-sm btn-warning" onclick="auditAdminAccess()">
                                            <i class="fas fa-search me-1"></i>Audit Access
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="revokeAllSessions()">
                                            <i class="fas fa-ban me-1"></i>Revoke All Sessions
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SMTP Configuration -->
                        <div class="security-card">
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>SMTP Configuration</h5>
                                <button class="btn btn-outline-primary btn-custom" onclick="testSMTP()">
                                    <i class="fas fa-paper-plane me-1"></i>Test SMTP
                                </button>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Current SMTP Status:</strong></p>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2">Connected</span>
                                        <small class="text-muted">Last test: 2 hours ago</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex gap-2">
                                        <a href="../settings/#smtp" class="btn btn-sm btn-primary">
                                            <i class="fas fa-cog me-1"></i>Configure SMTP
                                        </a>
                                        <a href="../email-templates/" class="btn btn-sm btn-info">
                                            <i class="fas fa-envelope-open-text me-1"></i>Email Templates
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <!-- Recent Security Events -->
                        <div class="security-card">
                            <h5 class="mb-4"><i class="fas fa-history me-2"></i>Recent Security Events</h5>

                            <div class="timeline">
                                <?php if (empty($recentEvents)): ?>
                                    <p class="text-muted">No recent security events</p>
                                <?php else: ?>
                                    <?php foreach ($recentEvents as $event): ?>
                                        <div class="timeline-item mb-3">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <?php
                                                    $iconClass = 'fas fa-info-circle text-info';
                                                    if ($event['action'] === 'login_failed') $iconClass = 'fas fa-exclamation-triangle text-warning';
                                                    if ($event['action'] === 'admin_created') $iconClass = 'fas fa-user-plus text-success';
                                                    if ($event['action'] === 'admin_deleted') $iconClass = 'fas fa-user-minus text-danger';
                                                    ?>
                                                    <i class="<?php echo $iconClass; ?>"></i>
                                                </div>
                                                <div class="flex-grow-1 ms-2">
                                                    <small class="text-muted"><?php echo date('M j, H:i', strtotime($event['created_at'])); ?></small>
                                                    <div class="small"><?php echo htmlspecialchars($event['description'] ?? $event['action']); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>

                            <div class="text-center mt-3">
                                <a href="../audit/" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-list me-1"></i>View All Logs
                                </a>
                            </div>
                        </div>

                        <!-- Audit Log Management -->
                        <div class="security-card">
                            <h5 class="mb-4"><i class="fas fa-broom me-2"></i>Audit Log Management</h5>

                            <form method="POST" id="auditCleanupForm">
                                <input type="hidden" name="csrf_token" value="<?php echo CSRFProtection::generateToken(); ?>">
                                <input type="hidden" name="action" value="clear_audit_logs">

                                <div class="mb-3">
                                    <label class="form-label">Clear logs older than:</label>
                                    <select class="form-select" name="days">
                                        <option value="30">30 days</option>
                                        <option value="60">60 days</option>
                                        <option value="90">90 days</option>
                                        <option value="180">6 months</option>
                                        <option value="365">1 year</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-warning btn-custom w-100" onclick="return confirm('Are you sure you want to clear old audit logs?')">
                                    <i class="fas fa-trash me-1"></i>Clear Old Logs
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function exportSecurityReport() {
    // Implementation for exporting security report
    alert('Security report export functionality will be implemented');
}

function emergencyLockdown() {
    if (confirm('This will lock down the system and log out all users. Continue?')) {
        // Implementation for emergency lockdown
        alert('Emergency lockdown functionality will be implemented');
    }
}

function testSMTP() {
    // Implementation for SMTP testing
    alert('SMTP test functionality will be implemented');
}

function auditAdminAccess() {
    // Implementation for admin access audit
    alert('Admin access audit functionality will be implemented');
}

function revokeAllSessions() {
    if (confirm('This will log out all users immediately. Continue?')) {
        // Implementation for revoking all sessions
        alert('Session revocation functionality will be implemented');
    }
}

// Form submission handling
document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving settings');
    });
});

document.getElementById('auditCleanupForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while clearing logs');
    });
});
</script>

</body>
</html>
