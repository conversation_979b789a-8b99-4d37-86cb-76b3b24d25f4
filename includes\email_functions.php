<?php
require_once __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService {
    private $mailer;
    
    public function __construct() {
        $this->mailer = new PHPMailer(true);
        $this->configureSMTP();
    }
    
    private function configureSMTP() {
        try {
            $this->mailer->isSMTP();
            $this->mailer->Host = SMTP_HOST;
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = SMTP_USERNAME;
            $this->mailer->Password = SMTP_PASSWORD;
            $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            $this->mailer->Port = SMTP_PORT;
            $this->mailer->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        } catch (Exception $e) {
            error_log("SMTP Configuration Error: " . $e->getMessage());
        }
    }
    
    public function sendEmail($to, $subject, $body, $isHTML = true) {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($to);
            $this->mailer->isHTML($isHTML);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            return $this->mailer->send();
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function testConnection() {
        try {
            $this->mailer->smtpConnect();
            $this->mailer->smtpClose();
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

// Email Template Functions
function getEmailTemplate($templateName, $variables = []) {
    $templatePath = __DIR__ . "/../layouts/email_templates/{$templateName}.php";
    
    if (!file_exists($templatePath)) {
        return false;
    }
    
    ob_start();
    extract($variables);
    include $templatePath;
    return ob_get_clean();
}

function sendWelcomeEmail($userEmail, $userName) {
    $emailService = new EmailService();
    $template = getEmailTemplate('welcome', [
        'userName' => $userName,
        'siteName' => SITE_NAME,
        'loginUrl' => BASE_URL . 'login.php'
    ]);
    
    return $emailService->sendEmail(
        $userEmail,
        'Welcome to ' . SITE_NAME,
        $template
    );
}

function sendDepositConfirmation($userEmail, $userName, $amount, $plan) {
    $emailService = new EmailService();
    $template = getEmailTemplate('deposit_confirmation', [
        'userName' => $userName,
        'amount' => formatCurrency($amount),
        'plan' => $plan,
        'bonus' => formatCurrency($amount * (DEPOSIT_BONUS_PERCENT / 100)),
        'dashboardUrl' => BASE_URL . 'user/dashboard/'
    ]);
    
    return $emailService->sendEmail(
        $userEmail,
        'Deposit Confirmation - ' . SITE_NAME,
        $template
    );
}

function sendPasswordReset($userEmail, $userName, $resetToken) {
    $emailService = new EmailService();
    $template = getEmailTemplate('password_reset', [
        'userName' => $userName,
        'resetUrl' => BASE_URL . 'reset_password.php?token=' . $resetToken
    ]);
    
    return $emailService->sendEmail(
        $userEmail,
        'Password Reset - ' . SITE_NAME,
        $template
    );
}
?>